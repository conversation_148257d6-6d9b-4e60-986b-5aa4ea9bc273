import { Injectable, Renderer2 } from '@angular/core';
import { Observable } from 'rxjs';
//import { JournalEntriesApiService } from './../../../../core/api/accounts/journal-entries-api.service';
import { JournalParams } from './../models/journalParams';
import { HttpParams } from '@angular/common/http';
import { map } from 'rxjs/operators';
import { TransactionLine, Journal } from '../models/journal';
import { JournalEntriesApiService } from 'src/app/core/api/accounts/journal-entries-api.service';

@Injectable({
  providedIn: 'root',
})
export class JournalEntriesService {
  constructor(private journalEntriesApiService: JournalEntriesApiService) {}

  getAllJournalEntries(journalParams: JournalParams): Observable<any> {
    let params = new HttpParams();

    if (journalParams.searchType) {
      if (journalParams.searchType == 'journalNumber') {
        journalParams.searchType = 'journalNumber';
      }
      params = params.append('searchType', journalParams.searchType);
    }

    if (journalParams.journalCreationType) {
      params = params.append('journalCreationType', journalParams.journalCreationType);
    }
    if (journalParams.journalType) {
      params = params.append('journalType', journalParams.journalType);
    }
    if (journalParams.searchString) {
      params = params.append('searchString', journalParams.searchString);
    }
    if (journalParams.pageNumber) {
      params = params.append('pageNumber', journalParams.pageNumber.toString());
    }
    if (journalParams.pageSize) {
      params = params.append('pageSize', journalParams.pageSize.toString());
    }
    return this.journalEntriesApiService
      .getAllJournalEntries(params)
      .pipe(map((response: any) => response));
  }

  createJournalEntries(journalEntries: any) {
    return this.journalEntriesApiService
      .createJournalEntries(journalEntries)
      .pipe(map((response: any) => response));
  }

  updateJournalEntries(accountId: number, journalEntries: any) {
    return this.journalEntriesApiService
      .updateJournalEntries(accountId, journalEntries)
      .pipe(map((response: any) => response));
  }

  deleteEntry(transactionLineId?: string): Observable<TransactionLine> {
    const params = new HttpParams();
    return this.journalEntriesApiService
      .deleteEntry(transactionLineId, params)
      .pipe(map((response: TransactionLine) => response));
  }

  getJournalById(journalId: number): Observable<Journal> {
    return this.journalEntriesApiService
      .getJournalById(journalId)
      .pipe(map((response: any) => response));
  }

  postJournalEntries(journalId: number) {
    return this.journalEntriesApiService
      .postJournalById(journalId)
      .pipe(map((response: any) => response));
  }
}
