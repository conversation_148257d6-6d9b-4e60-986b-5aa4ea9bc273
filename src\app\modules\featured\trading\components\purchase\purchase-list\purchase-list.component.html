<mat-card appearance="outlined">
  <mat-card-content>
    <mat-tab-group #tabs [dynamicHeight]="true" mat-stretch-tabs="false" animationDuration="0ms">
      <mat-tab>
        <!-- action bar -->
        <app-create-action
          *appHasPermission="['Purchase.Create', 'AllPermissions']"
          [label]="'purchase.register_purchase' | translate"></app-create-action>
        <!-- action bar -->

        <ng-template mat-tab-label>{{ 'purchase.purchaseList' | translate }}</ng-template>

        <!-- search field -->
        <div class="row no-gutters">
          <div class="col-md-6 col-lg-6 col-sm-12 d-flex">
            <mat-form-field class="w-100">
              <input
                #searchInput
                (keyup)="applyFilter($event.target.value)"
                matInput
                autocomplete="off" />
              <i-tabler class="icon-16" matPrefix name="search"></i-tabler>
              <a
                class="cursor-pointer"
                *ngIf="searchInput?.value"
                (click)="clearSearchInput()"
                matSuffix>
                <i-tabler class="icon-16 error" name="X"></i-tabler>
              </a>
            </mat-form-field>
          </div>
        </div>
        <!-- search field -->
        <mat-card-title class="m-t-10">{{ 'purchase.purchaseList' | translate }}</mat-card-title>
        <div class="table-responsive">
          <table class="w-100" #table [dataSource]="dataSource" mat-table matSort>
            <ng-container matColumnDef="documentNumber">
              <th *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'sales.invoiceNo' | translate }}
              </th>
              <td class="f-s-14" *matCellDef="let element" mat-cell>
                {{ element.documentNumber }}
              </td>
            </ng-container>
            <ng-container matColumnDef="issueDate">
              <th *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'sales.invoiceDate' | translate }}
              </th>
              <td class="f-s-14" *matCellDef="let element" mat-cell>
                {{ element.issueDate | date : 'yyyy-MM-dd' }}
              </td>
            </ng-container>
            <ng-container matColumnDef="invoiceTotal">
              <th *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'sales.invoiceAmt' | translate }}
              </th>
              <td class="f-s-14" *matCellDef="let element" mat-cell>
                {{ element.invoiceTotal }}
              </td>
            </ng-container>
            <ng-container matColumnDef="accountNumber">
              <th *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'purchase.invoiceCustomerNo' | translate }}
              </th>
              <td
                class="f-s-14"
                *matCellDef="let element"
                [data]="element.account?.accountNumber"
                [copyValue]="element.account?.accountNumber"
                appHyphen
                appCopyClick
                mat-cell>
                {{ element.account?.accountNumber }}
              </td>
            </ng-container>
            <ng-container matColumnDef="nameArabic">
              <th *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'common.field.nameArabic' | translate }}
              </th>
              <td
                *matCellDef="let element"
                [data]="element.account?.nameArabic"
                [copyValue]="element.account?.nameArabic"
                mat-cell
                appHyphen
                appCopyClick>
                {{ element.account?.nameArabic }}
              </td>
            </ng-container>
            <ng-container matColumnDef="nameEnglish">
              <th *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'common.field.nameEnglish' | translate }}
              </th>
              <td
                *matCellDef="let element"
                [data]="element.account?.nameEnglish"
                [copyValue]="element.account?.nameEnglish"
                mat-cell
                appHyphen
                appCopyClick>
                {{ element.account?.nameEnglish }}
              </td>
            </ng-container>
            <ng-container matColumnDef="vatNumber">
              <th *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'common.field.vatNo' | translate }}
              </th>
              <td
                class="f-s-14"
                *matCellDef="let element"
                [data]="element.vatNumber"
                mat-cell
                appHyphen>
                {{ element.vatNumber }}
              </td>
            </ng-container>
            <ng-container matColumnDef="phoneNumber">
              <th *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'common.field.phone' | translate }}
              </th>
              <td
                class="f-s-14"
                *matCellDef="let element"
                [data]="element.phoneNumber"
                mat-cell
                appHyphen>
                {{ element.phoneNumber }}
              </td>
            </ng-container>
            <ng-container matColumnDef="paymentMethod">
              <th *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'paymentsType.paymentMethod' | translate }}
              </th>
              <td *matCellDef="let element" mat-cell>
                <div class="text-center cursor-pointer rounded bg-light-primary">
                  {{ 'paymentsType.' + element.paymentMethod | translate }}
                </div>
              </td>
            </ng-container>
            <ng-container matColumnDef="returnStatus">
              <th *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'sales.returnStatus' | translate }}
              </th>
              <td class="f-s-14" *matCellDef="let element" mat-cell>
                <div
                  class="text-center cursor-pointer rounded"
                  [ngClass]="{
                    ' bg-light-error': element.returnStatus === 'NOTRETURNED',
                    'bg-light-primary': element.returnStatus === 'PARTIALLYRETURNED',
                    'bg-light-success': element.returnStatus === 'RETURNED'
                  }">
                  {{ element.returnStatus | translate }}
                </div>
              </td>
            </ng-container>
            <ng-container matColumnDef="action">
              <th *matHeaderCellDef mat-header-cell>
                {{ 'common.action' | translate }}
              </th>
              <td class="f-s-14" *matCellDef="let element" mat-cell>
                <button
                  class="d-flex justify-content-center"
                  [matMenuTriggerFor]="menu1"
                  mat-icon-button>
                  <i-tabler class="icon-20 d-flex" name="dots-vertical"></i-tabler>
                </button>
                <mat-menu class="cardWithShadow" #menu1="matMenu">
                  <button [routerLink]="['view', element.id]" mat-menu-item>
                    <div class="d-flex align-items-center">
                      <i-tabler class="icon-16 m-r-4" name="eye"></i-tabler>
                      <span>{{ 'common.viewAction' | translate }}</span>
                    </div>
                  </button>
                  <!-- <ng-container
                    *ngIf="
                      element.returnStatus !== 'RETURNED' &&
                      element.returnStatus !== 'PARTIALLYRETURNED'
                    ">
                    <button [routerLink]="['edit', element.id]" mat-menu-item>
                      <div class="d-flex align-items-center">
                        <i-tabler class="icon-16 m-r-4" name="edit"></i-tabler>
                        <span>{{ 'common.editAction' | translate }}</span>
                      </div>
                    </button>
                  </ng-container> -->

                  <ng-container
                    *ngIf="
                      element.returnStatus !== 'RETURNED' &&
                      element.returnStatus !== 'PARTIALLYRETURNED'
                    ">
                    <button [routerLink]="['creditnote', element.id]" mat-menu-item>
                      <div class="d-flex align-items-center">
                        <i-tabler class="icon-16 m-r-4" name="edit"></i-tabler>
                        <span>{{ 'purchase.fullReturn' | translate }}</span>
                      </div>
                    </button>
                  </ng-container>

                  <ng-container
                    *ngIf="
                      element.returnStatus !== 'RETURNED' ||
                      element.returnStatus === 'PARTIALLYRETURNED'
                    ">
                    <button [routerLink]="['partialcreditnote', element.id]" mat-menu-item>
                      <div class="d-flex align-items-center">
                        <i-tabler class="icon-16 m-r-4" name="edit"></i-tabler>
                        <span>{{ 'purchase.partialReturn' | translate }}</span>
                      </div>
                    </button>
                  </ng-container>

                  <ng-container *ngIf="element.returnStatus !== 'NOTRETURNED'">
                    <button (click)="openSalesNotes(element.id); (false)" mat-menu-item>
                      <div class="d-flex align-items-center">
                        <i-tabler class="icon-16 m-r-4" name="clipboard-list"></i-tabler>
                        <span>{{ 'purchase.viewNotes' | translate }}</span>
                      </div>
                    </button>
                  </ng-container>
                  <!-- <ng-container
                    *ngIf="
                      element.returnStatus !== 'RETURNED' &&
                      element.returnStatus !== 'PARTIALLYRETURNED'
                    ">
                    <button mat-menu-item>
                      <div class="d-flex align-items-center">
                        <i-tabler class="icon-16 m-r-4" name="trash"></i-tabler>
                        <span>{{ 'common.deleteAction' | translate }}</span>
                      </div>
                    </button>
                  </ng-container> -->
                </mat-menu>
              </td>
            </ng-container>
            <tr class="mat-row" *matNoDataRow>
              <td class="f-s-14" class="text-center" [attr.colspan]="displayedColumns.length">
                {{ 'common.noDataFound' | translate }}
              </td>
            </tr>
            <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
            <tr
              *matRowDef="let row; columns: displayedColumns"
              [appMatTableKeyboardNavigation]="selection"
              [rowModel]="row"
              [matTable]="table"
              [focusFirstOption]="true"
              [selectOnFocus]="true"
              [ngClass]="{ selected: selection.isSelected(row) }"
              mat-row></tr>
          </table>
        </div>
        <mat-paginator
          *ngIf="dataSource?.filteredData?.length > 0"
          [length]="dataSource.filteredData.length"
          [pageIndex]="0"
          [pageSize]="10"
          [pageSizeOptions]="[5, 10, 20]"></mat-paginator>
      </mat-tab>

      <mat-tab>
        <ng-template mat-tab-label>{{ 'sales.salesReceivables' | translate }}</ng-template>
        <app-sales-purchase-receivable
          [payables]="
            true
          "></app-sales-purchase-receivable> </mat-tab></mat-tab-group></mat-card-content
></mat-card>
