<!----------------------------------- mat table content --------------------------------------->
<ng-container *ngIf="!isLoading">
  <app-create-action
    *appHasPermission="['ChartOfAccounts.Create', 'AllPermissions']"
    [label]="'costCenter.create' | translate"></app-create-action>
  <!-- action bar -->

  <mat-card appearance="outlined">
    <mat-card-content>
      <!-- search field -->
      <div class="row no-gutters">
        <div class="col-md-6 col-lg-6 col-sm-12 d-flex">
          <mat-form-field class="w-100">
            <input
              #searchInput
              (keyup)="applyFilter($event.target.value)"
              matInput
              autocomplete="off" />
            <i-tabler class="icon-16" matPrefix name="search"></i-tabler>
            <a
              class="cursor-pointer"
              *ngIf="searchInput?.value"
              (click)="clearSearchInput()"
              matSuffix>
              <i-tabler class="icon-16 error" name="X"></i-tabler>
            </a>
          </mat-form-field>
        </div>
      </div>
      <mat-card-title class="m-t-10">{{ 'costCenter.listings' | translate }}</mat-card-title>
      <!-- search field -->
      <div class="table-responsive">
        <table class="w-100" [dataSource]="dataSource" mat-table matSort>
          <!-- CostCentres Name Column -->
          <ng-container matColumnDef="accountNumber">
            <th *matHeaderCellDef mat-header-cell mat-sort-header>
              {{ 'costCenter.accountNumber' | translate }}
            </th>
            <td *matCellDef="let element" mat-cell>
              {{ element.accountNumber }}
            </td>
          </ng-container>
          <!-- CostCentres Type Column -->
          <ng-container matColumnDef="nameArabic">
            <th *matHeaderCellDef mat-header-cell mat-sort-header>
              {{ 'costCenter.arabicName' | translate }}
            </th>
            <td *matCellDef="let element" mat-cell>
              {{ element.nameArabic }}
            </td>
          </ng-container>
          <!-- CostCentres Factor Column -->
          <ng-container matColumnDef="nameEnglish">
            <th *matHeaderCellDef mat-header-cell mat-sort-header>
              {{ 'costCenter.englishName' | translate }}
            </th>
            <td *matCellDef="let element" mat-cell>
              {{ element.nameEnglish }}
            </td>
          </ng-container>
          <!-- User Actions Column -->
          <ng-container matColumnDef="action">
            <th *matHeaderCellDef mat-header-cell>{{ 'common.action' | translate }}</th>
            <td *matCellDef="let element" mat-cell>
              <a
                class="m-r-10 cursor-pointer"
                *appHasPermission="['ChartOfAccounts.Update', 'AllPermissions']"
                [routerLink]="['edit', element.costCentreId]"
                ><i-tabler class="icon-16" name="edit"></i-tabler
              ></a>
              <a
                class="m-r-10 cursor-pointer"
                *appHasPermission="['ChartOfAccounts.View', 'AllPermissions']"
                [routerLink]="['view', element.costCentreId]"
                ><i-tabler class="icon-16" name="eye"></i-tabler
              ></a>
              <a
                class="m-r-10 cursor-pointer"
                *appHasPermission="['ChartOfAccounts.Delete', 'AllPermissions']"
                (click)="deleteCostCentres(element.costCentreId)"
                ><i-tabler class="icon-16" name="trash"></i-tabler
              ></a>
            </td>
          </ng-container>
          <tr class="mat-row" *matNoDataRow>
            <td class="text-center text-info" [attr.colspan]="displayedColumns.length">
              {{ 'common.noDataFound' | translate }}
            </td>
          </tr>
          <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
          <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
        </table>
      </div>
      <mat-paginator
        [length]="dataSource.filteredData.length"
        [pageIndex]="0"
        [pageSize]="10"
        [pageSizeOptions]="[5, 10, 20]"></mat-paginator>
    </mat-card-content>
  </mat-card>
</ng-container>
<!------------------------------------------- not costCentress message --------------------------------->
