import { Component, Input, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'app-journal-entries-search-box',
  templateUrl: './journal-entries-search-box.component.html',
  styleUrls: ['./journal-entries-search-box.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: JournalEntriesSearchBoxComponent,
    },
  ],
})
export class JournalEntriesSearchBoxComponent implements OnInit {
  @Input() filterTypes: string[];
  searchBoxForm: UntypedFormGroup;
  searchPlaceholder = 'searchPanel.searchByJournalRef';
  touched = false;
  defaultSearchType = 'journalNumber';

  constructor(private fb: UntypedFormBuilder) {}
  ngOnInit(): void {
    this.searchBoxForm = this.fb.group({
      searchString: [],
      searchType: [this.defaultSearchType],
    });
    // this.updatePlaceHolder(this.defaultSearchType);
    // this.searchBoxForm
    //   this.searchBoxForm?.controls['searchType'].valueChanges.subscribe((selection) => {
    //     this.updatePlaceHolder(selection);
    //   });
  }

  // updatePlaceHolder(selection) {
  //   switch (selection) {
  //     case 'journalId':
  //       this.searchPlaceholder = 'Search by Journal Ref';
  //       break;
  //     case 'entryDate':
  //       this.searchPlaceholder = 'Search by Entry Date';
  //       break;
  //     case 'entryType':
  //       this.searchPlaceholder = 'Search by Entry Type';
  //       break;
  //     default:
  //       this.searchPlaceholder = 'Search by Journal Number';
  //   }
  // }

  resetSearchBox(): void {
    this.searchBoxForm.reset();
    this.searchBoxForm.controls['searchType'].setValue(this.defaultSearchType);
    this.searchBoxForm.updateValueAndValidity();
  }

  writeValue(obj: any): void {
    obj && this.searchBoxForm.setValue(obj, { emitEvent: false });
  }
  registerOnChange(fn: any): void {
    this.searchBoxForm.valueChanges.subscribe(fn);
  }
  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }
  setDisabledState?(isDisabled: boolean): void {}

  onTouched() {}
}
