import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { UtilityDashboardComponent } from './components/dashboard/utility-dashboard.component';
import { DataImportComponent } from './components/data-import/data-import.component';
import { BranchSetupComponent } from './components/branch-setup/branch-setup.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'utility',
    pathMatch: 'full',
  },
  {
    path: 'utility',
    component: UtilityDashboardComponent,
    data: {
      title: 'Utility Dashboard',
      urls: [{ title: 'Main Dashboard', url: '/dashboard' }],
    },
  },
  {
    path: 'dataImport',
    component: DataImportComponent,
    data: {
      title: 'Data Import',
      urls: [{ title: 'Utility Dashboard', url: '/utility' }],
    },
  },
  {
    path: 'branchSetup',
    component: BranchSetupComponent,
    data: {
      title: 'Copy Accounts',
      urls: [{ title: 'Utility Dashboard', url: '/utility' }],
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class UtilityRoutingModule {}
