<app-dialog-header></app-dialog-header>

<!-- search field -->
<div class="row no-gutters m-t-10 p-4">
  <div class="col-md-6 col-lg-6 col-sm-12 d-flex">
    <mat-form-field class="w-100">
      <input
        #searchInput
        (keyup)="onSearchInput($event.target.value)"
        matInput
        autocomplete="off" />
      <i-tabler class="icon-16" matPrefix name="search"></i-tabler>
      <a class="cursor-pointer" *ngIf="searchInput?.value" (click)="clearSearchInput()" matSuffix>
        <i-tabler class="icon-16 error" name="X"></i-tabler>
      </a>
    </mat-form-field>
  </div>
</div>
<!-- search field -->
<div class="dialog-container table-responsive m-t-10">
  <table class="w-100 p-4" #table #searchTable [dataSource]="dataSource" mat-table matSort>
    <ng-container matColumnDef="nameArabic">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'common.field.nameArabic' | translate }}
      </th>
      <td *matCellDef="let element" mat-cell>
        {{ element.nameArabic }}
      </td>
    </ng-container>

    <ng-container matColumnDef="nameEnglish">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'common.field.nameEnglish' | translate }}
      </th>
      <td *matCellDef="let element" mat-cell>
        {{ element.nameEnglish }}
      </td>
    </ng-container>

    <ng-container matColumnDef="vatNumber">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'common.field.vatNo' | translate }}
      </th>
      <td *matCellDef="let element" mat-cell>
        {{ element.vatNumber }}
      </td>
    </ng-container>

    <ng-container matColumnDef="accountNumber">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'common.field.accountNumber' | translate }}
      </th>
      <td *matCellDef="let element" mat-cell>
        {{ element.accountNumber }}
      </td>
    </ng-container>

    <ng-container matColumnDef="phoneNumber">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'common.field.phone' | translate }}
      </th>
      <td *matCellDef="let element" mat-cell>
        {{ element.phoneNumber }}
      </td>
    </ng-container>

    <ng-container matColumnDef="emailId">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'common.field.email' | translate }}
      </th>
      <td *matCellDef="let element" mat-cell>
        {{ element.emailId }}
      </td>
    </ng-container>

    <tr *matHeaderRowDef="displayedSearchColumns; sticky: true" mat-header-row></tr>
    <tr
      *matRowDef="let row; columns: displayedSearchColumns"
      [rowModel]="row"
      [matTable]="table"
      [focusFirstOption]="true"
      [selectOnFocus]="true"
      [ngClass]="{ selected: selection.isSelected(row) }"
      [appMatTableKeyboardNavigation]="selection"
      (rowSelected)="selectRow($event)"
      (click)="selectRow(row)"
      mat-row></tr>
  </table>
</div>

<div class="spinner-container" *ngIf="isLoading">
  <mat-spinner class="spinner-wrapper" diameter="30"></mat-spinner>
</div>
