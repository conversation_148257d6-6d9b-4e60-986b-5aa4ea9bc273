import { Directionality } from '@angular/cdk/bidi';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/core/api/common.service';
import { DistributorService } from 'src/app/core/api/trading/distributor.service';
import { IDistributor, IDistributorResponse } from 'src/app/core/interfaces/distributor';
import { DistributorParams } from 'src/app/core/models/params/distributorParams';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { DeleteConfirmationComponent } from 'src/app/modules/shared/components/delete-confirmation/delete-confirmation.component';

@Component({
  selector: 'app-distributor-list',
  templateUrl: './distributor-list.component.html',
  styleUrls: ['./distributor-list.component.scss'],
})
export class DistributorListComponent implements OnInit {
  @ViewChild('filter') filter: ElementRef;
  @ViewChild('searchInput') searchInput: ElementRef;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  suppliers: IDistributor[];
  displayedColumns: string[];
  dataSource: MatTableDataSource<IDistributor>;
  isLoading = true;
  constructor(
    public distributorService: DistributorService,
    private authService: AuthService,
    public dialog: MatDialog,
    private toastr: ToastrService,
    private direction: Directionality,
    private commonService: CommonService
  ) {}

  ngOnInit(): void {
    this.getUnits();
    this.initColumns();
  }

  ngAfterViewInit(): void {
    if (this.dataSource) {
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
    }
  }

  getUnits(): void {
    const supplierParams = new DistributorParams();
    this.distributorService
      .getAllDistributors(supplierParams)
      .subscribe((result: IDistributorResponse) => {
        console.log(result, result.distributors);
        this.suppliers = result.distributors;
        this.isLoading = false;
        this.dataSource = new MatTableDataSource<IDistributor>(this.suppliers);
        this.dataSource.paginator = this.paginator;
      });
  }

  initColumns(): void {
    this.displayedColumns = [
      'action',
      'accountNumber',
      'nameArabic',
      'nameEnglish',
      'vatNumber',
      'phoneNumber',
    ];
  }

  applyFilter(filterValue: string): void {
    filterValue = filterValue.trim();
    filterValue = filterValue.toLowerCase();
    this.dataSource.filter = filterValue;
  }

  clearSearchInput() {
    this.searchInput.nativeElement.value = '';
    this.getUnits();
  }

  deleteUnit(customerId?: string) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '600px';
    dialogConfig.panelClass = 'green_theme';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(DeleteConfirmationComponent, dialogConfig);
    dialogRef.componentInstance.accepted.subscribe(result => {
      this.distributorService.deleteDistributor(customerId).subscribe(result => {
        this.commonService.playSuccessSound();
        this.getUnits();
        this.isLoading = false;
      });
    });
  }
}
