import { Injectable } from '@angular/core';
import { <PERSON>ttp<PERSON><PERSON><PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, <PERSON>ttpEvent, HttpInterceptor } from '@angular/common/http';
import { Observable } from 'rxjs';
import { delay, finalize } from 'rxjs/operators';
import { BusyService } from '../services/busy.service';

@Injectable()
export class LoadingInterceptor implements HttpInterceptor {
  totalRequests = 0;
  requestsCompleted = 0;
  constructor(private busyService: BusyService) {}

  intercept(request: HttpRequest<unknown>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<unknown>> {
    if (this.totalRequests === 0) {
      this.busyService.isLoading.next(true);
    }
    this.totalRequests++;

    return next.handle(request).pipe(
      finalize(() => {
        this.requestsCompleted++;

        console.log(this.requestsCompleted, this.totalRequests);

        if (this.requestsCompleted === this.totalRequests) {
          this.busyService.isLoading.next(false);
          this.totalRequests = 0;
          this.requestsCompleted = 0;
        }
      })
    );
  }
}
