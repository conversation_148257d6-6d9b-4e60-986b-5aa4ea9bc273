import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { UnitParams } from 'src/app/modules/featured/catalog/models/unitParams';
import { Units } from 'src/app/modules/featured/catalog/models/units';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class UnitService {
  baseUrl = environment.apiUrl + 'catalog/units';

  constructor(private http: HttpClient) {}

  getAllUnits() {
    const params = this.getParams(new UnitParams());
    return this.http.get<Units[]>(this.baseUrl, { params: params });
  }

  getUnitById(id: string) {
    const params = this.getParams(new UnitParams());
    return this.http.get<Units>(this.baseUrl + '/' + id, { params: params });
  }

  createUnit(units: Units) {
    const params = this.getParams(new UnitParams());
    return this.http.post(this.baseUrl, units, { params: params });
  }

  updateUnit(category: Units, id: string) {
    const params = this.getParams(new UnitParams());
    return this.http.put(this.baseUrl + '/' + id, category);
  }

  deleteUnit(id: string) {
    const params = this.getParams(new UnitParams());
    return this.http.delete(this.baseUrl + '/' + id, { params: params });
  }

  importUnit(formData: FormData) {
    const params = this.getParams(new UnitParams());
    return this.http.post(this.baseUrl + '/import', formData, {
      withCredentials: true,
      responseType: 'arraybuffer',
      params: params,
    });
  }

  getParams(categoryParams): HttpParams {
    let params = new HttpParams();
    if (categoryParams.searchString) {
      params = params.append('searchString', categoryParams.searchString);
    }
    if (categoryParams.pageNumber) {
      params = params.append('pageNumber', categoryParams.pageNumber.toString());
    }
    if (categoryParams.pageSize) {
      params = params.append('pageSize', categoryParams.pageSize.toString());
    }
    if (categoryParams.orderBy) {
      params = params.append('orderBy', categoryParams.orderBy.toString());
    }
    return params;
  }
}
