.row.no-gutters {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  gap: 10px;
  width: 100%;
  overflow-x: auto;
}

.filter-field {
  flex: 1;
  min-width: 150px;
  box-sizing: border-box;
  margin-top: 5px;
  margin-bottom: 5px;
}

.mat-form-field {
  width: 100%;
}

::ng-deep .custom-dropdown-panel {
  min-width: 300px;
}

:host ::ng-deep .mat-mdc-form-field-subscript-wrapper {
  display: none !important;
}

.submit {
  height: 30px;
  background-color: transparent;
  border: 1px solid grey;
  border-radius: 5px;
  cursor: pointer;
}

.clear-btn {
  height: 30px;
  background-color: transparent;
  border: 1px solid #ccc;
  border-radius: 5px;
  cursor: pointer;
  color: #666;
}

.clear-btn:hover {
  background-color: #f5f5f5;
  border-color: #999;
}

.report-type-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.no-report-selected {
  margin-top: 20px;
}

.info-card {
  background-color: #e3f2fd;
  border: 1px solid #2196f3;
  border-radius: 8px;
}

.info-content {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #1976d2;
}

.info-content mat-icon {
  color: #2196f3;
}

.info-content p {
  margin: 0;
  font-size: 14px;
}
