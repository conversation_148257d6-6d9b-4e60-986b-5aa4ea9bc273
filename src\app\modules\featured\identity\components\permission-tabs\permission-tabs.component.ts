import { Component, Input } from '@angular/core';
import { ActionType } from 'src/app/core/enums/actionType';

interface Permission {
  permission: string;
  thirdQualifier: string;
  selected?: boolean;
}

interface GroupedPermissions {
  [firstQualifier: string]: {
    [secondQualifier: string]: {
      permission: string;
      thirdQualifier: string;
      selected: boolean;
    }[];
  };
}

@Component({
  selector: 'app-permission-tabs',
  templateUrl: './permission-tabs.component.html',
  styleUrls: ['./permission-tabs.component.scss'],
})
export class PermissionTabsComponent {
  @Input() adminPermissions: string[];
  @Input() userPermissions: string[];
  @Input() roleName: string;
  @Input() disabled: boolean;
  @Input() mode: ActionType;
  // permissions: string[] = [
  //   'TradeManagement.Sales',
  //   'TradeManagement.Quotation.Create',
  //   'ReportsManagement.PriceReports',
  //   'IdentityManagement.User.View',
  //   'AccountsManagement.ChartOfAccounts.Delete',
  //   'EnterpriseManagement.WareHouse',
  //   'IdentityManagement.User.Create',
  //   'InventoryManagement.Units.Create',
  //   'AccountsManagement.CostCentre',
  //   'AccountsManagement.Depreciation',
  //   'EnterpriseManagement.Branch.Delete',
  //   'InventoryManagement.Adjustments.Create',
  //   'TradeManagement.Ucvoucher.Post',
  //   'AccountsManagement.ChartOfAccounts.Create',
  //   'TradeManagement.Quotation.View',
  //   'InventoryManagement.Categories.Update',
  //   'InventoryManagement.Categories.Delete',
  //   'InventoryManagement.Categories',
  //   'TradeManagement.Customer.View',
  //   'InventoryManagement.Categories.View',
  //   'EnterpriseManagement.Branch.Update',
  //   'AccountsManagement.ChartOfAccounts',
  //   'InventoryManagement.Units.Delete',
  //   'TradeManagement.Ucvoucher',
  //   'EnterpriseManagement.Branch.Create',
  //   'AccountsManagement.ChartOfAccounts.View',
  //   'ReportsManagement.PurchasePrice',
  //   'InventoryManagement.UnitPriceAdjustment.Create',
  //   'TradeManagement.Distributor',
  //   'TradeManagement.Ucvoucher.Delete',
  //   'EnterpriseManagement.Company.Update',
  //   'TradeManagement.Sales.View',
  //   'AccountsManagement.ChartOfAccounts.Update',
  //   'TradeManagement.Customer.Update',
  //   'InventoryManagement.Categories.Create',
  //   'InventoryManagement.Product.Create',
  //   'EnterpriseManagement.WareHouse.Delete',
  //   'TradeManagement.Quotation.Edit',
  //   'AccountsManagement.CostCentre.View',
  //   'EnterpriseManagement.Company',
  //   'AccountsManagement.JournalEntries.Update',
  //   'TradeManagement.Supplier.Create',
  //   'TradeManagement.Customer',
  //   'InventoryManagement.Units.View',
  //   'InventoryManagement.Product.View',
  //   'InventoryManagement.Product.BulkEdit',
  //   'TradeManagement.Supplier.Delete',
  //   'AccountsManagement.CostCentre.Create',
  //   'TradeManagement.Supplier.Update',
  //   'InventoryManagement.Product.Delete',
  //   'InventoryManagement.Transfer.Update',
  //   'TradeManagement.Customer.Delete',
  //   'AccountsManagement.Depreciation.Update',
  //   'IdentityManagement.User.Update',
  //   'EnterpriseManagement.Branch.View',
  //   'InventoryManagement.Transfer.Create',
  //   'IdentityManagement.Role.View',
  //   'InventoryManagement.Product',
  //   'InventoryManagement.Units.Update',
  //   'EnterpriseManagement.Branch',
  //   'TradeManagement.Supplier',
  //   'AccountsManagement.JournalEntries.Delete',
  //   'TradeManagement.Ucvoucher.View',
  //   'IdentityManagement.Role.Create',
  //   'AccountsManagement.Depreciation.Create',
  //   'TradeManagement.Distributor.Create',
  //   'TradeManagement.Purchase.View',
  //   'TradeManagement.Distributor.Update',
  //   'TradeManagement.Quotation',
  //   'InventoryManagement.Adjustments.View',
  //   'AccountsManagement.CostCentre.Update',
  //   'EnterpriseManagement.WareHouse.Update',
  //   'TradeManagement.Ucvoucher.Create',
  //   'AccountsManagement.JournalEntries',
  //   'AccountsManagement.JournalEntries.Create',
  //   'TradeManagement.Customer.Create',
  //   'TradeManagement.Ucvoucher.Edit',
  //   'IdentityManagement.User',
  //   'TradeManagement.Sales.Create',
  //   'InventoryManagement.Adjustments.PriceUpdate',
  //   'TradeManagement.Quotation.Post',
  //   'InventoryManagement.Units',
  //   'InventoryManagement.Transfer',
  //   'EnterpriseManagement.WareHouse.Create',
  //   'InventoryManagement.Adjustments.Update',
  //   'EnterpriseManagement.WareHouse.View',
  //   'TradeManagement.Purchase.Delete',
  //   'InventoryManagement.OpenQtyAdjustments.Create',
  //   'InventoryManagement.Product.Update',
  //   'AccountsManagement.Depreciation.View',
  //   'AccountsManagement.JournalEntries.View',
  //   'TradeManagement.Distributor.View',
  //   'TradeManagement.Supplier.View',
  //   'AccountsManagement.CostCentre.Delete',
  //   'TradeManagement.Purchase',
  //   'InventoryManagement.Adjustments.Post',
  //   'TradeManagement.Purchase.Create',
  //   'InventoryManagement.Transfer.Post',
  //   'TradeManagement.Quotation.Delete',
  //   'InventoryManagement.Transfer.Delete',
  //   'InventoryManagement.Transfer.View',
  //   'ReportsManagement.StockValueReports',
  //   'IdentityManagement.Role',
  //   'IdentityManagement.Role.Update',
  //   'TradeManagement.Distributor.Delete',
  //   'AccountsManagement.Depreciation.Delete',
  // ];
  // userpermissions: string[] = [
  //   'EnterpriseManagement.WareHouse',

  //   'EnterpriseManagement.Branch.Delete',

  //   'EnterpriseManagement.Branch.Update',

  //   'EnterpriseManagement.Branch.Create',

  //   'EnterpriseManagement.Company.Update',

  //   'EnterpriseManagement.WareHouse.Delete',

  //   'EnterpriseManagement.Company',

  //   'EnterpriseManagement.Branch.View',

  //   'EnterpriseManagement.Branch',

  //   'EnterpriseManagement.WareHouse.Update',

  //   'EnterpriseManagement.WareHouse.Create',

  //   'EnterpriseManagement.WareHouse.View',
  // ];
  selectAllChecked = false;
  groupedPermissions: GroupedPermissions;

  constructor() {
    //
  }

  get isViewMode(): boolean {
    return this.mode === ActionType.view;
  }

  ngOnInit() {
    // Helper function to filter out permissions for excluded roles
    const excludeRoles = (permissions: string[]): string[] => {
      if (!permissions) return [];
      return permissions.filter(p => !p.includes('ROLE_ADMIN') && !p.includes('ROLE_POS_ADMIN'));
    };

    if (this.mode === ActionType.edit) {
      this.groupedPermissions = this.groupPermissionsMatch(
        excludeRoles(this.adminPermissions),
        excludeRoles(this.userPermissions)
      );
    }

    if (this.mode === ActionType.create) {
      this.groupedPermissions = this.groupPermissionsMatch(
        excludeRoles(this.adminPermissions),
        excludeRoles(this.userPermissions)
      );
    }

    if (this.mode === ActionType.view) {
      this.groupedPermissions = this.groupPermissions(excludeRoles(this.userPermissions));
    }
  }

  groupPermissionsMatch(
    masterPermissions: string[],
    userPermissions: string[]
  ): GroupedPermissions {
    const groupedPermissions: GroupedPermissions = {};

    masterPermissions.forEach(permissionString => {
      const [firstQualifier, secondQualifier, thirdQualifier] = permissionString.split('.');

      if (!groupedPermissions[firstQualifier]) {
        groupedPermissions[firstQualifier] = {};
      }

      if (!groupedPermissions[firstQualifier][secondQualifier]) {
        groupedPermissions[firstQualifier][secondQualifier] = [];
      }

      const selected =
        userPermissions && userPermissions.length
          ? userPermissions.includes(permissionString)
          : false;
      groupedPermissions[firstQualifier][secondQualifier].push({
        permission: permissionString,
        thirdQualifier,
        selected,
      });
    });

    return groupedPermissions;
  }

  groupPermissions(permissions: string[]): GroupedPermissions {
    const groupedPermissions: GroupedPermissions = {};

    permissions.forEach(permissionString => {
      const [firstQualifier, secondQualifier, thirdQualifier] = permissionString.split('.');

      if (!groupedPermissions[firstQualifier]) {
        groupedPermissions[firstQualifier] = {};
      }

      if (!groupedPermissions[firstQualifier][secondQualifier]) {
        groupedPermissions[firstQualifier][secondQualifier] = [];
      }

      const selected = this.userPermissions.includes(permissionString); // Check if permissionString is present in permissions array
      groupedPermissions[firstQualifier][secondQualifier].push({
        permission: permissionString,
        thirdQualifier,
        selected,
      });
    });

    return groupedPermissions;
  }

  selectAll(parent: string, child: string) {
    if (this.groupedPermissions[parent] && this.groupedPermissions[parent][child]) {
      this.groupedPermissions[parent][child].forEach(permission => {
        permission.selected = true;
      });
    }
  }

  selectNone(parent: string, child: string) {
    if (this.groupedPermissions[parent] && this.groupedPermissions[parent][child]) {
      this.groupedPermissions[parent][child].forEach(permission => {
        permission.selected = false;
      });
    }
  }

  isAllSelected(parent: string, child: string) {
    if (this.groupedPermissions[parent] && this.groupedPermissions[parent][child]) {
      return this.groupedPermissions[parent][child].every(permission => permission.selected);
    }
    return false;
  }

  toggleSelectAll(parent: string, child: string, checked: boolean) {
    if (this.groupedPermissions[parent] && this.groupedPermissions[parent][child]) {
      this.groupedPermissions[parent][child].forEach(permission => {
        permission.selected = checked;
      });
    }
  }

  getCheckedCount(parent: string, child?: string): string {
    if (child) {
      if (this.groupedPermissions[parent] && this.groupedPermissions[parent][child]) {
        const permissions = this.groupedPermissions[parent][child];
        const checkedCount = permissions.filter(permission => permission.selected).length;
        const totalCount = permissions.length;
        return `${checkedCount}/${totalCount}`;
      }
    } else {
      let totalCount = 0;
      let checkedCount = 0;
      Object.keys(this.groupedPermissions[parent]).forEach(key => {
        const permissions = this.groupedPermissions[parent][key];
        checkedCount += permissions.filter(permission => permission.selected).length;
        totalCount += permissions.length;
      });
      return `${checkedCount}/${totalCount}`;
    }
    return '';
  }

  toggleSelection(permission: Permission) {
    const parent = Object.keys(this.groupedPermissions).find(key =>
      Object.keys(this.groupedPermissions[key]).includes(permission.thirdQualifier)
    );

    permission.selected = !permission.selected;

    if (!permission.selected && this.selectAllChecked) {
      this.selectAllChecked = false;
    }

    setTimeout(() => {
      if (parent) {
        this.updateCount(parent, permission.thirdQualifier);
      }
    });
  }

  updateCount(parent: string, child: string) {
    const checkedCount = this.groupedPermissions[parent][child].filter(
      permission => permission.selected
    ).length;
    const totalCount = this.groupedPermissions[parent][child].length;
    const tabElement = document.getElementById(`${parent}-${child}`);
    if (tabElement) {
      tabElement.textContent = `${child} (${checkedCount}/${totalCount})`;
    }
  }

  getCheckedPermissions(): string[] {
    const checkedPermissions: string[] = [];
    for (const firstQualifier of Object.keys(this.groupedPermissions)) {
      for (const secondQualifier of Object.keys(this.groupedPermissions[firstQualifier])) {
        const permissions = this.groupedPermissions[firstQualifier][secondQualifier];
        for (const permission of permissions) {
          if (permission.selected) {
            checkedPermissions.push(permission.permission);
          }
        }
      }
    }
    return checkedPermissions;
  }

  onSave() {
    const checkedPermissions = this.getCheckedPermissions();
    console.log('Checked permissions:', checkedPermissions);
    // Add your save logic here
  }

  public getRolePermissions(): string[] {
    return this.getCheckedPermissions();
  }

  isIndeterminate(parent: string, child: string): boolean {
    if (this.groupedPermissions[parent] && this.groupedPermissions[parent][child]) {
      const permissions = this.groupedPermissions[parent][child];
      const checkedCount = permissions.filter(permission => permission.selected).length;
      return checkedCount > 0 && checkedCount < permissions.length;
    }
    return false;
  }
}
