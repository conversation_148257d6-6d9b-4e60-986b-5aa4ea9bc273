@import 'variables';

/* account-tree.component.css */

/* Other existing styles... */

/* Styling for tree nodes */

/* Styling for node text */
.node-text {
  position: relative;
}

/* Styling for action icons */
.add-icon {
  position: relative;
  visibility: hidden;
}

.node-text:hover .add-icon {
  visibility: visible;
}

.primary {
  color: $primary;
}

.accent {
  color: $accent;
}

.warning {
  color: $warning;
}

.error {
  color: $error;
}

.success {
  color: $success;
}
