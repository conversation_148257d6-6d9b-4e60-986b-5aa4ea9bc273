import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { CustomValidators } from 'ngx-custom-validators';
import { ImageCroppedEvent } from 'ngx-image-cropper';
import { PerfectScrollbarConfigInterface } from 'ngx-perfect-scrollbar';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { Branch } from 'src/app/modules/featured/catalog/models/branch';
import { AddressComponent } from 'src/app/modules/shared/components/address/address.component';
import { BranchParams } from '../../../models/branchParams';
import { BranchService } from '../../../services/branch.service';
import { ActionType } from 'src/app/core/enums/actionType';
import { customerIndentifactionCode } from 'src/app/core/configs/dropDownConfig';
import { CommonService } from 'src/app/core/api/common.service';

@Component({
  selector: 'app-branch-form',
  templateUrl: './branch-form.component.html',
  styleUrls: ['./branch-form.component.scss'],
})
export class BranchFormComponent implements OnInit {
  @ViewChild('addressForm') addressForm: AddressComponent;
  branchForm: UntypedFormGroup;
  imageChangedEvent: any = '';
  croppedImage: any = '';
  showCropper = false;
  defaultImage = 'assets/images/users/default.png';
  file: File;
  formTitle: string;
  branchId: string;
  mode: ActionType;
  public config: PerfectScrollbarConfigInterface = {};
  identificationCodes = customerIndentifactionCode;
  constructor(
    private authService: AuthService,
    private toastr: ToastrService,
    private branchService: BranchService,
    private fb: UntypedFormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private commonService: CommonService
  ) {}

  get isEditMode(): boolean {
    return this.mode === ActionType.edit;
  }
  get isViewMode(): boolean {
    return this.mode === ActionType.view;
  }

  get isCreateMode(): boolean {
    return this.mode === ActionType.create;
  }

  ngOnInit(): void {
    this.mode = this.route.snapshot.data['mode'];
    this.formTitle = this.route.snapshot.data['title'];
    this.initializeForm();
    this.route.params.subscribe(params => {
      this.branchId = params['id'];
      if (this.branchId) {
        this.getBranches(this.branchId);
      }
    });
  }

  getBranches(branchId?: string): void {
    this.branchService.getBranchById(new BranchParams(), branchId).subscribe(
      response => {
        this.initializeForm(response);
      },
      error => console.log(error)
    );
  }

  initializeForm(data?: Branch) {
    this.branchForm = this.fb.group({
      nameArabic: [data?.nameArabic ?? null, Validators.compose([Validators.required])],
      nameEnglish: [data?.nameEnglish ?? null, Validators.compose([Validators.required])],
      phoneNumber: [data?.phoneNumber ?? 0, Validators.compose([Validators.required])],
      vatNumber: [data?.vatNumber ?? 0, Validators.compose([Validators.required])],
      maxWarehouses: [3],
      emailId: [data?.emailId ?? null, Validators.compose([CustomValidators.email])],
      address: data?.address ?? [],
      identification: [data?.identification ?? '', Validators.compose([Validators.required])],
      identificationCode: [
        data?.identificationCode ?? null,
        Validators.compose([Validators.required]),
      ],
      businessCategoryArabic: [
        data?.businessCategoryArabic ?? null,
        Validators.compose([Validators.required]),
      ],
      businessCategoryEnglish: [
        data?.businessCategoryEnglish ?? null,
        Validators.compose([Validators.required]),
      ],
      invoiceTrailing: [data?.invoiceTrailing ?? null, Validators.compose([Validators.required])],
    });
    if (this.isViewMode) {
      this.branchForm?.disable();
      this.addressForm?.disableForm();
    }
  }

  fileChangeEvent(event: any): void {
    const mimeType = event.target.files[0].type;
    if (mimeType.match(/image\/*/) == null) {
      return;
    }
    this.showCropper = true;
    this.imageChangedEvent = event;
  }
  imageCropped(event: ImageCroppedEvent) {
    this.croppedImage = event.base64;
  }
  imageLoaded() {
    // show cropper
  }
  cropperReady() {
    // cropper ready
  }
  loadImageFailed() {
    // show message
  }

  onSubmit(event: Event) {
    event.preventDefault();
    this.addressForm.markFormAsTouched();
    this.branchForm.markAllAsTouched();
    if (this.branchForm && this.branchForm?.valid && this.addressForm.isValid()) {
      if (!this.isEditMode) {
        const branchParams = new BranchParams();
        this.branchService.createBranch(branchParams, this.branchForm.value).subscribe(() => {
          //this.toastr.success('Branch Added Successfully');
          this.commonService.playSuccessSound();
          this.router.navigate(['../'], { relativeTo: this.route });
        });
      } else {
        this.branchService
          .updateBranch(new BranchParams(), this.branchId, this.branchForm.value)
          .subscribe(() => {
            //this.toastr.success('Branch Updated Successfully');
            this.commonService.playSuccessSound();
            this.router.navigate(['../../'], { relativeTo: this.route });
          });
      }
    } else {
      this.commonService.scrollToError();
    }
  }
}
