import { Component, ElementRef, Inject, OnInit, Optional, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { tap } from 'rxjs/operators';
import { PaginatedFilter } from 'src/app/core/interfaces/PaginatedFilter';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { SearchboxComponent } from 'src/app/modules/shared/components/searchbox/searchbox.component';
import { BulkEditData } from '../../../catalog/models/bulkedit';
import { CategoryService } from '../../../../../core/api/category.service';
import { ProductService } from '../../../../../core/api/product.service';
import { BranchService } from '../../../settings/services/branch.service';
import { UnitService } from 'src/app/core/api/unit.service';
import { StoreService } from 'src/app/core/api/store.service';
import { CommonService } from 'src/app/core/api/common.service';

@Component({
  selector: 'data-import',
  templateUrl: './data-import.component.html',
  styleUrls: ['./data-import.component.scss'],
})
export class DataImportComponent implements OnInit {
  @ViewChild('searchBoxForm') searchBoxForm: SearchboxComponent;
  loading = true;
  tableList: string[];

  @ViewChild('UploadFileInput', { static: false }) uploadFileInput: ElementRef;
  fileUploadForm: UntypedFormGroup;

  constructor(
    @Optional() @Inject(MAT_DIALOG_DATA) public data: BulkEditData,
    private branchService: BranchService,
    private storeService: StoreService,
    private categoryService: CategoryService,
    private productService: ProductService,
    private unitApiService: UnitService,
    private authService: AuthService,
    private toastr: ToastrService,
    private fb: UntypedFormBuilder,
    private commonService: CommonService
  ) {}

  ngOnInit(): void {
    this.loading = true;
    this.tableList = [
      'Branch',
      'Product',
      'ItemUnit',
      'Unit',
      'Category',
      'Warehouse',
      'Inventory',
    ];
    this.fileUploadForm = this.fb.group({
      myfile: [''],
      targetTable: [],
      fileName: ['', Validators.required],
    });
  }

  onFileSelect(event) {
    if (event.target.files.length > 0) {
      const file = event.target.files[0];
      this.fileUploadForm.get('myfile').setValue(file);
    }
  }

  onFormSubmit(event?: Event, pageEvent?: PaginatedFilter) {
    event?.preventDefault();
    this.fileUploadForm.markAllAsTouched();
    if (!this.fileUploadForm.get('myfile').value) {
      return false;
    }
    const targetTable = this.fileUploadForm.get('targetTable').value;
    const formData = new FormData();
    formData.append('file', this.fileUploadForm.get('myfile').value);

    if (targetTable === 'Branch') {
      this.branchService.importData(formData).subscribe(
        result => {
          //this.toastr.success('Branch is imported Successfully');
          this.commonService.playSuccessSound();
        },
        error => {
          console.log(error);
        }
      );
    } else if (targetTable === 'Warehouse') {
      this.storeService.importData(formData).subscribe(
        result => {
          //this.toastr.success('Warehouse is imported Successfully');
          this.commonService.playSuccessSound();
        },
        error => {
          console.log(error);
        }
      );
    } else if (targetTable === 'Category') {
      this.categoryService.importData(formData).subscribe(
        result => {
          //this.toastr.success('Category is imported Successfully');
          this.commonService.playSuccessSound();
        },
        error => {
          console.log(error);
        }
      );
    } else if (targetTable === 'Product') {
      this.productService.importData(formData).subscribe(
        result => {
          //this.toastr.success('Product is imported Successfully');
          this.commonService.playSuccessSound();
        },
        error => {
          console.log(error);
        }
      );
    } else if (targetTable === 'ItemUnit') {
      this.productService.importItemUnit(formData).subscribe(
        result => {
          //this.toastr.success('ItemUnit is imported Successfully');
          this.commonService.playSuccessSound();
        },
        error => {
          console.log(error);
        }
      );
    } else if (targetTable === 'Inventory') {
      this.productService.importInventory(formData).subscribe(
        result => {
          //this.toastr.success('Inventory is imported Successfully');
          this.commonService.playSuccessSound();
        },
        error => {
          console.log(error);
        }
      );
    } else if (targetTable === 'Unit') {
      this.unitApiService
        .importUnit(formData)
        .pipe(
          tap((result: ArrayBuffer) => {
            const fileURL = URL.createObjectURL(new Blob([result]));
            const link = document.createElement('a');
            link.href = fileURL;
            link.setAttribute('download', 'Unit_List.xls');
            document.body.appendChild(link);
            link.click();
          })
        )
        .subscribe(result => {
          //this.toastr.success('Unit is imported Successfully');
          this.commonService.playSuccessSound();
        });
    }
  }
}
