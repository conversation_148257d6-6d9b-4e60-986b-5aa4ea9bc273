import { Component, EventEmitter, Inject, Output } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ZatcaModalData } from 'src/app/core/interfaces/sales';

@Component({
  selector: 'app-zatca-response',
  templateUrl: './zatca-response.component.html',
  styleUrls: ['./zatca-response.component.scss'],
})
export class ZatcaResponseComponent {
  constructor(@Inject(MAT_DIALOG_DATA) public data: ZatcaModalData) {}
  @Output() public accepted: EventEmitter<any> = new EventEmitter();
  acceptWarning = false;
  get hasWarnings(): boolean {
    return (
      this.data?.zatcaReportingStatus &&
      this.data?.zatcaReportingStatus.validationResults &&
      this.data?.zatcaReportingStatus.validationResults['warningMessages'] &&
      this.data?.zatcaReportingStatus.validationResults['warningMessages'].length > 0
    );
  }

  get hasErrors(): boolean {
    return (
      this.data?.zatcaReportingStatus &&
      this.data?.zatcaReportingStatus.validationResults &&
      this.data?.zatcaReportingStatus.validationResults['errorMessages'] &&
      this.data?.zatcaReportingStatus.validationResults['errorMessages'].length > 0
    );
  }

  getObjectKeys(obj: unknown): string[] {
    return obj ? Object.keys(obj) : [];
  }

  isObject(value: unknown): boolean {
    return typeof value === 'object' && value !== null;
  }

  sendToZatca() {
    // Implement the logic to send data to Zatca
    if (this.acceptWarning) {
      console.log('Sending data to Zatca...');
      this.accepted.emit(true);
      // You can make an HTTP request to your API endpoint here
    } else {
      alert('Please accept the warning messages before proceeding.');
    }
  }
}
