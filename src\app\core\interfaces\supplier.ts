import { IAddress } from 'src/app/modules/shared/components/address/address';

export interface ISupplier {
  supplierId: number;
  accountId: number;
  parentAccountId: number;
  branchId: number;
  companyId: number;
  accountNumber: number;
  nameArabic: string;
  nameEnglish: string;
  isHidden: boolean;
  isFreezed: boolean;
  hasCashPrivilege: boolean;
  hasCreditPrivilege: boolean;
  hasCardPrivilege: boolean;
  hasTransferPrivilege: boolean;
  discountPercent: number;
  paymentToleranceDays: number;
  allowedBalance: number;
  yearlyTarget: number;
  commission: number;
  phoneNumber: string;
  emailId: string;
  vatNumber: string;
  address?: IAddress;
  costCentreId?: number;
  distributorAccountId?: number;
  identification?: string;
  identificationCode?: string;
}

export interface ISupplierResponse {
  searchTimeStamp: Date;
  totalRecordsCount: number;
  totalPages: number;
  morePages: boolean;
  suppliers: ISupplier[];
}
