import {
  Component,
  Input,
  Output,
  EventEmitter,
  ViewChildren,
  QueryList,
  OnInit,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { MatDatepicker } from '@angular/material/datepicker';
import { MatSelect } from '@angular/material/select';

interface ReportConfig {
  id: number;
  type: number;
  name: string;
  nameArabic: string;
  authority: string;
  languageCode: string;
  searchConfigs: any[];
  endPoint: string;
  jasperEndPoint: string;
}

@Component({
  selector: 'app-filter-form',
  templateUrl: './filter-form.component.html',
  styleUrls: ['./filter-form.component.scss'],
})
export class FilterFormComponent implements OnInit, OnChanges {
  @Input() searchConfigs: any[];
  @Input() filtersForm: FormGroup;
  @Input() isFullScreen = false;
  @Input() isAdvancedSearchVisible = false;
  @Input() getListForField: (field: string) => any[];
  @Input() getReportData: (event: Event) => void;
  @Input() onSearchInput: (input: string) => void;
  @Input() onItemCodeSelected: (input: any) => void;
  @Input() formName: string;
  @Input() reportConfigs: ReportConfig[] = []; // New input for report configurations
  @Output() reportTypeChanged = new EventEmitter<ReportConfig>(); // New output for report type changes
  @ViewChildren(MatSelect) matSelects!: QueryList<MatSelect>;

  // Component state
  selectedReportType: ReportConfig | null = null;
  currentSearchConfigs: any[] = [];
  reportTypeForm: FormGroup;
  @ViewChildren(MatDatepicker) datepickers: QueryList<MatDatepicker<any>>;

  constructor(private fb: FormBuilder) {
    this.reportTypeForm = this.fb.group({
      reportType: [null, Validators.required],
    });
  }

  ngOnInit() {
    // Initialize with hardcoded report configurations if not provided
    if (!this.reportConfigs || this.reportConfigs.length === 0) {
      this.reportConfigs = this.getHardcodedReportConfigs();
    }

    // Set up report type change listener
    this.reportTypeForm.get('reportType')?.valueChanges.subscribe((reportId: number) => {
      if (reportId) {
        this.onReportTypeChange(reportId);
      }
    });

    // Use existing searchConfigs if provided (backward compatibility)
    if (this.searchConfigs && this.searchConfigs.length > 0) {
      this.currentSearchConfigs = this.searchConfigs;
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['searchConfigs'] && this.searchConfigs) {
      this.currentSearchConfigs = this.searchConfigs;
    }
  }

  onReportTypeChange(reportId: number) {
    const selectedReport = this.reportConfigs.find(config => config.id === reportId);
    if (selectedReport) {
      this.selectedReportType = selectedReport;
      this.currentSearchConfigs = selectedReport.searchConfigs.sort(
        (a, b) => a.position - b.position
      );

      // Reset the filters form when report type changes
      if (this.filtersForm) {
        this.filtersForm.reset();
      }

      // Emit the change to parent component
      this.reportTypeChanged.emit(selectedReport);
    }
  }

  openDatepicker(backendParam: string) {
    const datepicker = this.datepickers.find(
      picker =>
        picker.datepickerInput
          ?.getConnectedOverlayOrigin()
          .nativeElement.getAttribute('formcontrolname') === backendParam
    );
    if (datepicker) {
      datepicker.open();
    }
  }
  getSelectForField(field: string): MatSelect | undefined {
    return this.matSelects?.toArray().find(matSelect => matSelect.ngControl?.name === field);
  }
  toggleAdvancedSearch() {
    this.isAdvancedSearchVisible = !this.isAdvancedSearchVisible;
  }
  datePickerId(param: string): string {
    return param;
  }
  clearFilters(event: Event) {
    event.preventDefault();
    this.filtersForm.markAsUntouched();
    this.filtersForm.markAsPristine();
    this.filtersForm.reset();
  }

  getHardcodedReportConfigs(): ReportConfig[] {
    return [
      {
        id: 1,
        type: 1,
        name: 'Pricing',
        nameArabic: 'Pricing',
        authority: 'ReportsManagement.Inventory.PriceReports',
        languageCode: 'AR',
        searchConfigs: [
          {
            field: 'branch',
            type: 'dropdown',
            mandatory: true,
            placeholder: ' ',
            position: 1,
            isAdvanced: false,
            fieldLabel: 'branch',
            backendParam: 'branchId',
            idField: 'branchId',
          },
          {
            field: 'year',
            type: 'dropdown',
            mandatory: true,
            placeholder: ' ',
            position: 2,
            isAdvanced: false,
            fieldLabel: 'year',
            backendParam: 'yearId',
            idField: 'id',
          },
          {
            field: 'itemCode',
            type: 'input',
            mandatory: false,
            placeholder: 'Search By ItemCode/Name',
            position: 3,
            isAdvanced: false,
            fieldLabel: 'item',
            backendParam: 'searchString',
            idField: 'itemId',
          },
          {
            field: 'categories',
            type: 'multiSelectDropdown',
            mandatory: false,
            placeholder: ' ',
            position: 4,
            isAdvanced: true,
            fieldLabel: 'categories',
            backendParam: 'categoryIds',
            idField: 'categoryId',
          },
          {
            field: 'units',
            type: 'multiSelectDropdown',
            mandatory: false,
            placeholder: ' ',
            position: 5,
            isAdvanced: true,
            fieldLabel: 'units',
            backendParam: 'unitIds',
            idField: 'unitId',
          },
        ],
        endPoint: '/pages/price-list',
        jasperEndPoint: '/dynamic/price-list',
      },
      {
        id: 3,
        type: 1,
        name: 'Item Detailed',
        nameArabic: 'Item Detailed',
        authority: 'ReportsManagement.Inventory.StockValueReports',
        languageCode: 'AR',
        searchConfigs: [
          {
            field: 'branch',
            type: 'dropdown',
            mandatory: true,
            placeholder: ' ',
            position: 1,
            isAdvanced: false,
            fieldLabel: 'branch',
            backendParam: 'branchId',
            idField: 'branchId',
          },
          {
            field: ' year',
            type: 'dropdown',
            mandatory: true,
            placeholder: ' ',
            position: 2,
            isAdvanced: false,
            fieldLabel: 'year',
            backendParam: 'yearId',
            idField: 'id',
          },
          {
            field: 'itemCode',
            type: 'input',
            mandatory: false,
            placeholder: 'Search By ItemCode/Name',
            position: 4,
            isAdvanced: false,
            fieldLabel: 'item',
            backendParam: 'searchString',
            idField: 'itemId',
          },
          {
            field: 'warehouse',
            type: 'multiSelectDropdown',
            mandatory: false,
            placeholder: ' ',
            position: 5,
            isAdvanced: false,
            fieldLabel: 'warehouse',
            backendParam: 'warehouseIds',
            idField: 'warehouseId',
          },
          {
            field: 'categories',
            type: 'multiSelectDropdown',
            mandatory: false,
            placeholder: ' ',
            position: 6,
            isAdvanced: true,
            fieldLabel: 'categories',
            backendParam: 'categoryIds',
            idField: 'categoryId',
          },
          {
            field: 'units',
            type: 'multiSelectDropdown',
            mandatory: false,
            placeholder: ' ',
            position: 7,
            isAdvanced: true,
            fieldLabel: 'units',
            backendParam: 'unitIds',
            idField: 'unitId',
          },
        ],
        endPoint: '/pages/item-detailed',
        jasperEndPoint: '/dynamic/item-detailed',
      },
      {
        id: 2,
        type: 1,
        name: 'Stock Value',
        nameArabic: 'Stock Value',
        authority: 'ReportsManagement.Inventory.StockValueReports',
        languageCode: 'AR',
        searchConfigs: [
          {
            field: 'branch',
            type: 'dropdown',
            mandatory: true,
            placeholder: ' ',
            position: 1,
            isAdvanced: false,
            fieldLabel: 'branch',
            backendParam: 'branchId',
            idField: 'branchId',
          },
          {
            field: 'year',
            type: 'dropdown',
            mandatory: true,
            placeholder: ' ',
            position: 2,
            isAdvanced: false,
            fieldLabel: 'year',
            backendParam: 'yearId',
            idField: 'id',
          },
          {
            field: 'itemCode',
            type: 'input',
            mandatory: false,
            placeholder: 'Search By ItemCode/Name',
            position: 4,
            isAdvanced: false,
            fieldLabel: 'item',
            backendParam: 'searchString',
            idField: 'itemId',
          },
          {
            field: 'warehouse',
            type: 'multiSelectDropdown',
            mandatory: true,
            placeholder: ' ',
            position: 5,
            isAdvanced: false,
            fieldLabel: 'warehouse',
            backendParam: 'warehouseIds',
            idField: 'warehouseId',
          },
          {
            field: 'categories',
            type: 'multiSelectDropdown',
            mandatory: false,
            placeholder: ' ',
            position: 6,
            isAdvanced: true,
            fieldLabel: 'categories',
            backendParam: 'categoryIds',
            idField: 'categoryId',
          },
          {
            field: 'units',
            type: 'multiSelectDropdown',
            mandatory: false,
            placeholder: ' ',
            position: 7,
            isAdvanced: true,
            fieldLabel: 'units',
            backendParam: 'unitIds',
            idField: 'unitId',
          },
        ],
        endPoint: '/pages/stock-value',
        jasperEndPoint: '/dynamic/stock-value',
      },
      {
        id: 4,
        type: 1,
        name: 'Item Movement',
        nameArabic: 'Item Movement',
        authority: 'ReportsManagement.Inventory.StockValueReports',
        languageCode: 'AR',
        searchConfigs: [
          {
            field: 'branch',
            type: 'dropdown',
            mandatory: true,
            placeholder: ' ',
            position: 1,
            isAdvanced: false,
            fieldLabel: 'branch',
            backendParam: 'branchId',
            idField: 'branchId',
          },
          {
            field: 'year',
            type: 'dropdown',
            mandatory: true,
            placeholder: ' ',
            position: 2,
            isAdvanced: false,
            fieldLabel: 'year',
            backendParam: 'yearId',
            idField: 'id',
          },
          {
            field: 'itemCode',
            type: 'input',
            mandatory: false,
            placeholder: 'Search By ItemCode/Name',
            position: 4,
            isAdvanced: false,
            fieldLabel: 'item',
            backendParam: 'searchString',
            idField: 'itemId',
          },
          {
            field: 'date',
            type: 'dateRange',
            mandatory: false,
            placeholder: ' ',
            position: 4,
            isAdvanced: true,
            fieldLabel: 'dateRange',
            backendParam: 'date',
            idField: 'id',
          },
        ],
        endPoint: '/pages/movement',
        jasperEndPoint: '/dynamic/movement',
      },
      {
        id: 7,
        type: 1,
        name: 'Inventory Ageing',
        nameArabic: 'Inventory Ageing',
        authority: 'ReportsManagement.Inventory.StockValueReports',
        languageCode: 'AR',
        searchConfigs: [
          {
            field: 'branch',
            type: 'dropdown',
            mandatory: true,
            placeholder: ' ',
            position: 1,
            isAdvanced: false,
            fieldLabel: 'branch',
            backendParam: 'branchId',
            idField: 'branchId',
          },
          {
            field: 'year',
            type: 'dropdown',
            mandatory: true,
            placeholder: ' ',
            position: 2,
            isAdvanced: false,
            fieldLabel: 'year',
            backendParam: 'yearId',
            idField: 'id',
          },
          {
            field: 'itemCode',
            type: 'input',
            mandatory: false,
            placeholder: 'Search By ItemCode/Name',
            position: 4,
            isAdvanced: false,
            fieldLabel: 'item',
            backendParam: 'searchString',
            idField: 'itemId',
          },
          {
            field: 'warehouse',
            type: 'multiSelectDropdown',
            mandatory: true,
            placeholder: ' ',
            position: 5,
            isAdvanced: false,
            fieldLabel: 'warehouse',
            backendParam: 'warehouseIds',
            idField: 'warehouseId',
          },
          {
            field: 'date',
            type: 'date',
            mandatory: false,
            placeholder: ' ',
            position: 6,
            isAdvanced: true,
            fieldLabel: 'Date',
            backendParam: 'date',
            idField: 'id',
          },
        ],
        endPoint: '/pages/ageing',
        jasperEndPoint: null,
      },
      {
        id: 13,
        type: 1,
        name: 'Inventory Snapshot',
        nameArabic: null,
        authority: 'ReportsManagement.Inventory.StockValueReports',
        languageCode: 'AR',
        searchConfigs: [
          {
            field: 'branch',
            type: 'dropdown',
            mandatory: true,
            placeholder: ' ',
            position: 1,
            isAdvanced: false,
            fieldLabel: 'branch',
            backendParam: 'branchId',
            idField: 'branchId',
          },
          {
            field: 'year',
            type: 'dropdown',
            mandatory: true,
            placeholder: ' ',
            position: 2,
            isAdvanced: false,
            fieldLabel: 'year',
            backendParam: 'yearId',
            idField: 'id',
          },
          {
            field: 'itemCode',
            type: 'input',
            mandatory: false,
            placeholder: 'Search By ItemCode/Name',
            position: 4,
            isAdvanced: false,
            fieldLabel: 'item',
            backendParam: 'searchString',
            idField: 'itemId',
          },
          {
            field: 'warehouse',
            type: 'multiSelectDropdown',
            mandatory: false,
            placeholder: ' ',
            position: 5,
            isAdvanced: false,
            fieldLabel: 'warehouse',
            backendParam: 'warehouseIds',
            idField: 'warehouseId',
          },
          {
            field: 'date',
            type: 'dateRange',
            mandatory: false,
            placeholder: ' ',
            position: 4,
            isAdvanced: true,
            fieldLabel: 'dateRange',
            backendParam: 'date',
            idField: 'id',
          },
        ],
        endPoint: '/pages/transaction',
        jasperEndPoint: null,
      },
    ];
  }
}
