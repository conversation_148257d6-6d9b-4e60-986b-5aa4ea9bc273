import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { ZatcaRegistration } from '../zatca';
import { ICertificateListResponse } from '../tenant';

@Injectable({
  providedIn: 'root',
})
export class ZatcaRegistrationService {
  baseUrl = environment.apiUrl + 'zatca';

  constructor(private http: HttpClient) {}

  register(tenant: string, formData: ZatcaRegistration): Observable<any> {
    const params = new HttpParams().set('tenant', tenant);
    return this.http.post(this.baseUrl + `/onboard`, formData, {
      params,
      headers: {
        'x-zatcaenv': formData.environment,
      },
    });
  }

  getAllZatcaTenants() {
    return this.http.get<ICertificateListResponse[]>(this.baseUrl + `/certificates`);
  }
}
