<ng-container *ngIf="!loading">
  <mat-card appearance="outlined">
    <form [formGroup]="branchPartnerForm" [ngClass]="{ readOnly: IsViewMode }" autocomplete="off">
      <!-- <mat-card-title>Branch Partner</mat-card-title> -->
      <div class="row no-gutters m-t-10">
        <!-- nameArabic  -->
        <div class="p-2 col-md-6 col-sm-6">
          <mat-label>{{ 'customer.parentId' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <mat-select [mode]="mode" appPreselectOption formControlName="parentAccountId">
              <mat-option
                *ngFor="let parentAccount of parentAccounts"
                [value]="parentAccount.accountId">
                {{ parentAccount | localized }}
              </mat-option>
            </mat-select>
            <mat-error
              *ngIf="
                branchPartnerForm?.controls['parentAccountId'].hasError('required') &&
                branchPartnerForm?.controls['parentAccountId'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
        <div class="p-2 col-md-6 col-sm-6">
          <mat-label>{{ 'stockTransfer.partnerBranch' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <mat-select [mode]="mode" appPreselectOption formControlName="partnerBranchId">
              <mat-option *ngFor="let branch of branches" [value]="branch.branchId">
                {{ (branch | localized) + ' - BranchId(' + branch.branchId + ')' }}
              </mat-option>
            </mat-select>
            <mat-error
              *ngIf="
                branchPartnerForm?.controls['partnerBranchId'].hasError('required') &&
                branchPartnerForm?.controls['partnerBranchId'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
        <div class="p-2 col-md-6 col-sm-6">
          <mat-label>{{ 'stockTransfer.accountArabic' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input type="text" matInput formControlName="nameArabic" />
            <mat-error
              *ngIf="
                branchPartnerForm?.controls['nameArabic'].hasError('required') &&
                branchPartnerForm?.controls['nameArabic'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
        <div class="p-2 col-md-6 col-sm-6">
          <mat-label>{{ 'stockTransfer.accountEnglish' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input type="text" matInput formControlName="nameEnglish" />
            <mat-error
              *ngIf="
                branchPartnerForm?.controls['nameEnglish'].hasError('required') &&
                branchPartnerForm?.controls['nameEnglish'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>

        <ng-container *ngIf="IsViewMode">
          <!-- <div class="p-2 col-md-4 col-sm-6">
            <mat-label>ID</mat-label>
<mat-form-field class="w-100">
  <input type="text" matInput formControlName="id" />
              <mat-error
                *ngIf="
                  branchPartnerForm?.controls['id'].hasError('required') &&
                  branchPartnerForm?.controls['id'].touched
                ">
                {{ 'common.required' | translate }}
              </mat-error>
            
</mat-form-field>
          </div> -->
          <div class="p-2 col-md-6 col-sm-6">
            <mat-label>{{ 'stockTransfer.accno' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <input type="text" matInput formControlName="accountNumber" />
              <mat-error
                *ngIf="
                  branchPartnerForm?.controls['accountNumber'].hasError('required') &&
                  branchPartnerForm?.controls['accountNumber'].touched
                ">
                {{ 'common.required' | translate }}
              </mat-error>
            </mat-form-field>
          </div>
          <!-- <div class="p-2 col-md-6 col-sm-6">
            <mat-label>accountId</mat-label>
<mat-form-field class="w-100">
  <input type="text" matInput formControlName="accountId" />
              <mat-error
                *ngIf="
                  branchPartnerForm?.controls['accountId'].hasError('required') &&
                  branchPartnerForm?.controls['accountId'].touched
                ">
                {{ 'common.required' | translate }}
              </mat-error>
            
</mat-form-field>
          </div> -->
          <div class="p-2 col-md-6 col-sm-6">
            <mat-label>{{ 'stockTransfer.branchArabic' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <input type="text" matInput formControlName="partnerBranchNameArabic" />
              <mat-error
                *ngIf="
                  branchPartnerForm?.controls['partnerBranchNameArabic'].hasError('required') &&
                  branchPartnerForm?.controls['partnerBranchNameArabic'].touched
                ">
                {{ 'common.required' | translate }}
              </mat-error>
            </mat-form-field>
          </div>
          <div class="p-2 col-md-6 col-sm-6">
            <mat-label>{{ 'stockTransfer.branchEnglish' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <input type="text" matInput formControlName="partnerBranchNameEnglish" />
              <mat-error
                *ngIf="
                  branchPartnerForm?.controls['partnerBranchNameEnglish'].hasError('required') &&
                  branchPartnerForm?.controls['partnerBranchNameEnglish'].touched
                ">
                {{ 'common.required' | translate }}
              </mat-error>
            </mat-form-field>
          </div>
        </ng-container>
      </div>
    </form>
  </mat-card>

  <app-action-buttons [mode]="mode" (submitAction)="onSubmit($event)"></app-action-buttons>
</ng-container>
