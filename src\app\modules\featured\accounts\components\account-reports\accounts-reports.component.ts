import { Component, OnInit } from '@angular/core';
import { DashboardModulesHolder } from 'src/app/modules/shared/components/dashboard-modules-holder/modulesHolder';

@Component({
  selector: 'app-accounts-reports',
  templateUrl: './accounts-reports.component.html',
  styleUrls: ['./accounts-reports.component.scss'],
})
export class AccountsReportsComponent {
  productModulesList: DashboardModulesHolder[] = [
    {
      moduleName: 'navigationMenus.listOfAccount',
      moduleDescription: 'Get report about list of accounts.',
      modulePermission: ['Accounting.Accounts', 'AllPermissions'],
      moduleImage: 'inventory',
      moduleRouterLink: '../accountsList',
      moduleButtonAction: 'BackOffice',
      moduleType: 'subModule',
    },
    {
      moduleName: 'navigationMenus.statementOfAccount',
      moduleDescription: 'Find statements for accounts.',
      modulePermission: ['Accounting.AccountStatement', 'AllPermissions'],
      moduleImage: 'inventory',
      moduleRouterLink: '../accountsStatement',
      moduleButtonAction: 'BackOffice',
      moduleType: 'subModule',
    },
  ];
}
