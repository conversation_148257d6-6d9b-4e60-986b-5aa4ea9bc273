import { SelectionModel } from '@angular/cdk/collections';
import { AfterViewInit, Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { fromEvent } from 'rxjs';
import { debounceTime, switchMap } from 'rxjs/operators';
import { ProductService } from 'src/app/core/api/product.service';
import { IInventory, IProductSearch } from 'src/app/modules/featured/catalog/models/product';
import { ProductParams } from 'src/app/modules/featured/catalog/models/productParams';
import { MatTableKeyboardNavigationDirective } from '../../../directives/tablekeynavigator.directive';

@Component({
  selector: 'app-product-get-all',
  templateUrl: './product-get-all.component.html',
  styleUrls: ['./product-get-all.component.scss'],
})
export class ProductGetAllComponent implements OnInit, AfterViewInit {
  @ViewChild(MatSort) sort: MatSort;
  @ViewChild('filter') filter: ElementRef;
  @ViewChild('searchInput') searchInput: ElementRef;
  @ViewChild(MatTableKeyboardNavigationDirective)
  keyboardNavDirective: MatTableKeyboardNavigationDirective;
  constructor(
    private productService: ProductService,
    public dialogRef: MatDialogRef<ProductGetAllComponent>
  ) {}
  displayedSearchColumns: string[] = [
    'itemCode',
    'itemName',
    'unitName',
    'warehouseName',
    'currentQty',
    'retailPrice',
    'wholesalePrice',
    'distributorPrice',
    'purchasePrice',
    'discount',
    'category',
  ];
  public isLoading = false;
  public products: IInventory[] = [];
  public resultNotFound = false;
  dataSource: MatTableDataSource<IInventory>;
  selection = new SelectionModel<IInventory>();

  ngOnInit(): void {
    this.getAllProducts();
  }

  ngAfterViewInit(): void {
    this.setupSearchInput(); // Set up the search input observable here
  }

  setupSearchInput() {
    fromEvent(this.searchInput.nativeElement, 'input')
      .pipe(
        debounceTime(500), // Wait for 300ms pause in events
        switchMap((event: any) => {
          const filterValue = event.target.value.trim().toLowerCase();
          this.applyFilter(filterValue); // Call applyFilter with the trimmed value
          return [];
        })
      )
      .subscribe();
  }

  getAllProducts() {
    this.isLoading = true;
    const params = new ProductParams();
    params.pageSize = 99999;
    this.productService
      .getProductsByFilter(params)
      .subscribe(
        (searchResult: IProductSearch) => (
          (this.isLoading = false),
          (this.products = searchResult.inventories),
          (this.resultNotFound = this.products?.length > 0 ? false : true),
          (this.dataSource = new MatTableDataSource<IInventory>(this.products)),
          (this.selection = new SelectionModel<IInventory>(true)),
          (this.dataSource.sort = this.sort)
        )
      );
  }

  applyFilter(filterValue: string): void {
    filterValue = filterValue.trim();
    filterValue = filterValue.toLowerCase();
    this.dataSource.filter = filterValue;
    if (this.dataSource.filteredData.length > 0) {
      setTimeout(() => {
        this.selection.clear(); // Clear previous selection
        this.keyboardNavDirective.onAutocompleteOpened();
        this.selection.select(this.dataSource.filteredData[0]); // Select the first record
      });
    }
  }

  clearSearchInput() {
    this.searchInput.nativeElement.value = '';
    this.applyFilter('');
  }

  selectRow(product: IInventory) {
    console.log('selected row', product);
    this.dialogRef.close(product);
  }

  onNoClick(event: Event): void {
    event.preventDefault();
    this.dialogRef.close(false);
  }
}
