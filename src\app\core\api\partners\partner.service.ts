import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  BranchPartnerResponse,
  Partner,
  PartnerList,
} from 'src/app/modules/featured/stock/models/partner';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class PartnerService {
  baseUrl = environment.apiUrl + 'partner/branches';

  constructor(private http: HttpClient) {}

  getAll() {
    return this.http.get<BranchPartnerResponse[]>(this.baseUrl);
  }

  getById(partnerID: string) {
    return this.http.get<BranchPartnerResponse>(this.baseUrl + '/' + partnerID);
  }

  create(partnerBranch: Partner) {
    return this.http.post(this.baseUrl, partnerBranch);
  }

  edit(partnerBranch: Partner, partnerID: string) {
    return this.http.post(this.baseUrl + '/' + partnerID, partnerBranch);
  }

  delete(partnerID: string) {
    return this.http.delete(this.baseUrl + '/' + partnerID);
  }
}
