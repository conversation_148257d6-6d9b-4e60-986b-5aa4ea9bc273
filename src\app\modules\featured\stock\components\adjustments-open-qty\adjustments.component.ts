import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  OnInit,
  ViewChild,
} from '@angular/core';
import { UntypedFormArray, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ToastrService } from 'ngx-toastr';
import { forkJoin } from 'rxjs';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { SearchboxComponent } from 'src/app/modules/shared/components/searchbox/searchbox.component';
import { CategoryParams } from '../../../catalog/models/categoryParams';
import { ProductAdjustmentsParams } from '../../../catalog/models/productAdjustmentParams';
import { CategoryService } from '../../../../../core/api/category.service';
import { ProductService } from '../../../../../core/api/product.service';
import { StoreParams } from '../../../settings/models/storeParams';
import { Inventory } from '../../models/unitPrice';
import { StoreService } from 'src/app/core/api/store.service';
import { CommonService } from 'src/app/core/api/common.service';

@Component({
  selector: 'app-adjustments',
  templateUrl: './adjustments.component.html',
  styleUrls: ['./adjustments.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AdjustmentsComponent implements OnInit {
  @ViewChild('searchBoxForm') searchBoxForm: SearchboxComponent;
  @ViewChild(MatPaginator) set matPaginator(paginator: MatPaginator) {
    if (this.tableData) {
      this.tableData.paginator = paginator;
    }
  }
  @ViewChild(MatSort) set matSort(sort: MatSort) {
    if (this.tableData) {
      this.tableData.sort = sort;
    }
  }
  title: string;
  companyId: string;
  tableData = new MatTableDataSource<Inventory>();
  adjustmentRows: UntypedFormArray = this.formBuilder.array([]);
  adjustmentForm: UntypedFormGroup = this.formBuilder.group({
    InventoryOpeningRequest: this.adjustmentRows,
  });
  warehouseList: any;
  categoryList: any;
  products: any;
  displayedColumns: string[] = [
    'action',
    'warehouseName',
    'itemCode',
    'nameArabic',
    'nameEnglish',
    'unitName',
    'openQty',
  ];
  editedUnitId = '';
  formTitle: string;
  isEditMode = false;
  parentCategories: any;
  subCategories: any;
  filterForm: UntypedFormGroup;
  constructor(
    private storeService: StoreService,
    private categoryService: CategoryService,
    private formBuilder: UntypedFormBuilder,
    private toastr: ToastrService,
    private authService: AuthService,
    private fb: UntypedFormBuilder,
    private productService: ProductService,
    private commonService: CommonService
  ) {}

  ngOnInit(): void {
    this.getAllDropDownData();
    this.initializeFilterForm();
  }

  initializeFilterForm() {
    this.filterForm = this.fb.group({
      categoryId: [[]],
      warehouseIds: [[], Validators.required],
      searchBoxForm: [],
    });
  }

  getParentName(parentId: number) {
    return this.parentCategories.filter(data => data.categoryId === parentId)[0]?.nameArabic;
  }

  getAllDropDownData() {
    const warehouses = this.storeService.getStores(
      new StoreParams(),
      this.authService.getCompanyID
    );
    const category = this.categoryService.getAllCategories(new CategoryParams());
    forkJoin([warehouses, category]).subscribe(results => {
      this.warehouseList = results[0];
      this.categoryList = results[1];
      this.splitCategoryandParentCategory();
      if (this.warehouseList.length > 0) {
        this.filterForm.controls['warehouseIds'].setValue(
          this.warehouseList.map(data => data.warehouseId)
        );
      }
      if (this.categoryList.length > 0) {
        this.filterForm.controls['categoryId'].setValue(
          this.categoryList.map(data => data.categoryId)
        );
      }
      this.getFilterData();
    });
  }

  splitCategoryandParentCategory() {
    this.parentCategories = this.categoryList.filter(data => data.parentCategoryId === null);
    this.subCategories = this.categoryList.filter(data => data.parentCategoryId !== null);
  }

  deleteEntry(id: any) {
    this.adjustmentRows.removeAt(id);
    this.tableData.data.splice(id, 1);
    this.tableData.data = [...this.tableData.data];
    console.log(this.tableData);
  }

  addUnitsPriceRow(data) {
    const row = this.formBuilder.group({
      warehouseName: [data && data?.warehouseName],
      warehouseId: [data && data?.warehouseId],
      itemUnitId: [data && data?.itemUnitId],
      currentQty: [data && data?.currentQty],
      itemCode: [data && data?.itemCode],
      itemName: [data && data?.itemName],
      itemId: [data && data?.itemId],
      openQty: [data && data?.openQty, Validators.required],
      unitName: [data && data?.unitName],
      yearId: [data && data?.yearId],
    });
    this.adjustmentRows.push(row);
  }

  markFormArrayTouched() {
    const formData = (this.adjustmentForm.get('InventoryOpeningRequest') as UntypedFormArray)
      .controls;
    formData.forEach(formArrayData => {
      Object.keys(formArrayData['controls']).forEach(key => {
        formArrayData.get(key).markAsTouched();
      });
    });
  }

  getFilterData(event?: Event) {
    event?.preventDefault();
    this.filterForm.markAllAsTouched();
    if (this.filterForm.valid) {
      const params: ProductAdjustmentsParams = <ProductAdjustmentsParams>{};
      console.log(this.filterForm);
      params.categoryIds = this.filterForm.controls['categoryId'].value;
      params.warehouseIds = this.filterForm.controls['warehouseIds'].value;
      params.searchString = this.searchBoxForm.searchBoxForm.controls['searchString'].value;
      params.searchType = this.searchBoxForm.searchBoxForm.controls['searchType'].value;

      this.productService.getProductsByFilter(params).subscribe(result => {
        console.log(result);
        this.products = null;
        this.products = result.inventories;
        setTimeout(() => {
          this.tableData = new MatTableDataSource(result.inventories);
        });
        this.adjustmentRows.clear();
        this.products.forEach(data => this.addUnitsPriceRow(data));
      });
    } else {
      this.commonService.scrollToError();
    }
  }

  clearFilters(event: Event) {
    event.preventDefault();
    this.filterForm.markAsUntouched();
    this.filterForm.markAsPristine();
    this.filterForm.reset();
    this.searchBoxForm.resetSearchBox();
  }

  onSubmit(event: Event) {
    console.log('adjustmentForm', this.adjustmentForm.value['InventoryOpeningRequest']);
    event.preventDefault();
    this.adjustmentForm.markAllAsTouched();
    this.markFormArrayTouched();
    if (this.adjustmentForm.valid) {
      // add logic to update
      this.productService
        .updateOpenQuantity(this.adjustmentForm.value['InventoryOpeningRequest'])
        .subscribe(() => {
          //this.toastr.success('Open Quantity updated Successfully');
          this.commonService.playSuccessSound();
        });
    } else {
      this.commonService.scrollToError();
    }
  }

  getActualIndex(index: number) {
    if (this.matPaginator) {
      return index + this.matPaginator.pageSize * this.matPaginator.pageIndex;
    }
    return index;
  }
}
