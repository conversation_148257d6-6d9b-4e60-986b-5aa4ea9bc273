import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { fromEvent, Observable, Subscription } from 'rxjs';
import { Events } from './modules/core/core/enums/events';
import { EmitEvent } from './modules/core/core/models/emit-event';
import { EventBusService } from './modules/core/core/services/event-bus.service';
import { MultilingualService } from './modules/core/core/services/multilingual.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent implements OnInit, OnDestroy {
  themeVariant = '';
  darkModeIcon = '';
  onlineEvent: Observable<Event>;
  offlineEvent: Observable<Event>;
  subscriptions: Subscription[] = [];
  connectionStatusMessage: string;
  connectionStatus: string;
  previousUrl: string = null;
  currentUrl: string = null;

  constructor(
    private multilingualService: MultilingualService,
    private toastrService: ToastrService,
    private eventbus: EventBusService
  ) {}

  ngOnInit(): void {
    this.onlineOfflineTriggers();
    this.multilingualService.loadDefaultLanguage();
  }

  onlineOfflineTriggers() {
    this.onlineEvent = fromEvent(window, 'online');
    this.offlineEvent = fromEvent(window, 'offline');

    this.subscriptions.push(
      this.onlineEvent.subscribe(() => {
        this.connectionStatusMessage = 'Back to online';
        this.toastrService.clear();
        this.toastrService.info(this.connectionStatusMessage);
        this.eventbus.emit(new EmitEvent(Events.OnlineOffline, true));
      })
    );

    this.subscriptions.push(
      this.offlineEvent.subscribe(() => {
        this.connectionStatusMessage = 'Connection lost! You are not connected to internet';
        this.toastrService.clear();
        this.toastrService.warning(this.connectionStatusMessage);
        this.eventbus.emit(new EmitEvent(Events.OnlineOffline, false));
      })
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(subscription => subscription.unsubscribe());
  }
}
