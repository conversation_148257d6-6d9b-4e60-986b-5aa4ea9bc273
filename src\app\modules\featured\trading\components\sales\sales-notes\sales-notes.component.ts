import { Directionality } from '@angular/cdk/bidi';
import {
  AfterViewInit,
  Component,
  ElementRef,
  Inject,
  OnInit,
  Optional,
  SkipSelf,
  ViewChild,
} from '@angular/core';
import { FormBuilder, UntypedFormGroup } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { CommonService } from 'src/app/core/api/common.service';
import { PurchaseService } from 'src/app/core/api/trading/purchase.service';
import { SalesService } from 'src/app/core/api/trading/sales.service';
import { ZatcaService } from 'src/app/core/api/trading/zatca.service';
import { IPurchaseInvoice, IPurchaseResponse } from 'src/app/core/interfaces/purchase';
import { IInvoice, ISalesResponse, SaletransactionTypes } from 'src/app/core/interfaces/sales';
import { NotesDetails } from 'src/app/core/models/params/salesNotesParams';
import { SalesParams } from 'src/app/core/models/params/salesParams';
import { ReportService } from 'src/app/modules/featured/settings/services/report.service';
import { PrintDialogComponent } from 'src/app/modules/shared/components/print-dialog/print-dialog.component';

@Component({
  selector: 'app-sales-notes',
  templateUrl: './sales-notes.component.html',
  styleUrls: ['./sales-notes.component.scss'],
})
export class SalesNotesComponent implements OnInit, AfterViewInit {
  @ViewChild(MatSort) sort: MatSort;
  @ViewChild('filter') filter: ElementRef;
  @ViewChild('searchInput') searchInput: ElementRef;
  displayedColumns: string[];
  dataSource: MatTableDataSource<IInvoice | IPurchaseInvoice>;
  invoices: IInvoice[] | IPurchaseInvoice[];
  isReportPulling = false;
  filterValues = {
    transactionType: '',
  };
  filterSelection: UntypedFormGroup;
  constructor(
    @Optional() @SkipSelf() @Inject(MAT_DIALOG_DATA) public modalData: NotesDetails,
    public salesService: SalesService,
    public reportService: ReportService,
    private purchaseService: PurchaseService,
    private formBuilder: FormBuilder,
    private zatcaService: ZatcaService,
    private direction: Directionality,
    private commonService: CommonService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.filterSelection = this.formBuilder.group({
      filterTYpe: ['ALL'],
    });
    this.getAllNotes();
    this.initColumns();
    this.filterSelection?.controls['filterTYpe'].valueChanges.subscribe(value => {
      this.filterValues.transactionType = value.trim().toLowerCase();
      this.applyFilters();
    });
  }

  ngAfterViewInit(): void {
    if (this.dataSource) {
      this.dataSource.sort = this.sort;
    }
  }

  get isSalesProcessing() {
    return this.modalData && this.modalData?.salesProcessing;
  }

  initColumns(): void {
    this.displayedColumns = [
      'action',
      'documentNumber',
      'issueDate',
      'invoiceTotal',
      'referenceDocumentNumber',
      'referenceDocumentDate',
      'nameArabic',
      'vatNumber',
      'transactionType',
      'stage',
    ];
    if (this.modalData?.purchaseNotes) {
      const index = this.displayedColumns.indexOf('stage');
      if (index > -1) {
        this.displayedColumns.splice(index, 1);
      }
    }
  }

  applyFilters() {
    console.log(this.filterValues);
    this.dataSource.filter =
      this.filterValues.transactionType === 'all' ? null : this.filterValues.transactionType;
    this.dataSource.data = [...this.dataSource.data];
  }

  getAllNotes(): void {
    const salesParams = new SalesParams();
    salesParams.invoiceId = this.modalData.invoiceId;
    salesParams.transactionType = SaletransactionTypes.allNotes;
    if (this.modalData.purchaseNotes) {
      this.purchaseService
        .getPurchaseNotesById(salesParams)
        .subscribe((result: IPurchaseResponse) => {
          console.log(result, result.transactions);
          this.invoices = result.transactions;
          this.dataSource = new MatTableDataSource<IPurchaseInvoice>(this.invoices);
          this.dataSource.sort = this.sort;
        });
    } else {
      this.salesService.getSalesNotesById(salesParams).subscribe((result: ISalesResponse) => {
        console.log(result, result.transactions);
        this.invoices = result.transactions;
        this.dataSource = new MatTableDataSource<IInvoice>(this.invoices);
        this.dataSource.sort = this.sort;
      });
    }
  }

  applyFilter(filterValue: string): void {
    filterValue = filterValue.trim();
    filterValue = filterValue.toLowerCase();
    this.dataSource.filter = filterValue;
  }

  clearSearchInput() {
    this.searchInput.nativeElement.value = '';
    // this.getAllNotes();
  }

  getZatcaErrorForInvoice(documentUUID: string): void {
    this.zatcaService.getZatcaErrorForInvoice(documentUUID, this.direction.value);
  }

  getXmlForInvoice(documentUUID: string): void {
    this.zatcaService.getXmlForInvoice(documentUUID, this.direction.value);
  }

  printNotes(transactionId: number, transactionType: string): void {
    this.showPrintDialog(transactionId, transactionType);
  }

  /**
   * Shows the print dialog for the selected invoice
   */
  private showPrintDialog(transactionId: number, transactionType: string): void {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.position = { top: '10vh' };
    dialogConfig.minWidth = '400px';
    dialogConfig.maxWidth = '500px';
    dialogConfig.panelClass = 'green_theme';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = true;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(PrintDialogComponent, dialogConfig);
    dialogRef.componentInstance.printRequested.subscribe((shouldPrint: boolean) => {
      if (shouldPrint) {
        if (transactionType == 'fullCreditNote' || transactionType == 'partialCreditNote') {
          transactionType = 'CREDIT_NOTE';
        }
        this.commonService.openInvoiceInNewWindow(transactionId, transactionType);
      }
    });
  }
}
