import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { OwnerdashboardComponent } from './components/ownerdashboard/ownerdashboard.component';
import { PermissionGuard } from '../../core/core/guards/permission.guard';
import { TenantListingComponent } from './components/tenant-listing/tenant-listing.component';
import { TenantConfigurationComponent } from './components/tenant-configuration/tenant-configuration.component';
import { ZatcaRegistrationComponent } from './components/zatca-registration/zatca-registration.component';
import { ZatcaListingsComponent } from './components/zatca-listings/zatca-listings.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'dashboard',
    pathMatch: 'full',
  },
  {
    path: 'dashboard',
    component: OwnerdashboardComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Tenant Management',
      urls: [{ title: 'Main Dashboard', url: '/dashboard' }],
      allowedPermissions: ['Provider.TenantManagement', 'AllPermissions'],
    },
  },
  {
    path: 'tenants',
    component: TenantListingComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Tenant Listing',
      urls: [{ title: 'Tenant Dashboard', url: 'owner/dashboard' }],
      allowedPermissions: ['Provider.TenantManagement', 'AllPermissions'],
    },
  },
  {
    path: 'tenants/create',
    component: TenantConfigurationComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Create Tenant Configuration',
      urls: [{ title: 'Tenant Listings', url: '/owner/tenants' }],
      allowedPermissions: ['Provider.TenantManagement', 'AllPermissions'],
      mode: 'create',
    },
  },
  {
    path: 'tenants/edit/:id',
    component: TenantConfigurationComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Edit Tenant Configuration',
      urls: [{ title: 'Tenant Listings', url: '/owner/tenants' }],
      allowedPermissions: ['Provider.TenantManagement', 'AllPermissions'],
      mode: 'edit',
    },
  },
  {
    path: 'tenants/view/:id',
    component: TenantConfigurationComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Tenant Configuration',
      urls: [{ title: 'Tenant Listings', url: '/owner/tenants' }],
      allowedPermissions: ['Provider.TenantManagement', 'AllPermissions'],
      mode: 'view',
    },
  },
  {
    path: 'tenants/zatca/:id',
    component: ZatcaRegistrationComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Zatca Registration',
      urls: [{ title: 'Tenant Listings', url: 'owner/dashboard' }],
      allowedPermissions: ['Provider.TenantManagement', 'AllPermissions'],
    },
  },
  {
    path: 'tenants/zatcalistings',
    component: ZatcaListingsComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Zatca Listings',
      urls: [{ title: 'Tenant Dashboard', url: 'owner/dashboard' }],
      allowedPermissions: ['Provider.TenantManagement', 'AllPermissions'],
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class OwnerRoutingModule {}
