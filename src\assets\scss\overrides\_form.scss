// for radius

.mdc-text-field--outlined.mdc-text-field--disabled {
  .mdc-notched-outline__leading,
  .mdc-notched-outline__trailing,
  .mdc-notched-outline__notch {
    border-color: $borderColor;
  }
}

.hide-hint {
  .mat-mdc-form-field-subscript-wrapper {
    display: none;
  }
}

.auto-complete-search mat-option .mdc-list-item__primary-text {
  flex-grow: 1;
}

.auto-complete-search mat-option {
  padding: 0px;
}

// theme select
.theme-select {
  width: 125px;
  height: 36px;
  .mat-form-field-infix {
    padding: 6px 0 1px !important;
  }
  .mat-mdc-form-field-subscript-wrapper {
    display: none;
  }
  .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading {
    border-top-left-radius: $border-radius;
    border-bottom-left-radius: $border-radius;
    border-color: $borderColor;
  }
  .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing {
    border-top-right-radius: $border-radius;
    border-bottom-right-radius: $border-radius;
    border-color: $borderColor;
  }
  .mat-mdc-form-field-flex {
    height: 36px;
    align-items: center;
  }
}

.no-padding .mat-expansion-panel-body {
  padding: 0px;
}

.pe-none {
  pointer-events: none;
}

.toast-container .ngx-toastr {
  box-shadow: none !important;
}

// .mat-mdc-header-cell {
//   text-align: center !important;
// }

.table-edit-element-row td {
  border-bottom-width: 0;
}

tr.table-edit-detail-row {
  height: 0;
}

.expanded-detail {
  overflow: hidden;
}

.wrap {
  word-wrap: break-word !important;
  //white-space: pre-wrap !important;
}

.line-item-table-container {
  position: relative;
  max-height: 300px !important;
  overflow: auto;
}

.line-item-content-container {
  position: relative;
  min-height: 0;
}

.mat-expansion-panel-header-title,
.mat-expansion-panel-header-description {
  justify-content: center;
}

.line-item-table-container .mdc-data-table__cell .mdc-data-table__header-cell {
  border-bottom-width: 0px;
  border-bottom-style: none;
}

// when form field disabled the text color
.mdc-text-field--disabled .mdc-text-field__input {
  color: $borderColor;
}

// for form label color
.mdc-text-field:not(.mdc-text-field--disabled) .mdc-floating-label {
  color: $borderColor;
}

// when form field disabled the label text color
.mdc-text-field--disabled .mdc-floating-label {
  color: $borderColor;
}

.mat-mdc-slide-toggle .mdc-switch--disabled + label {
  color: $borderColor;
}

.mat-mdc-radio-button.mat-primary .mdc-radio--disabled + label {
  color: $borderColor;
}

.mat-mdc-radio-button.mat-accent .mdc-radio--disabled + label {
  color: $borderColor;
}

.mat-mdc-select-disabled .mat-mdc-select-value {
  color: $borderColor;
}

.mat-mdc-checkbox.mat-mdc-checkbox-disabled label {
  color: $borderColor;
}

.mat-tree {
  background: none;
}

// .mdc-list-item.mdc-list-item--disabled,
// .mdc-list-item.mdc-list-item--non-interactive {
//   cursor: none;
// }

.mat-mdc-form-field
  .mdc-text-field--outlined
  .mdc-notched-outline--upgraded
  .mdc-floating-label--float-above {
  font-size: 16px;
}

.expanded-detail {
  background: $background;
}

.mdc-tab__text-label {
  font-size: 16px;
}

input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.text-error-wrap {
  text-wrap: wrap;
}

.mat-mdc-form-field-hint-wrapper,
.mat-mdc-form-field-error-wrapper {
  padding: 0px;
}

.no-wrap {
  white-space: normal;
}

.mat-mdc-menu-trigger {
  margin: 0px;
}

.mdc-list-item__primary-text tr {
  height: auto;
}

.control-border {
  border: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 5px;
}

.form-group {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  width: 100%;
}
