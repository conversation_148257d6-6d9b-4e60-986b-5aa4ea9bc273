import { Directionality } from '@angular/cdk/bidi';
import { Component, OnInit, ViewChild } from '@angular/core';
import {
  FormArray,
  UntypedFormArray,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatAccordion } from '@angular/material/expansion';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { ImageCroppedEvent } from 'ngx-image-cropper';
import { ToastrService } from 'ngx-toastr';
import { forkJoin, of } from 'rxjs';
import { CommonService } from 'src/app/core/api/common.service';
import { fixedPercentageDropDown, itemTypes } from 'src/app/core/configs/dropDownConfig';
import { ActionType } from 'src/app/core/enums/actionType';
import { DeleteConfirmationComponent } from 'src/app/modules/shared/components/delete-confirmation/delete-confirmation.component';
import { BranchParams } from '../../../../settings/models/branchParams';
import { StoreParams } from '../../../../settings/models/storeParams';
import { BranchService } from '../../../../settings/services/branch.service';
import { Branch } from '../../../models/branch';
import { Category } from '../../../models/category';
import { CategoryParams } from '../../../models/categoryParams';
import { Product, ProductUnits } from '../../../models/product';
import { WareHouse } from '../../../models/store';
import { Units } from '../../../models/units';
import { CategoryService } from '../../../../../../core/api/category.service';
import { ProductService } from '../../../../../../core/api/product.service';
import { UnitService } from 'src/app/core/api/unit.service';
import { StoreService } from 'src/app/core/api/store.service';

@Component({
  selector: 'app-product-form',
  templateUrl: './product-form.component.html',
  styleUrls: ['./product-form.component.scss'],
})
export class ProductFormComponent implements OnInit {
  // Returns true if the element's content is overflowing its visible area
  isOverflowing(element: HTMLElement): boolean {
    return element && element.scrollWidth > element.clientWidth;
  }

  @ViewChild(MatAccordion) accordion: MatAccordion;
  @ViewChild(MatSort) sort: MatSort = Object.create(null);
  @ViewChild(MatPaginator) paginator: MatPaginator = Object.create(null);

  // Form and data variables
  mode: ActionType;
  productForm: UntypedFormGroup;
  imageChangedEvent: any = '';
  croppedImage: any = '';
  showCropper = false;
  defaultImage = 'assets/images/company-placeholder-image.png';
  loading = true;
  title: string;
  companyId: string;
  companyError = false;
  displayRemoveIcon = false;
  loaded = false;
  dataSource: MatTableDataSource<unknown>;
  uomrows: UntypedFormArray;
  uomform: UntypedFormGroup;
  disabledUnitIds: number[] = [];

  branchesList: Branch[];
  categoryList: Category[];
  wareHouseList: WareHouse[];
  units: Units[];
  products: Product;
  submitted = false;
  // drop downs
  fixedPercentageDropDown = fixedPercentageDropDown;
  itemTypes = itemTypes;
  displayedColumns: string[] = [
    'action',
    'unitBarcode',
    'unitofmeasure',
    // 'costPrice',
    'purchasePrice',
    'openPurchasePrice',
    'wholesalePrice',
    'distributorPrice',
    'retailPrice',
    // 'transportCost',
    'isGeneralDscntMethod',
    'discount',
    // 'isGeneralProfitMethod',
    // 'profit',
  ];

  editedUnitId: string | null = null;
  formTitle: string;
  parentCategories: Category[];
  subCategories: Category[];

  constructor(
    private sanitizer: DomSanitizer,
    private route: ActivatedRoute,
    private branchService: BranchService,
    private categoryService: CategoryService,
    private storeService: StoreService,
    private formBuilder: UntypedFormBuilder,
    private toastr: ToastrService,
    private productService: ProductService,
    private router: Router,
    public dialog: MatDialog,
    private direction: Directionality,
    private commonService: CommonService,
    private unitService: UnitService
  ) {
    // Initialize the form array
    this.uomrows = this.formBuilder.array([]);
    this.uomform = this.formBuilder.group({
      itemUnitRequests: this.uomrows,
    });
  }

  ngOnInit(): void {
    this.mode = this.route.snapshot.data['mode'];
    this.formTitle = this.route.snapshot.data['title'];
    this.initializeForm();
    this.route.params.subscribe(params => {
      const id = params['id'];
      if (id) {
        this.editedUnitId = id;
      }
      this.loadDropDownData();
    });
    this.formValueChanges();
  }

  formValueChanges(): void {
    let lastAutoSetBarcode: string | null = null;
    this.productForm?.controls['itemCode'].valueChanges.subscribe(itemCodeValue => {
      if (this.isCreateMode) {
        const itemUnitRequests = this.uomform.get('itemUnitRequests') as FormArray;
        if (itemUnitRequests && itemUnitRequests.length > 0) {
          const firstRow = itemUnitRequests.at(0);
          const barcodeCtrl = firstRow.get('unitBarcode');
          // Only auto-set if empty or matches last auto-set value
          if (!barcodeCtrl.value || barcodeCtrl.value === lastAutoSetBarcode) {
            barcodeCtrl.setValue(itemCodeValue);
            lastAutoSetBarcode = itemCodeValue;
          }
        }
      }
    });

    this.productForm?.controls['isItemFree'].valueChanges.subscribe(value => {
      if (value) {
        this.productForm.get('freeStartDate').enable();
        this.productForm.get('freeEndDate').enable();
        this.productForm.get('freeStartDate').addValidators(Validators.required);
        this.productForm.get('freeEndDate').addValidators(Validators.required);
      } else {
        this.productForm.get('freeStartDate').disable();
        this.productForm.get('freeEndDate').disable();
        this.productForm.get('freeStartDate').setValue(null);
        this.productForm.get('freeEndDate').setValue(null);
        this.productForm.get('freeStartDate').clearValidators();
        this.productForm.get('freeEndDate').clearValidators();
      }
      this.productForm.get('freeStartDate').updateValueAndValidity();
      this.productForm.get('freeEndDate').updateValueAndValidity();
    });

    this.productForm?.controls['isPrintFrozen'].valueChanges.subscribe(value => {
      if (value) {
        this.productForm.get('reasonToFreezePrint').enable();
        this.productForm.get('reasonToFreezePrint').addValidators(Validators.required);
      } else {
        this.productForm.get('reasonToFreezePrint').setValue(null);
        this.productForm.get('reasonToFreezePrint').clearValidators();
        this.productForm.get('reasonToFreezePrint').disable();
      }
      this.productForm.get('reasonToFreezePrint').updateValueAndValidity();
    });

    this.productForm?.controls['isSaleFrozen'].valueChanges.subscribe(value => {
      if (value) {
        this.productForm.get('reasonToFreezeSale').enable();
        this.productForm.get('reasonToFreezeSale').addValidators(Validators.required);
      } else {
        this.productForm.get('reasonToFreezeSale').setValue(null);
        this.productForm.get('reasonToFreezeSale').disable();
        this.productForm.get('reasonToFreezeSale').clearValidators();
      }
      this.productForm.get('reasonToFreezeSale').updateValueAndValidity();
    });

    this.productForm?.controls['isSaleFrozen'].valueChanges.subscribe(value => {
      if (value) {
        this.productForm.get('reasonToFreezeSale').addValidators(Validators.required);
      } else {
        this.productForm.get('reasonToFreezeSale').clearValidators();
      }
      this.productForm.get('reasonToFreezeSale').updateValueAndValidity();
    });

    (this.uomform.get('itemUnitRequests') as FormArray).valueChanges.subscribe(() => {
      this.updateDisabledUnitIds();
    });
  }

  // Load data
  loadDropDownData(): void {
    const categories$ = this.categoryService.getAllCategories(new CategoryParams());
    const branches$ = this.branchService.getAllBranches(new BranchParams());
    const units$ = this.unitService.getAllUnits();
    const products$ = this.editedUnitId
      ? this.productService.getProductById(this.editedUnitId)
      : of(null);
    const warehouses$ = this.storeService.getBrancheStores(new StoreParams());
    forkJoin([categories$, branches$, units$, products$, warehouses$]).subscribe(results => {
      const [categories, branches, units, products, warehouses] = results;
      this.categoryList = categories;
      this.splitCategories();
      this.branchesList = branches;
      this.units = units;
      this.wareHouseList = warehouses;
      if (products) {
        this.products = products;
        this.products?.itemUnitRequests.forEach(data => this.addUnitPriceRow(data));
        if (this.products?.base64EncodedImageString) {
          this.croppedImage = this.sanitizer.bypassSecurityTrustResourceUrl(
            'data:image/jpg;base64,' + this.products.base64EncodedImageString
          );
        }
        this.patchProductData(this.products);
      } else {
        this.addUnitsPriceRow(null);
        if (this.wareHouseList.length) {
          this.productForm.controls['warehouseIds'].setValue(
            this.wareHouseList.map(w => w.warehouseId)
          );
        }
      }
      this.updateActionButtonStatus();
      this.loading = false;
    });
  }

  // Split categories into parent and sub-categories
  splitCategories(): void {
    this.parentCategories = this.categoryList.filter(cat => !cat.parentCategoryId);
    this.subCategories = this.categoryList.filter(cat => cat.parentCategoryId);
  }

  // Add a unit price row to the form array
  addUnitPriceRow(data?: ProductUnits): void {
    const row = this.formBuilder.group({
      unitBarcode: [data?.unitBarcode, Validators.required],
      unitId: [data?.unitId, Validators.required],
      costPrice: [data?.costPrice || 0, Validators.required],
      openPurchasePrice: [data?.openPurchasePrice || 0, Validators.required],
      purchasePrice: [data?.purchasePrice || 0, Validators.required],
      transportCost: [data?.transportCost || 0, Validators.required],
      retailPrice: [data?.retailPrice || 0, Validators.required],
      wholesalePrice: [data?.wholesalePrice || 0, Validators.required],
      distributorPrice: [data?.distributorPrice || 0, Validators.required],
      isGeneralDscntMethod: [data?.isGeneralDscntMethod || false],
      discount: [data?.discount || 0],
      isGeneralProfitMethod: [data?.isGeneralProfitMethod || false],
      profit: [data?.profit || 0],
      itemUnitId: [data?.itemUnitId || null],
    });
    this.uomrows.push(row);
    this.updateActionButtonStatus();
  }

  // Update the status of the remove icon
  updateActionButtonStatus(): void {
    this.dataSource = new MatTableDataSource(this.uomrows.controls);
    this.displayRemoveIcon = this.uomrows.length > 1;
  }

  // Delete a unit of measure entry
  deleteUoMEntry(index: number): void {
    const itemUnitId = this.uomrows.at(index).get('itemUnitId').value;
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '600px';
    dialogConfig.panelClass = 'green_theme';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(DeleteConfirmationComponent, dialogConfig);
    dialogRef.componentInstance.accepted.subscribe(() => {
      if (this.isCreateMode) {
        this.uomrows.removeAt(index);
        this.updateActionButtonStatus();
      } else {
        this.deleteUoMEntryFromServer(itemUnitId, index);
      }
    });
  }

  // Delete unit of measure entry from the server
  deleteUoMEntryFromServer(itemUnitId: string, index: number): void {
    this.productService.deleteItemUnit(itemUnitId).subscribe(() => {
      //this.toastr.success('Successfully Deleted', 'Success');
      this.uomrows.removeAt(index);
      this.updateActionButtonStatus();
      this.commonService.playSuccessSound();
    });
  }

  // Handle form submission
  onSubmit(): void {
    this.submitted = true;
    this.productForm.markAllAsTouched();
    this.uomform.get('itemUnitRequests').markAllAsTouched();
    if (this.productForm.invalid || this.uomform.invalid) {
      this.commonService.playErrorSound();
      return;
    }
    if (this.isCreateMode) {
      this.createNewProduct();
    } else {
      this.updateProduct();
    }
  }

  createNewProduct(): void {
    const formData: FormData = new FormData();
    let data: Product = <Product>{};
    data = this.productForm.value;
    data.itemUnitRequests = this.uomform.get('itemUnitRequests').value;
    formData.append('createItemRequest', JSON.stringify(data));
    if (this.showCropper) {
      const fileToUpload: File = new File([this.dataURItoBlob(this.croppedImage)], 'profile.png');
      formData.append('file', fileToUpload);
    }
    this.productService.create(formData).subscribe(() => {
      //this.toastr.success('Product added successfully', 'Success');
      this.router.navigate(['../../'], { relativeTo: this.route });
    });
  }

  updateProduct(): void {
    const formData: FormData = new FormData();
    let data: Product = <Product>{};
    data = this.productForm.value;
    data.itemUnitRequests = this.uomform.get('itemUnitRequests').value;
    formData.append('updateItemRequest', JSON.stringify(data));
    if (this.showCropper) {
      const fileToUpload: File = new File([this.dataURItoBlob(this.croppedImage)], 'profile.png');
      formData.append('file', fileToUpload);
    }
    this.productService.updateProduct(formData, this.editedUnitId).subscribe(() => {
      //this.toastr.success('Product updated successfully', 'Success');
      this.router.navigate(['../../'], { relativeTo: this.route });
    });
  }

  dataURItoBlob(dataURI) {
    // Split the data URI into the MIME type and the base64 encoded string
    const [mimeString, byteString] = dataURI.split(',');
    const mimeType = mimeString.split(':')[1].split(';')[0];

    // Decode the base64 string into binary data
    const binaryString = atob(byteString);

    // Create a Uint8Array from the binary string
    const arrayBuffer = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      arrayBuffer[i] = binaryString.charCodeAt(i);
    }

    // Create and return a Blob from the Uint8Array and MIME type
    return new Blob([arrayBuffer], { type: mimeType });
  }

  // Image cropping logic
  imageCropped(event: ImageCroppedEvent): void {
    this.croppedImage = event.base64;
  }

  initializeForm(): void {
    // Initialize form with default values and validators
    this.productForm = this.formBuilder.group({
      itemCode: ['', [Validators.required, Validators.maxLength(40)]],
      categoryId: [null, [Validators.required]],
      itemType: [0],
      //alternateItems: [null, [Validators.maxLength(40)]],
      brand: ['', [Validators.maxLength(40)]],
      nameArabic: ['', [Validators.required, Validators.maxLength(40)]],
      nameEnglish: ['', [Validators.required, Validators.maxLength(40)]],
      partNumber: ['', [Validators.maxLength(40)]],
      color: [null],
      size: [null],
      maximumQuantity: [0, [Validators.required, Validators.maxLength(40)]],
      minimumQuantity: [0, [Validators.required, Validators.maxLength(40)]],
      notes: [''],
      vat: [0, [Validators.required, Validators.maxLength(40)]],
      isGeneralSalesPolicy: [true, [Validators.required]],
      isItemWeighed: [false, [Validators.required]],
      isActive: [true, [Validators.required]],
      warehouseIds: [null, [Validators.required]],
      isItemFree: [false],
      freeStartDate: [null],
      freeEndDate: [null],
      isPrintFrozen: [false],
      reasonToFreezePrint: [''],
      isSaleFrozen: [false],
      reasonToFreezeSale: [''],
      isTransferFrozen: [false],
      hasExpirationDate: [false],
      daysToAlert: [0, [Validators.required]],
    });
  }

  patchProductData(data: Product | null): void {
    if (data) {
      // Patch values from the provided data object
      this.productForm.patchValue({
        itemCode: data.itemCode ?? '',
        categoryId: data.categoryId ?? null,
        itemType: data.itemType ?? 0,
        //alternateItems: data.alternateItems ?? '',
        brand: data.brand ?? '',
        nameArabic: data.nameArabic ?? '',
        nameEnglish: data.nameEnglish ?? '',
        partNumber: data.partNumber ?? '',
        color: data.color ?? null,
        size: data.size ?? null,
        maximumQuantity: data.maximumQuantity ?? 0,
        minimumQuantity: data.minimumQuantity ?? 0,
        notes: data.notes ?? '',
        vat: data.vat ?? 0,
        isGeneralSalesPolicy: data.isGeneralSalesPolicy ?? true,
        isItemWeighed: data.isItemWeighed ?? false,
        isActive: data.isActive ?? true,
        warehouseIds: data.warehouseIds ?? null,
        isItemFree: data.isItemFree ?? false,
        freeStartDate: data.freeStartDate ?? null,
        freeEndDate: data.freeEndDate ?? null,
        isPrintFrozen: data.isPrintFrozen ?? false,
        reasonToFreezePrint: data.reasonToFreezePrint ?? '',
        isSaleFrozen: data.isSaleFrozen ?? false,
        reasonToFreezeSale: data.reasonToFreezeSale ?? '',
        isTransferFrozen: data.isTransferFrozen ?? false,
        hasExpirationDate: data.hasExpirationDate ?? false,
        daysToAlert: data.daysToAlert ?? 0,
      });

      if (this.isViewMode) {
        this.disableFormControls();
      }
    }
  }

  private disableFormControls(): void {
    this.productForm.disable();
    if (this.uomform) {
      this.uomform.disable();
    }
  }

  isDisabled(unitId: number): boolean {
    return this.disabledUnitIds.includes(unitId);
  }

  updateDisabledUnitIds() {
    const formArray = this.uomform.get('itemUnitRequests') as FormArray;
    this.disabledUnitIds = formArray.controls
      .map(control => control.get('unitId')?.value)
      .filter((unitId: number) => unitId !== undefined) as number[];
  }

  isSpecialTabError() {
    return (
      this.productForm.get('freeStartDate').invalid ||
      this.productForm.get('freeEndDate').invalid ||
      this.productForm.get('reasonToFreezePrint').invalid ||
      this.productForm.get('reasonToFreezeSale').invalid
    );
  }

  addUnitsPriceRow(data: ProductUnits | null) {
    const row = this.formBuilder.group({
      unitBarcode: [data && data?.unitBarcode, Validators.required],
      unitId: [data && data?.unitId, Validators.required],
      costPrice: [(data && data?.costPrice) || 0, Validators.required],
      openPurchasePrice: [(data && data?.openPurchasePrice) || 0, Validators.required],
      purchasePrice: [(data && data?.purchasePrice) || 0, Validators.required],
      transportCost: [(data && data?.transportCost) || 0, Validators.required],
      retailPrice: [(data && data?.retailPrice) || 0, Validators.required],
      wholesalePrice: [(data && data?.wholesalePrice) || 0, Validators.required],
      distributorPrice: [(data && data?.distributorPrice) || 0, Validators.required],
      isGeneralDscntMethod: [(data && data?.isGeneralDscntMethod) || false],
      discount: [(data && data?.discount) || 0],
      isGeneralProfitMethod: [(data && data?.isGeneralProfitMethod) || false],
      profit: [(data && data?.profit) || 0],
      itemUnitId: [data ? data.itemUnitId : null],
    });
    // if (row.get('unitId').value == null) {
    //   row.get('unitId').setValue(this.units[0].unitId);
    // }
    this.uomrows.push(row);
    this.updateActionButtonStatus();
  }

  // Get create mode status
  get isCreateMode(): boolean {
    return this.mode === ActionType.create;
  }

  get isEditMode(): boolean {
    return this.mode === ActionType.edit;
  }

  // Get create mode status
  get isViewMode(): boolean {
    return this.mode === ActionType.view;
  }

  fileChangeEvent(event: any): void {
    const mimeType = event.target.files[0].type;
    if (mimeType.match(/image\/*/) == null) {
      return;
    }
    this.showCropper = true;
    this.imageChangedEvent = event;
  }

  imageLoaded() {
    // show cropper
  }
  cropperReady() {
    // cropper ready
  }
  loadImageFailed() {
    // show message
  }
}
