import { Directionality } from '@angular/cdk/bidi';
import { Component, EventEmitter, Output } from '@angular/core';
import { UntypedFormBuilder } from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { of } from 'rxjs';
import { catchError, finalize } from 'rxjs/operators';
import { ProductService } from 'src/app/core/api/product.service';
import { IInventory, IProductSearch } from 'src/app/modules/featured/catalog/models/product';
import { ProductAdjustmentsParams } from 'src/app/modules/featured/catalog/models/productAdjustmentParams';
import { BaseSearchSelectionComponent } from '../../base-search-selection/base-search-selection.component';
import { ProductGetAllComponent } from '../product-get-all/product-get-all.component';

@Component({
  selector: 'app-product-search-selection',
  templateUrl: './product-search-selection.component.html',
  styleUrls: ['./product-search-selection.component.scss'],
})
export class ProductSearchSelectionComponent extends BaseSearchSelectionComponent<IInventory> {
  displayedSearchColumns: string[] = [
    'itemCode',
    'itemName',
    'unitName',
    'warehouseName',
    'totalQuantityPerUnit',
    'retailPrice',
    'wholesalePrice',
    'distributorPrice',
    'purchasePrice',
    'discount',
    'category',
  ];
  constructor(
    formBuilder: UntypedFormBuilder,
    private productService: ProductService,
    direction: Directionality,
    private dialog: MatDialog
  ) {
    super(formBuilder, direction);
  }

  protected fetchItems(value: string) {
    const params = new ProductAdjustmentsParams();
    params.searchString = value;
    params.pageSize = 999;
    return this.productService.getProductsByFilter(params).pipe(
      catchError(err => {
        console.error('Error fetching products', err);
        this.isLoading = false;
        this.resultNotFound = true;
        return of([]);
      }),
      finalize(() => (this.isLoading = false))
    );
  }

  protected extractData(searchResult: IProductSearch): IInventory[] {
    return searchResult?.inventories ?? [];
  }

  protected clearForms(): void {
    //
  }

  getAllProducts() {
    this.clearSelection();
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '90vw';
    dialogConfig.position = {
      top: '50px',
    };
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(ProductGetAllComponent, dialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.itemSelected.emit(result);
      }
    });
  }
}
