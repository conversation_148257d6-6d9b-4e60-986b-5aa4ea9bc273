import { Directionality } from '@angular/cdk/bidi';
import { Component, EventEmitter, Inject, OnInit, Output } from '@angular/core';
import {
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogConfig,
  MatDialogRef,
} from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';

import { CommonService } from 'src/app/core/api/common.service';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { ConfirmDialogComponent } from 'src/app/modules/shared/components/confirm-dialog/confirm-dialog.component';
import { BulkEditData } from '../../../models/bulkedit';
import { Category } from '../../../models/category';
import { CategoryParams } from '../../../models/categoryParams';
import { CategoryService } from '../../../../../../core/api/category.service';
import { ProductService } from '../../../../../../core/api/product.service';

@Component({
  selector: 'app-bulk-edit-products',
  templateUrl: './bulk-edit-products.component.html',
  styleUrls: ['./bulk-edit-products.component.scss'],
})
export class BulkEditProductsComponent implements OnInit {
  @Output() accepted: EventEmitter<void> = new EventEmitter();

  productBulkEditForm: UntypedFormGroup;
  categoryList: Category[] = [];
  categoryUpdate = new UntypedFormControl(false);
  vatUpdate = new UntypedFormControl(false);
  statusUpdate = new UntypedFormControl(false);

  constructor(
    private toastr: ToastrService,
    private productService: ProductService,
    private fb: UntypedFormBuilder,
    private authService: AuthService,
    private categoryService: CategoryService,
    private dialogRef: MatDialogRef<BulkEditProductsComponent>,
    @Inject(MAT_DIALOG_DATA) public data: BulkEditData,
    private dialog: MatDialog,
    private commonService: CommonService,
    private direction: Directionality
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.loadCategoryData();
    this.productBulkEditForm.get('itemIds').setValue(this.data.selectedRecords);
  }

  private initializeForm(): void {
    this.productBulkEditForm = this.fb.group({
      categoryId: [null],
      vat: [0],
      isActive: ['true'],
      itemIds: [],
      companyId: [this.authService.getCompanyID],
    });

    this.setupControlValidators();
  }

  private setupControlValidators(): void {
    this.categoryUpdate.valueChanges.subscribe(value =>
      this.updateControlValidators('categoryId', value)
    );
    this.vatUpdate.valueChanges.subscribe(value => this.updateControlValidators('vat', value));
    this.statusUpdate.valueChanges.subscribe(value =>
      this.updateControlValidators('isActive', value)
    );
  }

  private updateControlValidators(controlName: string, shouldValidate: boolean): void {
    const control = this.productBulkEditForm.get(controlName);
    if (shouldValidate) {
      control?.addValidators(Validators.required);
    } else {
      control?.clearValidators();
      control?.setValue(null); // Clear the value if not validating
    }
    control?.updateValueAndValidity();
  }

  private loadCategoryData(): void {
    const categoryParams = new CategoryParams();
    this.categoryService.getAllCategories(categoryParams).subscribe(categories => {
      this.categoryList = categories;
    });
  }

  onSubmit(event: Event): void {
    event.preventDefault();
    this.productBulkEditForm.markAllAsTouched();
    if (this.productBulkEditForm.valid) {
      this.confirmSubmission();
    } else {
      this.commonService.scrollToError();
    }
  }

  private confirmSubmission(): void {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.minWidth = '600px';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    dialogConfig.data = 'common.confirmAction';

    const dialogRef = this.dialog.open(ConfirmDialogComponent, dialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.processData();
      }
    });
  }

  private processData(): void {
    const formData = { ...this.productBulkEditForm.value };

    if (!this.vatUpdate.value) {
      delete formData.vat;
    }
    if (!this.categoryUpdate.value) {
      delete formData.categoryId;
    }
    if (!this.statusUpdate.value) {
      delete formData.isActive;
    }

    this.productService.bulkUpdateProducts(formData).subscribe(() => {
      //this.toastr.success('Bul Edit Successful');
      this.commonService.playSuccessSound();
      this.dialogRef.close();
      this.accepted.emit();
    });
  }

  onNoClick(event: Event): void {
    event.preventDefault();
    this.dialogRef.close(false);
  }
}
