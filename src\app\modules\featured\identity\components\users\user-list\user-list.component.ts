import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { IUser } from '../../../../../../core/interfaces/user';
import { UserParams } from '../../../../../../core/models/params/userParams';
import { UserService } from '../../../../../../core/api/identity/user.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-user-list',
  templateUrl: './user-list.component.html',
  styleUrls: ['./user-list.component.scss'],
})
export class UserListComponent implements OnInit {
  @ViewChild('filter') filter: ElementRef;
  @ViewChild('searchInput') searchInput: ElementRef;
  @ViewChild(MatPaginator) set matPaginator(paginator: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = paginator;
    }
  }
  @ViewChild(MatSort) set matSort(sort: MatSort) {
    if (this.dataSource) {
      this.dataSource.sort = sort;
    }
  }
  users: IUser[];
  displayedColumns: string[];
  dataSource: MatTableDataSource<IUser>;
  isLoading = true;
  formTitle: string;
  constructor(public userService: UserService, private route: ActivatedRoute) {}

  ngOnInit(): void {
    this.formTitle = this.route.snapshot.data['title'];
    this.getUsers();
    this.initColumns();
  }

  getUsers(): void {
    this.userService.getAllUsers(new UserParams()).subscribe((result: IUser[]) => {
      this.users = result;
      this.isLoading = false;
      this.dataSource = new MatTableDataSource<IUser>(this.users);
    });
  }

  initColumns(): void {
    this.displayedColumns = ['action', 'username', 'firstName', 'lastName', 'roleNames'];
  }

  applyFilter(filterValue: string): void {
    filterValue = filterValue.trim();
    filterValue = filterValue.toLowerCase();
    this.dataSource.filter = filterValue;
  }

  clearSearchInput() {
    this.searchInput.nativeElement.value = '';
    this.getUsers();
  }
}
