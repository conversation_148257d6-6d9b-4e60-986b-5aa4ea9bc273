import { AfterViewInit, Component, Input, OnInit, SimpleChanges, ViewChild } from '@angular/core';
import {
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { addDays } from 'date-fns';
import { CustomValidators } from 'ngx-custom-validators';
import { combineLatest, forkJoin } from 'rxjs';
import { distinctUntilChanged, startWith } from 'rxjs/operators';
import { ChartOfAccountsApiService } from 'src/app/core/api/accounts/chart-of-accounts-api.service';
import { CustomerService } from 'src/app/core/api/trading/customer.service';
import { ActionType } from 'src/app/core/enums/actionType';
import { ICustomer } from 'src/app/core/interfaces/customer';
import { IPaymentDetails, IPaymentViewData } from 'src/app/core/interfaces/payment';
import { ISaleDetails, customePayment } from 'src/app/core/interfaces/sales';
import { convertDateForBE } from 'src/app/core/utils/date-utils';
import { Account } from 'src/app/modules/featured/accounts/models/account';
import { PaymentsPostSaleComponent } from '../payments-post-sale/payments-post-sale.component';

@Component({
  selector: 'app-standard-payments',
  templateUrl: './standard-payments.component.html',
  styleUrls: ['./standard-payments.component.scss'],
})
export class StandardPaymentsComponent implements OnInit, AfterViewInit {
  @ViewChild('paymentpostsales') private paymentpostsales: PaymentsPostSaleComponent;
  @Input() grandTotal: number;
  @Input() totalVat: number;
  @Input() discount: number;
  @Input() totalExcVatDisc: number;
  @Input() paymentViewData: IPaymentViewData;
  @Input() saleDetails: ISaleDetails;
  @Input() mode: ActionType;
  @Input() fractionAmountEnabled: boolean;
  @Input() singlePaymentsAllowed: boolean;
  @Input() customePaymentSelection: customePayment;
  @Input() hideFractionAllowed = false;
  //booleans
  showCardField = false;
  isMultiOptionSelected = false;
  showAcceptCheckBox = false;
  isCashPaymentAllowed = false;
  isCardPaymentAllowed = false;
  isCreditPaymentAllowed = false;
  isBankPaymentAllowed = false;
  paymentError = false;
  balanceError = false;
  // calculation fields
  balanceAmount = 0;
  changeAmount = 0;
  fractionAmount = 0;
  availableBalance = 0;
  // forms
  form: UntypedFormGroup;
  paymentForm: UntypedFormGroup;

  cashAccounts: Account[] = [];
  bankAccounts: Account[] = [];
  actionMode = ActionType;
  isCashPaymentDisabled = false;
  isCardPaymentDisabled = false;
  isCreditPaymentDisabled = false;
  isBankPaymentDisabled = false;
  customerProfile: ICustomer;
  isExistingClient: boolean;
  constructor(
    private formBuilder: UntypedFormBuilder,
    private chartOfAccountsService: ChartOfAccountsApiService,
    private cusomerService: CustomerService
  ) {}

  get isNotViewMode() {
    return this.mode !== ActionType.view;
  }

  get grandTotals() {
    return this.paymentForm?.controls['grandTotal'].value;
  }

  get fractionTotals() {
    return this.paymentForm?.controls['fractionAmount'].value;
  }

  get balanceAmounts() {
    return this.balanceAmount;
  }

  get changeAmounts() {
    return this.changeAmount;
  }

  get discounts() {
    return this.discount;
  }

  get totalExcVatDiscs() {
    return this.totalExcVatDisc;
  }

  get totalVats() {
    return this.totalVat;
  }

  ngOnInit(): void {
    this.setUpPaymentForm(this.paymentViewData);
    this.subscribeToPaymentTypeChanges();
    this.subscribeToAmountCalculationChanges();
    this.subscribeToCustomerProfile();
  }

  ngAfterViewInit() {
    this.getAccountDetails();
  }

  private subscribeToCustomerProfile(): void {
    this.cusomerService.customerSelection$.subscribe(selection => {
      console.error('Customer Selection ', selection);
      if (selection) {
        this.handleCustomerTypeChange(selection);
      } else {
        this.isExistingClient = false;
        this.customerProfile = null; // Extract the customer profile
        this.resetPaymentFields();
        console.log('Walk-in customer defaulted');
        this.setPaymentsForCustomers(true, true, false, false);
      }
    });
  }

  private subscribeToPaymentTypeChanges(): void {
    this.paymentForm?.controls['bankType'].valueChanges.subscribe(changes => {
      console.log('paymentTypeChanges -> bankType', changes);
      this.paymentForm?.controls['bankAmount'].markAsUntouched();
      this.paymentForm?.controls['bankAccountId'].markAsUntouched();
      if (!changes) {
        this.removeBankTypeSettings();
      } else {
        this.addBankTypeSettings();
      }
      this.paymentForm?.controls['bankAccountId'].updateValueAndValidity();
      this.paymentForm?.controls['bankAmount'].updateValueAndValidity();
      this.singlePaymentType();
    });

    this.paymentForm?.controls['cashType'].valueChanges.subscribe(changes => {
      console.log('paymentTypeChanges -> cashType', changes);
      this.paymentForm?.controls['cashAmount'].markAsUntouched();
      this.paymentForm?.controls['cashAccountId'].markAsUntouched();
      if (!changes) {
        this.removeCashTypeSettings();
      } else {
        this.addCashTypeSettings();
      }
      this.paymentForm?.controls['cashAccountId'].updateValueAndValidity();
      this.paymentForm?.controls['cashAmount'].updateValueAndValidity();
      this.singlePaymentType();
    });

    this.paymentForm?.controls['cardType'].valueChanges.subscribe(changes => {
      console.log('paymentTypeChanges -> cardType', changes);
      this.paymentForm?.controls['cardAccountId'].markAsUntouched();
      this.paymentForm?.controls['cardAmount'].markAsUntouched();
      if (!changes) {
        this.removeCardTypeSettings();
      } else {
        this.addCardTypeSettings();
      }
      this.paymentForm?.controls['cardAccountId'].updateValueAndValidity();
      this.paymentForm?.controls['cardAmount'].updateValueAndValidity();
      this.singlePaymentType();
    });

    this.paymentForm?.controls['creditType'].valueChanges.subscribe(changes => {
      console.log('paymentTypeChanges -> creditType', changes);
      this.paymentForm?.controls['creditDueDate'].markAsUntouched();
      if (!changes) {
        this.removeCreditTypeSettings();
      } else {
        // see if the customer has option for credit date and add days
        const profile = this.customerProfile;
        if (profile['hasCreditPrivilege']) {
          const creditDueDate = addDays(new Date(), Number(profile['paymentToleranceDays']));
          this.paymentForm?.controls['creditDueDate'].setValue(creditDueDate);
        }
        this.paymentForm?.controls['creditDueDate'].addValidators(Validators.required);
        this.paymentForm?.controls['creditAmount'].setValue(this.grandTotals);
      }
      this.paymentForm?.controls['creditDueDate'].updateValueAndValidity();
      this.singlePaymentType();
    });

    this.paymentForm?.controls['halala'].valueChanges.subscribe(changes => {
      console.log('paymentTypeChanges -> halala', changes);
      if (changes) {
        this.fractionAmount =
          +this.paymentForm?.controls['grandTotal'].value -
          Math.trunc(+this.paymentForm?.controls['grandTotal'].value);
        this.paymentForm?.controls['fractionAmount'].setValue(this.fractionAmount.toFixed(2));
      } else {
        this.fractionAmount = 0;
        this.paymentForm?.controls['fractionAmount'].setValue(0);
      }
      this.singlePaymentType();
    });
  }

  private subscribeToAmountCalculationChanges(): void {
    combineLatest([
      this.paymentForm?.controls['cashAmount'].valueChanges.pipe(
        startWith(+this.paymentForm?.controls['cashAmount'].value),
        distinctUntilChanged()
      ),
      this.paymentForm?.controls['cardAmount'].valueChanges.pipe(
        startWith(+this.paymentForm?.controls['cardAmount'].value),
        distinctUntilChanged()
      ),
      this.paymentForm?.controls['bankAmount'].valueChanges.pipe(
        startWith(+this.paymentForm?.controls['bankAmount'].value),
        distinctUntilChanged()
      ),
      this.paymentForm?.controls['creditAmount'].valueChanges.pipe(
        startWith(+this.paymentForm?.controls['creditAmount'].value),
        distinctUntilChanged()
      ),
    ]).subscribe(([cashAmount, cardAmount, bankAmount, creditAmount]) => {
      const total = cashAmount + cardAmount + bankAmount + creditAmount;
      this.finalCounts(total);
      this.singlePaymentType();
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    // grand Total triggers changes in totals
    if (changes['grandTotal']?.currentValue !== changes['grandTotal']?.previousValue) {
      console.log('ngOnChanges grandTotal -> ', changes['grandTotal'].currentValue);
      this.paymentForm?.controls['grandTotal'].setValue(changes['grandTotal'].currentValue);
      this.paymentForm?.controls['halala'].setValue(false, { emitEvent: false });
      this.singlePaymentType();
      // when total is zero that means user has deleted all entries, so we reset fields
      if (changes['grandTotal'].currentValue <= 0) {
        this.paymentForm?.controls['fractionAmount'].setValue(0);
        this.resetPaymentFields();
      }
    }
    if (changes['customePaymentSelection']) {
      console.log('customePaymentSelection done');
      this.setPaymentsForCustomers(
        this.customePaymentSelection.cashType,
        this.customePaymentSelection.cardType,
        this.customePaymentSelection.bankType,
        this.customePaymentSelection.creditType
      );
    }
  }

  private handleCustomerTypeChange(data: { existingClient: boolean; customer: ICustomer }): void {
    this.isExistingClient = data.existingClient;
    this.customerProfile = data.customer; // Extract the customer profile
    console.log(this.paymentViewData);
    // Rule check for change in customer type
    if (!this.paymentViewData?.isViewMode) {
      if (!this.isExistingClient) {
        this.resetPaymentFields();
        console.log('Walk-in customer');
        this.setPaymentsForCustomers(true, true, false, false);
      } else {
        this.resetPaymentFields();
        console.log('Existing Customer - profile', this.customerProfile);
        if (this.customerProfile) {
          this.setPaymentsForCustomers(
            this.customerProfile.hasCashPrivilege,
            this.customerProfile.hasCardPrivilege,
            this.customerProfile.hasTransferPrivilege,
            this.customerProfile.hasCreditPrivilege
          ); // Show based on profile
          // if there was credit pribilege get balances
          if (this.customerProfile['hasCreditPrivilege'] && !this.paymentViewData?.isViewMode) {
            this.getBalances(
              this.customerProfile['accountId'],
              this.customerProfile['allowedBalance']
            );
          }
        } else {
          this.setPaymentsForCustomers(false, false, false, false); // Do not show any types
        }
      }
    }
  }

  private getAccountDetails(): void {
    const cashAccounts = this.chartOfAccountsService.getChartOfAccountsForPayments(
      'ASSETS',
      'CASHIER',
      'DETAILED'
    );
    const bankAccounts = this.chartOfAccountsService.getChartOfAccountsForPayments(
      'ASSETS',
      'BANK',
      'DETAILED'
    );
    forkJoin([cashAccounts, bankAccounts]).subscribe(results => {
      console.log(results);
      this.cashAccounts = results[0].accounts;
      this.bankAccounts = results[1].accounts;
      // always patch the first data
    });
  }

  patchDefaultBankAccount(): void {
    this.paymentForm?.controls['bankAccountId'].setValue(this.bankAccounts[0].accountId);
  }

  patchDefaultCashAccount(): void {
    this.paymentForm?.controls['cashAccountId'].setValue(this.cashAccounts[0].accountId);
  }

  patchDefaultCardAccount(): void {
    this.paymentForm?.controls['cardAccountId'].setValue(this.bankAccounts[0].accountId);
  }

  resetPaymentFields(): void {
    this.paymentForm?.controls['bankType'].setValue(false, { onlySelf: true, emitEvent: false });
    this.paymentForm?.controls['bankAccountId'].setValue(null, {
      onlySelf: true,
      emitEvent: false,
    });
    this.paymentForm?.controls['bankAmount'].setValue(null, { onlySelf: true, emitEvent: false });

    this.paymentForm?.controls['cashType'].setValue(false, { onlySelf: true, emitEvent: false });
    this.paymentForm?.controls['cashAccountId'].setValue(null, {
      onlySelf: true,
      emitEvent: false,
    });
    this.paymentForm?.controls['cashAmount'].setValue(null, { onlySelf: true, emitEvent: false });

    this.paymentForm?.controls['cardType'].setValue(false, { onlySelf: true, emitEvent: false });
    this.paymentForm?.controls['cardAccountId'].setValue(null, {
      onlySelf: true,
      emitEvent: false,
    });
    this.paymentForm?.controls['cardAmount'].setValue(null, { onlySelf: true, emitEvent: false });

    this.paymentForm?.controls['creditType'].setValue(false, { onlySelf: true, emitEvent: false });
    this.paymentForm?.controls['creditDueDate'].setValue(null, {
      onlySelf: true,
      emitEvent: false,
    });
    this.paymentForm?.controls['creditAmount'].setValue(null, { onlySelf: true, emitEvent: false });

    this.paymentForm?.markAsUntouched();
    this.paymentForm?.markAsPristine();
    this.paymentForm?.clearValidators();
  }

  setPaymentsForCustomers(cash: boolean, card: boolean, bank: boolean, credit: boolean): void {
    this.isCashPaymentAllowed = cash;
    this.isCardPaymentAllowed = card;
    this.isCreditPaymentAllowed = credit;
    this.isBankPaymentAllowed = bank;
  }

  setUpPaymentForm(paymentData?: IPaymentViewData) {
    this.paymentForm = this.formBuilder.group({
      //bank
      bankType: new UntypedFormControl(paymentData.bankType ?? false),
      bankAccountId: new UntypedFormControl(paymentData.bankAccountId ?? null),
      bankAmount: new UntypedFormControl(paymentData.bankAmount ?? 0),
      //cash
      cashType: new UntypedFormControl(paymentData.cashType ?? false),
      cashAccountId: new UntypedFormControl(paymentData.cashAccountId ?? null),
      cashAmount: new UntypedFormControl(paymentData.cashAmount ?? 0),
      //card
      cardType: new UntypedFormControl(paymentData.cardType ?? false),
      cardAccountId: new UntypedFormControl(paymentData.cardAccountId ?? null),
      cardAmount: new UntypedFormControl(paymentData.cardAmount ?? 0),
      //credit
      creditType: new UntypedFormControl(paymentData.creditType ?? false),
      creditDueDate: new UntypedFormControl(paymentData.creditDueDate ?? null),
      creditAmount: new UntypedFormControl(paymentData.creditAmount ?? 0),
      //
      grandTotal: new UntypedFormControl(paymentData.grandTotal ?? 0),
      //fraction
      halala: new UntypedFormControl(paymentData.halala ?? false),
      fractionAmount: new UntypedFormControl(paymentData.fractionAmount ?? 0),
    });
    // set data
    if (paymentData.isViewMode) {
      if (paymentData.fractionAmount > 0.0) {
        this.showAcceptCheckBox = true;
      } else {
        this.showAcceptCheckBox = false;
      }
      this.setPaymentsForCustomers(
        paymentData.cashType,
        paymentData.cardType,
        paymentData.bankType,
        paymentData.creditType
      );
      this.paymentForm.disable();
    }
  }

  getBalances(accountId: number, customerBalance: number) {
    this.chartOfAccountsService
      .getBalance(accountId)
      .subscribe(balance => (this.availableBalance = customerBalance - balance));
  }

  finalCounts(total: number) {
    if (total === this.grandTotal) {
      this.balanceAmount = 0;
      this.changeAmount = 0;
    } else if (total < this.grandTotal) {
      this.balanceAmount = this.grandTotal - total;
      this.changeAmount = 0;
    } else {
      this.balanceAmount = 0;
      this.changeAmount = total - this.grandTotal;
    }
  }

  removeBankTypeSettings() {
    this.paymentForm?.controls['bankAccountId'].setValue(null);
    this.paymentForm?.controls['bankAmount'].setValue(0);
    this.paymentForm?.controls['bankAmount'].clearValidators();
    this.paymentForm?.controls['bankAccountId'].clearValidators();
  }

  addBankTypeSettings() {
    this.paymentForm?.controls['bankAmount'].addValidators(
      Validators.compose([Validators.required, CustomValidators.gt(0)])
    );
    this.paymentForm?.controls['bankAccountId'].addValidators(Validators.required);
    if (this.isNotViewMode) {
      this.patchDefaultBankAccount();
    }
  }

  removeCashTypeSettings() {
    this.paymentForm?.controls['cashAccountId'].setValue(null);
    this.paymentForm?.controls['cashAmount'].setValue(0);
    this.paymentForm?.controls['cashAmount'].clearValidators();
    this.paymentForm?.controls['cashAccountId'].clearValidators();
  }

  addCashTypeSettings() {
    this.paymentForm?.controls['cashAmount'].addValidators(
      Validators.compose([Validators.required, CustomValidators.gt(0)])
    );
    this.paymentForm?.controls['cashAccountId'].addValidators(Validators.required);
    if (this.isNotViewMode) {
      this.patchDefaultCashAccount();
    }
  }

  removeCardTypeSettings() {
    this.paymentForm?.controls['cardAccountId'].setValue(null);
    this.paymentForm?.controls['cardAmount'].setValue(0);
    this.paymentForm?.controls['cardAmount'].clearValidators();
    this.paymentForm?.controls['cardAccountId'].clearValidators();
  }

  removeCreditTypeSettings() {
    this.paymentForm?.controls['creditDueDate'].setValue(null);
    this.paymentForm?.controls['creditAmount'].setValue(0);
    this.paymentForm?.controls['creditAmount'].clearValidators();
    this.paymentForm?.controls['creditDueDate'].clearValidators();
  }

  addCreditTypeSettings() {
    this.paymentForm?.controls['creditAmount'].addValidators(
      Validators.compose([Validators.required, CustomValidators.gt(0)])
    );
    this.paymentForm?.controls['creditDueDate'].addValidators(Validators.required);
  }

  addCardTypeSettings() {
    this.paymentForm?.controls['cardAmount'].addValidators(
      Validators.compose([Validators.required, CustomValidators.gt(0)])
    );
    this.paymentForm?.controls['cardAccountId'].addValidators(Validators.required);
    if (this.isNotViewMode) {
      this.patchDefaultCardAccount();
    }
  }

  singlePaymentType(): void {
    const isSingle = this.checkPaymentSelection();
    if (isSingle === 1) {
      this.paymentError = false;
      console.log('singlePaymentType under process ');
      if (this.paymentForm?.controls['cashType'].value) {
        this.paymentForm?.controls['cashAmount'].setValue(this.grandTotal, { emitEvent: false });
        this.finalCounts(this.grandTotal);
        const value =
          +this.paymentForm?.controls['grandTotal'].value -
          Math.trunc(+this.paymentForm?.controls['grandTotal'].value);
        if (value > 0.0) {
          this.showAcceptCheckBox = true;
        } else {
          this.showAcceptCheckBox = false;
        }
        // if single payment only
        if (this.singlePaymentsAllowed) {
          this.isCardPaymentDisabled = true;
          this.isCreditPaymentDisabled = true;
          this.isBankPaymentDisabled = true;
        } else {
          this.recheckStatus();
        }
      }
      if (this.paymentForm?.controls['cardType'].value) {
        this.paymentForm?.controls['cardAmount'].setValue(this.grandTotal, { emitEvent: false });
        this.finalCounts(this.grandTotal);
        this.showAcceptCheckBox = false;
        if (this.singlePaymentsAllowed) {
          this.isCashPaymentDisabled = true;
          this.isCreditPaymentDisabled = true;
          this.isBankPaymentDisabled = true;
        } else {
          this.recheckStatus();
        }
      }
      if (this.paymentForm?.controls['bankType'].value) {
        this.paymentForm?.controls['bankAmount'].setValue(this.grandTotal, { emitEvent: false });
        this.finalCounts(this.grandTotal);
        this.showAcceptCheckBox = false;
        if (this.singlePaymentsAllowed) {
          this.isCashPaymentDisabled = true;
          this.isCardPaymentDisabled = true;
          this.isCreditPaymentDisabled = true;
        } else {
          this.recheckStatus();
        }
      }
      if (this.paymentForm?.controls['creditType'].value) {
        this.paymentForm?.controls['creditAmount'].setValue(this.grandTotals);
        if (this.singlePaymentsAllowed) {
          this.isCashPaymentDisabled = true;
          this.isCardPaymentDisabled = true;
          this.isBankPaymentDisabled = true;
        } else {
          this.recheckStatus();
        }
      }
    }
    if (isSingle > 1) {
      this.paymentError = false;
      console.log('singlePaymentType NOT FOUND ');
      const [cashAmount, cardAmount, bankAmount] = [
        this.paymentForm?.controls['cashAmount'].value,
        this.paymentForm?.controls['cardAmount'].value,
        this.paymentForm?.controls['bankAmount'].value,
      ];
      const total = cashAmount + cardAmount + bankAmount;
      this.showAcceptCheckBox = false;
      this.paymentForm?.controls['halala'].setValue(false, { emitEvent: false });
      this.paymentForm?.controls['fractionAmount'].setValue(0);
      this.finalCounts(total);
    }
    if (isSingle === 0) {
      this.recheckStatus();
    }
  }

  private recheckStatus(): void {
    if (this.checkPaymentSelection() === 0) {
      this.isCashPaymentDisabled = false;
      this.isCreditPaymentDisabled = false;
      this.isBankPaymentDisabled = false;
      this.isCardPaymentDisabled = false;
    }
  }

  public paymentFormIsAllValid(): boolean {
    const isPaymentSelected = this.checkPaymentSelection();
    this.paymentForm.markAllAsTouched();
    // if user has selected atleast 1 and is controls are valid, then GOOD else ERROR
    isPaymentSelected > 0 ? (this.paymentError = false) : (this.paymentError = true);
    if (isPaymentSelected > 0 && this.paymentForm.valid && this.isBalanceEnough()) {
      return true;
    } else {
      return false;
    }
  }

  public paymentFormIsAllValidPurchase(): boolean {
    const isPaymentSelected = this.checkPaymentSelection();
    this.paymentForm.markAllAsTouched();
    // if user has selected atleast 1 and is controls are valid, then GOOD else ERROR
    isPaymentSelected > 0 ? (this.paymentError = false) : (this.paymentError = true);
    if (isPaymentSelected > 0 && this.paymentForm.valid) {
      return true;
    } else {
      return false;
    }
  }

  isBalanceEnough(): boolean {
    if (this.checkifDueDateEnabled()) {
      console.log('isBalanceEnough', this.grandTotals, this.availableBalance);
      return this.grandTotals > this.availableBalance
        ? ((this.balanceError = true), false)
        : ((this.balanceError = false), true);
    } else {
      return true;
    }
  }

  checkifDueDateEnabled(): boolean {
    // see if the date is selected:
    // if yes then show error message that cant be selected
    if (this.paymentForm?.controls['creditType'].value) {
      this.isCashPaymentDisabled = true;
      this.isCardPaymentDisabled = true;
      this.isBankPaymentDisabled = true;
    }
    return this.paymentForm?.controls['creditType'].value;
  }

  checkifOtherPaymentsEnabled(): boolean {
    // see if the other payment other than date is selected:
    // if yes then show error message that cant be selected
    return (
      this.paymentForm?.controls['cardType'].value ||
      this.paymentForm?.controls['bankType'].value ||
      this.paymentForm?.controls['cashType'].value
    );
  }

  private checkPaymentSelection(): number {
    const [cashType, cardType, bankType, creditType] = [
      this.paymentForm?.controls['cashType'].value,
      this.paymentForm?.controls['cardType'].value,
      this.paymentForm?.controls['bankType'].value,
      this.paymentForm?.controls['creditType'].value,
    ];
    return [cashType, cardType, bankType, creditType].filter(bool => bool === true)?.length;
  }

  getPaymentsFormValue(): IPaymentDetails {
    const value: IPaymentDetails = {
      ...this.paymentForm.value,
      grandTotal: this.grandTotal,
      totalExclVatDiscount: this.totalExcVatDisc,
      totalVat: this.totalVat,
      totalDiscount: this.discount,
      balanceAmount: this.balanceAmount,
      changeAmount: this.changeAmount,
      creditDueDate: this.paymentForm?.controls['creditDueDate'].value
        ? convertDateForBE(this.paymentForm?.controls['creditDueDate'].value)
        : null,
    };
    return value;
  }

  resetForm(): void {
    this.paymentForm.reset();
    this.resetPaymentFields();
    this.balanceAmount = 0;
    this.fractionAmount = 0;
    this.changeAmount = 0;
    this.paymentForm.markAsPristine();
    this.paymentForm.markAsUntouched();
    console.log('resetting payment', this.paymentForm.getRawValue());
  }
}
