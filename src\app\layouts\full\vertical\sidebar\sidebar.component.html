<div class="flex">
  <div
    class="user-profile profile-bar"
    style="background: url(assets/images/backgrounds/user-info.jpg) no-repeat">
    <!-- User profile image -->
    <div class="profile-img">
      <img class="rounded-circle" src="assets/images/profile/profile.png" alt="user" width="50" />
    </div>
    <!-- User profile text-->
    <!-- ============================================================== -->
    <!-- Profile - style you can find in header.scss -->
    <!-- ============================================================== -->
    <div class="profile-text">
      <a class="d-flex align-items-center w-100 f-s-16" [matMenuTriggerFor]="sdprofile">
        <PERSON><PERSON><PERSON>
        <i-tabler class="icon-16 m-l-auto" name="chevron-down"></i-tabler>
      </a>
    </div>
    <mat-menu class="cardWithShadow profile-dd" #sdprofile="matMenu" xPosition="after">
      <button mat-menu-item>
        <mat-icon class="d-flex align-items-center"
          ><i-tabler class="icon-16 d-flex" name="settings"></i-tabler
        ></mat-icon>
        Settings
      </button>
      <button mat-menu-item>
        <mat-icon class="d-flex align-items-center"
          ><i-tabler class="icon-16 d-flex" name="users"></i-tabler
        ></mat-icon>
        Profile
      </button>
      <button mat-menu-item>
        <mat-icon class="d-flex align-items-center"
          ><i-tabler class="icon-16 d-flex" name="bell-off"></i-tabler
        ></mat-icon>
        Disable notifications
      </button>
      <a [routerLink]="['/authentication/side-login']" mat-menu-item
        ><mat-icon class="d-flex align-items-center"
          ><i-tabler class="icon-16 d-flex" name="logout"></i-tabler
        ></mat-icon>
        Sign Out</a
      >
    </mat-menu>
  </div>
</div>
