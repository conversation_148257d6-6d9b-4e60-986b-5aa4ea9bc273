import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-sales-summary',
  templateUrl: './sales-summary.component.html',
  styleUrls: ['./sales-summary.component.scss'],
})
export class SalesSummaryComponent {
  @Input() totalExcVatDisc: number;
  @Input() discount: number;
  @Input() totalVat: number;
  @Input() fractionTotals: number | undefined;
  @Input() balanceAmount: number | undefined;
  @Input() changeAmount: number | undefined;
  @Input() grandTotal: number;
  @Input() showOnlyGrandTotal = false;

  get isFractionShown() {
    return this.fractionTotals !== undefined;
  }

  get isbalanceAmountShown() {
    return this.balanceAmount !== undefined;
  }

  get ischangeAmountShown() {
    return this.changeAmount !== undefined;
  }
}
