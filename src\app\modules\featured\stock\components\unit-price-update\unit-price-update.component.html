<form [formGroup]="filterForm" autocomplete="off">
  <mat-card>
    <div class="row">
      <div class="col-12">
        <div class="row no-gutters">
          <div class="p-2 col-md-5">
            <app-searchbox #searchBoxForm formControlName="searchBoxForm"></app-searchbox>
          </div>
          <div class="p-2 col-lg-2 col-md-2 col-sm-3 d-flex align-items-end">
            <div class="form-group">
              <mat-label>{{ 'openqty.category' | translate }}</mat-label>
              <mat-form-field class="w-100">
                <mat-select #select multiple formControlName="categoryId">
                  <app-select-check-all [model]="filterForm.get('categoryId')" [values]="select">
                  </app-select-check-all>
                  <mat-option
                    *ngFor="let parentCategory of categoryList"
                    [value]="parentCategory.categoryId">
                    {{
                      parentCategory.parentCategoryId
                        ? getParentName(parentCategory.parentCategoryId) +
                          '/' +
                          (parentCategory | localized)
                        : (parentCategory | localized)
                    }}</mat-option
                  >
                </mat-select>
              </mat-form-field>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div>
      <button
        (click)="getFilterData($event); searchBoxForm.markAllAsTouched(); (false)"
        mat-raised-button
        color="primary">
        {{ 'searchPanel.searchString' | translate }}
      </button>
      <button class="m-l-10" (click)="clearFilters($event); (false)" mat-raised-button>
        {{ 'searchPanel.clear' | translate }}
      </button>
    </div>
  </mat-card>
</form>

<!-- Main Table Contents -->
<form [formGroup]="adjustmentForm" autocomplete="off">
  <!-- <form [formGroup]="adjustmentForm" autocomplete="off"> -->
  <div class="table-responsive">
    <table
      class="w-100"
      [dataSource]="tableData"
      mat-table
      matSort
      formArrayName="InventoryOpeningRequest">
      <ng-container matColumnDef="itemCode">
        <th *matHeaderCellDef mat-header-cell mat-sort-header>
          {{ 'priceUpdate.itemCode' | translate }}
        </th>
        <td
          class="f-s-14"
          *matCellDef="let element; let i = index"
          [formGroupName]="getActualIndex(i)"
          mat-cell>
          {{ element.itemCode }}
        </td>
      </ng-container>

      <!-- Cost Price -->
      <ng-container matColumnDef="nameArabic">
        <th *matHeaderCellDef mat-header-cell mat-sort-header>
          {{ 'priceUpdate.name' | translate }}
        </th>
        <td
          class="f-s-14"
          *matCellDef="let element; let i = index"
          [formGroupName]="getActualIndex(i)"
          mat-cell>
          {{ element.nameArabic }}
        </td>
      </ng-container>
      <!-- Purchase Price  -->
      <ng-container matColumnDef="unitName">
        <th *matHeaderCellDef mat-header-cell mat-sort-header>
          {{ 'priceUpdate.unit' | translate }}
        </th>
        <td
          class="f-s-14"
          *matCellDef="let element; let i = index"
          [formGroupName]="getActualIndex(i)"
          mat-cell>
          {{ element.unitName }}
        </td>
      </ng-container>
      <!-- Cost Price -->
      <ng-container matColumnDef="costPrice">
        <th *matHeaderCellDef mat-header-cell>{{ 'priceUpdate.costPrice' | translate }}</th>
        <td
          class="f-s-14"
          *matCellDef="let element; let i = index"
          [formGroupName]="getActualIndex(i)"
          mat-cell>
          <mat-form-field class="w-100">
            <input matInput type="number" formControlName="costPrice" />
          </mat-form-field>
        </td>
      </ng-container>
      <!-- Purchase Price  -->
      <ng-container matColumnDef="purchasePrice">
        <th *matHeaderCellDef mat-header-cell>{{ 'priceUpdate.purchasePrice' | translate }}</th>
        <td
          class="f-s-14"
          *matCellDef="let element; let i = index"
          [formGroupName]="getActualIndex(i)"
          mat-cell>
          <mat-form-field class="w-100">
            <input matInput type="number" formControlName="purchasePrice" />
          </mat-form-field>
        </td>
      </ng-container>
      <!-- open Purchase Price -->
      <ng-container matColumnDef="openPurchasePrice">
        <th *matHeaderCellDef mat-header-cell>{{ 'priceUpdate.openPurchasePrice' | translate }}</th>
        <td
          class="f-s-14"
          *matCellDef="let element; let i = index"
          [formGroupName]="getActualIndex(i)"
          mat-cell>
          <mat-form-field class="w-100">
            <input matInput type="number" formControlName="openPurchasePrice" />
          </mat-form-field>
        </td>
      </ng-container>
      <!-- Whole Sale Price -->
      <ng-container matColumnDef="wholesalePrice">
        <th *matHeaderCellDef mat-header-cell>{{ 'priceUpdate.wholesalesPrice' | translate }}</th>
        <td
          class="f-s-14"
          *matCellDef="let element; let i = index"
          [formGroupName]="getActualIndex(i)"
          mat-cell>
          <mat-form-field class="w-100">
            <input matInput type="number" formControlName="wholesalePrice" />
          </mat-form-field>
        </td>
      </ng-container>
      <!-- distributorPrice -->
      <ng-container matColumnDef="distributorPrice">
        <th *matHeaderCellDef mat-header-cell>{{ 'priceUpdate.distributorPrice' | translate }}</th>
        <td
          class="f-s-14"
          *matCellDef="let element; let i = index"
          [formGroupName]="getActualIndex(i)"
          mat-cell>
          <mat-form-field class="w-100">
            <input matInput type="number" formControlName="distributorPrice" />
          </mat-form-field>
        </td>
      </ng-container>
      <!-- retailPrice -->
      <ng-container matColumnDef="retailPrice">
        <th *matHeaderCellDef mat-header-cell>{{ 'priceUpdate.retailPrice' | translate }}</th>
        <td
          class="f-s-14"
          *matCellDef="let element; let i = index"
          [formGroupName]="getActualIndex(i)"
          mat-cell>
          <mat-form-field class="w-100">
            <input matInput type="number" formControlName="retailPrice" />
          </mat-form-field>
        </td>
      </ng-container>
      <tr class="mat-row" *matNoDataRow>
        <td class="f-s-14" class="text-center" [attr.colspan]="displayedColumns.length">
          {{ 'common.noDataFound' | translate }}
        </td>
      </tr>
      <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
      <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
    </table>
  </div>
  <mat-paginator
    [length]="totalRecords"
    [pageSize]="10"
    [pageSizeOptions]="[100, 125]"
    (page)="onPageChange($event)">
  </mat-paginator>
</form>
<!------------------------------------ Action Buttons ------------------------------------------------->
<div class="text-center">
  <button class="m-l-10" (click)="onSubmit($event)" mat-stroked-button color="primary">
    {{ 'common.submit' | translate }}
  </button>
  <button class="m-l-10" [routerLink]="['../']" mat-stroked-button color="warn">
    {{ 'common.cancel' | translate }}
  </button>
</div>
<!-- Main Table Contents -->

<!-- Not Found -->
<!-- <div *ngIf="!products?.length && submitted" fxLayout="row wrap">
  <div fxFlex.gt-sm="100" fxFlex.gt-xs="100" fxFlex="100">
    <mat-card appearance="outlined">
      <mat-card-content class="mat-typography">
        <div style="text-align: center">No Results Found.</div>
      </mat-card-content>
    </mat-card>
  </div>
</div> -->
<!-- Not Found -->
