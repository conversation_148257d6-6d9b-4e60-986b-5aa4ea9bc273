import { Directionality } from '@angular/cdk/bidi';
import { FlatTreeControl } from '@angular/cdk/tree';
import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatTreeFlatDataSource, MatTreeFlattener } from '@angular/material/tree';
import { ChartOfAccountsFormComponent } from '../chart-of-accounts/chart-of-accounts-form/chart-of-accounts-form.component';
import { Account } from '../../models/account';

interface AccountNode {
  accountId: number;
  parentAccountId: number;
  nameEnglish: string;
  nameArabic: string;
  accountNumber: number;
  accountType: string;
  color: string;
  children?: AccountNode[];
}

interface FlatAccountNode {
  expandable: boolean;
  level: number;
  accountId: number;
  nameEnglish: string;
  nameArabic: string;
  color: string;
}

@Component({
  selector: 'app-accounts-tree',
  templateUrl: './accounts-tree.component.html',
  styleUrls: ['./accounts-tree.component.scss'],
})
export class AccountsTreeComponent implements OnInit {
  @ViewChild('searchInput') searchInput: ElementRef;
  @Input() set accounts(data: Account[]) {
    if (data && data.length) {
      this.originalData = this.generateHierarchy(data);
    }
  }
  constructor(private dialog: MatDialog, private direction: Directionality) {}
  originalData: AccountNode[] = [];
  showIcons = false;
  // originalData = this.generateHierarchy([
  //   {
  //     accountId: 1,
  //     branchId: 1,
  //     accountNumber: 1,
  //     nameArabic: 'Asset Accounts',
  //     nameEnglish: 'Asset Accounts',
  //     parentAccountId: 0,
  //     accountLevel: 1,
  //     accountType: 'GENERAL',
  //     accountGroup: 'ASSETS',
  //     accountNature: 'DEBIT',
  //     finalAccountGroup: 'BUDGET',
  //     businessGroup: 'GENERAL',
  //     isDepreciating: false,
  //     isHidden: false,
  //     isFreezed: false,
  //     isContraAccount: false,
  //     costCentreId: 0,
  //     note: null,
  //   },
  //   {
  //     accountId: 6,
  //     branchId: 1,
  //     accountNumber: 11,
  //     nameArabic: 'currentassets',
  //     nameEnglish: 'currentassets',
  //     parentAccountId: 1,
  //     accountLevel: 2,
  //     accountType: 'GENERAL',
  //     accountGroup: 'ASSETS',
  //     accountNature: 'DEBIT',
  //     finalAccountGroup: 'BUDGET',
  //     businessGroup: 'GENERAL',
  //     isDepreciating: false,
  //     isHidden: false,
  //     isFreezed: false,
  //     isContraAccount: false,
  //     costCentreId: 0,
  //     note: '',
  //   },
  //   {
  //     accountId: 8,
  //     branchId: 1,
  //     accountNumber: 111,
  //     nameArabic: 'cashaccounts',
  //     nameEnglish: 'cashaccounts',
  //     parentAccountId: 6,
  //     accountLevel: 3,
  //     accountType: 'GENERAL',
  //     accountGroup: 'ASSETS',
  //     accountNature: 'DEBIT',
  //     finalAccountGroup: 'BUDGET',
  //     businessGroup: 'CASHIER',
  //     isDepreciating: false,
  //     isHidden: false,
  //     isFreezed: false,
  //     isContraAccount: false,
  //     costCentreId: 0,
  //     note: '',
  //   },
  //   {
  //     accountId: 9,
  //     branchId: 1,
  //     accountNumber: ***********,
  //     nameArabic: 'cashaccount-1',
  //     nameEnglish: 'cashaccount-1',
  //     parentAccountId: 8,
  //     accountLevel: 4,
  //     accountType: 'DETAILED',
  //     accountGroup: 'ASSETS',
  //     accountNature: 'DEBIT',
  //     finalAccountGroup: 'BUDGET',
  //     businessGroup: 'CASHIER',
  //     isDepreciating: false,
  //     isHidden: false,
  //     isFreezed: false,
  //     isContraAccount: false,
  //     costCentreId: 0,
  //     note: '',
  //   },
  //   {
  //     accountId: 10,
  //     branchId: 1,
  //     accountNumber: 112,
  //     nameArabic: 'bankaccounts',
  //     nameEnglish: 'bankaccounts',
  //     parentAccountId: 6,
  //     accountLevel: 3,
  //     accountType: 'GENERAL',
  //     accountGroup: 'ASSETS',
  //     accountNature: 'DEBIT',
  //     finalAccountGroup: 'BUDGET',
  //     businessGroup: 'BANK',
  //     isDepreciating: false,
  //     isHidden: false,
  //     isFreezed: false,
  //     isContraAccount: false,
  //     costCentreId: 0,
  //     note: '',
  //   },
  //   {
  //     accountId: 11,
  //     branchId: 1,
  //     accountNumber: ***********,
  //     nameArabic: 'bankaccount-1',
  //     nameEnglish: 'bankaccount-1',
  //     parentAccountId: 10,
  //     accountLevel: 4,
  //     accountType: 'DETAILED',
  //     accountGroup: 'ASSETS',
  //     accountNature: 'DEBIT',
  //     finalAccountGroup: 'BUDGET',
  //     businessGroup: 'BANK',
  //     isDepreciating: false,
  //     isHidden: false,
  //     isFreezed: false,
  //     isContraAccount: false,
  //     costCentreId: 0,
  //     note: '',
  //   },
  //   {
  //     accountId: 12,
  //     branchId: 1,
  //     accountNumber: 113,
  //     nameArabic: 'customeraccounts',
  //     nameEnglish: 'customeraccounts',
  //     parentAccountId: 6,
  //     accountLevel: 3,
  //     accountType: 'GENERAL',
  //     accountGroup: 'ASSETS',
  //     accountNature: 'DEBIT',
  //     finalAccountGroup: 'BUDGET',
  //     businessGroup: 'CUSTOMER',
  //     isDepreciating: false,
  //     isHidden: false,
  //     isFreezed: false,
  //     isContraAccount: false,
  //     costCentreId: 0,
  //     note: '',
  //   },
  //   {
  //     accountId: 13,
  //     branchId: 1,
  //     accountNumber: ***********,
  //     nameArabic: 'customer-1',
  //     nameEnglish: 'customer-1',
  //     parentAccountId: 12,
  //     accountLevel: 4,
  //     accountType: 'DETAILED',
  //     accountGroup: 'ASSETS',
  //     accountNature: 'DEBIT',
  //     finalAccountGroup: 'BUDGET',
  //     businessGroup: 'CUSTOMER',
  //     isDepreciating: false,
  //     isHidden: false,
  //     isFreezed: false,
  //     isContraAccount: false,
  //     costCentreId: 0,
  //     note: '',
  //   },
  //   {
  //     accountId: 22,
  //     branchId: 1,
  //     accountNumber: 114,
  //     nameArabic: 'distributoraccounts',
  //     nameEnglish: 'distributoraccounts',
  //     parentAccountId: 6,
  //     accountLevel: 3,
  //     accountType: 'GENERAL',
  //     accountGroup: 'ASSETS',
  //     accountNature: 'DEBIT',
  //     finalAccountGroup: 'BUDGET',
  //     businessGroup: 'DISTRIBUTOR',
  //     isDepreciating: false,
  //     isHidden: false,
  //     isFreezed: false,
  //     isContraAccount: false,
  //     costCentreId: 0,
  //     note: '',
  //   },
  //   {
  //     accountId: 23,
  //     branchId: 1,
  //     accountNumber: ***********,
  //     nameArabic: 'dist1',
  //     nameEnglish: 'dist1',
  //     parentAccountId: 22,
  //     accountLevel: 4,
  //     accountType: 'DETAILED',
  //     accountGroup: 'ASSETS',
  //     accountNature: 'DEBIT',
  //     finalAccountGroup: null,
  //     businessGroup: 'DISTRIBUTOR',
  //     isDepreciating: false,
  //     isHidden: false,
  //     isFreezed: false,
  //     isContraAccount: false,
  //     costCentreId: 0,
  //     note: null,
  //   },
  //   {
  //     accountId: 7,
  //     branchId: 1,
  //     accountNumber: 12,
  //     nameArabic: 'fixedassets',
  //     nameEnglish: 'fixedassets',
  //     parentAccountId: 1,
  //     accountLevel: 2,
  //     accountType: 'GENERAL',
  //     accountGroup: 'ASSETS',
  //     accountNature: 'DEBIT',
  //     finalAccountGroup: 'BUDGET',
  //     businessGroup: 'GENERAL',
  //     isDepreciating: false,
  //     isHidden: false,
  //     isFreezed: false,
  //     isContraAccount: false,
  //     costCentreId: 0,
  //     note: '',
  //   },
  //   {
  //     accountId: 2,
  //     branchId: 1,
  //     accountNumber: 2,
  //     nameArabic: 'Liability Accounts',
  //     nameEnglish: 'Liability Accounts',
  //     parentAccountId: 0,
  //     accountLevel: 1,
  //     accountType: 'GENERAL',
  //     accountGroup: 'LIABILITIES',
  //     accountNature: 'CREDIT',
  //     finalAccountGroup: 'BUDGET',
  //     businessGroup: 'GENERAL',
  //     isDepreciating: false,
  //     isHidden: false,
  //     isFreezed: false,
  //     isContraAccount: false,
  //     costCentreId: 0,
  //     note: null,
  //   },
  //   {
  //     accountId: 14,
  //     branchId: 1,
  //     accountNumber: 21,
  //     nameArabic: 'vataccounts',
  //     nameEnglish: 'vataccounts',
  //     parentAccountId: 2,
  //     accountLevel: 2,
  //     accountType: 'GENERAL',
  //     accountGroup: 'LIABILITIES',
  //     accountNature: 'DEBIT',
  //     finalAccountGroup: 'BUDGET',
  //     businessGroup: 'GENERAL',
  //     isDepreciating: false,
  //     isHidden: false,
  //     isFreezed: false,
  //     isContraAccount: false,
  //     costCentreId: 0,
  //     note: '',
  //   },
  //   {
  //     accountId: 15,
  //     branchId: 1,
  //     accountNumber: ***********,
  //     nameArabic: 'vataccount-1',
  //     nameEnglish: 'vataccount-1',
  //     parentAccountId: 14,
  //     accountLevel: 3,
  //     accountType: 'DETAILED',
  //     accountGroup: 'LIABILITIES',
  //     accountNature: 'DEBIT',
  //     finalAccountGroup: 'BUDGET',
  //     businessGroup: 'GENERAL',
  //     isDepreciating: false,
  //     isHidden: false,
  //     isFreezed: false,
  //     isContraAccount: false,
  //     costCentreId: 0,
  //     note: '',
  //   },
  //   {
  //     accountId: 3,
  //     branchId: 1,
  //     accountNumber: 3,
  //     nameArabic: 'Capital Accounts',
  //     nameEnglish: 'Capital Accounts',
  //     parentAccountId: 0,
  //     accountLevel: 1,
  //     accountType: 'GENERAL',
  //     accountGroup: 'CAPITALS',
  //     accountNature: 'CREDIT',
  //     finalAccountGroup: 'BUDGET',
  //     businessGroup: 'GENERAL',
  //     isDepreciating: false,
  //     isHidden: false,
  //     isFreezed: false,
  //     isContraAccount: false,
  //     costCentreId: 0,
  //     note: null,
  //   },
  //   {
  //     accountId: 4,
  //     branchId: 1,
  //     accountNumber: 4,
  //     nameArabic: 'Revenue Accounts',
  //     nameEnglish: 'Revenue Accounts',
  //     parentAccountId: 0,
  //     accountLevel: 1,
  //     accountType: 'GENERAL',
  //     accountGroup: 'REVENUES',
  //     accountNature: 'CREDIT',
  //     finalAccountGroup: 'BUDGET',
  //     businessGroup: 'GENERAL',
  //     isDepreciating: false,
  //     isHidden: false,
  //     isFreezed: false,
  //     isContraAccount: false,
  //     costCentreId: 0,
  //     note: null,
  //   },
  //   {
  //     accountId: 21,
  //     branchId: 1,
  //     accountNumber: ***********,
  //     nameArabic: 'discountaccount-1',
  //     nameEnglish: 'discountaccount-1',
  //     parentAccountId: 4,
  //     accountLevel: 2,
  //     accountType: 'DETAILED',
  //     accountGroup: 'REVENUES',
  //     accountNature: 'DEBIT',
  //     finalAccountGroup: 'BUDGET',
  //     businessGroup: 'GENERAL',
  //     isDepreciating: false,
  //     isHidden: false,
  //     isFreezed: false,
  //     isContraAccount: true,
  //     costCentreId: 0,
  //     note: '',
  //   },
  //   {
  //     accountId: 16,
  //     branchId: 1,
  //     accountNumber: 41,
  //     nameArabic: 'salesaccounts',
  //     nameEnglish: 'salesaccounts',
  //     parentAccountId: 4,
  //     accountLevel: 2,
  //     accountType: 'GENERAL',
  //     accountGroup: 'REVENUES',
  //     accountNature: 'CREDIT',
  //     finalAccountGroup: 'BUDGET',
  //     businessGroup: 'GENERAL',
  //     isDepreciating: false,
  //     isHidden: false,
  //     isFreezed: false,
  //     isContraAccount: false,
  //     costCentreId: 0,
  //     note: '',
  //   },
  //   {
  //     accountId: 17,
  //     branchId: 1,
  //     accountNumber: ***********,
  //     nameArabic: 'cashsalesacoount-1',
  //     nameEnglish: 'cashsalesacoount-1',
  //     parentAccountId: 16,
  //     accountLevel: 3,
  //     accountType: 'DETAILED',
  //     accountGroup: 'REVENUES',
  //     accountNature: 'CREDIT',
  //     finalAccountGroup: 'BUDGET',
  //     businessGroup: 'GENERAL',
  //     isDepreciating: false,
  //     isHidden: false,
  //     isFreezed: false,
  //     isContraAccount: false,
  //     costCentreId: 0,
  //     note: '',
  //   },
  //   {
  //     accountId: 18,
  //     branchId: 1,
  //     accountNumber: ***********,
  //     nameArabic: 'creditsalesaccounts-1',
  //     nameEnglish: 'creditsalesaccounts-1',
  //     parentAccountId: 16,
  //     accountLevel: 3,
  //     accountType: 'DETAILED',
  //     accountGroup: 'REVENUES',
  //     accountNature: 'CREDIT',
  //     finalAccountGroup: 'BUDGET',
  //     businessGroup: 'GENERAL',
  //     isDepreciating: false,
  //     isHidden: false,
  //     isFreezed: false,
  //     isContraAccount: false,
  //     costCentreId: 0,
  //     note: '',
  //   },
  //   {
  //     accountId: 19,
  //     branchId: 1,
  //     accountNumber: ***********,
  //     nameArabic: 'cardsalesaccounts-1',
  //     nameEnglish: 'cardsalesaccounts-1',
  //     parentAccountId: 16,
  //     accountLevel: 3,
  //     accountType: 'DETAILED',
  //     accountGroup: 'REVENUES',
  //     accountNature: 'CREDIT',
  //     finalAccountGroup: 'BUDGET',
  //     businessGroup: 'GENERAL',
  //     isDepreciating: false,
  //     isHidden: false,
  //     isFreezed: false,
  //     isContraAccount: false,
  //     costCentreId: 0,
  //     note: '',
  //   },
  //   {
  //     accountId: 20,
  //     branchId: 1,
  //     accountNumber: ***********,
  //     nameArabic: 'banksalesaccount-1',
  //     nameEnglish: 'banksalesaccount-1',
  //     parentAccountId: 16,
  //     accountLevel: 3,
  //     accountType: 'DETAILED',
  //     accountGroup: 'REVENUES',
  //     accountNature: 'CREDIT',
  //     finalAccountGroup: 'BUDGET',
  //     businessGroup: 'GENERAL',
  //     isDepreciating: false,
  //     isHidden: false,
  //     isFreezed: false,
  //     isContraAccount: false,
  //     costCentreId: 0,
  //     note: '',
  //   },
  //   {
  //     accountId: 5,
  //     branchId: 1,
  //     accountNumber: 5,
  //     nameArabic: 'Expense Accounts',
  //     nameEnglish: 'Expense Accounts',
  //     parentAccountId: 0,
  //     accountLevel: 1,
  //     accountType: 'GENERAL',
  //     accountGroup: 'EXPENSES',
  //     accountNature: 'DEBIT',
  //     finalAccountGroup: 'BUDGET',
  //     businessGroup: 'GENERAL',
  //     isDepreciating: false,
  //     isHidden: false,
  //     isFreezed: false,
  //     isContraAccount: false,
  //     costCentreId: 0,
  //     note: null,
  //   },
  // ]);

  generateHierarchy(data: any[]): AccountNode[] {
    const nodes: { [key: number]: AccountNode } = {};

    data.forEach(item => {
      nodes[item.accountId] = {
        accountId: item.accountId,
        parentAccountId: item.parentAccountId,
        nameEnglish: item.nameEnglish,
        nameArabic: item.nameArabic,
        accountType: item.accountType,
        accountNumber: item.accountNumber,
        children: [],
        color: '',
      };
    });

    const roots: AccountNode[] = [];

    data.forEach(item => {
      if (item.parentAccountId !== 0) {
        nodes[item.parentAccountId].children?.push(nodes[item.accountId]);
      } else {
        roots.push(nodes[item.accountId]);
        nodes[item.accountId].color = 'primary';
      }
    });
    data.forEach(item => {
      if (
        item.parentAccountId !== 0 &&
        !nodes[item.accountId].color &&
        nodes[item.accountId].accountType === 'DETAILED'
      ) {
        nodes[item.accountId].color = 'error'; // Non-top-level parents
      }
    });
    data.forEach(item => {
      if (
        item.parentAccountId !== 0 &&
        !nodes[item.accountId].color &&
        nodes[item.accountId].accountType === 'GENERAL'
      ) {
        nodes[item.accountId].color = 'success'; // Non-top-level parents
      }
    });
    roots.forEach(root => {
      root.children?.forEach(child => {
        child.color = 'warning'; // Immediate children of top-level parent
      });
    });

    return this.sortNodes(roots);
  }

  sortNodes(data: AccountNode[]): AccountNode[] {
    const sortedNodes: AccountNode[] = data.filter(
      node => node.parentAccountId === 0 && node.children && node.children.length > 0
    );
    const remainingNodes: AccountNode[] = data.filter(
      node =>
        node.parentAccountId !== 0 ||
        (node.parentAccountId === 0 && (!node.children || node.children.length === 0))
    );

    return [...sortedNodes, ...remainingNodes];
  }
  treeControl = new FlatTreeControl<FlatAccountNode>(
    node => node.level,
    node => node.expandable
  );

  private transformer = (node: AccountNode, level?: number) => {
    return {
      expandable: !!node.children && node.children.length > 0,
      accountId: node.accountId,
      nameEnglish: node.nameEnglish,
      nameArabic: node.nameArabic,
      accountNumber: node.accountNumber,
      accountType: node.accountType,
      level: level,
      color: node.color,
    };
  };

  treeFlattener = new MatTreeFlattener(
    this.transformer,
    node => node.level,
    node => node.expandable,
    node => node.children
  );

  dataSource = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener);

  filteredData: AccountNode[] = [];
  filterText = '';

  ngOnInit() {
    this.filteredData = this.originalData;
    this.dataSource.data = this.filteredData;
  }

  applyFilter() {
    this.dataSource.data = this.filterTreeNodes(this.originalData, this.filterText);
    if (this.filterText) {
      this.treeControl.expandAll();
    } else {
      this.treeControl.collapseAll();
    }
  }

  filterTreeNodes(nodes: AccountNode[], filter: string): AccountNode[] {
    return nodes
      .map(node => {
        const filteredNode: AccountNode = { ...node };
        if (node.children) {
          filteredNode.children = this.filterTreeNodes(node.children, filter);
        }
        return filteredNode;
      })
      .filter(
        filteredNode =>
          filteredNode.nameEnglish.toLowerCase().includes(filter.toLowerCase()) ||
          filteredNode.nameArabic.toLowerCase().includes(filter.toLowerCase()) ||
          filteredNode.accountNumber.toString().includes(filter) ||
          (filteredNode.children && filteredNode.children.length > 0)
      );
  }

  hasChild = (_: number, node: FlatAccountNode) => node.expandable;

  openModal(accountId: number, mode: string) {
    const dialogConfig = new MatDialogConfig();
    const data: any = {
      accountId: accountId,
    };
    dialogConfig.minWidth = '90vw';
    dialogConfig.position = {
      top: '50px',
    };
    dialogConfig.maxHeight = '90vh';
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.data = data;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(ChartOfAccountsFormComponent, dialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      //
    });
  }

  clearSearchInput() {
    this.searchInput.nativeElement.value = '';
    this.dataSource.data = [...this.originalData];
  }
}
