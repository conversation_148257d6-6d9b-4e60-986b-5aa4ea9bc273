<div class="action-buttons">
  <button (click)="printInvoice()" mat-raised-button color="primary">
    <mat-icon>print</mat-icon>
    Print Invoice
  </button>
</div>

<div class="invoice-container">
  <div class="invoice-header">
    <div class="header-layout">
      <!-- Left Address (English) -->
      <div class="english-address address">
        <h3>{{ invoice.templateStaticData.optionalText1 }}</h3>
        <p class="company-info">{{ invoice.templateStaticData.optionalText2 }}</p>
        <p class="company-info">{{ invoice.templateStaticData.optionalText3 }}</p>
        <p class="contact-detail">{{ invoice.templateStaticData.optionalContactLine1 }}</p>
        <p class="contact-detail">{{ invoice.templateStaticData.optionalContactLine2 }}</p>
        <p class="contact-detail">{{ invoice.templateStaticData.optionalContactLine3 }}</p>
      </div>

      <!-- Center Logo -->
      <div class="logo">
        <img [src]="companyLogo" alt="Company Logo" />
      </div>

      <!-- Right Address (Arabic) -->
      <div class="arabic-address address">
        <h3>{{ invoice.templateStaticData.mainText1 }}</h3>
        <p class="company-info">{{ invoice.templateStaticData.mainText2 }}</p>
        <p class="company-info">{{ invoice.templateStaticData.mainText3 }}</p>
        <p class="contact-detail">{{ invoice.templateStaticData.contactLine1 }}</p>
        <p class="contact-detail">{{ invoice.templateStaticData.contactLine2 }}</p>
        <p class="contact-detail">{{ invoice.templateStaticData.contactLine3 }}</p>
      </div>
    </div>

    <div class="invoice-title">
      <table class="title-table">
        <tr>
          <td class="title-cell" colspan="2">
            <span class="title-text">TAX INVOICE</span>
            <span class="title-text arabic">فاتورة ضريبية</span>
          </td>
        </tr>
        <tr class="info-row">
          <td class="info-cell equal-width">
            <div class="doc-container">
              <div class="doc-header">
                <span class="doc-label">Doc No:</span>
                <span class="doc-value">
                  {{ invoice.documentNumber }} ({{
                    invoice.createdAt | date : 'dd/MM/yyyy HH:mm:ss'
                  }})
                </span>
                <span class="doc-arabic-label">رقم الفاتورة</span>
              </div>
            </div>
          </td>
          <td class="info-cell equal-width">
            <div class="doc-container">
              <div class="doc-header">
                <span class="doc-label">Ref No:</span>
                <span class="doc-value">
                  {{ invoice.referenceDocumentNumber }} ({{
                    invoice.referenceDocumentDate | date : 'dd/MM/yyyy HH:mm:ss'
                  }})
                </span>
                <span class="doc-arabic-label">رقم المرجع</span>
              </div>
            </div>
          </td>
        </tr>
        <tr class="info-row">
          <td class="info-cell equal-width">
            <div class="info-content">
              <div class="left-content">
                <span class="label">Payment Type:</span>
                <span class="value">{{ getPaymentTypeText(invoice.payments) }}</span>
              </div>
              <div class="right-content">
                <span class="arabic-label">طريقة الدفع</span>
              </div>
            </div>
          </td>
          <td class="info-cell equal-width">
            <div class="info-content">
              <div class="left-content">
                <span class="label">Distributor:</span>
                <span class="value">{{ getFormattedDistributorName() }}</span>
              </div>
              <div class="right-content">
                <span class="arabic-label">الموزع</span>
              </div>
            </div>
          </td>
        </tr>
      </table>
    </div>
  </div>

  <div class="invoice-client-info">
    <table class="client-info-table">
      <tr>
        <td class="client-section" colspan="2">
          <table class="client-details-table">
            <tr>
              <td class="label">Client Name</td>
              <td class="value">
                <span class="client-name-arabic">{{ invoice.customer?.nameArabic || '-' }}</span>
                <span class="client-name-english">{{ invoice.customer?.nameEnglish || '-' }}</span>
              </td>
              <td class="arabic">اسم العميل</td>
            </tr>
            <tr>
              <td class="label">Account No</td>
              <td class="value">{{ invoice.customer?.accountNumber || '-' }}</td>
              <td class="arabic">رقم العميل</td>
            </tr>
            <tr>
              <td class="label">Address</td>
              <td class="value">{{ getFormattedAddress() }}</td>
              <td class="arabic">عنوان العميل</td>
            </tr>
            <tr>
              <td class="label">Phone / Email</td>
              <td class="value">{{ getFormattedPhoneEmail() }}</td>
              <td class="arabic">رقم الهاتف والايميل</td>
            </tr>
            <tr>
              <td class="label">VAT No. / Identification</td>
              <td class="value">{{ getFormattedVatIdentification() }}</td>
              <td class="arabic">الرقم الضريبي /الهوية</td>
            </tr>
          </table>
        </td>
        <td class="meta-section">
          <div class="qr-code-container">
            <div class="qr-code">
              <!-- ZATCA QR Code using angularx-qrcode -->
              <qrcode
                *ngIf="invoice?.qrInvoice"
                [elementType]="'img'"
                [errorCorrectionLevel]="'M'"
                [qrdata]="invoice.qrInvoice">
              </qrcode>
              <!-- Fallback image if no QR data -->
              <img
                *ngIf="!invoice?.qrInvoice"
                src="assets/images/sawami_logo.png"
                alt="Company Logo"
                style="width: 100%; height: auto; max-width: 200px; max-height: 200px" />
            </div>
          </div>
        </td>
      </tr>
    </table>
  </div>

  <div class="invoice-items">
    <table class="items-table">
      <thead>
        <tr>
          <th>
            <div class="header-bilingual">
              <span class="arabic">رقم الصنف</span>
              <span class="en">Item Code</span>
            </div>
          </th>
          <th>
            <div class="header-bilingual">
              <span class="arabic">البيان</span>
              <span class="en">Description</span>
            </div>
          </th>
          <th>
            <div class="header-bilingual">
              <span class="arabic">الوحدة</span>
              <span class="en">Unit</span>
            </div>
          </th>
          <th>
            <div class="header-bilingual">
              <span class="arabic">الكمية</span>
              <span class="en">Quantity</span>
            </div>
          </th>
          <th class="sar-header">
            <div class="header-bilingual">
              <span class="arabic">السعر</span>
              <span class="en">Price</span>
            </div>
            <span class="header-currency-badge">
              <img class="sar-symbol" src="assets/images/sar.svg" alt="SAR" />
            </span>
          </th>
          <th class="sar-header">
            <div class="header-bilingual">
              <span class="arabic">المجموع قبل الضريبة</span>
              <span class="en">Total Excl VAT</span>
            </div>
            <span class="header-currency-badge">
              <img class="sar-symbol" src="assets/images/sar.svg" alt="SAR" />
            </span>
          </th>
          <th>
            <div class="header-bilingual">
              <span class="arabic">نسبة الضريبة</span>
              <span class="en">% VAT</span>
            </div>
          </th>
          <th class="sar-header">
            <div class="header-bilingual">
              <span class="arabic">قيمة الضريبة</span>
              <span class="en">VAT Amt</span>
            </div>
            <span class="header-currency-badge">
              <img class="sar-symbol" src="assets/images/sar.svg" alt="SAR" />
            </span>
          </th>
          <th class="sar-header">
            <div class="header-bilingual">
              <span class="arabic">المجموع بعد الضريبة</span>
              <span class="en">Total Incl VAT</span>
            </div>
            <span class="header-currency-badge">
              <img class="sar-symbol" src="assets/images/sar.svg" alt="SAR" />
            </span>
          </th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngIf="invoice.items && invoice.items.length > 0">
          <tr *ngFor="let item of invoice.items">
            <td class="left-align">{{ item.itemCode }}</td>
            <td>
              <span class="desc-arabic">{{ item.product?.nameArabic || '-' }}</span>
              <span class="desc-english">{{
                item.itemName || item.product?.nameEnglish || '-'
              }}</span>
            </td>
            <td>
              <span class="unit-arabic">{{ item.product?.unitName || '-' }}</span>
              <span class="unit-english">{{ item.unitName || '-' }}</span>
            </td>
            <td class="right-align">{{ item.quantity }}</td>
            <td class="right-align">{{ invoice.payments.totalExclVatDiscount || '0.00' }}</td>
            <td class="right-align">{{ invoice.payments.totalExclVatDiscount || '0.00' }}</td>
            <td class="right-align">{{ item.vat || '0.00' }}</td>
            <td class="right-align">{{ invoice.payments.totalVat || '0.00' }}</td>
            <td class="right-align">{{ invoice.payments.grandTotal || '0.00' }}</td>
          </tr>
        </ng-container>
        <!-- <ng-template #noItems>
          <tr>
            <td class="right-align">ITEM001</td>
            <td>
              <span class="desc-arabic">منتج عربي تجريبي</span>
              <span class="desc-english">Sample Product</span>
            </td>
            <td>
              <span class="unit-arabic">وحدة</span>
              <span class="unit-english">Unit</span>
            </td>
            <td class="right-align">1</td>
            <td class="right-align">0.00</td>
            <td class="right-align">0.00</td>
            <td class="right-align">15%</td>
            <td class="right-align">0.00</td>
            <td class="right-align">0.00</td>
          </tr>
        </ng-template> -->
      </tbody>
    </table>
  </div>

  <div class="invoice-footer">
    <table class="footer-table">
      <tr>
        <td class="notes-section">
          <div class="invoice-notes">
            <pre class="notes-pre">{{ invoice?.notes }}</pre>
          </div>
        </td>
        <td class="totals-section">
          <table class="totals-table">
            <tr>
              <td class="label">Total Excluding VAT</td>
              <td class="value">
                <div class="amount-container">
                  <span class="currency-badge">
                    <img class="sar-symbol" src="assets/images/sar.svg" alt="SAR" />
                  </span>
                  <span class="amount">{{ invoice.payments.totalExclVatDiscount }}</span>
                </div>
              </td>
              <td class="arabic">الإجمالي غير شامل ضريبة القيمة المضافة</td>
            </tr>
            <tr>
              <td class="label">Total Discount</td>
              <td class="value">
                <div class="amount-container">
                  <span class="currency-badge">
                    <img class="sar-symbol" src="assets/images/sar.svg" alt="SAR" />
                  </span>
                  <span class="amount">{{ invoice.payments.totalDiscount }}</span>
                </div>
              </td>
              <td class="arabic">الخصم</td>
            </tr>
            <tr>
              <td class="label">Total VAT</td>
              <td class="value">
                <div class="amount-container">
                  <span class="currency-badge">
                    <img class="sar-symbol" src="assets/images/sar.svg" alt="SAR" />
                  </span>
                  <span class="amount">{{ invoice.payments.totalVat }}</span>
                </div>
              </td>
              <td class="arabic">إجمالي ضريبة القيمة المضافة</td>
            </tr>
            <tr>
              <td class="label">Fractional deduction</td>
              <td class="value">
                <div class="amount-container">
                  <span class="currency-badge">
                    <img class="sar-symbol" src="assets/images/sar.svg" alt="SAR" />
                  </span>
                  <span class="amount">{{ invoice.payments.fractionAmount || '0.00' }}</span>
                </div>
              </td>
              <td class="arabic">خصم الكسور</td>
            </tr>
            <tr class="total-row">
              <td class="label grand-total">Grand Total</td>
              <td class="value grand-total">
                <div class="amount-container">
                  <span class="currency-badge">
                    <img class="sar-symbol" src="assets/images/sar.svg" alt="SAR" />
                  </span>
                  <span class="amount">{{ grantTotal() }}</span>
                </div>
              </td>
              <td class="arabic grand-total">اجمالي المبلغ المستحق شامل قيمة الضريبة المضافة</td>
            </tr>
          </table>
          <div class="total-in-words">
            <div class="arabic-words">{{ arabicTotalInWords }}</div>
            <div class="english-words">
              {{ totalInWords }}
            </div>
          </div>
        </td>
      </tr>
    </table>
  </div>

  <div class="invoice-footer-info">
    <table class="footer-info-table">
      <tr>
        <td class="signature-section" colspan="3">
          <table class="signatures-table">
            <tr>
              <td class="qr-code-cell">
                <div class="footer-qr-container">
                  <!-- Footer QR Code using angularx-qrcode -->
                  <qrcode
                    *ngIf="invoice?.templateStaticData?.qrText"
                    [allowEmptyString]="false"
                    [elementType]="'img'"
                    [errorCorrectionLevel]="'L'"
                    [scale]="1"
                    [width]="75"
                    [qrdata]="invoice.templateStaticData.qrText">
                  </qrcode>
                  <!-- Fallback image if no QR data -->
                  <img
                    class="footer-qr-image"
                    *ngIf="!invoice?.templateStaticData?.qrText"
                    src="assets/images/sawami-qr.png"
                    alt="Footer QR Code" />
                </div>
              </td>
              <td class="prepared-by">
                <div class="signature-header">
                  <div class="signature-label">Prepared By:</div>
                  <div class="signature-label-ar">بواسطة</div>
                </div>
                <div class="signature-value">{{ invoice?.issuedBy || '-' }}</div>
              </td>
              <td class="client-rep">
                <div class="signature-header">
                  <div class="signature-label">Client Signature:</div>
                  <div class="signature-label-ar">توقيع العميل</div>
                </div>
                <div class="signature-line"></div>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </div>
</div>
