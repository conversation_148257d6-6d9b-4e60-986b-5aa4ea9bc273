import { Directionality } from '@angular/cdk/bidi';
import { AfterViewChecked, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { of } from 'rxjs';
import { catchError, distinctUntilChanged, finalize } from 'rxjs/operators';
import { CustomerService } from 'src/app/core/api/trading/customer.service';
import { SupplierService } from 'src/app/core/api/trading/supplier.service';
import { supplierTypes } from 'src/app/core/configs/dropDownConfig';
import { ActionType } from 'src/app/core/enums/actionType';
import { ICustomerModalData, ICustomerView } from 'src/app/core/interfaces/customer';
import { ISupplier, ISupplierResponse } from 'src/app/core/interfaces/supplier';
import { SupplierParams } from 'src/app/core/models/params/supplierParams';
import { IWalkin } from 'src/app/modules/featured/catalog/models/walkin-customer';
import { BaseSearchSelectionComponent } from 'src/app/modules/shared/components/base-search-selection/base-search-selection.component';
import { WalkInCustomerComponent } from '../../customers/walk-in-customer/walk-in-customer.component';
import { SupplierFormComponent } from '../supplier-form/supplier-form.component';
import { SupplierGetAllComponent } from '../supplier-get-all/supplier-get-all.component';

@Component({
  selector: 'app-supplier-selection',
  templateUrl: './supplier-selection.component.html',
  styleUrls: ['./supplier-selection.component.scss'],
})
export class SupplierSelectionComponent
  extends BaseSearchSelectionComponent<ISupplier>
  implements OnInit, AfterViewChecked
{
  @Input() customerViewData: ICustomerView;
  @Input() mode: ActionType;
  @Output()
  customerTypeSelection: EventEmitter<any> = new EventEmitter();
  @Output() customerProfileSelection: EventEmitter<any> = new EventEmitter();
  public supplierSelectionForm: UntypedFormGroup;
  public supplierTypes = supplierTypes;
  public displayedSearchColumns: string[] = [
    'nameArabic',
    'nameEnglish',
    'vatNumber',
    'accountNumber',
    'phoneNumber',
    'emailId',
  ];
  constructor(
    private supplierService: SupplierService,
    private customerService: CustomerService,
    private translate: TranslateService,
    formBuilder: UntypedFormBuilder,
    direction: Directionality,
    private dialog: MatDialog
  ) {
    super(formBuilder, direction);
  }

  get isViewMode(): boolean {
    return this.mode === ActionType.view;
  }

  get isEditMode(): boolean {
    return this.mode === ActionType.edit;
  }

  get isCustomerSelected(): boolean {
    return this.supplierSelectionForm?.get('customer').value !== null;
  }

  get isExistingCustomer() {
    return this.supplierSelectionForm?.get('existingClient')?.value;
  }

  get hasCustomerData() {
    return this.customerViewData?.isViewMode;
  }

  get isCreateMode() {
    return this.mode === ActionType.create;
  }

  protected clearForms(): void {
    if (this.isCreateMode) {
      this.supplierSelectionForm?.get('customer').patchValue(null, { emitEvent: true });
    }
  }

  protected extractData(searchResult: ISupplierResponse): ISupplier[] {
    return searchResult?.suppliers ?? [];
  }

  protected fetchItems(value: string) {
    const params = new SupplierParams();
    params.searchString = value;
    params.pageSize = 999;
    this.isLoading = true;
    return this.supplierService.getAllSuppliers(params).pipe(
      catchError(err => {
        console.error('Error fetching customers', err);
        this.isLoading = false;
        this.resultNotFound = true;
        return of([]);
      }),
      finalize(() => (this.isLoading = false))
    );
  }

  ngOnInit(): void {
    super.ngOnInit();
    console.log('customerViewData', this.customerViewData);
    this.patchData(this.customerViewData);
    this.supplierSelectionForm?.get('existingClient').valueChanges.subscribe(value => {
      this.customerTypeSelection.emit(value);
      this.supplierSelectionForm?.get('customer').setValue(null);
      if (value) {
        setTimeout(() => this.setFocusInputSearch());
      }
      if (!value) {
        this.autoCompleteInput.get('userInput').patchValue(null, { emitEvent: false });
      }
    });
    //
    this.supplierSelectionForm.valueChanges
      .pipe(
        distinctUntilChanged((prev, curr) => JSON.stringify(prev) === JSON.stringify(curr)) // Prevent duplicate emissions
      )
      .subscribe(value => {
        this.customerService.setCustomerSelection(value); // Update the service with the entire form
      });
  }

  ngAfterViewChecked(): void {
    if (this.customerViewData && this.customerViewData.isViewMode) {
      this.supplierSelectionForm?.disable({ emitEvent: false });
      this.disbaleInputSearch();
    }
  }

  patchData(customerViewData?: ICustomerView): void {
    this.supplierSelectionForm = this.formBuilder.group({
      existingClient: new UntypedFormControl(customerViewData.existingClient ?? false),
      customer: new UntypedFormControl(customerViewData.customer ?? null),
    });
    if (this.hasCustomerData && customerViewData?.existingClient) {
      this.autoCompleteInput
        .get('userInput')
        .setValue(customerViewData.customer, { emitEvent: true });
    }
    if (this.hasCustomerData) {
      console.log('The existingClient control is disabled');
      this.customerService.setCustomerSelection(this.supplierSelectionForm.getRawValue());
    }
  }

  onSelection(selectedCustomer: ISupplier) {
    console.log('onSelection', selectedCustomer);
    this.supplierSelectionForm?.controls['customer'].patchValue(selectedCustomer, {
      emitEvent: true,
    });
    this.customerProfileSelection.emit(selectedCustomer);
    setTimeout(() => {
      this.autoCompleteInput.get('userInput').setValue(selectedCustomer, { emitEvent: true });
    }, 100);
    this.itemSource = null;
  }

  onViewCustomerSelection(event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    if (!this.supplierSelectionForm?.controls['existingClient'].value) {
      this.showWalkInModal();
    } else {
      this.showExistingClientModal();
    }
  }

  showWalkInModal() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '90vw';
    dialogConfig.position = {
      top: '50px',
    };
    dialogConfig.maxHeight = '90vh';
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = this.customerViewData.isViewMode ? false : true;
    dialogConfig.autoFocus = false;
    const data: IWalkin = {
      header: 'Customer Details',
      data: this.supplierSelectionForm?.get('customer').value,
      isViewMode: this.supplierSelectionForm?.controls['existingClient'].disabled,
    };
    dialogConfig.data = data;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(WalkInCustomerComponent, dialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result === 'update') {
        console.log('result', result, dialogRef.componentInstance.getCustomerDataAsObject());
        this.supplierSelectionForm
          .get('customer')
          .setValue(dialogRef.componentInstance.getCustomerDataAsObject());
      }
    });
  }

  showExistingClientModal() {
    const dialogConfig = new MatDialogConfig();
    const data: ICustomerModalData = {
      header: 'Customer Details',
      data: this.supplierSelectionForm?.get('customer').value,
      isViewMode: this.supplierSelectionForm?.controls['existingClient'].disabled,
    };
    dialogConfig.minWidth = '90vw';
    dialogConfig.position = {
      top: '50px',
    };
    dialogConfig.maxHeight = '90vh';
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.data = data;
    dialogConfig.direction = this.direction.value;
    this.dialog.open(SupplierFormComponent, dialogConfig);
  }

  displayFn(user: ISupplier) {
    if (user) {
      const currentLanguage = this.translate.currentLang;
      const name = currentLanguage === 'ar' ? user.nameArabic : user.nameEnglish;
      return user ? name : undefined;
    }
  }

  clearSelections(event: Event) {
    event.preventDefault();
    event.stopPropagation();
    super.clearSelection();
    this.supplierSelectionForm?.get('customer').patchValue(null, { emitEvent: true });
  }

  public customerSelectionFormIsAllValid(): boolean {
    this.supplierSelectionForm?.markAllAsTouched();
    return this.supplierSelectionForm?.disabled
      ? true
      : this.supplierSelectionForm?.valid
      ? true
      : false;
  }

  getAllCustomers(event: Event) {
    event.preventDefault();
    event.stopPropagation();
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '90vw';
    dialogConfig.position = {
      top: '50px',
    };
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.hasBackdrop = true;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(SupplierGetAllComponent, dialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if ((result as ISupplier).supplierId !== undefined) {
        console.log('getAllCustomers', result);
        this.onSelection(result);
      }
    });
  }

  resetForm(): void {
    this.supplierSelectionForm?.reset();
    this.supplierSelectionForm?.get('existingClient').setValue(false);
    this.supplierSelectionForm?.markAsPristine();
    this.supplierSelectionForm?.markAsUntouched();
    console.log('resetting customer fields', this.supplierSelectionForm?.getRawValue());
  }
}
