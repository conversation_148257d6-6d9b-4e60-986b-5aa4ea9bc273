import { HttpClient, HttpClientModule } from '@angular/common/http';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatPaginatorIntl } from '@angular/material/paginator';
import { BrowserModule, HammerModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { CookieService } from 'ngx-cookie-service';
import { NgxScrollTopModule } from 'ngx-scrolltop';
import { AppComponent } from './app.component';
import { AppRoutingModule } from './app.routing';
import { CustomMatPaginatorIntl } from './core/utils/custom-mat-paginator-int';
import { AuthLayoutComponent } from './layouts/auth-layout/auth-layout.component';
import { InvoiceLayoutComponent } from './layouts/invoice-layout/invoice-layout.component';
import { PosLayoutComponent } from './layouts/pos-layout/pos-layout.component';
import { CoreModule } from './modules/core/core/core.module';
import { AuthenticationModule } from './modules/featured/authentication/authentication.module';
import { InvoiceDemoComponent } from './modules/invoice/invoice-demo/invoice-demo.component';
import { SharedModule } from './modules/shared/shared.module';

export function HttpLoaderFactory(http: HttpClient): any {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

@NgModule({
  declarations: [
    AppComponent,
    AuthLayoutComponent,
    PosLayoutComponent,
    InvoiceDemoComponent,
    InvoiceLayoutComponent,
  ],
  imports: [
    CoreModule,
    BrowserModule,
    BrowserAnimationsModule,
    AuthenticationModule,
    AppRoutingModule,
    FormsModule,
    HttpClientModule,
    SharedModule,
    NgMultiSelectDropDownModule.forRoot(),
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient],
      },
    }),
    HammerModule,
    NgxScrollTopModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [
    {
      provide: MatPaginatorIntl,
      useClass: CustomMatPaginatorIntl,
    },
    CookieService,
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
