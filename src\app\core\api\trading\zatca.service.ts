import { Direction } from '@angular/cdk/bidi';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { MatDialog, MatDialogConfig, MatDialogRef } from '@angular/material/dialog';
import { Observable } from 'rxjs';
import { IXmlViewer } from 'src/app/modules/shared/components/xml-viewer/xml-viewer';
import { XmlViewerComponent } from 'src/app/modules/shared/components/xml-viewer/xml-viewer.component';
import { ZatcaResponseComponent } from 'src/app/modules/shared/components/zatca-response/zatca-response.component';
import { environment } from 'src/environments/environment';
import { ValidationResults, ZatcaExceptionResponse, ZatcaModalData } from '../../interfaces/sales';
import { IZatcaEInvoices, InvoiceParams } from '../../interfaces/zatca';
import { convertDateForBE } from '../../utils/date-utils';

@Injectable({
  providedIn: 'root',
})
export class ZatcaService {
  baseUrl = environment.apiUrl + 'zatca';
  constructor(private http: HttpClient, public dialog: MatDialog) {}

  getXmlContent(documentUuid: string): Observable<string> {
    const url = `${this.baseUrl}/xml/${documentUuid}`;
    return this.http.get(url, { responseType: 'text' });
  }

  getZatcaEInvoices(): Observable<IZatcaEInvoices> {
    return this.http.get<IZatcaEInvoices>(`${this.baseUrl}/einvoices/pages`);
  }

  getZatcaEInvoiceError(documentUuid: string): Observable<ValidationResults> {
    return this.http.get<ValidationResults>(`${this.baseUrl}/${documentUuid}/error`);
  }

  getZatcaStatusClass(stage: string) {
    switch (stage) {
      case 'CLEARED':
        return 'bg-light-success';
      case 'EINVOICE_SAVED':
        return 'bg-light-accent';
      case 'REJECTED':
        return 'bg-light-error';
      default:
        return 'bg-light-primary';
    }
  }

  getZatcaErrorForInvoice(documentUUID: string, direction: Direction): void {
    this.getZatcaEInvoiceError(documentUUID).subscribe((result: ValidationResults) => {
      console.log('getZatcaErrorForInvoice', result);
      const data: ZatcaExceptionResponse = {
        validationResults: result,
        reportingStatus: null,
        clearanceStatus: null,
      };
      this.openZatcaStatusModal(data, direction, false);
    });
  }

  openZatcaStatusModal(
    zatcaResponse: ZatcaExceptionResponse,
    direction: Direction,
    action = true
  ): MatDialogRef<ZatcaResponseComponent> {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '600px';
    dialogConfig.maxWidth = '900px';
    dialogConfig.maxHeight = '700px';
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = direction;
    const data: ZatcaModalData = {
      zatcaReportingStatus: zatcaResponse,
      viewOnly: action,
    };
    dialogConfig.data = data;
    return this.dialog.open(ZatcaResponseComponent, dialogConfig);
  }

  openInvoiceXmlDialog(xmlData: string, documentUUID: string, direction: Direction): void {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.maxWidth = '900px';
    dialogConfig.maxHeight = '700px';
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = 'ltr';
    const data: IXmlViewer = {
      xmlData: xmlData,
      documentUUID: documentUUID,
    };
    dialogConfig.data = data;
    this.dialog.open(XmlViewerComponent, dialogConfig);
  }

  getXmlForInvoice(documentUUID: string, direction: Direction): void {
    this.getXmlContent(documentUUID).subscribe((result: string) => {
      console.log('getXmlForInvoice', result);
      this.openInvoiceXmlDialog(result, documentUUID, direction);
    });
  }

  getInvoices(params: InvoiceParams): Observable<IZatcaEInvoices> {
    // Convert Date objects to string in 'yyyy-MM-dd' format if necessary
    const formattedParams = {
      ...params,
      issueDateFrom: convertDateForBE(params.issueDateFrom),
      issueDateTo: convertDateForBE(params.issueDateTo),
    };

    // Create HttpParams object for query parameters
    let httpParams = new HttpParams()
      .set('invoiceType', formattedParams.invoiceType)
      .set('issueDateFrom', formattedParams.issueDateFrom)
      .set('issueDateTo', formattedParams.issueDateTo);

    if (formattedParams.stage) {
      httpParams = httpParams.set('stage', formattedParams.stage);
    }

    // Make GET request
    return this.http.get<IZatcaEInvoices>(`${this.baseUrl}/einvoices/pages`, {
      params: httpParams,
    });
  }
}
