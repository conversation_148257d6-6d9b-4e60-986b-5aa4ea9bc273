# Dynamic Report Component

A highly flexible and reusable component for generating dynamic reports based on JSON configuration.

## Features

✅ **Fully Dynamic**: Generates forms and tables based on JSON configuration  
✅ **Type Safety**: Strong TypeScript interfaces for configuration  
✅ **Multiple Field Types**: Supports dropdown, multi-select, input, date, and date range fields  
✅ **Advanced Filters**: Toggle-able advanced filter sections  
✅ **Responsive Design**: Works on all screen sizes  
✅ **Pagination & Sorting**: Built-in table features  
✅ **PDF Export**: Integrated report download functionality  
✅ **Clean Architecture**: Separation of concerns and reusable design  

## Usage

### Basic Implementation

```typescript
// In your component
export class MyReportsComponent {
  reportConfigs: ReportConfig[] = [
    {
      id: 1,
      type: 1,
      name: "Pricing Report",
      authority: "ReportsManagement.Inventory.PriceReports",
      languageCode: "AR",
      searchConfigs: [
        {
          field: "branch",
          type: "dropdown",
          mandatory: true,
          placeholder: " ",
          position: 1,
          isAdvanced: false,
          fieldLabel: "branch",
          backendParam: "branchId",
          idField: "branchId"
        }
        // ... more configs
      ],
      endPoint: "/pages/price-list",
      jasperEndPoint: "/dynamic/price-list"
    }
  ];

  dataProviders = {
    'branchId': () => this.branches,
    'yearId': () => this.years,
    'categoryIds': () => this.categories
  };
}
```

```html
<!-- In your template -->
<app-dynamic-report
  [reportConfigs]="reportConfigs"
  [dataProviders]="dataProviders">
</app-dynamic-report>
```

### Configuration Interface

```typescript
interface ReportConfig {
  id: number;
  type: number;
  name: string;
  nameArabic?: string;
  authority: string;
  languageCode: string;
  searchConfigs: SearchConfig[];
  endPoint: string;
  jasperEndPoint?: string;
}

interface SearchConfig {
  field: string;
  type: 'dropdown' | 'multiSelectDropdown' | 'input' | 'date' | 'dateRange';
  mandatory: boolean;
  placeholder: string;
  position: number;
  isAdvanced: boolean;
  fieldLabel: string;
  backendParam: string;
  idField: string;
}
```

### Data Providers

Data providers are functions that return arrays of data for each field:

```typescript
dataProviders = {
  // Simple static data
  'branchId': () => this.branches,
  
  // Dynamic data based on other selections
  'yearId': () => {
    const selectedBranchId = this.getSelectedBranchId();
    return this.getYearsForBranch(selectedBranchId);
  },
  
  // Filtered data
  'warehouseIds': () => {
    const selectedBranchId = this.getSelectedBranchId();
    return this.warehouses.filter(w => w.branchId === selectedBranchId);
  }
};
```

## Supported Field Types

### 1. Dropdown
Single selection dropdown with options from data provider.

### 2. Multi-Select Dropdown
Multiple selection dropdown with checkboxes.

### 3. Input
Text input field with optional autocomplete.

### 4. Date
Single date picker.

### 5. Date Range
Start and end date picker.

## Advanced Features

### Advanced Filters
Mark fields as advanced to show/hide them with a toggle:

```json
{
  "field": "categories",
  "type": "multiSelectDropdown",
  "isAdvanced": true,
  // ... other config
}
```

### Mandatory Fields
Set validation requirements:

```json
{
  "field": "branch",
  "mandatory": true,
  // ... other config
}
```

### Field Positioning
Control the order of fields:

```json
{
  "field": "branch",
  "position": 1,
  // ... other config
}
```

## Benefits Over Old Implementation

### ❌ Old Problems:
- 367 lines of complex, hard-to-maintain code
- Hard-coded field mappings
- Tight coupling between form generation and business logic
- Difficult to add new field types
- Poor separation of concerns

### ✅ New Solutions:
- **Clean Architecture**: Separated concerns with clear interfaces
- **Reusable**: Works with any JSON configuration
- **Type Safe**: Strong TypeScript interfaces prevent errors
- **Extensible**: Easy to add new field types
- **Maintainable**: Simple, focused components
- **Testable**: Clear separation makes unit testing easier

## Migration Guide

### From Old Component:
1. Replace `<inventory-report>` with `<app-dynamic-report>`
2. Convert your report configurations to the new interface
3. Set up data providers for your fields
4. Remove old hard-coded field mappings

### Example Migration:
```typescript
// OLD WAY
getListForField(field: string) {
  switch (field) {
    case 'branchId': return this.branchList;
    case 'yearId': return this.filteredYearIdList;
    // ... 20+ more cases
  }
}

// NEW WAY
dataProviders = {
  'branchId': () => this.branchList,
  'yearId': () => this.filteredYearIdList,
  // Clean, declarative mapping
};
```

## Performance Benefits

- **Lazy Loading**: Data providers only called when needed
- **Efficient Rendering**: Angular OnPush change detection
- **Memory Efficient**: No unnecessary data duplication
- **Fast Filtering**: Optimized data provider functions

## Extensibility

Adding new field types is simple:

1. Add the type to the `SearchConfig` interface
2. Add the HTML template in the component
3. Handle the field in form building logic

The component is designed to grow with your needs while maintaining clean, maintainable code.
