<ng-container *ngIf="!loading">
  <mat-card appearance="outlined">
    <form [formGroup]="companyForm" (ngSubmit)="onSubmit($event)" autocomplete="off">
      <mat-card-title>{{ formTitle | translate }}</mat-card-title>
      <div class="row no-gutters">
        <div class="col-md-6 col-lg-6 col-sm-6 p-2">
          <mat-label>{{ 'company.nameArabic' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input maxlength="40" matInput formControlName="nameArabic" />
            <mat-error
              *ngIf="
                companyForm.get('nameArabic').hasError('required') &&
                companyForm.get('nameArabic').touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>

        <div class="col-md-6 col-lg-6 col-sm-6 p-2">
          <mat-label>{{ 'company.nameEnglish' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input maxlength="40" matInput formControlName="nameEnglish" />
          </mat-form-field>
        </div>

        <div class="col-md-3 col-lg-3 col-sm-6 p-2">
          <mat-label>{{ 'company.vatNo' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input maxlength="15" matInput formControlName="regCertNo" />
            <mat-error
              *ngIf="
                companyForm.get('regCertNo').hasError('required') &&
                companyForm.get('regCertNo').touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>

        <div class="col-md-3 col-lg-3 col-sm-6 p-2">
          <mat-label>{{ 'company.phone' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input maxlength="15" matInput formControlName="phoneNo" />
            <mat-error
              *ngIf="
                companyForm.get('phoneNo').hasError('required') &&
                companyForm.get('phoneNo').touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>

        <div class="col-md-3 col-lg-3 col-sm-6 p-2">
          <mat-label>{{ 'company.email' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input maxlength="50" matInput formControlName="emailId" />
            <mat-error
              *ngIf="
                companyForm.get('emailId').hasError('required') &&
                companyForm.get('emailId').touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
            <mat-error
              *ngIf="
                companyForm.get('emailId').hasError('email') && companyForm.get('emailId').touched
              ">
              {{ 'common.invalidEmail' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
      </div>

      <div class="row no-gutters">
        <div class="p-2 col-md-6 col-sm-6">
          <mat-label>{{ 'tenants.identification' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input [appMaxlength]="30" matInput formControlName="identification" />
            <mat-error
              *ngIf="
                companyForm.get('identification').hasError('required') &&
                companyForm.get('identification').touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>

        <div class="p-2 col-md-6 col-sm-6">
          <mat-label>{{ 'tenants.identificationCode' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <mat-select formControlName="identificationCode">
              <mat-option
                *ngFor="let identificationCode of identificationCodes"
                [value]="identificationCode.value">
                {{ identificationCode.display | translate }} ({{ identificationCode.value }})
              </mat-option>
            </mat-select>
            <mat-error
              *ngIf="
                companyForm.get('identificationCode').hasError('required') &&
                companyForm.get('identificationCode').touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
      </div>

      <div class="row no-gutters">
        <app-address #addressForm formControlName="address"></app-address>
      </div>

      <div class="image-preview">
        <img
          class="cropped-image"
          [src]="croppedImage ? croppedImage : defaultImage"
          alt="Cropped Image" />
        <button
          (click)="fileInput.click(); $event.preventDefault()"
          mat-raised-button
          color="primary">
          {{ 'common.chooseFile' | translate }}
        </button>
        <input
          #fileInput
          (change)="fileChangeEvent($event)"
          style="display: none"
          type="file"
          accept="image/*" />
      </div>

      <div class="image-cropper-container">
        <image-cropper
          *ngIf="imageChangedEvent"
          [imageChangedEvent]="imageChangedEvent"
          [maintainAspectRatio]="true"
          [aspectRatio]="1"
          [canvasWidth]="200"
          [canvasHeight]="200"
          [minWidth]="200"
          [minHeight]="150"
          [maxWidth]="200"
          [maxHeight]="200"
          [centerTouchRadius]="0"
          [cropperMinHeight]="100"
          [cropperMinWidth]="100"
          (imageCropped)="imageCropped($event)"
          (imageLoaded)="imageLoaded()"
          (cropperReady)="cropperReady()"
          (loadImageFailed)="loadImageFailed()"
          format="png">
        </image-cropper>
      </div>
    </form>
  </mat-card>
  <div class="text-center">
    <button class="m-l-10" (click)="onSubmit($event)" mat-flat-button color="primary">
      {{ 'common.buttons.save' | translate }}
    </button>
    <button class="m-l-10" [routerLink]="['../']" mat-stroked-button color="warn">
      {{ 'common.buttons.cancel' | translate }}
    </button>
  </div>
</ng-container>
