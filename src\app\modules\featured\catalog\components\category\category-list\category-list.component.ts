import { Directionality } from '@angular/cdk/bidi';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/core/api/common.service';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { DeleteConfirmationComponent } from 'src/app/modules/shared/components/delete-confirmation/delete-confirmation.component';
import { CategoryService } from '../../../../../../core/api/category.service';
import { Category } from '../../../models/category';
import { CategoryParams } from '../../../models/categoryParams';

@Component({
  selector: 'app-category-list',
  templateUrl: './category-list.component.html',
  styleUrls: ['./category-list.component.scss'],
})
export class CategoryListComponent implements OnInit {
  @ViewChild('filter') filter: ElementRef;
  @ViewChild('searchInput') searchInput: ElementRef;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  categories: Category[];
  displayedColumns: string[];
  tableData = new MatTableDataSource<any>();
  categoryParams = new CategoryParams();
  isLoading = true;
  constructor(
    public categoryService: CategoryService,
    private dialog: MatDialog,
    private direction: Directionality,
    private toastr: ToastrService,
    private authService: AuthService,
    private commonService: CommonService
  ) {}

  ngOnInit(): void {
    this.getCategories();
    this.initColumns();
  }

  ngAfterViewInit(): void {
    if (this.tableData) {
      this.tableData.paginator = this.paginator;
      this.tableData.sort = this.sort;
    }
  }

  getCategories(): void {
    this.categoryService.getAllCategories(this.categoryParams).subscribe(result => {
      this.categories = result;
      this.isLoading = false;
      setTimeout(() => {
        this.tableData = new MatTableDataSource(this.categories);
        this.tableData.paginator = this.paginator;
        this.tableData.sort = this.sort;
      });
    });
  }

  initColumns(): void {
    this.displayedColumns = ['action', 'nameArabic', 'nameEnglish'];
  }

  applyFilter(filterValue: string): void {
    filterValue = filterValue.trim();
    filterValue = filterValue.toLowerCase();
    this.tableData.filter = filterValue;
  }

  clearSearchInput() {
    this.searchInput.nativeElement.value = '';
    this.getCategories();
  }

  deleteCategory(categoryId?: string) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '600px';
    dialogConfig.panelClass = 'green_theme';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(DeleteConfirmationComponent, dialogConfig);
    dialogRef.componentInstance.accepted.subscribe(result => {
      this.categoryService.deleteCategory(categoryId).subscribe(result => {
        this.commonService.playSuccessSound();
        this.getCategories();
        this.isLoading = false;
      });
    });
  }
}
