import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FlexLayoutModule } from '@angular/flex-layout';
import { NgxScrollTopModule } from 'ngx-scrolltop';
import { SharedModule } from 'src/app/modules/shared/shared.module';
import { MaterialModule } from '../../material/material.module';
import { AccountsRoutingModule } from './accounts-routing.module';
import { AccountsComponent } from './accounts.component';
import { AccountsDashboardComponent } from './components/dashboard/accounts-dashboard.component';
import { ChartOfAccountsComponent } from './components/chart-of-accounts/chart-of-accounts.component';
import { ChartOfAccountsFormComponent } from './components/chart-of-accounts/chart-of-accounts-form/chart-of-accounts-form.component';
import { AccountsSearchBoxComponent } from './components/accounts-search-box/accounts-search-box.component';
import { AccountsReportsComponent } from './components/account-reports/accounts-reports.component';
import { AccountListReportsComponent } from './components/account-list-reports/account-list-reports.component';
import { AccountStatementReportsComponent } from './components/account-statement-reports/account-statement-reports.component';
import { AccountSetupComponent } from './components/account-setup/account-setup.component';
import { CostCentresComponent } from './components/cost-centres/cost-centres.component';
import { CostCentreFormComponent } from './components/cost-centres/cost-centre-form/cost-centre-form.component';
import { JournalEntriesComponent } from './components/journal-entries/journal-entries.component';
import { JournalEntriesFormComponent } from './components/journal-entries/journal-entries-form/journal-entries-form.component';
import { JournalEntriesSearchBoxComponent } from './components/journal-entries-search-box/journal-entries-search-box.component';
import { DepreciationComponent } from './components/depreciation/depreciation.component';
import { DepreciationSearchBoxComponent } from './components/depreciation-search-box/depreciation-search-box.component';
import { DepreciationFormComponent } from './components/depreciation/depreciation-form/depreciation-form.component';
import { FixedAssetDashboardComponent } from './components/fixed-asset-dashboard/fixed-asset-dashboard.component';
import { MatDialogModule } from '@angular/material/dialog';
import { AccountsListWindowComponent } from './components/accounts-list-window/accounts-list-window.component';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { AccountsProsearchBoxComponent } from './components/accounts-prosearch-box/accounts-prosearch-box.component';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { AccountAutoSearchComponent } from './components/accountAutoSearch/account-auto-search.component';
import { ChartOfAccountsTreeComponent } from './components/chart-of-accounts-tree/chart-of-accounts-tree.component';
import { AccountsTreeComponent } from './components/accounts-tree/accounts-tree.component';

@NgModule({
  declarations: [
    AccountsComponent,
    AccountsDashboardComponent,
    ChartOfAccountsComponent,
    ChartOfAccountsFormComponent,
    AccountsSearchBoxComponent,
    AccountsReportsComponent,
    AccountListReportsComponent,
    AccountStatementReportsComponent,
    AccountSetupComponent,
    CostCentresComponent,
    CostCentreFormComponent,
    JournalEntriesComponent,
    JournalEntriesFormComponent,
    JournalEntriesSearchBoxComponent,
    DepreciationComponent,
    DepreciationSearchBoxComponent,
    DepreciationFormComponent,
    FixedAssetDashboardComponent,
    AccountsListWindowComponent,
    AccountsProsearchBoxComponent,
    AccountAutoSearchComponent,
    ChartOfAccountsTreeComponent,
    AccountsTreeComponent,
  ],
  imports: [
    CommonModule,
    AccountsRoutingModule,
    SharedModule,
    FlexLayoutModule,
    NgxScrollTopModule,
    MaterialModule,
    MatDialogModule,
    MatInputModule,
    MatFormFieldModule,
    MatAutocompleteModule,
    MatInputModule,
  ],
  exports: [AccountAutoSearchComponent, AccountsSearchBoxComponent, AccountsProsearchBoxComponent],
})
export class AccountsModule {}
