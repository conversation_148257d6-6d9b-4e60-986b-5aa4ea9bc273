<div>
  <div class="table-container">
    <!-- Report Type Selection -->
    <div class="report-type-section" *ngIf="reportConfigs && reportConfigs.length > 0">
      <form [formGroup]="reportTypeForm">
        <div class="row no-gutters">
          <div class="filter-field">
            <mat-form-field class="w-100" [subscriptSizing]="'fixed'" appearance="outline">
              <mat-label>{{ 'Select Report Type' | translate }}</mat-label>
              <mat-select formControlName="reportType" panelClass="custom-dropdown-panel">
                <mat-option [value]="null">{{ 'Choose Report Type' | translate }}</mat-option>
                <mat-option *ngFor="let report of reportConfigs" [value]="report.id">
                  {{ report.name }}
                  <span *ngIf="report.nameArabic && report.nameArabic !== report.name">
                    - {{ report.nameArabic }}</span
                  >
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
      </form>
    </div>

    <!-- Filters Form -->
    <form
      *ngIf="currentSearchConfigs && currentSearchConfigs.length > 0"
      [formGroup]="filtersForm"
      autocomplete="off">
      <div class="row no-gutters">
        <div
          class="filter-field"
          *ngFor="let config of currentSearchConfigs | orderBy : 'position'">
          <mat-form-field
            class="w-100"
            *ngIf="config.type === 'dropdown'"
            [subscriptSizing]="'fixed'"
            hideHints>
            <mat-label>
              {{ 'report.' + config.fieldLabel.trim() | translate }}
            </mat-label>
            <!-- Handle dropdown fields -->
            <mat-select [formControlName]="config.backendParam" panelClass="custom-dropdown-panel">
              <mat-option [value]="null">{{ 'None' | translate }}</mat-option>
              <mat-option
                *ngFor="let item of getListForField(config.backendParam)"
                [value]="item.display ? item.value : item[config.idField]">
                {{ item.name ? item.name : item.year ? item.year : (item | localized) }}
                {{ item.display | translate }}
              </mat-option>
            </mat-select>
          </mat-form-field>
          <mat-form-field
            class="w-100"
            *ngIf="config.type === 'multiSelectDropdown'"
            [subscriptSizing]="'fixed'">
            <mat-label>
              {{ 'report.' + config.fieldLabel.trim() | translate }}
            </mat-label>
            <!-- Handle dropdown fields -->
            <mat-select
              [formControlName]="config.backendParam"
              multiple
              panelClass="custom-dropdown-panel">
              <app-select-check-all
                [model]="filtersForm.get(config.backendParam)"
                [values]="getSelectForField(config.backendParam)"
                text="Select All"></app-select-check-all>
              <!-- Dynamic options rendering based on config.fieldLabel -->
              <mat-option
                *ngFor="let item of getListForField(config.backendParam)"
                [value]="item[config.idField]">
                {{ item.name ? item.name : item.year ? item.year : (item | localized) }}
              </mat-option>
            </mat-select>
          </mat-form-field>
          <!-- Handle input fields with autocomplete -->
          <mat-form-field class="w-100" *ngIf="config.type === 'input'" [subscriptSizing]="'fixed'">
            <mat-label>
              {{ 'report.' + config.fieldLabel.trim() | translate }}
            </mat-label>
            <input
              [matAutocomplete]="auto"
              [formControlName]="config.backendParam"
              [placeholder]="config.placeholder || 'Search by Item Name/Code'"
              (input)="onSearchInput($event.target.value, config.backendParam)"
              matInput />
            <mat-autocomplete #auto="matAutocomplete" autoActiveFirstOption>
              <mat-option
                *ngFor="let item of getListForField(config.backendParam)"
                [value]="item.itemCode"
                (onSelectionChange)="onItemCodeSelected(item)">
                {{ item | localized }} - {{ item.itemCode }}
              </mat-option>
            </mat-autocomplete>
          </mat-form-field>
          <mat-form-field
            class="w-100"
            *ngIf="config.type === 'dateRange'"
            [subscriptSizing]="'fixed'">
            <mat-label>{{ 'report.' + config.fieldLabel.trim() | translate }}</mat-label>
            <mat-date-range-input [rangePicker]="picker" [max]="todayDate" disabled>
              <input
                matStartDate
                disabled
                formControlName="{{ config.backendParam + 'From' }}"
                placeholder="{{ 'voucher.startDate' | translate }}" />
              <input
                matEndDate
                disabled
                formControlName="{{ config.backendParam + 'To' }}"
                placeholder="{{ 'voucher.endDate' | translate }}" />
            </mat-date-range-input>
            <mat-datepicker-toggle [for]="picker" matIconSuffix></mat-datepicker-toggle>
            <mat-date-range-picker #picker disabled="false"></mat-date-range-picker>
          </mat-form-field>

          <mat-form-field class="w-100" *ngIf="config.type === 'date'" [subscriptSizing]="'fixed'">
            <mat-label>
              {{ 'report.' + config.fieldLabel.trim() | translate }}
            </mat-label>
            <input [matDatepicker]="picker" [formControlName]="config.backendParam" matInput />
            <mat-datepicker-toggle [for]="picker" matSuffix> </mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
          </mat-form-field>
          <div class="w-100 margin-bottom" *ngIf="config.type === 'accountSearch'">
            <app-accounts-prosearch-box
              [formControlName]="config.backendParam"
              reportLabel="{{ 'report.' + config.fieldLabel.trim() | translate }}">
            </app-accounts-prosearch-box>
          </div>
        </div>
        <div *ngIf="selectedReportType">
          <button class="submit" (click)="getReportData($event)" color="primary">
            {{ 'report.submit' | translate }}
          </button>
          <button
            class="clear-btn"
            (click)="clearFilters($event)"
            type="button"
            style="margin-left: 10px">
            {{ 'Clear Filters' | translate }}
          </button>
        </div>
      </div>
    </form>

    <!-- No Report Selected Message -->
    <div
      class="no-report-selected"
      *ngIf="reportConfigs && reportConfigs.length > 0 && !selectedReportType">
      <mat-card class="info-card">
        <mat-card-content>
          <div class="info-content">
            <mat-icon>info</mat-icon>
            <p>{{ 'Please select a report type to view filters' | translate }}</p>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>
