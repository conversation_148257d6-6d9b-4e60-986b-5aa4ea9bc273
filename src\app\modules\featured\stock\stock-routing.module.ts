import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionGuard } from '../../core/core/guards/permission.guard';
import { AdjustmentListComponent } from './components/adjustment-list/adjustment-list.component';
import { OpenQuantityAdjustmentsComponent } from './components/adjustments-open-qty/open-quantity-adjustments.component';
import { BranchPartnerListingComponent } from './components/branch-partner-listing/branch-partner-listing.component';
import { BranchPartnerComponent } from './components/branch-partner/branch-partner.component';
import { StockAdjustmentComponent } from './components/stock-adjustment-create/stock-adjustment.component';
import { StockTransferCreateObComponent } from './components/stock-transfer-create-ob/stock-transfer-create-ob.component';
import { StockTransferComponent } from './components/stock-transfer/stock-transfer.component';
import { StocksDashboardComponent } from './components/stocks-dashboard/stocks-dashboard.component';
import { UnitPriceUpdateComponent } from './components/unit-price-update/unit-price-update.component';

const config = {
  mainDashBoard: '/dashboard',
  stockDashBoard: '/stocks/dashboard',
  stockDashBoardTitle: 'stockTransfer.stock',
  stockAdjustmentDashBoard: '/stocks/stockadjustment',
  stockAdjustmentDashBoardTitle: '',
};

const routes: Routes = [
  {
    path: '',
    redirectTo: 'dashboard',
    pathMatch: 'full',
  },
  {
    path: 'dashboard',
    component: StocksDashboardComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Stock Management',
      urls: [{ title: 'Main Dashboard', url: config.mainDashBoard }],
      allowedPermissions: ['InventoryManagement', 'AllPermissions'],
    },
  },
  {
    path: 'adjustments',
    component: OpenQuantityAdjustmentsComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Adjust Open Quantity',
      urls: [{ title: config.stockDashBoardTitle, url: config.stockDashBoard }],
      allowedPermissions: ['OpenQtyAdjustments.CreateOQA', 'AllPermissions'],
    },
  },
  {
    path: 'stockadjustment/create',
    component: StockAdjustmentComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Create Adjustment',
      urls: [{ title: 'Adjustments List', url: config.stockAdjustmentDashBoard }],
      allowedPermissions: ['Adjustments.CreateAdj', 'AllPermissions'],
    },
  },
  {
    path: 'stockadjustment',
    component: AdjustmentListComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Adjustments List',
      urls: [{ title: config.stockDashBoardTitle, url: config.stockDashBoard }],
      allowedPermissions: ['Adjustments', 'AllPermissions'],
    },
  },
  {
    path: 'stockadjustment/edit/:id',
    component: StockAdjustmentComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Edit Stock Adjustment',
      urls: [{ title: 'Adjustments List', url: config.stockAdjustmentDashBoard }],
      allowedPermissions: ['Adjustments.UpdateAdj', 'AllPermissions'],
    },
  },
  {
    path: 'stockadjustment/view/:id',
    component: StockAdjustmentComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'View Stock Adjustment',
      urls: [{ title: 'Adjustments List', url: config.stockAdjustmentDashBoard }],
      allowedPermissions: ['Adjustments.ViewAdj', 'AllPermissions'],
    },
  },
  {
    path: 'priceupdate',
    component: UnitPriceUpdateComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Price Update Listing',
      urls: [{ title: config.stockDashBoardTitle, url: config.stockDashBoard }],
      allowedPermissions: ['Adjustments.PriceUpdateAdj', 'AllPermissions'],
    },
  },
  {
    path: 'branchpartner',
    component: BranchPartnerListingComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'stockTransfer.branchParentListings',
      urls: [{ title: config.stockDashBoardTitle, url: config.stockDashBoard }],
      allowedPermissions: ['Transfer.ViewTRPA', 'AllPermissions'],
    },
  },
  {
    path: 'branchpartner/create',
    component: BranchPartnerComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'stockTransfer.configurePartner',
      urls: [{ title: config.stockDashBoardTitle, url: config.stockDashBoard }],
      allowedPermissions: ['Transfer.CreateTRPA', 'AllPermissions'],
      mode: 'create',
    },
  },
  {
    path: 'branchpartner/view/:id',
    component: BranchPartnerComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'stockTransfer.branchParentListings',
      urls: [{ title: config.stockDashBoardTitle, url: config.stockDashBoard }],
      allowedPermissions: ['Transfer.ViewTRPA', 'AllPermissions'],
      mode: 'view',
    },
  },
  {
    path: 'transfer',
    component: StockTransferComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'stockTransfer.outgoingStock',
      urls: [{ title: config.stockDashBoardTitle, url: config.stockDashBoard }],
      allowedPermissions: ['Transfer.ViewTIO', 'AllPermissions'],
    },
  },
  {
    path: 'transfer/obtransfer',
    component: StockTransferCreateObComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'stockTransfer.stockOutward',
      urls: [{ title: config.stockDashBoardTitle, url: config.stockDashBoard }],
      allowedPermissions: ['Transfer.CreateTIO', 'AllPermissions'],
      mode: 'create',
      transferMode: 'Outward',
    },
  },
  {
    path: 'transfer/obtransfer/view/:id',
    component: StockTransferCreateObComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'stockTransfer.stockOutward',
      urls: [{ title: config.stockDashBoardTitle, url: config.stockDashBoard }],
      allowedPermissions: ['Transfer.ViewTIO', 'AllPermissions'],
      mode: 'view',
      transferMode: 'Outward',
    },
  },
  {
    path: 'transfer/ibtransfer',
    component: StockTransferCreateObComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'stockTransfer.stockInward',
      urls: [{ title: config.stockDashBoardTitle, url: config.stockDashBoard }],
      allowedPermissions: ['Transfer.CreateTIO', 'AllPermissions'],
      mode: 'create',
      transferMode: 'Inward',
    },
  },
  {
    path: 'transfer/ibtransfer/view/:id',
    component: StockTransferCreateObComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'stockTransfer.stockInward',
      urls: [{ title: config.stockDashBoardTitle, url: config.stockDashBoard }],
      allowedPermissions: ['Transfer.ViewTIO', 'AllPermissions'],
      mode: 'view',
      transferMode: 'Inward',
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class StockRoutingModule {}
