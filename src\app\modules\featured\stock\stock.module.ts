import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { MaterialModule } from '../../material/material.module';
import { SharedModule } from '../../shared/shared.module';
import { BulkEditProductsComponent } from '../catalog/components/product/bulk-edit-products/bulk-edit-products.component';
import { AdjustmentListComponent } from './components/adjustment-list/adjustment-list.component';
import { OpenQuantityAdjustmentsComponent } from './components/adjustments-open-qty/open-quantity-adjustments.component';
import { BranchPartnerListingComponent } from './components/branch-partner-listing/branch-partner-listing.component';
import { BranchPartnerComponent } from './components/branch-partner/branch-partner.component';
import { StockAdjustmentComponent } from './components/stock-adjustment-create/stock-adjustment.component';
import { StockTransferCreateObComponent } from './components/stock-transfer-create-ob/stock-transfer-create-ob.component';
import { StockTransferIbComponent } from './components/stock-transfer-ib/stock-transfer-ib.component';
import { StockTransferObComponent } from './components/stock-transfer-ob/stock-transfer-ob.component';
import { StockTransferComponent } from './components/stock-transfer/stock-transfer.component';
import { StocksDashboardComponent } from './components/stocks-dashboard/stocks-dashboard.component';
import { UnitPriceUpdateComponent } from './components/unit-price-update/unit-price-update.component';
import { StockRoutingModule } from './stock-routing.module';

@NgModule({
  declarations: [
    StocksDashboardComponent,
    OpenQuantityAdjustmentsComponent,
    AdjustmentListComponent,
    StockAdjustmentComponent,
    BulkEditProductsComponent,
    UnitPriceUpdateComponent,
    BranchPartnerComponent,
    BranchPartnerListingComponent,
    StockTransferComponent,
    StockTransferIbComponent,
    StockTransferObComponent,
    StockTransferCreateObComponent,
  ],
  imports: [CommonModule, StockRoutingModule, SharedModule, FlexLayoutModule, MaterialModule],
  providers: [],
})
export class StockModule {}
