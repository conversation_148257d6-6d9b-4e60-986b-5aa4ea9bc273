<mat-card appearance="outlined">
  <mat-card-content>
    <!-- <mat-card-title>{{ 'transfer.listings' | translate }}</mat-card-title> -->
    <mat-tab-group #tabs [dynamicHeight]="true" mat-stretch-tabs="false" animationDuration="0ms">
      <mat-tab>
        <ng-template mat-tab-label>{{ 'stockTransfer.outgoingStocktab' | translate }}</ng-template>
        <app-stock-transfer-ob></app-stock-transfer-ob>
      </mat-tab>

      <mat-tab>
        <ng-template mat-tab-label>{{ 'stockTransfer.incomingStocktab' | translate }}</ng-template>
        <app-stock-transfer-ib></app-stock-transfer-ib> </mat-tab></mat-tab-group></mat-card-content
></mat-card>
