import { Directionality } from '@angular/cdk/bidi';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { CustomerService } from 'src/app/core/api/trading/customer.service';
import { ICustomer } from '../../../../../../core/interfaces/customer';
import { CustomerParams } from '../../../../../../core/models/params/customerParams';
import { DeleteConfirmationComponent } from 'src/app/modules/shared/components/delete-confirmation/delete-confirmation.component';
import { CommonService } from 'src/app/core/api/common.service';

@Component({
  selector: 'app-customer-list',
  templateUrl: './customer-list.component.html',
  styleUrls: ['./customer-list.component.scss'],
})
export class CustomerListComponent implements OnInit {
  @ViewChild('filter') filter: ElementRef;
  @ViewChild('searchInput') searchInput: ElementRef;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  units: ICustomer[];
  displayedColumns: string[];
  dataSource: MatTableDataSource<ICustomer>;
  isLoading = true;
  constructor(
    public customerService: CustomerService,
    private authService: AuthService,
    public dialog: MatDialog,
    private toastr: ToastrService,
    private direction: Directionality,
    private commonService: CommonService
  ) {}

  ngOnInit(): void {
    this.getUnits();
    this.initColumns();
  }

  ngAfterViewInit(): void {
    if (this.dataSource) {
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
    }
  }

  getUnits(): void {
    const customerParams = new CustomerParams();
    this.customerService.getCustomers(customerParams).subscribe(result => {
      console.log(result, result.customers);
      this.units = result.customers;
      this.isLoading = false;
      this.dataSource = new MatTableDataSource<ICustomer>(this.units);
      this.dataSource.paginator = this.paginator;
    });
  }

  initColumns(): void {
    this.displayedColumns = [
      'action',
      'accountNumber',
      'nameArabic',
      'nameEnglish',
      'vatNumber',
      'phoneNumber',
    ];
  }

  applyFilter(filterValue: string): void {
    filterValue = filterValue.trim();
    filterValue = filterValue.toLowerCase();
    this.dataSource.filter = filterValue;
  }

  clearSearchInput() {
    this.searchInput.nativeElement.value = '';
    this.getUnits();
  }

  deleteUnit(customerId?: string) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '600px';
    dialogConfig.panelClass = 'green_theme';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(DeleteConfirmationComponent, dialogConfig);
    dialogRef.componentInstance.accepted.subscribe(result => {
      this.customerService.deleteCustomer(customerId).subscribe(result => {
        //this.toastr.success('Customer deleted Successfully');
        this.commonService.playSuccessSound();
        this.getUnits();
        this.isLoading = false;
      });
    });
  }
}
