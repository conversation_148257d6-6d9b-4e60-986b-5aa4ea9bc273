import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ICustomer } from 'src/app/core/interfaces/customer';
import { environment } from 'src/environments/environment';

@Injectable()
export class CustomerApiService {
  baseUrl = environment.apiUrl + 'sales/customers';

  constructor(private http: HttpClient) {}

  getAlls(params: HttpParams) {
    return this.http.get(this.baseUrl + '/pages', { params: params });
  }

  getById(id: string) {
    return this.http.get<ICustomer>(this.baseUrl + `/${id}`);
  }

  create(customer: ICustomer) {
    return this.http.post(this.baseUrl, customer);
  }

  update(customer: ICustomer, id: string) {
    return this.http.put(this.baseUrl + '/' + id, customer);
  }

  delete(id: string) {
    return this.http.delete(this.baseUrl + `/${id}`);
  }
}
