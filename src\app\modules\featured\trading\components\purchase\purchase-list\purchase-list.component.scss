table tr.selected,
mat-table mat-row.selected {
  background-color: seagreen;
}
// tr:focus {
//   outline: none !important;
//   transform: scale(1);
//   -webkit-transform: scale(1);
//   -moz-transform: scale(1);
//   box-shadow: 0px 0px 5px #f7d304e7;
//   -webkit-box-shadow: 0px 0px 5px #f7d304e7;
//   -moz-box-shadow: 0px 0px 5px #f7d304e7;
//   background: #0d64b6;
//   /* color: white; */
//   -webkit-text-fill-color: aliceblue;
// }
