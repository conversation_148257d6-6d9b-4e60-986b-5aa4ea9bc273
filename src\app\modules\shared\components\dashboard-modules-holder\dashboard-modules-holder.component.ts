import { Component, Input, OnInit } from '@angular/core';
import { DashboardModulesHolder, moduleType } from './modulesHolder';

@Component({
  selector: 'app-dashboard-modules-holder',
  templateUrl: './dashboard-modules-holder.component.html',
  styleUrls: ['./dashboard-modules-holder.component.scss'],
})
export class DashboardModulesHolderComponent implements OnInit {
  @Input() optionType: moduleType;
  @Input() modules: DashboardModulesHolder;
  constructor() {}

  ngOnInit(): void {}
}
