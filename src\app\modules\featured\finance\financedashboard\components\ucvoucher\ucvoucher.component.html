<mat-card appearance="outlined">
  <mat-card-content>
    <mat-card-title>{{ 'ucvoucher.listings' | translate }}</mat-card-title>
    <mat-tab-group #tabs [dynamicHeight]="true" mat-stretch-tabs="false" animationDuration="0ms">
      <mat-tab>
        <ng-template mat-tab-label>{{ 'ucvoucher.receipt' | translate }}</ng-template>
        <app-ucvoucher-receipt></app-ucvoucher-receipt>
      </mat-tab>

      <mat-tab>
        <ng-template mat-tab-label>{{ 'ucvoucher.payment' | translate }}</ng-template>
        <app-ucvoucher-payment></app-ucvoucher-payment> </mat-tab
    ></mat-tab-group>
  </mat-card-content>
</mat-card>
