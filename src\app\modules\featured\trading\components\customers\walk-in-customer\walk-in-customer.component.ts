import { Component, Inject, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { CustomValidators } from 'ngx-custom-validators';
import { CommonService } from 'src/app/core/api/common.service';
import { ICustomer } from 'src/app/core/interfaces/customer';
import { ISupplier } from 'src/app/core/interfaces/supplier';
import { IWalkin } from 'src/app/modules/featured/catalog/models/walkin-customer';

@Component({
  selector: 'app-walk-in-customer',
  templateUrl: './walk-in-customer.component.html',
  styleUrls: ['./walk-in-customer.component.scss'],
})
export class WalkInCustomerComponent implements OnInit {
  walkInCustomerForm: UntypedFormGroup;
  constructor(
    public dialogRef: MatDialogRef<WalkInCustomerComponent>,
    @Inject(MAT_DIALOG_DATA) public data: IWalkin,
    private formBuilder: UntypedFormBuilder,
    private commonService: CommonService
  ) {}

  ngOnInit(): void {
    console.log(this.data);
    this.initializeForm(this.data.data);
  }

  initializeForm(data?: ICustomer | ISupplier) {
    this.walkInCustomerForm = this.formBuilder.group({
      nameEnglish: [data?.nameEnglish ?? '', Validators.compose([Validators.required])],
      nameArabic: [data?.nameEnglish ?? ''],
      phoneNumber: [data?.phoneNumber ?? 0, Validators.compose([Validators.required])],
      emailId: [data?.emailId ?? null, Validators.compose([CustomValidators.email])],
    });
    if (this.data?.isViewMode) {
      this.walkInCustomerForm.clearValidators();
      this.walkInCustomerForm.disable();
    }
  }

  onSubmit(event: Event): void {
    event.preventDefault();
    this.walkInCustomerForm.markAllAsTouched();
    if (!this.walkInCustomerForm.valid) {
      this.commonService.playErrorSound();
    } else {
      console.log('onSubmit - walkin customer:', this.walkInCustomerForm.value);
      this.dialogRef.close('update');
    }
  }

  onNoClick(): void {
    this.dialogRef.close('cancel');
  }

  public getCustomerDataAsObject(): FormData {
    return {
      ...this.walkInCustomerForm.value,
      nameArabic: this.walkInCustomerForm.get('nameEnglish').value,
    };
  }
}
