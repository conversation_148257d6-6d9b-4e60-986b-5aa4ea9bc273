<!-- src/app/modules/featured/trading/components/pos-session/pos-session.component.html -->
<div class="f-s-20 main-container">
  <!-- Make the div focusable -->

  <mat-form-field class="w-100 m-b-20">
    <input
      class="f-s-20"
      #barcodeInput
      (keyup.enter)="addProduct(barcodeInput.value); barcodeInput.value = ''"
      matInput
      type="text"
      placeholder="Enter barcode" />
  </mat-form-field>

  <div class="content-container">
    <div class="table-container table-responsive" #tableContainer>
      <table class="w-100" [dataSource]="dataSource" mat-table>
        <ng-container matColumnDef="productInfo">
          <th *matHeaderCellDef mat-header-cell>Product Info</th>
          <td class="f-s-20 wrap-text" *matCellDef="let product" mat-cell>
            <div>{{ product.nameArabic }}</div>
            <div class="f-s-14 text-primary">
              VAT(%): {{ product.vat }} - Unit: {{ product.unitName }}
            </div>
          </td>
        </ng-container>
        <ng-container matColumnDef="quantity">
          <th *matHeaderCellDef mat-header-cell>Quantity/الخصم</th>
          <td class="f-s-20" *matCellDef="let product" mat-cell>
            <!-- {{ product.quantity }} -->
            <div class="quantity-container">
              <button
                [disabled]="product.quantity <= 0"
                (click)="decreaseQuantity(product)"
                mat-icon-button>
                <mat-icon color="warn">remove</mat-icon>
              </button>
              <mat-form-field class="f-s-20">
                <input [(ngModel)]="product.quantity" matInput type="number" min="0" />
              </mat-form-field>

              <button (click)="increaseQuantity(product)" mat-icon-button>
                <mat-icon color="primary">add</mat-icon>
              </button>
            </div>
          </td>
        </ng-container>
        <ng-container matColumnDef="vat">
          <th *matHeaderCellDef mat-header-cell>Vat Amount</th>
          <td class="f-s-20" *matCellDef="let product" mat-cell>
            {{ vatAmount(product) }}
          </td>
        </ng-container>

        <ng-container matColumnDef="total">
          <th *matHeaderCellDef mat-header-cell>Total</th>
          <td class="f-s-20" *matCellDef="let product" mat-cell>
            {{ subTotal(product) }}
          </td>
        </ng-container>

        <ng-container matColumnDef="price">
          <th *matHeaderCellDef mat-header-cell>Price Type</th>
          <td class="f-s-20" *matCellDef="let product" mat-cell>
            <div class="quantity-container">
              <button
                [disabled]="product.retailPrice <= 0"
                (click)="decreasePrice(product)"
                mat-icon-button>
                <mat-icon color="warn">remove</mat-icon>
              </button>
              <mat-form-field class="f-s-20">
                <input [(ngModel)]="product.retailPrice" matInput type="number" min="0" />
              </mat-form-field>

              <button (click)="increasePrice(product)" mat-icon-button>
                <mat-icon color="primary">add</mat-icon>
              </button>
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="actions">
          <th *matHeaderCellDef mat-header-cell>Actions</th>
          <td class="f-s-20" *matCellDef="let product" mat-cell>
            <a class="cursor-pointer">
              <i-tabler class="icon-16" (click)="deleteProduct(product)" name="trash"></i-tabler>
            </a>
          </td>
        </ng-container>
        <tr *matHeaderRowDef="displayedColumns; sticky: true" mat-header-row></tr>
        <tr class="f-s-20" #row *matRowDef="let row; columns: displayedColumns" mat-row></tr>
        <!-- Set tabindex on the row -->
      </table>
    </div>
  </div>

  <div class="summary">
    <table class="summary-table">
      <tr class="f-w-600">
        <td class="text-center">Items</td>
        <td class="text-center">{{ dataSource?.data?.length ?? 0 }}</td>
        <td class="text-center">Total Tax</td>
        <td class="text-center">{{ grandVat }}</td>
        <td class="text-center">Discount</td>
        <td class="text-center">{{ discounts }}</td>
      </tr>
      <tr class="f-w-600 f-s-24">
        <td class="text-center text-primary" colspan="2">Total Payable</td>
        <td class="text-center text-primary" colspan="5">{{ grandTotals }}</td>
        <!-- Adjust calculation as needed -->
      </tr>
    </table>
  </div>
  <!-- Action Buttons -->
  <div class="action-buttons dense-0">
    <button (click)="openPaymentDialog()" mat-raised-button color="primary">Payment</button>
    <button mat-raised-button color="warn">Suspend</button>
    <button mat-raised-button mat-raised-button color="accent">Transactions</button>
  </div>
</div>
