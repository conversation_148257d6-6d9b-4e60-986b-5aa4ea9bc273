<ng-container *ngIf="!isLoading">
  <form [formGroup]="accountForm" autocomplete="off">
    <div class="row no-gutters">
      <div class="col-lg-12" id="tabsTarget">
        <mat-card appearance="outlined">
          <mat-tab-group #tabs mat-stretch-tabs="false" animationDuration="0ms">
            <!-- Sales Account Tab -->
            <mat-tab>
              <ng-template mat-tab-label>
                {{ 'accountSetupSales.salesAccount' | translate }}
                <mat-icon class="tab-icon taberror" *ngIf="!accountForm.valid && submitted"
                  >error</mat-icon
                >
              </ng-template>
              <div class="row no-gutters m-t-10">
                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4 p-2">
                  <app-account-auto-search
                    [control]="accountForm.get('cashSalesAccount')"
                    [label]="'accountSetupSales.salesAccount' | translate"
                    placeholder="{{ getAccountIdPlaceholder(configuredAccount?.cashSalesAccount) }}"
                    searchStringLength="3"
                    accountType="DETAILED">
                  </app-account-auto-search>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4 p-2">
                  <app-account-auto-search
                    [control]="accountForm.get('creditSalesAccount')"
                    [label]="'accountSetupSales.creditSalesAccount' | translate"
                    placeholder="{{
                      getAccountIdPlaceholder(configuredAccount?.creditSalesAccount)
                    }}"
                    searchStringLength="3"
                    accountType="DETAILED">
                  </app-account-auto-search>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4 p-2">
                  <app-account-auto-search
                    [control]="accountForm.get('cardSalesAccount')"
                    [label]="'accountSetupSales.cardSalesAccount' | translate"
                    placeholder="{{ getAccountIdPlaceholder(configuredAccount?.cardSalesAccount) }}"
                    searchStringLength="3"
                    accountType="DETAILED">
                  </app-account-auto-search>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4 p-2">
                  <app-account-auto-search
                    [control]="accountForm.get('wireTransferSalesAccount')"
                    [label]="'accountSetupSales.wireTransSalesAccount' | translate"
                    placeholder="{{
                      getAccountIdPlaceholder(configuredAccount?.wireTransferSalesAccount)
                    }}"
                    searchStringLength="3"
                    accountType="DETAILED">
                  </app-account-auto-search>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4 p-2">
                  <app-account-auto-search
                    [control]="accountForm.get('vatSalesAccount')"
                    [label]="'accountSetupSales.vatSalesAccount' | translate"
                    placeholder="{{ getAccountIdPlaceholder(configuredAccount?.vatSalesAccount) }}"
                    searchStringLength="3"
                    accountType="DETAILED">
                  </app-account-auto-search>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4 p-2">
                  <app-account-auto-search
                    [control]="accountForm.get('discountSalesAccount')"
                    [label]="'accountSetupSales.discountSalesAccount' | translate"
                    placeholder="{{
                      getAccountIdPlaceholder(configuredAccount?.discountSalesAccount)
                    }}"
                    searchStringLength="3"
                    accountType="DETAILED">
                  </app-account-auto-search>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4 p-2">
                  <app-account-auto-search
                    [control]="accountForm.get('returnSalesAccount')"
                    [label]="'accountSetupSales.returnSalesAccount' | translate"
                    placeholder="{{
                      getAccountIdPlaceholder(configuredAccount?.returnSalesAccount)
                    }}"
                    searchStringLength="3"
                    accountType="DETAILED">
                  </app-account-auto-search>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4 p-2">
                  <app-account-auto-search
                    [control]="accountForm.get('roundingDiscountAccount')"
                    [label]="'accountSetupSales.roundingFractionSalesAccount' | translate"
                    placeholder="{{
                      getAccountIdPlaceholder(configuredAccount?.roundingDiscountAccount)
                    }}"
                    searchStringLength="3"
                    accountType="DETAILED">
                  </app-account-auto-search>
                </div>
                <!-- Add more form controls here -->
              </div>
            </mat-tab>

            <!-- Purchase Account Tab -->
            <mat-tab>
              <ng-template mat-tab-label>
                {{ 'accountSetupPurchase.purchaseAccount' | translate }}
                <mat-icon class="tab-icon taberror" *ngIf="!accountForm.valid && submitted"
                  >error</mat-icon
                >
              </ng-template>
              <div class="row no-gutters m-t-10">
                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4 p-2">
                  <app-account-auto-search
                    [control]="accountForm.get('cashPurchaseAccount')"
                    [label]="'accountSetupPurchase.cashPurchaseAccount' | translate"
                    placeholder="{{
                      getAccountIdPlaceholder(configuredAccount?.cashPurchaseAccount)
                    }}"
                    searchStringLength="3"
                    accountType="DETAILED">
                  </app-account-auto-search>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4 p-2">
                  <app-account-auto-search
                    [control]="accountForm.get('creditPurchaseAccount')"
                    [label]="'accountSetupPurchase.creditPurchaseAccount' | translate"
                    placeholder="{{
                      getAccountIdPlaceholder(configuredAccount?.creditPurchaseAccount)
                    }}"
                    searchStringLength="3"
                    accountType="DETAILED">
                  </app-account-auto-search>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4 p-2">
                  <app-account-auto-search
                    [control]="accountForm.get('cardPurchaseAccount')"
                    [label]="'accountSetupPurchase.cardPurchaseAccount' | translate"
                    placeholder="{{
                      getAccountIdPlaceholder(configuredAccount?.cardPurchaseAccount)
                    }}"
                    searchStringLength="3"
                    accountType="DETAILED">
                  </app-account-auto-search>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4 p-2">
                  <app-account-auto-search
                    [control]="accountForm.get('wireTransferPurchaseAccount')"
                    [label]="'accountSetupPurchase.wireTransPurchaseAccount' | translate"
                    placeholder="{{
                      getAccountIdPlaceholder(configuredAccount?.wireTransferPurchaseAccount)
                    }}"
                    searchStringLength="3"
                    accountType="DETAILED">
                  </app-account-auto-search>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4 p-2">
                  <app-account-auto-search
                    [control]="accountForm.get('vatPurchaseAccount')"
                    [label]="'accountSetupPurchase.vatPurchaseAccount' | translate"
                    placeholder="{{
                      getAccountIdPlaceholder(configuredAccount?.vatPurchaseAccount)
                    }}"
                    searchStringLength="3"
                    accountType="DETAILED">
                  </app-account-auto-search>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4 p-2">
                  <app-account-auto-search
                    [control]="accountForm.get('discountPurchaseAccount')"
                    [label]="'accountSetupPurchase.discountPurchaseAccount' | translate"
                    placeholder="{{
                      getAccountIdPlaceholder(configuredAccount?.discountPurchaseAccount)
                    }}"
                    searchStringLength="3"
                    accountType="DETAILED">
                  </app-account-auto-search>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4 p-2">
                  <app-account-auto-search
                    [control]="accountForm.get('returnPurchaseAccount')"
                    [label]="'accountSetupPurchase.returnPurchaseAccount' | translate"
                    placeholder="{{
                      getAccountIdPlaceholder(configuredAccount?.returnPurchaseAccount)
                    }}"
                    searchStringLength="3"
                    accountType="DETAILED">
                  </app-account-auto-search>
                </div>
                <!-- Add more form controls here -->
              </div>
            </mat-tab>

            <!-- Transfer Account Tab -->
            <mat-tab>
              <ng-template mat-tab-label>
                {{ 'accountSetupTransfer.transferAccount' | translate }}
                <mat-icon class="tab-icon taberror" *ngIf="!accountForm.valid && submitted"
                  >error</mat-icon
                >
              </ng-template>
              <div class="row no-gutters m-t-10">
                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4 p-2">
                  <app-account-auto-search
                    [control]="accountForm.get('transferInAccount')"
                    [label]="'accountSetupTransfer.transferInAccount' | translate"
                    placeholder="{{
                      getAccountIdPlaceholder(configuredAccount?.transferInAccount)
                    }}"
                    searchStringLength="3"
                    accountType="DETAILED">
                  </app-account-auto-search>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4 p-2">
                  <app-account-auto-search
                    [control]="accountForm.get('transferOutAccount')"
                    [label]="'accountSetupTransfer.transferOutAccount' | translate"
                    placeholder="{{
                      getAccountIdPlaceholder(configuredAccount?.transferOutAccount)
                    }}"
                    searchStringLength="3"
                    accountType="DETAILED">
                  </app-account-auto-search>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4 p-2">
                  <app-account-auto-search
                    [control]="accountForm.get('transferInReturnAccount')"
                    [label]="'accountSetupTransfer.transferInReturnAccount' | translate"
                    placeholder="{{
                      getAccountIdPlaceholder(configuredAccount?.transferInReturnAccount)
                    }}"
                    searchStringLength="3"
                    accountType="DETAILED">
                  </app-account-auto-search>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4 p-2">
                  <app-account-auto-search
                    [control]="accountForm.get('transferOutReturnAccount')"
                    [label]="'accountSetupTransfer.transferOutReturnAccount' | translate"
                    placeholder="{{
                      getAccountIdPlaceholder(configuredAccount?.transferOutReturnAccount)
                    }}"
                    searchStringLength="3"
                    accountType="DETAILED">
                  </app-account-auto-search>
                </div>
                <!-- Add more form controls here -->
              </div>
            </mat-tab>

            <!-- Inventory Account Tab -->
            <mat-tab>
              <ng-template mat-tab-label>
                {{ 'accountSetupInventory.inventoryAccount' | translate }}
                <mat-icon class="tab-icon taberror" *ngIf="!accountForm.valid && submitted"
                  >error</mat-icon
                >
              </ng-template>
              <div class="row no-gutters m-t-10">
                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4 p-2">
                  <app-account-auto-search
                    [control]="accountForm.get('badInventoryDebitAccount')"
                    [label]="'accountSetupInventory.badInventoryDebitAccount' | translate"
                    placeholder="{{
                      getAccountIdPlaceholder(configuredAccount?.badInventoryDebitAccount)
                    }}"
                    searchStringLength="3"
                    accountType="DETAILED">
                  </app-account-auto-search>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4 p-2">
                  <app-account-auto-search
                    [control]="accountForm.get('badInventoryCreditAccount')"
                    [label]="'accountSetupInventory.badInventoryCreditAccount' | translate"
                    placeholder="{{
                      getAccountIdPlaceholder(configuredAccount?.badInventoryCreditAccount)
                    }}"
                    searchStringLength="3"
                    accountType="DETAILED">
                  </app-account-auto-search>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4 p-2">
                  <app-account-auto-search
                    [control]="accountForm.get('shortageInventoryDebitAccount')"
                    [label]="'accountSetupInventory.shortageInventoryDebitAccount' | translate"
                    placeholder="{{
                      getAccountIdPlaceholder(configuredAccount?.shortageInventoryDebitAccount)
                    }}"
                    searchStringLength="3"
                    accountType="DETAILED">
                  </app-account-auto-search>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4 p-2">
                  <app-account-auto-search
                    [control]="accountForm.get('shortageInventoryCreditAccount')"
                    [label]="'accountSetupInventory.shortageInventoryCreditAccount' | translate"
                    placeholder="{{
                      getAccountIdPlaceholder(configuredAccount?.shortageInventoryCreditAccount)
                    }}"
                    searchStringLength="3"
                    accountType="DETAILED">
                  </app-account-auto-search>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4 p-2">
                  <app-account-auto-search
                    [control]="accountForm.get('surplusInventoryDebitAccount')"
                    [label]="'accountSetupInventory.surplusInventoryDebitAccount' | translate"
                    placeholder="{{
                      getAccountIdPlaceholder(configuredAccount?.surplusInventoryDebitAccount)
                    }}"
                    searchStringLength="3"
                    accountType="DETAILED">
                  </app-account-auto-search>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4 p-2">
                  <app-account-auto-search
                    [control]="accountForm.get('surplusInventoryCreditAccount')"
                    [label]="'accountSetupInventory.surplusInventoryCreditAccount' | translate"
                    placeholder="{{
                      getAccountIdPlaceholder(configuredAccount?.surplusInventoryCreditAccount)
                    }}"
                    searchStringLength="3"
                    accountType="DETAILED">
                  </app-account-auto-search>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4 p-2">
                  <app-account-auto-search
                    [control]="accountForm.get('cogsAccount')"
                    [label]="'accountSetupInventory.costOfGoodsAccount' | translate"
                    placeholder="{{ getAccountIdPlaceholder(configuredAccount?.cogsAccount) }}"
                    searchStringLength="3"
                    accountType="DETAILED">
                  </app-account-auto-search>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-4 p-2">
                  <app-account-auto-search
                    [control]="accountForm.get('cogsEndAccount')"
                    [label]="'accountSetupInventory.goodsEndAccount' | translate"
                    placeholder="{{ getAccountIdPlaceholder(configuredAccount?.cogsEndAccount) }}"
                    searchStringLength="3"
                    accountType="DETAILED">
                  </app-account-auto-search>
                </div>
                <!-- Add more form controls here -->
              </div>
            </mat-tab>
          </mat-tab-group>
        </mat-card>
      </div>
    </div>
  </form>
  <div class="text-center">
    <button class="m-l-10" (click)="onSubmit($event)" mat-stroked-button color="primary">
      {{ 'common.submit' | translate }}
    </button>
    <button class="m-l-10" [routerLink]="['../']" type="button" mat-stroked-button color="warn">
      {{ 'common.cancel' | translate }}
    </button>
  </div>
</ng-container>
