import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { TranslateModule } from '@ngx-translate/core';
import { MaterialModule } from '../../material/material.module';
import { SharedModule } from '../../shared/shared.module';
import { ReportDashboardComponent } from './components/dashboard/report-dashboard.component';
import { PriceReportComponent } from './components/price-report/price-report.component';
import { StockReportComponent } from './components/stock-report/stock-report.component';
import { ReportingRoutingModule } from './reporting-routing.module';
import { InventoryReportComponent } from './components/inventory-report/inventory-report.component';
import { AccountingReportComponent } from './components/accounting-report/accounting-reports.component';
import { AccountsSearchBoxComponent } from '../accounts/components/accounts-search-box/accounts-search-box.component';
import { AccountsModule } from '../accounts/accounts.module';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { AccountAutoSearchComponent } from '../accounts/components/accountAutoSearch/account-auto-search.component';
import { OrderModule } from 'ngx-order-pipe';
import { FilterFormComponent } from './components/flter-form/filter-form.component';
import { SalesReportComponent } from './components/sales-report/sales-reports.component';
import { PurchaseReportsComponent } from './components/purchase-reports/purchase-reports.component';
import { ReportSetupComponent } from './components/report-setup/report-setup.component';
import { InventoryReportsComponent } from './components/inventory-reports/inventory-reports.component';
import { DynamicReportComponent } from './components/dynamic-report/dynamic-report.component';
@NgModule({
  declarations: [
    ReportDashboardComponent,
    StockReportComponent,
    PriceReportComponent,
    InventoryReportComponent,
    AccountingReportComponent,
    SalesReportComponent,
    FilterFormComponent,
    PurchaseReportsComponent,
    ReportSetupComponent,
    InventoryReportsComponent,
    DynamicReportComponent,
  ],
  imports: [
    CommonModule,
    ReportingRoutingModule,
    SharedModule,
    MaterialModule,
    TranslateModule,
    FlexLayoutModule,
    AccountsModule,
    MatTableModule,
    MatSortModule,
    AccountsModule,
    OrderModule,
  ],
  providers: [AccountAutoSearchComponent],
})
export class ReportingModule {}
