import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MaterialModule } from '../../../../material/material.module';

import { DynamicReportComponent } from './dynamic-report.component';
import { InventoryReportsComponent } from '../inventory-reports/inventory-reports.component';

@NgModule({
  declarations: [DynamicReportComponent, InventoryReportsComponent],
  imports: [CommonModule, ReactiveFormsModule, MaterialModule],
  exports: [DynamicReportComponent, InventoryReportsComponent],
})
export class DynamicReportModule {}
