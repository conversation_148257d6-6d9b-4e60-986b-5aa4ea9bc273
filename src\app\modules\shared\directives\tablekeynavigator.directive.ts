import { FocusMonitor, FocusOrigin } from '@angular/cdk/a11y';
import { SelectionModel } from '@angular/cdk/collections';
import {
  AfterViewInit,
  ContentChildren,
  Directive,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  NgZone,
  OnDestroy,
  Output,
  QueryList,
} from '@angular/core';
import { MatTable, MatTableDataSource } from '@angular/material/table';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Directive({
  selector: '[appMatTableKeyboardNavigation]',
})
export class MatTableKeyboardNavigationDirective implements AfterViewInit, OnDestroy {
  @ContentChildren('row', { read: ElementRef }) rowRefs: QueryList<ElementRef<HTMLElement>>;
  @Input() selection: SelectionModel<any>;
  @Input() rowModel: any;
  @Input() matTable: MatTable<any>;
  @Input() toggleOnEnter = true;
  @Input() selectOnFocus = true;
  @Input() deselectOnBlur = false;
  @Input() preventNewSelectionOnTab = false;
  @Input() focusFirstOption = false;
  @Output() rowSelected = new EventEmitter<any>();

  private dataSource: MatTableDataSource<any>;
  private rows: HTMLElement[];
  private renderedData: any[];
  private unsubscriber$ = new Subject<void>();
  private keydownSubject = new Subject<KeyboardEvent>();

  constructor(
    private el: ElementRef<HTMLElement>,
    private ngZone: NgZone,
    private focusMonitor: FocusMonitor
  ) {}

  ngAfterViewInit(): void {
    this.validateInputs();
    this.initializeDirective();
    this.setupFocusMonitor();
  }

  ngOnDestroy(): void {
    this.unsubscriber$.next();
    this.unsubscriber$.complete();
    this.focusMonitor.stopMonitoring(this.el);
  }

  public onAutocompleteOpened(): void {
    console.log('Autocomplete panel opened');
    // You can add any logic you want to execute when the panel opens
    this.initializeDirective(); // Example: reinitialize the directive
  }

  private setupFocusMonitor(): void {
    this.focusMonitor
      .monitor(this.el)
      .pipe(takeUntil(this.unsubscriber$))
      .subscribe((origin: FocusOrigin) => {
        if (origin) {
          this.onFocus();
        } else {
          this.onBlur();
        }
      });
  }

  private onFocus(): void {
    if (this.selectOnFocus && !this.selection.isMultipleSelection()) {
      this.selection.select(this.rowModel || this.renderedData[this.getCurrentIndex()]);
    }

    if (this.selectOnFocus && this.preventNewSelectionOnTab) {
      this.rows.forEach(row => (row.tabIndex = row === this.el.nativeElement ? 0 : -1));
    }
  }

  private onBlur(): void {
    if (this.deselectOnBlur && !this.selection.isMultipleSelection()) {
      this.selection.deselect(this.rowModel || this.renderedData[this.getCurrentIndex()]);
    }
    if (this.selectOnFocus) {
      this.el.nativeElement.tabIndex = 0;
    }
  }

  private selectRow(): void {
    if (!this.selection.isMultipleSelection()) {
      this.selection.clear();
    }
    const selectedRow = this.rowModel || this.renderedData[this.getCurrentIndex()];
    this.selection.select(selectedRow);
    this.rowSelected.emit(selectedRow);
  }

  @HostListener('keydown', ['$event']) onKeydown(event: KeyboardEvent) {
    const currentIndex = this.getCurrentIndex();
    let newRow: HTMLElement | undefined;
    let newRowModel: any | undefined;

    switch (event.key) {
      case 'ArrowDown':
        // Move to the next row, wrap to the first if at the last
        if (currentIndex < this.rows.length - 1) {
          newRow = this.rows[currentIndex + 1];
          newRowModel = this.renderedData[currentIndex + 1];
        } else {
          // Wrap to the first row
          newRow = this.rows[0];
          newRowModel = this.renderedData[0];
        }
        break;
      case 'ArrowUp':
        if (currentIndex > 0) {
          newRow = this.rows[currentIndex - 1];
          newRowModel = this.renderedData[currentIndex - 1];
        } else {
          // Wrap to the last row
          newRow = this.rows[this.rows.length - 1];
          newRowModel = this.renderedData[this.renderedData.length - 1];
        }
        break;
      case 'Enter':
        this.selectRow();
        event.preventDefault();
        return;
    }

    if (newRow && newRowModel) {
      event.preventDefault();
      this.focusMonitor.focusVia(newRow, 'keyboard');
      if (this.selectOnFocus) {
        this.selection.select(newRowModel);
      }
    }
  }

  private validateInputs(): void {
    if (!this.selection) {
      this.selection = new SelectionModel<any>(true, []);
    }

    if (!this.matTable || !this.matTable.dataSource) {
      throw new Error('MatTable [dataSource] is required');
    }

    if (!this.rowModel && (!this.rowRefs || this.rowRefs.length === 0)) {
      throw new Error('Either [rowModel] or #row reference is required');
    }
  }

  private initializeDirective(): void {
    if (this.el.nativeElement.tabIndex < 0) {
      this.el.nativeElement.tabIndex = 0;
    }

    this.dataSource = this.matTable.dataSource as MatTableDataSource<any>;
    this.dataSource
      .connect()
      .pipe(takeUntil(this.unsubscriber$))
      .subscribe(data => {
        this.renderedData = data;

        if (this.rowModel) {
          this.rows = this.getTableRows();
        } else if (this.rowRefs) {
          this.rows = this.rowRefs.map(ref => ref.nativeElement);
        }

        if (this.focusFirstOption && this.rows.length > 0) {
          this.focusMonitor.focusVia(this.rows[0], 'program');
          if (this.renderedData.length > 0) {
            this.selection?.select(this.renderedData[0]);
          }
        }
      });
  }

  private getTableRows(): HTMLElement[] {
    let el: HTMLElement | null = this.el.nativeElement;
    while (el && el.parentElement) {
      el = el.parentElement;
      if (el.tagName.toLowerCase() === 'mat-table' || el.hasAttribute('mat-table')) {
        return Array.from(el.querySelectorAll('mat-row, tr[mat-row]'));
      }
    }
    return [];
  }

  private getCurrentIndex(): number {
    if (this.rowModel) {
      return this.renderedData.findIndex(row => row === this.rowModel);
    } else {
      return this.rows.findIndex(row => row === this.el.nativeElement);
    }
  }
}
