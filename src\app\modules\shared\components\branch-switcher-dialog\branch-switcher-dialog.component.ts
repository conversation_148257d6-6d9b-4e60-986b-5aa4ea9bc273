import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ILoginBranches } from 'src/app/modules/core/core/models/login';

@Component({
  selector: 'app-branch-switcher-dialog',
  templateUrl: './branch-switcher-dialog.component.html',
  styleUrls: ['./branch-switcher-dialog.component.scss'],
})
export class BranchSwitcherDialogComponent implements OnInit {
  selectedBranch: ILoginBranches;
  selectedYear: any;

  constructor(
    public dialogRef: MatDialogRef<BranchSwitcherDialogComponent>,
    @Inject(MAT_DIALOG_DATA)
    public data: {
      branches: ILoginBranches[];
      currentBranch: string;
      currentYear: string;
    }
  ) {}

  ngOnInit(): void {
    // Set initial selection based on current branch/year
    if (this.data.branches.length > 0) {
      this.selectedBranch =
        this.data.branches.find(b => b.branchName === this.data.currentBranch) ||
        this.data.branches[0];

      if (this.selectedBranch.years.length > 0) {
        this.selectedYear =
          this.selectedBranch.years.find(y => y.year === this.data.currentYear) ||
          this.selectedBranch.years[0];
      }
    }
  }

  onBranchChange(branch: ILoginBranches): void {
    this.selectedBranch = branch;
    this.selectedYear = branch.years[0];
  }

  onYearChange(year: any): void {
    this.selectedYear = year;
  }

  switchBranch(): void {
    if (this.selectedBranch && this.selectedYear) {
      this.dialogRef.close({
        branch: this.selectedBranch,
        year: this.selectedYear,
      });
    }
  }

  cancel(): void {
    this.dialogRef.close();
  }
}
