<form *ngIf="!loading" [formGroup]="unitForm" autocomplete="off">
  <mat-card appearance="outlined">
    <mat-card-title>{{ formTitle | translate }}</mat-card-title>
    <div class="row no-gutters">
      <div class="col-md-4 col-lg-4 col-sm-4 p-2">
        <mat-label>{{ 'productUnits.name' | translate }}</mat-label>
        <mat-form-field class="w-100" [subscriptSizing]="'fixed'">
          <input [appInputFormat]="'capital-case'" matInput formControlName="name" />
          <mat-error
            *ngIf="
              unitForm.controls['name'].hasError('required') && unitForm.controls['name'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>

      <div class="col-md-4 col-lg-4 col-sm-4 p-2">
        <mat-label>{{ 'productUnits.factor' | translate }}</mat-label>
        <mat-form-field class="w-100" [subscriptSizing]="'fixed'">
          <input matInput type="number" formControlName="factorRefUom" />
          <mat-error
            *ngIf="
              unitForm.controls['factorRefUom'].hasError('required') &&
              unitForm.controls['factorRefUom'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>
      <div class="col-md-4 col-lg-4 col-sm-4 p-2" class="hide">
        <mat-label>{{ 'productUnits.type' | translate }}</mat-label>
        <mat-form-field class="w-100" [subscriptSizing]="'fixed'">
          <mat-select #warehouses formControlName="type">
            <mat-option
              *ngFor="let unitFactorType of unitFactorTypes"
              [value]="unitFactorType.value"
              >{{ unitFactorType.display }}
            </mat-option>
          </mat-select>
          <mat-error
            *ngIf="
              unitForm.controls['type'].hasError('required') && unitForm.controls['type'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>
      -->
    </div>
  </mat-card>
  <div class="text-center">
    <button
      class="m-l-10"
      *ngIf="!isEditMode"
      (click)="onSubmit($event)"
      mat-stroked-button
      color="primary">
      {{ 'common.submit' | translate }}
    </button>
    <button
      class="m-l-10"
      *ngIf="isEditMode"
      (click)="onSubmit($event)"
      mat-stroked-button
      color="primary">
      {{ 'common.submit' | translate }}
    </button>
    <button
      class="m-l-10"
      *ngIf="!isEditMode"
      [routerLink]="['../']"
      mat-stroked-button
      color="warn">
      {{ 'common.cancel' | translate }}
    </button>
    <button
      class="m-l-10"
      *ngIf="isEditMode"
      [routerLink]="['../../']"
      mat-stroked-button
      color="warn">
      {{ 'common.cancel' | translate }}
    </button>
  </div>
</form>
