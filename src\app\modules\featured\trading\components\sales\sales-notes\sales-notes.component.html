<app-dialog-header></app-dialog-header>
<div fxLayout="row wrap">
  <div fxFlex="100">
    <mat-card appearance="outlined">
      <mat-card-content>
        <div fxLayout="row wrap">
          <div fxFlex="100">
            <form [formGroup]="filterSelection">
              <mat-radio-group aria-label="Select an option" formControlName="filterTYpe">
                <mat-radio-button class="example-margin" value="fullCreditNote">{{
                  (isSalesProcessing ? 'salesNotes.fullCredit' : 'purchaseNotes.fullCredit')
                    | translate
                }}</mat-radio-button>
                <mat-radio-button
                  class="example-margin"
                  color="primary"
                  value="partialCreditNote"
                  >{{
                    (isSalesProcessing ? 'salesNotes.partialCredit' : 'purchaseNotes.partialCredit')
                      | translate
                  }}</mat-radio-button
                >
                <mat-radio-button
                  class="example-margin"
                  *ngIf="isSalesProcessing"
                  color="primary"
                  value="debitNote"
                  >{{ 'salesNotes.debitNote' | translate }}</mat-radio-button
                >
                <mat-radio-button class="example-margin" color="primary" value="ALL">{{
                  (isSalesProcessing ? 'salesNotes.allNotes' : 'purchaseNotes.allNotes') | translate
                }}</mat-radio-button>
              </mat-radio-group>
            </form>
            <!-- search field -->
            <div class="row no-gutters">
              <div class="col-md-6 col-lg-6 col-sm-12 d-flex">
                <mat-form-field class="w-100">
                  <input
                    #searchInput
                    (keyup)="applyFilter($event.target.value)"
                    matInput
                    autocomplete="off" />
                  <i-tabler class="icon-16" matPrefix name="search"></i-tabler>
                  <a
                    class="cursor-pointer"
                    *ngIf="searchInput?.value"
                    (click)="clearSearchInput()"
                    matSuffix>
                    <i-tabler class="icon-16 error" name="X"></i-tabler>
                  </a>
                </mat-form-field>
              </div>
            </div>
            <!-- search field -->
          </div>
        </div>
        <div class="table-responsive">
          <table *ngIf="dataSource?.data.length > 0" [dataSource]="dataSource" mat-table matSort>
            <ng-container matColumnDef="documentNumber">
              <th class="font-14" *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'notes.notesNo' | translate }}
              </th>
              <td *matCellDef="let element" [data]="element.documentNumber" mat-cell appHyphen>
                {{ element.documentNumber }}
              </td>
            </ng-container>
            <ng-container matColumnDef="referenceDocumentNumber">
              <th class="font-14" *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'notes.invoiceNo' | translate }}
              </th>
              <td
                *matCellDef="let element"
                [data]="element.referenceDocumentNumber"
                mat-cell
                appHyphen>
                {{ element.referenceDocumentNumber }}
              </td>
            </ng-container>
            <ng-container matColumnDef="vatNumber">
              <th class="font-14" *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'notes.vatNo' | translate }}
              </th>
              <td *matCellDef="let element" [data]="element.vatNumber" mat-cell appHyphen>
                {{ element.vatNumber }}
              </td>
            </ng-container>
            <ng-container matColumnDef="nameArabic">
              <th class="font-14" *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'notes.customerName' | translate }}
              </th>
              <td *matCellDef="let element" [data]="element.nameArabic" mat-cell appHyphen>
                {{ element.nameArabic }}
              </td>
            </ng-container>
            <ng-container matColumnDef="transactionType">
              <th class="font-14" *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'notes.noteType' | translate }}
              </th>
              <td *matCellDef="let element" mat-cell>
                <div
                  class="text-center cursor-pointer rounded"
                  [ngClass]="{
                    'bg-light-primary': element.transactionType === 'partialCreditNote',
                    'bg-light-success': element.transactionType === 'fullCreditNote',
                    'bg-light-warning': element.transactionType === 'debitNote'
                  }">
                  {{
                    (isSalesProcessing
                      ? 'salesNotes.' + element.transactionType
                      : 'purchaseNotes.' + element.transactionType
                    ) | translate
                  }}
                </div>
              </td>
            </ng-container>
            <ng-container matColumnDef="invoiceDate">
              <th class="font-14" *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'notes.invoiceDate' | translate }}
              </th>
              <td *matCellDef="let element" [data]="element.invoiceDate" mat-cell appHyphen>
                {{ element.invoiceDate }}
              </td>
            </ng-container>
            <ng-container matColumnDef="issueDate">
              <th class="font-14" *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'notes.notesDate' | translate }}
              </th>
              <td *matCellDef="let element" [data]="element.issueDate" mat-cell appHyphen>
                {{ element.issueDate }}
              </td>
            </ng-container>
            <ng-container matColumnDef="referenceDocumentDate">
              <th class="font-14" *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'notes.notesDate' | translate }}
              </th>
              <td
                *matCellDef="let element"
                [data]="element.referenceDocumentDate"
                mat-cell
                appHyphen>
                {{ element.referenceDocumentDate }}
              </td>
            </ng-container>
            <ng-container matColumnDef="invoiceTotal">
              <th class="font-14" *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'notes.grandTotal' | translate }}
              </th>
              <td *matCellDef="let element" [data]="element.invoiceTotal" mat-cell appHyphen>
                {{ element.invoiceTotal }}
              </td>
            </ng-container>
            <ng-container matColumnDef="stage">
              <th *matHeaderCellDef mat-header-cell>
                {{ 'zatcaInvoiceListings.status' | translate }}
              </th>
              <td class="text-center" *matCellDef="let element" mat-cell>
                <div class="status-container">
                  <span
                    class="status-text rounded"
                    [ngClass]="{
                      'bg-light-error': element.stage === 'REJECTED',
                      'bg-light-accent': element.stage === 'EINVOICE_SAVED',
                      'bg-light-success': element.stage === 'CLEARED'
                    }">
                    {{
                      element?.stage ? ('zatcaInvoiceStatus.' + element?.stage | translate) : '-'
                    }}
                  </span>
                  <div class="button-container">
                    <a
                      (click)="
                        getZatcaErrorForInvoice(element.documentUuid);
                        (false);
                        $event.preventDefault()
                      "
                      mat-icon-button
                      matTooltip="Invoice Zatca Status"
                      matTooltipClass="text-uppercase"
                      matTooltipPosition="above">
                      <i-tabler class="icon-12 theme-icon" name="exclamation-circle"></i-tabler>
                    </a>
                    <a
                      (click)="
                        getXmlForInvoice(element.documentUuid); (false); $event.preventDefault()
                      "
                      mat-icon-button
                      matTooltip="Invoice XML"
                      matTooltipClass="text-uppercase"
                      matTooltipPosition="above">
                      <i-tabler class="icon-12 theme-icon" name="file-type-xml"></i-tabler>
                    </a>
                  </div>
                </div>
              </td>
            </ng-container>
            <ng-container matColumnDef="action">
              <th class="font-14" *matHeaderCellDef mat-header-cell>
                {{ 'common.action' | translate }}
              </th>
              <td *matCellDef="let element" mat-cell>
                <button
                  class="d-flex justify-content-center"
                  [matMenuTriggerFor]="menu1"
                  mat-icon-button>
                  <i-tabler class="icon-20 d-flex" name="dots-vertical"></i-tabler>
                </button>
                <mat-menu class="cardWithShadow" #menu1="matMenu">
                  <!-- <a
                    (click)="
                      printNotes(element.id, element.transactionType);
                      (false);
                      $event.preventDefault()
                    ">
                    <i-tabler class="icon-16 m-r-4" name="printer"></i-tabler>
                    <span>{{ 'common.print' | translate }}</span>
                  </a> -->
                  <button
                    *appHasPermission="['Sales.View', 'AllPermissions']"
                    (click)="
                      printNotes(element.id, element.transactionType);
                      (false);
                      $event.preventDefault()
                    "
                    mat-menu-item>
                    <div class="d-flex align-items-center">
                      <i-tabler class="icon-16 m-r-4" name="printer"></i-tabler>
                      <span>{{ 'common.print' | translate }}</span>
                    </div>
                  </button>
                </mat-menu>
              </td>
            </ng-container>
            <tr class="mat-row" *matNoDataRow>
              <td class="text-center" [attr.colspan]="displayedColumns.length">
                {{ 'common.noDataFound' | translate }}
              </td>
            </tr>
            <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
            <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
          </table>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
