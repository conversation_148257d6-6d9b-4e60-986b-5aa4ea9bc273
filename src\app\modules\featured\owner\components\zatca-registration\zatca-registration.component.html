<mat-card appearance="outlined">
  <mat-card-content>
    <form [formGroup]="registrationForm" autocomplete="off">
      <mat-card-title>Zatca Registration</mat-card-title>
      <div class="row no-gutters m-t-10">
        <div class="col-md-6 col-lg-6 col-sm-6 p-2">
          <mat-label>OTP</mat-label>
          <mat-form-field class="w-100 p-2">
            <input matInput formControlName="otp" type="text" />
            <mat-error
              *ngIf="registrationForm.get('otp').invalid && registrationForm.get('otp').touched">
              Field Required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="col-md-6 col-lg-6 col-sm-6 p-2">
          <mat-label>Branches</mat-label>
          <mat-form-field class="w-100 p-2">
            <mat-select formControlName="branchId">
              <mat-option *ngFor="let branch of branches" [value]="branch.branchId">
                {{ branch | localized }}
              </mat-option>
            </mat-select>
            <mat-error
              *ngIf="
                registrationForm.get('branchId').invalid && registrationForm.get('branchId').touched
              ">
              Field Required
            </mat-error>
          </mat-form-field>
        </div>
        <div class="col-md-6 col-lg-6 col-sm-6 p-2">
          <mat-label>Common Name</mat-label>
          <mat-form-field class="w-100 p-2">
            <input matInput formControlName="commonName" type="text" />
            <mat-error
              *ngIf="
                registrationForm.get('commonName').invalid &&
                registrationForm.get('commonName').touched
              ">
              Field Required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="col-md-6 col-lg-6 col-sm-6 p-2">
          <mat-label>Serial Number</mat-label>
          <mat-form-field class="w-100 p-2">
            <input matInput formControlName="serialNumber" type="text" />
            <mat-error
              *ngIf="
                registrationForm.get('serialNumber').hasError('required') &&
                registrationForm.get('serialNumber').touched
              ">
              Field Required
            </mat-error>
            <mat-error
              *ngIf="
                registrationForm.get('serialNumber').hasError('invalidSerialNumber') &&
                !registrationForm.get('serialNumber').hasError('required') &&
                registrationForm.get('serialNumber').touched
              ">
              {{ registrationForm.get('serialNumber').errors.invalidSerialNumber }}
            </mat-error>
          </mat-form-field>
        </div>

        <div class="col-md-6 col-lg-6 col-sm-6 p-2">
          <mat-label>Organization Identifier/Vat No.</mat-label>
          <mat-form-field class="w-100 p-2">
            <input matInput formControlName="organizationIdentifier" type="text" />
            <mat-error
              *ngIf="
                registrationForm.get('organizationIdentifier').hasError('required') &&
                registrationForm.get('organizationIdentifier').touched
              ">
              Field Required
            </mat-error>
            <mat-error
              *ngIf="
                registrationForm.get('organizationIdentifier').hasError('invalidVatNumber') &&
                !registrationForm.get('organizationIdentifier').hasError('required') &&
                registrationForm.get('organizationIdentifier').touched
              ">
              Invalid VAT Number
            </mat-error>
          </mat-form-field>
        </div>

        <div class="col-md-6 col-lg-6 col-sm-6 p-2">
          <mat-label>Organization Unit Name</mat-label>
          <mat-form-field class="w-100 p-2">
            <input matInput formControlName="organizationUnitName" type="text" />
            <mat-error
              *ngIf="
                registrationForm.get('organizationUnitName').invalid &&
                registrationForm.get('organizationUnitName').touched
              ">
              Field Required
            </mat-error>
          </mat-form-field>
        </div>
        <div class="col-md-6 col-lg-6 col-sm-6 p-2">
          <mat-label>Organization Name</mat-label>
          <mat-form-field class="w-100 p-2">
            <input matInput formControlName="organizationName" type="text" />
            <mat-error
              *ngIf="
                registrationForm.get('organizationName').invalid &&
                registrationForm.get('organizationName').touched
              ">
              Field Required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="col-md-6 col-lg-6 col-sm-6 p-2">
          <mat-label>Country Code</mat-label>
          <mat-form-field class="w-100 p-2">
            <input matInput formControlName="countryName" type="text" disabled />
          </mat-form-field>
        </div>

        <div class="col-md-6 col-lg-6 col-sm-6 p-2">
          <mat-label>Invoice Type</mat-label>
          <mat-form-field class="w-100 p-2">
            <input matInput formControlName="invoiceType" type="text" disabled />
          </mat-form-field>
        </div>

        <div class="col-md-6 col-lg-6 col-sm-6 p-2">
          <mat-label>Location</mat-label>
          <mat-form-field class="w-100 p-2">
            <input matInput formControlName="location" type="text" />
            <mat-error
              *ngIf="
                registrationForm.get('location').invalid && registrationForm.get('location').touched
              ">
              Field Required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="col-md-6 col-lg-6 col-sm-6 p-2">
          <mat-label>Industry</mat-label>
          <mat-form-field class="w-100 p-2">
            <input matInput formControlName="industry" type="text" />
            <mat-error
              *ngIf="
                registrationForm.get('industry').invalid && registrationForm.get('industry').touched
              ">
              Field Required
            </mat-error>
          </mat-form-field>
        </div>
        <div class="col-md-6 col-lg-6 col-sm-6 p-2">
          <mat-label>Environment</mat-label>
          <mat-form-field class="w-100 p-2">
            <mat-select formControlName="environment">
              <mat-option *ngFor="let environment of environments" [value]="environment">
                {{ environment }}
              </mat-option>
            </mat-select>
            <mat-error
              *ngIf="
                registrationForm.get('environment').invalid &&
                registrationForm.get('environment').touched
              ">
              Field Required
            </mat-error>
          </mat-form-field>
        </div>
      </div>
    </form>
  </mat-card-content>
</mat-card>
<div class="text-center">
  <button class="m-l-10" (click)="onSubmit($event); (false)" mat-stroked-button color="primary">
    {{ 'common.submit' | translate }}
  </button>
  <button class="m-l-10" [routerLink]="['../../']" mat-stroked-button color="warn">
    {{ 'common.cancel' | translate }}
  </button>
</div>
