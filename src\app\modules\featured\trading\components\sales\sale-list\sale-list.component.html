<mat-card appearance="outlined">
  <mat-card-content>
    <mat-tab-group #tabs [dynamicHeight]="true" mat-stretch-tabs="false" animationDuration="0ms">
      <mat-tab>
        <!-- action bar -->
        <app-create-action
          *appHasPermission="['Sales.Create', 'AllPermissions']"
          [label]="'sales.register_sales' | translate"></app-create-action>
        <!-- action bar -->
        <ng-template mat-tab-label>{{ 'sales.salesListing' | translate }}</ng-template>
        <!-- search field -->
        <div class="row no-gutters">
          <div class="col-md-6 col-lg-6 col-sm-12 d-flex">
            <mat-form-field class="w-100">
              <input
                #searchInput
                (keyup)="applyFilter($event.target.value)"
                matInput
                autocomplete="off" />
              <i-tabler class="icon-16" matPrefix name="search"></i-tabler>
              <a
                class="cursor-pointer"
                *ngIf="searchInput?.value"
                (click)="clearSearchInput()"
                matSuffix>
                <i-tabler class="icon-16 error" name="X"></i-tabler>
              </a>
            </mat-form-field>
          </div>
        </div>
        <!-- search field -->
        <mat-card-title class="m-t-10">{{ 'sales.salesList' | translate }}</mat-card-title>
        <div class="table-responsive">
          <table class="w-100" [dataSource]="dataSource" mat-table matSort>
            <ng-container matColumnDef="documentNumber">
              <th *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'sales.invoiceNo' | translate }}
              </th>
              <td *matCellDef="let element" mat-cell>
                {{ element.documentNumber }}
              </td>
            </ng-container>
            <ng-container matColumnDef="issueDate">
              <th *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'sales.invoiceDate' | translate }}
              </th>
              <td *matCellDef="let element" mat-cell>
                {{ element.issueDate | date : 'yyyy-MM-dd' }}
              </td>
            </ng-container>
            <ng-container matColumnDef="invoiceTotal">
              <th *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'sales.invoiceAmt' | translate }}
              </th>
              <td *matCellDef="let element" mat-cell>
                {{ element.invoiceTotal }}
              </td>
            </ng-container>
            <ng-container matColumnDef="accountNumber">
              <th *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'sales.invoiceCustomerNo' | translate }}
              </th>
              <td
                *matCellDef="let element"
                [data]="element.account['accountNumber']"
                [copyValue]="element.account['accountNumber']"
                appHyphen
                mat-cell
                appCopyClick>
                {{ element.account['accountNumber'] }}
              </td>
            </ng-container>
            <ng-container matColumnDef="nameArabic">
              <th *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'common.field.nameArabic' | translate }}
              </th>
              <td
                *matCellDef="let element"
                [data]="element.account?.nameArabic"
                [copyValue]="element.account?.nameArabic"
                mat-cell
                appHyphen
                appCopyClick>
                {{ element.account?.nameArabic }}
              </td>
            </ng-container>
            <ng-container matColumnDef="nameEnglish">
              <th *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'common.field.nameEnglish' | translate }}
              </th>
              <td
                *matCellDef="let element"
                [data]="element.account?.nameEnglish"
                [copyValue]="element.account?.nameEnglish"
                mat-cell
                appHyphen
                appCopyClick>
                {{ element.account?.nameEnglish }}
              </td>
            </ng-container>
            <ng-container matColumnDef="paymentMethod">
              <th *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'paymentsType.paymentMethod' | translate }}
              </th>
              <td *matCellDef="let element" mat-cell>
                <div class="text-center cursor-pointer rounded bg-light-primary">
                  {{ 'paymentsType.' + element.paymentMethod | translate }}
                </div>
              </td>
            </ng-container>
            <ng-container matColumnDef="vatNumber">
              <th *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'common.field.vatNo' | translate }}
              </th>
              <td *matCellDef="let element" [data]="element.vatNumber" mat-cell appHyphen>
                {{ element.vatNumber }}
              </td>
            </ng-container>
            <ng-container matColumnDef="phoneNumber">
              <th *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'common.field.phone' | translate }}
              </th>
              <td *matCellDef="let element" [data]="element.phoneNumber" mat-cell appHyphen>
                {{ element.phoneNumber }}
              </td>
            </ng-container>
            <ng-container matColumnDef="returnStatus">
              <th *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'sales.returnStatus' | translate }}
              </th>
              <td *matCellDef="let element" mat-cell>
                <div
                  class="text-center cursor-pointer rounded"
                  [ngClass]="{
                    'bg-light-error': element.returnStatus === 'NOTRETURNED',
                    'bg-light-primary': element.returnStatus === 'PARTIALLYRETURNED',
                    'bg-light-success': element.returnStatus === 'RETURNED'
                  }">
                  {{ element.returnStatus | translate }}
                </div>
              </td>
            </ng-container>
            <ng-container matColumnDef="stage">
              <th *matHeaderCellDef mat-header-cell>
                {{ 'zatcaInvoiceListings.status' | translate }}
              </th>
              <td class="text-center" *matCellDef="let element" mat-cell>
                <div class="status-container">
                  <span
                    class="status-text rounded"
                    [ngClass]="{
                      'bg-light-error': element.stage === 'REJECTED',
                      'bg-light-accent': element.stage === 'EINVOICE_SAVED',
                      'bg-light-success': element.stage === 'CLEARED'
                    }">
                    {{
                      element?.stage ? ('zatcaInvoiceStatus.' + element?.stage | translate) : '-'
                    }}
                  </span>
                  <div class="button-container">
                    <a
                      (click)="
                        getZatcaErrorForInvoice(element.documentUuid);
                        (false);
                        $event.preventDefault()
                      "
                      mat-icon-button
                      matTooltip="Invoice Zatca Status"
                      matTooltipClass="text-uppercase"
                      matTooltipPosition="above">
                      <i-tabler class="icon-12 theme-icon" name="exclamation-circle"></i-tabler>
                    </a>
                    <a
                      (click)="
                        getXmlForInvoice(element.documentUuid); (false); $event.preventDefault()
                      "
                      mat-icon-button
                      matTooltip="Invoice XML"
                      matTooltipClass="text-uppercase"
                      matTooltipPosition="above">
                      <i-tabler class="icon-12 theme-icon" name="file-type-xml"></i-tabler>
                    </a>
                  </div>
                </div>
              </td>
            </ng-container>
            <ng-container matColumnDef="action">
              <th *matHeaderCellDef mat-header-cell>
                {{ 'common.action' | translate }}
              </th>
              <td *matCellDef="let element" mat-cell>
                <button
                  class="d-flex justify-content-center"
                  [matMenuTriggerFor]="menu1"
                  mat-icon-button>
                  <i-tabler class="icon-20 d-flex" name="dots-vertical"></i-tabler>
                </button>
                <mat-menu class="cardWithShadow" #menu1="matMenu">
                  <button
                    *appHasPermission="['Sales.View', 'AllPermissions']"
                    [routerLink]="['view', element.id]"
                    mat-menu-item>
                    <div class="d-flex align-items-center">
                      <i-tabler class="icon-16 m-r-4" name="eye"></i-tabler>
                      <span>{{ 'common.viewAction' | translate }}</span>
                    </div>
                  </button>

                  <ng-container
                    *ngIf="
                      element.returnStatus !== 'RETURNED' &&
                      element.returnStatus !== 'PARTIALLYRETURNED'
                    ">
                    <button
                      *appHasPermission="['Sales.Create', 'AllPermissions']"
                      [routerLink]="['creditnote', element.id]"
                      mat-menu-item>
                      <div class="d-flex align-items-center">
                        <i-tabler class="icon-16 m-r-4" name="edit"></i-tabler>
                        <span>{{ 'sales.fullCreditNote' | translate }}</span>
                      </div>
                    </button>
                  </ng-container>

                  <ng-container
                    *ngIf="
                      element.returnStatus !== 'RETURNED' ||
                      element.returnStatus === 'PARTIALLYRETURNED'
                    ">
                    <button
                      *appHasPermission="['Sales.Create', 'AllPermissions']"
                      [routerLink]="['partialcreditnote', element.id]"
                      mat-menu-item>
                      <div class="d-flex align-items-center">
                        <i-tabler class="icon-16 m-r-4" name="edit"></i-tabler>
                        <span>{{ 'sales.partialCreditNote' | translate }}</span>
                      </div>
                    </button>
                  </ng-container>

                  <ng-container>
                    <button
                      *appHasPermission="['Sales.Create', 'AllPermissions']"
                      [routerLink]="['debitnote', element.id]"
                      mat-menu-item>
                      <div class="d-flex align-items-center">
                        <i-tabler class="icon-16 m-r-4" name="edit"></i-tabler>
                        <span>{{ 'sales.debitNote' | translate }}</span>
                      </div>
                    </button>
                  </ng-container>

                  <ng-container *ngIf="element.returnStatus !== 'NOTRETURNED'">
                    <button
                      *appHasPermission="['Sales.Create', 'AllPermissions']"
                      (click)="openSalesNotes(element.id); (false)"
                      mat-menu-item>
                      <div class="d-flex align-items-center">
                        <i-tabler class="icon-16 m-r-4" name="clipboard-list"></i-tabler>
                        <span>{{ 'sales.viewNotes' | translate }}</span>
                      </div>
                    </button>
                  </ng-container>
                  <ng-container>
                    <button
                      *appHasPermission="['Sales.View', 'AllPermissions']"
                      (click)="transactionReport(element); (false); $event.preventDefault()"
                      mat-menu-item>
                      <div class="d-flex align-items-center">
                        <i-tabler class="icon-16 m-r-4" name="printer"></i-tabler>
                        <span>{{ 'common.print' | translate }}</span>
                      </div>
                    </button>
                  </ng-container>
                </mat-menu>
              </td>
            </ng-container>
            <tr class="mat-row" *matNoDataRow>
              <td class="text-center" [attr.colspan]="displayedColumns.length">
                {{ 'common.noDataFound' | translate }}
              </td>
            </tr>
            <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
            <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
          </table>
        </div>
        <mat-paginator
          *ngIf="dataSource?.filteredData?.length > 0"
          [length]="dataSource.filteredData.length"
          [pageIndex]="0"
          [pageSize]="10"
          [pageSizeOptions]="[5, 10, 20]"></mat-paginator>
      </mat-tab>
      <mat-tab>
        <ng-template mat-tab-label>{{ 'sales.salesReceivables' | translate }}</ng-template>
        <app-sales-purchase-receivable></app-sales-purchase-receivable>
      </mat-tab>
      <mat-tab>
        <ng-template mat-tab-label>{{ 'sales.zatcaEinvoices' | translate }}</ng-template>
        <app-zatca-invoice-status></app-zatca-invoice-status>
      </mat-tab>
    </mat-tab-group>
  </mat-card-content>
</mat-card>
