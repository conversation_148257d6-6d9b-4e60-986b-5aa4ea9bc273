import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { TranslateModule } from '@ngx-translate/core';
import { SharedModule } from '../../shared/shared.module';
import { AccountsModule } from '../accounts/accounts.module';
import { AccountAutoSearchComponent } from '../accounts/components/accountAutoSearch/account-auto-search.component';
import { AddNewTermsModalComponent } from './components/add-new-terms-modal/add-new-terms-modal.component';
import { CreateVoucherComponent } from './components/create-voucher/create-voucher.component';
import { CustomerFormComponent } from './components/customers/customer-form/customer-form.component';
import { CustomerGetAllComponent } from './components/customers/customer-get-all/customer-get-all.component';
import { CustomerListComponent } from './components/customers/customer-list/customer-list.component';
import { CustomerSelectionComponent } from './components/customers/customer-selection/customer-selection.component';
import { WalkInCustomerComponent } from './components/customers/walk-in-customer/walk-in-customer.component';
import { DistributorFormComponent } from './components/distributors/distributor-form/distributor-form.component';
import { DistributorListComponent } from './components/distributors/distributor-list/distributor-list.component';
import { EditTermsModalComponent } from './components/edit-terms-modal/edit-terms-modal.component';
import { PreviewAllTermsModalComponent } from './components/preview-all-terms-modal/preview-all-terms-modal.component';
import { PreviewTermsModalComponent } from './components/preview-terms-modal/preview-terms-modal.component';
import { PurchaseFormComponent } from './components/purchase/purchase-form/purchase-form.component';
import { PurchaseListComponent } from './components/purchase/purchase-list/purchase-list.component';
import { PurchasedebitnoteComponent } from './components/purchase/purchasedebitnote/purchasedebitnote.component';
import { PurchasepartialnoteComponent } from './components/purchase/purchasepartialnote/purchasepartialnote.component';
import { QuotationTermsComponent } from './components/quotation-terms/quotation-terms.component';
import { QuotationFormComponent } from './components/quotation/quotation-form/quotation-form.component';
import { QuotationListComponent } from './components/quotation/quotation-list/quotation-list.component';
import { SalesPurchaseReceivableComponent } from './components/sales-purchase-receivable/sales-purchase-receivable.component';
import { SaleDebitNoteModalComponent } from './components/sales/sale-debit-note/sale-debit-note-modal/sale-debit-note-modal.component';
import { SaleDebitNoteComponent } from './components/sales/sale-debit-note/sale-debit-note.component';
import { SaleFormComponent } from './components/sales/sale-form/sale-form.component';
import { SaleListComponent } from './components/sales/sale-list/sale-list.component';
import { SalePartialNoteComponent } from './components/sales/sale-partial-note/sale-partial-note.component';
import { SalesNotesComponent } from './components/sales/sales-notes/sales-notes.component';
import { SupplierFormComponent } from './components/suppliers/supplier-form/supplier-form.component';
import { SupplierGetAllComponent } from './components/suppliers/supplier-get-all/supplier-get-all.component';
import { SupplierListComponent } from './components/suppliers/supplier-list/supplier-list.component';
import { SupplierSelectionComponent } from './components/suppliers/supplier-selection/supplier-selection.component';
import { TradingDashboardComponent } from './components/trading-dashboard/trading-dashboard.component';
import { VoucherListInvoiceComponent } from '../finance/financedashboard/components/voucher-list-invoice/voucher-list-invoice.component';
import { TradingRoutingModule } from './trading-routing.module';

@NgModule({
  declarations: [
    SaleFormComponent,
    TradingDashboardComponent,
    CustomerListComponent,
    CustomerFormComponent,
    DistributorFormComponent,
    DistributorListComponent,
    SupplierListComponent,
    SupplierFormComponent,
    CustomerSelectionComponent,
    WalkInCustomerComponent,
    SaleListComponent,
    SalesNotesComponent,
    CustomerGetAllComponent,
    SalePartialNoteComponent,
    SaleDebitNoteComponent,
    SaleDebitNoteModalComponent,
    QuotationFormComponent,
    QuotationListComponent,
    PurchaseFormComponent,
    PurchaseListComponent,
    QuotationTermsComponent,
    AddNewTermsModalComponent,
    PreviewTermsModalComponent,
    EditTermsModalComponent,
    PreviewAllTermsModalComponent,
    SupplierSelectionComponent,
    SupplierGetAllComponent,
    SalesPurchaseReceivableComponent,
    CreateVoucherComponent,
    VoucherListInvoiceComponent,
    PurchasepartialnoteComponent,
    PurchasedebitnoteComponent,
  ],
  imports: [
    CommonModule,
    TradingRoutingModule,
    TranslateModule,
    SharedModule,
    FlexLayoutModule,
    AccountsModule,
  ],
  providers: [AccountAutoSearchComponent],
})
export class TradingModule {}
