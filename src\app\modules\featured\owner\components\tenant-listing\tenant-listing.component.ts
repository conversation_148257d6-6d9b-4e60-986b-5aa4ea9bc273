import { Component, ElementRef, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { TenantsService } from '../../services/tenants.service';
import { ITenant, ITenants } from '../../tenant';

@Component({
  selector: 'app-tenant-listing',
  templateUrl: './tenant-listing.component.html',
  styleUrls: ['./tenant-listing.component.scss'],
})
export class TenantListingComponent {
  @ViewChild('filter') filter: ElementRef;
  @ViewChild('searchInput') searchInput: ElementRef;
  @ViewChild(MatPaginator) set matPaginator(paginator: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = paginator;
    }
  }
  @ViewChild(MatSort) set matSort(sort: MatSort) {
    if (this.dataSource) {
      this.dataSource.sort = sort;
    }
  }
  units: ITenant[];
  displayedColumns: string[];
  dataSource: MatTableDataSource<ITenant>;
  isLoading = true;
  constructor(public tenantService: TenantsService, public dialog: MatDialog) {}

  ngOnInit(): void {
    this.getAllTenants();
    this.initColumns();
  }

  getAllTenants(): void {
    this.tenantService.getAllTenants().subscribe((result: ITenants) => {
      console.log(result, result);
      this.units = result.tenants;
      this.isLoading = false;
      this.dataSource = new MatTableDataSource<ITenant>(this.units);
    });
  }

  initColumns(): void {
    this.displayedColumns = [
      'action',
      'tenantId',
      'tenantName',
      'commercialRegistrationNo',
      'vatNumber',
      'phoneNumber',
      'emailId',
    ];
  }

  applyFilter(filterValue: string): void {
    filterValue = filterValue.trim();
    filterValue = filterValue.toLowerCase();
    this.dataSource.filter = filterValue;
  }

  clearSearchInput() {
    this.searchInput.nativeElement.value = '';
    this.getAllTenants();
  }
}
