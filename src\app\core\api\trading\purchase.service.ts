/* eslint-disable @typescript-eslint/ban-types */
import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { IPurchaseDetails, IPurchaseResponse } from '../../interfaces/purchase';
import { createParamsFromObject } from '../../utils/date-utils';

@Injectable({
  providedIn: 'root',
})
export class PurchaseService {
  baseUrl = environment.apiUrl + 'trading/purchase';

  constructor(private http: HttpClient) {}

  // eslint-disable-next-line @typescript-eslint/ban-types
  getAllPurchaseSales(params: Object) {
    const getSalesParams = this.getParams(params);
    return this.http.get<IPurchaseResponse>(`${this.baseUrl}/pages`, {
      params: getSalesParams,
    });
  }

  getPurchaseSalesById(id: string, params: Object) {
    const getSalesParams = this.getParams(params);
    return this.http.get<IPurchaseDetails>(this.baseUrl + `/${id}`, {
      params: getSalesParams,
    });
  }

  createpurchaseSales(supplier: IPurchaseDetails) {
    return this.http.post(this.baseUrl, supplier);
  }

  editpurchaseSales(supplier: IPurchaseDetails, id: string) {
    return this.http.put(this.baseUrl + `/${id}`, supplier);
  }

  getPurchaseNotesById(params: Object) {
    const getSalesParams = this.getParams(params);
    return this.http.get<IPurchaseResponse>(this.baseUrl + '/pages', { params: getSalesParams });
  }

  // eslint-disable-next-line @typescript-eslint/ban-types
  getParams(salesParams: Object): HttpParams {
    return createParamsFromObject(salesParams);
  }
}
