<button class="close-button" *ngIf="modalData?.data" [mat-dialog-close]="true" mat-icon-button>
  <mat-icon class="close-icon" color="warn">close</mat-icon>
</button>

<ng-container>
  <mat-card appearance="outlined">
    <form [formGroup]="distributorForm" autocomplete="off">
      <mat-card-title>{{ 'distributor.distributorTitle' | translate }}</mat-card-title>
      <div class="row no-gutters m-t-10">
        <!-- parentAccountId  -->
        <div class="p-2 col-lg-4 col-md-4 col-sm-6">
          <mat-label>{{ 'distributor.parentId' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <mat-select formControlName="parentAccountId">
              <mat-option
                *ngFor="let parentAccount of parentAccounts"
                [value]="parentAccount.accountId">
                {{ parentAccount | localized }}
              </mat-option>
            </mat-select>
            <mat-error
              *ngIf="
                distributorForm?.controls['parentAccountId'].hasError('required') &&
                distributorForm?.controls['parentAccountId'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
        <!-- nameArabic  -->
        <div class="p-2 col-lg-4 col-md-4 col-sm-6">
          <mat-label>{{ 'distributor.name' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input type="text" maxlength="40" matInput formControlName="nameArabic" />
            <mat-error
              *ngIf="
                distributorForm?.controls['nameArabic'].hasError('required') &&
                distributorForm?.controls['nameArabic'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
        <!-- nameEnglish -->
        <div class="p-2 col-lg-4 col-md-4 col-sm-6">
          <mat-label>{{ 'distributor.englishName' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input type="text" maxlength="40" matInput formControlName="nameEnglish" />
            <mat-error
              *ngIf="
                distributorForm?.controls['nameEnglish'].hasError('required') &&
                distributorForm?.controls['nameEnglish'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="row no-gutters">
        <!-- accountNumber  -->
        <div class="p-2 col-lg-3 col-md-6 col-sm-6">
          <mat-label>{{ 'common.field.accountNumber' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input readonly type="text" maxlength="40" matInput formControlName="accountNumber" />
          </mat-form-field>
        </div>
        <!-- vatNumber -->
        <div class="p-2 col-lg-3 col-md-6 col-sm-6">
          <mat-label>{{ 'common.field.vatNo' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input type="text" maxlength="40" matInput formControlName="vatNumber" />
            <mat-error
              *ngIf="
                distributorForm?.controls['vatNumber'].hasError('required') &&
                distributorForm?.controls['vatNumber'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
        <!-- phoneNumber -->
        <div class="p-2 col-lg-3 col-md-6 col-sm-6">
          <mat-label>{{ 'common.field.phone' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input type="text" maxlength="40" matInput formControlName="phoneNumber" />
            <mat-error
              *ngIf="
                distributorForm?.controls['phoneNumber'].hasError('required') &&
                distributorForm?.controls['phoneNumber'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
        <div class="p-2 col-lg-3 col-md-6 col-sm-6">
          <mat-label>{{ 'common.field.email' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input type="text" maxlength="40" matInput formControlName="emailId" />
            <mat-error
              *ngIf="
                distributorForm.controls['emailId'].hasError('required') &&
                distributorForm.controls['emailId'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
            <mat-error
              *ngIf="
                distributorForm.controls['emailId'].errors?.email &&
                distributorForm.controls['emailId'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
      </div>
      <mat-card-title>{{ 'common.field.transactionDetails' | translate }}</mat-card-title>
      <div class="row no-gutters m-t-10">
        <!-- discountPercent -->
        <div class="p-2 col-lg-3 col-md-6 col-sm-6">
          <mat-label>{{ 'common.field.percDiscount' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input type="text" maxlength="40" matInput formControlName="discountPercent" />
            <mat-error
              *ngIf="
                distributorForm?.controls['discountPercent'].hasError('required') &&
                distributorForm?.controls['discountPercent'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
        <!-- allowedBalance -->
        <div class="p-2 col-lg-3 col-md-6 col-sm-6">
          <mat-label>{{ 'common.field.allowedBalance' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input type="number" matInput formControlName="allowedBalance" />
            <mat-error
              *ngIf="
                distributorForm?.controls['allowedBalance'].hasError('required') &&
                distributorForm?.controls['allowedBalance'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
        <!-- paymentToleranceDays -->
        <div class="p-2 col-lg-3 col-md-6 col-sm-6">
          <mat-label>{{ 'common.field.allowedDays' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input type="text" maxlength="40" matInput formControlName="paymentToleranceDays" />
            <mat-error
              *ngIf="
                distributorForm?.controls['paymentToleranceDays'].hasError('required') &&
                distributorForm?.controls['paymentToleranceDays'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
        <!-- commission -->
        <div class="p-2 col-lg-3 col-md-6 col-sm-6">
          <mat-label>{{ 'common.field.commission' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input type="number" matInput formControlName="commission" />
            <mat-error
              *ngIf="
                distributorForm?.controls['commission'].hasError('required') &&
                distributorForm?.controls['commission'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
        <!-- yearlyTarget -->
        <div class="p-2 col-lg-3 col-md-6 col-sm-6">
          <mat-label>{{ 'common.field.yrlyTarget' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input type="number" matInput formControlName="yearlyTarget" />
            <mat-error
              *ngIf="
                distributorForm?.controls['yearlyTarget'].hasError('required') &&
                distributorForm?.controls['yearlyTarget'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="row">
        <div class="col-lg-12">
          <mat-slide-toggle
            class="m-l-10 m-t-5"
            [labelPosition]="'after'"
            formControlName="isFreezed">
            {{ 'common.field.freezed' | translate }}
          </mat-slide-toggle>
        </div>
      </div>
      <div class="row no-gutters">
        <div class="col-lg-12">
          <app-address #addressForm [noValidators]="true" formControlName="address"></app-address>
        </div>
      </div>
    </form>
  </mat-card>
  <div class="text-center">
    <button
      class="m-l-10"
      *ngIf="isCreateMode"
      (click)="onSubmit($event); (false)"
      type="button"
      mat-stroked-button
      color="primary">
      {{ 'common.buttons.submit' | translate }}
    </button>
    <button
      class="m-l-10"
      *ngIf="isCreateMode"
      [routerLink]="['../']"
      type="button"
      mat-stroked-button
      color="warn">
      {{ 'common.buttons.cancel' | translate }}
    </button>
    <button
      class="m-l-10"
      *ngIf="isEditMode"
      (click)="onSubmit($event); (false)"
      type="button"
      mat-flat-button
      color="primary">
      {{ 'common.buttons.save' | translate }}
    </button>
    <button
      class="m-l-10"
      *ngIf="isEditMode"
      [routerLink]="['../../']"
      type="button"
      mat-stroked-button
      color="warn">
      {{ 'common.buttons.cancel' | translate }}
    </button>
    <button
      class="m-l-10"
      *ngIf="isViewMode"
      [routerLink]="['../../']"
      type="button"
      mat-stroked-button
      color="primary">
      {{ 'common.buttons.back' | translate }}
    </button>
  </div>
</ng-container>
