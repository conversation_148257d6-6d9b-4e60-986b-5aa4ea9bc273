.mat-mdc-card-avatar {
  height: 150px;
  width: 150px;
  flex-shrink: 0;
  object-fit: cover;
  border-radius: 0;
}

.button-centre {
  display: flex;
  justify-content: center;
  padding-top: 10px;
}

.example-action-buttons {
  display: flex;
  gap: 10px;
  padding-bottom: 20px;
}

.radio-button-margin {
  margin: 10px;
}

.p-m {
  vertical-align: middle;
  margin-top: 10px;
}

.form {
  margin-top: 30px;
}
.select {
  align-items: center;
  vertical-align: middle;
  margin-top: 5px;
}
.title {
  text-decoration-style: solid;
  text-shadow: 0cap;
}
