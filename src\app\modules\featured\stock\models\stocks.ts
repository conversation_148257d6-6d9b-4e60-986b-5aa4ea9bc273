import { IInventory } from '../../catalog/models/product';

export interface IStock {
  issueDate: string;
  isExternalTransfer: boolean;
  grandTotal: number;
  warehouseIdExternal: number;
  transferType: TransferType;
  items: IItem[];
  costCentreId: number;
  notes: string;
  partner: IBranchPartnerResponse;
}

export enum TransferType {
  OUTWARD_TRANSFER = 'OUTWARD_TRANSFER',
  INWARD_TRANSFER = 'INWARD_TRANSFER',
}

export interface IBranchPartnerResponse {
  id: number;
  nameArabic: string;
  nameEnglish: string;
  accountNumber: number;
  accountId: number;
  parentAccountId: number;
  branchId: number;
  partnerBranchId: number;
  partnerBranchNameArabic: string;
  partnerBranchNameEnglish: string;
}

export interface IItem {
  transferItemId: number;
  itemId: number;
  itemCode: string;
  itemNameEnglish: string | null;
  itemNameArabic: string | null;
  quantity: number;
  qtyToAdd: number | null;
  itemUnitId: number;
  price: number;
  warehouseId: number;
  warehouseName: string;
  warehouseIdIn: number | null;
  warehouseIdOut: number | null;
  itemUnitIdIn: number | null;
  itemUnitIdOut: number | null;
  subtotal: number;
  unitName: string;
  priceType: number;
  product?: IInventory;
}

export enum TransferMode {
  Inward = 'Inward',
  Outward = 'Outward',
}

export interface PaginatedTransferListResponse {
  searchTimestamp: string;
  totalRecordsCount: number;
  totalPages: number;
  morePages: boolean;
  transfers: TransferListResponse[];
}

export interface TransferListResponse {
  id: number;
  branchIdIn: number;
  branchIdOut: number;
  documentNumberIn: string;
  documentNumberOut: string;
  issueDate: Date;
  creditAmount: number;
  isExternalTransfer: boolean;
  grandTotal: number;
  accountBasic: AccountBasicDTO;
}
export interface AccountBasicDTO {
  accountId: number;
  accountNumber: number;
  nameArabic: string;
  nameEnglish: string;
}

export interface StockRead {
  branchIdIn: number;
  branchIdOut: number;
  documentNumberIn: string;
  documentNumberOut: string;
  issueDate: Date;
  isExternalTransfer: boolean;
  grandTotal: number;
  warehouseIdExternal: number;
  transferType: null;
  items: IItem[];
  partner: Partial<IBranchPartnerResponse>;
}
