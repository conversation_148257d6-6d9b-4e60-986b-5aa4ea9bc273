export interface IUser {
  id: string;
  userName: string;
  firstName: string;
  lastName: string;
  email: string;
  emailConfirmed: boolean;
  curentPassword: string;
  password: string;
  confirmPassword: string;
  isActive: boolean;
  phoneNumber: string;
  phoneNumberConfirmed: boolean;
  profilePictureUrl: string;
  username: string;
  emailId: string;
  roleNames: string[];
  defaultBranchId: number;
  branchIds: number[];
  warehouseIds: number[];
  roleIds: number[];
  active: boolean;
  isPosUser: boolean;
}
