<form class="readOnly" [formGroup]="paymentForm" autocomplete="off">
  <!-- cashType -->
  <div class="col-12 control-border" *ngIf="paymentForm?.controls['cashType']?.value">
    <div class="row align-items-center">
      <div class="col-md-6 col-sm-12 m-t-10">
        <mat-slide-toggle
          [labelPosition]="'after'"
          [disabled]="true"
          color="accent"
          formControlName="cashType"
          >{{ 'paymentsType.cash' | translate }}</mat-slide-toggle
        >
      </div>
      <div class="col-md-6 col-sm-12 m-b-3">
        <mat-label>{{ 'paymentsType.cashAccounts' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <mat-select formControlName="cashAccountId">
            <mat-option *ngFor="let accountName of cashAccounts" [value]="accountName.accountId">
              {{ accountName | localized }}
            </mat-option>
          </mat-select>
          <mat-error
            *ngIf="
              paymentForm?.controls['cashAccountId']?.hasError('required') &&
              paymentForm?.controls['cashAccountId']?.touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>
    </div>
  </div>
  <!-- cashType -->
  <!-- creditType -->
  <div class="col-12 control-border" *ngIf="paymentForm?.controls['creditType']?.value">
    <div class="row align-items-center">
      <div class="col-md-6 col-sm-12 m-t-10">
        <mat-slide-toggle [labelPosition]="'after'" color="accent" formControlName="creditType">{{
          'paymentsType.credit' | translate
        }}</mat-slide-toggle>
      </div>
      <div class="col-md-6 col-sm-12 m-t-10">
        <mat-label>{{ 'paymentsType.amount' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <input matInput readonly type="number" formControlName="creditAmount" />
        </mat-form-field>
      </div>
    </div>
  </div>
  <!-- creditType -->
  <!-- return date and description -->
  <div class="col-12 control-border">
    <div class="row align-items-center">
      <div class="col-md-6 col-sm-12">
        <mat-label>{{ 'paymentsType.returnDate' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <input
            [min]="salesDate"
            [matDatepicker]="creditNotePicker"
            matInput
            formControlName="creditNoteDate" />
          <mat-datepicker-toggle [for]="creditNotePicker" matSuffix></mat-datepicker-toggle>
          <mat-datepicker #creditNotePicker></mat-datepicker>
          <mat-error
            *ngIf="
              paymentForm?.controls['creditNoteDate']?.hasError('required') &&
              paymentForm?.controls['creditNoteDate']?.touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>
      <div class="col-md-6 col-sm-12">
        <mat-label>{{ 'paymentsType.returnText' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <textarea
            #streetName
            #autosize="cdkTextareaAutosize"
            maxlength="100"
            type="text"
            matInput
            formControlName="creditNoteText"
            cdkTextareaAutosize
            cdkAutosizeMaxRows="5"></textarea>
        </mat-form-field>
      </div>
    </div>
  </div>
  <!-- return date and description -->
</form>
