import { Component, Inject } from '@angular/core';
import { FormBuilder, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { TranslateService } from '@ngx-translate/core';
import { PaymentService } from 'src/app/core/api/trading/payment.service';
import { VoucherInvoice } from 'src/app/core/interfaces/payment';

@Component({
  selector: 'app-voucher-list-invoice',
  templateUrl: './voucher-list-invoice.component.html',
  styleUrls: ['./voucher-list-invoice.component.scss'],
})
export class VoucherListInvoiceComponent {
  voucherForm: UntypedFormGroup = this.fb.group({
    invoiceNumber: new UntypedFormControl(null),
    invoiceDate: new UntypedFormControl(null),
    accountNumber: new UntypedFormControl(null),
    name: new UntypedFormControl(null),
  });
  displayedColumns: string[] = [
    'voucherNumber',
    'voucherDate',
    'paidAmount',
    'distributorAccountId',
    'costCentreId',
    'paymentMethod',
    'notes',
  ];
  dataSource = new MatTableDataSource<VoucherInvoice>();
  isLoading = true;
  total = 0;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private paymentService: PaymentService,
    private dialogRef: MatDialogRef<VoucherListInvoiceComponent>,
    private fb: FormBuilder,
    private translate: TranslateService
  ) {}

  ngOnInit() {
    this.fetchData();
  }

  fetchData() {
    this.paymentService
      .getVoucherForInvoice(this.data.id, this.data.voucherType)
      .subscribe(data => {
        this.dataSource.data = data;
        data.forEach(value => (this.total = value.paidAmount + this.total));
        this.voucherForm.patchValue({
          invoiceNumber: data[0].invoiceNumber,
          invoiceDate: data[0].invoiceDate,
          name:
            this.translate.currentLang === 'ar'
              ? data[0]?.account.nameArabic
              : data[0]?.account?.nameEnglish,
          accountNumber: data[0]?.account.accountNumber,
        });
        this.isLoading = false;
      });
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
  }

  onNoClick(): void {
    this.dialogRef.close();
  }

  getPaymentType(paymentType: number) {
    switch (paymentType) {
      case 0:
        return 'paymentsType.cash';
      case 1:
        return 'paymentsType.card';
      case 2:
        return 'paymentsType.bank';
    }
  }
}
