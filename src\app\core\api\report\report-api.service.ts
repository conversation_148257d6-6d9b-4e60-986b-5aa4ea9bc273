import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../../../environments/environment';

@Injectable()
export class ReportApiService {
  baseUrl = environment.apiUrl + 'reports/inventories';
  accountingBaseUrl = environment.apiUrl + 'reports/accounts';
  salesBaseUrl = environment.apiUrl + 'reports/sales';
  purchaseBaseUrl = environment.apiUrl + 'reports/purchase';

  constructor(private http: HttpClient) {}

  public getStockReports(params: HttpParams) {
    return this.http.get(this.baseUrl + '/stock-value', {
      withCredentials: true,
      responseType: 'arraybuffer',
      params: params,
    });
  }
  getInventoryJasperReports(params: HttpParams, endPoint: string) {
    return this.http.get(this.baseUrl + endPoint, {
      withCredentials: true,
      responseType: 'arraybuffer',
      params: params,
    });
  }
  getAccountingJasperReports(params: HttpParams, endPoint: string) {
    return this.http.get(this.accountingBaseUrl + endPoint, {
      withCredentials: true,
      responseType: 'arraybuffer',
      params: params,
    });
  }
  public getPriceReports(params: HttpParams) {
    return this.http.get(this.baseUrl + '/price-list', {
      withCredentials: true,
      responseType: 'arraybuffer',
      params: params,
    });
  }

  public getInventoryReports(params: HttpParams, endPoint: string) {
    return this.http.get(this.baseUrl + endPoint, {
      withCredentials: true,
      params: params,
    });
  }

  getAccountingReports(params: HttpParams, endPoint: string) {
    return this.http.get(this.accountingBaseUrl + endPoint, {
      withCredentials: true,
      params: params,
    });
  }

  getSalesReports(params: HttpParams, endPoint: string) {
    return this.http.get(this.salesBaseUrl + endPoint, {
      withCredentials: true,
      params: params,
    });
  }
  getAllReportType(params: HttpParams) {
    return this.http.get(environment.apiUrl + 'reports/template', {
      withCredentials: true,
      params: params,
    });
  }

  getTransactionReport(params: HttpParams) {
    console.log('Report Api Service');
    return this.http.get(environment.apiUrl + 'reports/sales/invoice', {
      withCredentials: true,
      responseType: 'arraybuffer',
      params: params,
    });
  }

  getPurchaseReports(params: HttpParams, endPoint: string) {
    return this.http.get(this.purchaseBaseUrl + endPoint, {
      withCredentials: true,
      params: params,
    });
  }

  createReportSetup(params: HttpParams, body: any) {
    return this.http.post(environment.apiUrl + 'reports/template/setup', body, {
      params: params,
    });
  }

  getReportSetup(params: HttpParams) {
    return this.http.get(environment.apiUrl + 'reports/template/setup', {
      withCredentials: true,
      params: params,
    });
  }
}
