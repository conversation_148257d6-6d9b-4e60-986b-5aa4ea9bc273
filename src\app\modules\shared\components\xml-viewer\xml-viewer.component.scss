/* xml-modal.component.css */
pre {
  font-family: monospace; /* Use monospace font for code-like appearance */
  white-space: pre-wrap; /* Preserve white-space and line breaks */
  word-break: break-word; /* Allow long lines to break */
  direction: ltr; /* Ensure text direction is left-to-right */
  text-align: left; /* Align text to the left */
  user-select: none; /* Prevent text selection */
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Safari */
  -khtml-user-select: none; /* Konqueror HTML */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none; /* Non-prefixed version, currently supported by Chrome, Edge, Opera and Firefox */
  pointer-events: none; /* Prevent click events */
}
