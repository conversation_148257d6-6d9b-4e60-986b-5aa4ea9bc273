import { Directionality } from '@angular/cdk/bidi';
import { Component, Input, OnD<PERSON>roy, OnInit, TrackByFunction } from '@angular/core';
import { MatTabChangeEvent } from '@angular/material/tabs';
import { ActivatedRoute } from '@angular/router';
import {
  GetPermissionListResultDto,
  PermissionGrantInfoDto,
  PermissionGroupDto,
} from 'src/app/core/interfaces/permission';
import { PermissionService } from '../../services/permission.service';
import { ActionType } from 'src/app/core/enums/actionType';

type PermissionWithStyle = PermissionGrantInfoDto & {
  style: string;
};

@Component({
  selector: 'app-permission',
  templateUrl: './permission.component.html',
  styleUrls: ['./permission.component.scss'],
})
export class PermissionComponent implements OnInit, OnDestroy {
  @Input() allowedPermissions: string[];
  @Input() roleName: string;
  @Input() mode: ActionType;
  data: GetPermissionListResultDto = { groups: [], entityDisplayName: null };
  selectedGroup: PermissionGroupDto;
  permissions: PermissionGrantInfoDto[] = [];
  selectThisTab = false;
  selectAllTab = false;
  selectAllTabIntermediate = false;
  isLoading = true;
  editMode = false;
  trackByFn: TrackByFunction<PermissionGroupDto> = (_, item) => item.name;

  constructor(
    private permissionService: PermissionService,
    private route: ActivatedRoute,
    private direction: Directionality
  ) {}

  get isEditMode(): boolean {
    return this.mode === ActionType.edit;
  }
  get isViewMode(): boolean {
    return this.mode === ActionType.view;
  }

  get isCreateMode(): boolean {
    return this.mode === ActionType.create;
  }

  get selectedGroupPermissions(): PermissionWithStyle[] {
    if (!this.selectedGroup) return [];
    const margin = `margin-${this.direction.value === 'rtl' ? 'right' : 'left'}.px`;

    const permissions = this.data.groups.find(
      group => group.name === this.selectedGroup.name
    ).permissions;

    return permissions.map(
      permission =>
        ({
          ...permission,
          style: { [margin]: this.findMargin(permissions, permission) },
          isGranted: this.permissions.find(per => per.name === permission.name).isGranted,
        } as unknown as PermissionWithStyle)
    );
  }

  ngOnDestroy(): void {}

  ngOnInit(): void {
    console.log('this.noteService.getPermissions();', this.permissionService.getPermissions());
    this.isLoading = true;
    this.permissions = null;
    this.route.params.subscribe(params => {
      if (params['id']) {
        this.editPermissions();
      } else {
        this.newPermissions();
      }
    });
  }

  newPermissions(): void {
    this.data = null;
    this.data = this.permissionService.getPermissions();
    this.selectedGroup = this.permissionService.getPermissions().groups[0];
    this.permissions = this.getPermissions(this.permissionService.getPermissions().groups);
    this.isLoading = false;
    this.setTabCheckboxState();
    this.setGrantCheckboxState();
  }

  editPermissions(): void {
    const permissionsConfig: GetPermissionListResultDto =
      this.permissionService.getPermissionsFromUser(this.allowedPermissions);
    this.data = permissionsConfig;
    this.isLoading = false;
    this.selectedGroup = this.data.groups[0];
    this.permissions = this.getPermissions(this.data.groups);
    this.setTabCheckboxState();
    this.setGrantCheckboxState();
  }

  getChecked(name: string) {
    this.permissions.find(per => per.name === name) || { isGranted: false };
    return (this.permissions.find(per => per.name === name) || { isGranted: false }).isGranted;
  }

  onClickCheckbox(clickedPermission: PermissionGrantInfoDto, value: boolean) {
    setTimeout(() => {
      this.permissions = this.permissions.map(per => {
        if (clickedPermission.name === per.name) {
          return { ...per, isGranted: value };
        } else if (clickedPermission.name === per.parentName) {
          return { ...per, isGranted: value };
        } else if (clickedPermission.parentName === per.name && value) {
          return { ...per, isGranted: true };
        }
        return per;
      });
      this.selectThisTab = this.selectedGroupPermissions.every(t => t.isGranted);
      this.setGrantCheckboxState();
    }, 0);
  }

  setTabCheckboxState() {
    const selectedPermissions = this.selectedGroupPermissions.filter(per => per.isGranted);
    if (selectedPermissions.length === this.selectedGroupPermissions.length) {
      this.selectThisTab = true;
    } else if (selectedPermissions.length === 0) {
      this.selectThisTab = false;
    }
  }

  setGrantCheckboxState() {
    const selectedAllPermissions = this.permissions.filter(per => per.isGranted);
    if (selectedAllPermissions.length === this.permissions.length) {
      this.selectAllTabIntermediate = false;
      this.selectAllTab = true;
    } else if (selectedAllPermissions.length === 0) {
      this.selectAllTabIntermediate = false;
      this.selectAllTab = false;
    } else {
      this.selectAllTabIntermediate = true;
    }
  }

  onClickSelectThisTab() {
    this.selectedGroupPermissions.forEach(permission => {
      const index = this.permissions.findIndex(per => per.name === permission.name);
      this.permissions = [
        ...this.permissions.slice(0, index),
        { ...this.permissions[index], isGranted: !this.selectThisTab },
        ...this.permissions.slice(index + 1),
      ];
    });

    this.setGrantCheckboxState();
  }

  onClickSelectAll(value) {
    this.permissions = this.permissions.map(permission => ({
      ...permission,
      isGranted: value,
    }));

    this.selectThisTab = value;
  }

  onChangeGroup(tabChangeEvent: MatTabChangeEvent) {
    this.selectedGroup = this.permissionService.getPermissions().groups[tabChangeEvent.index];
    this.setTabCheckboxState();
  }

  initModal() {
    this.setTabCheckboxState();
    this.setGrantCheckboxState();
  }

  getAssignedCount(groupName: string) {
    return this.permissions.reduce(
      (acc, val) => (val.permissionGroupName === groupName && val.isGranted ? acc + 1 : acc),
      0
    );
  }

  findMargin(permissions: PermissionGrantInfoDto[], permission: PermissionGrantInfoDto) {
    const parentPermission = permissions.find(per => per.name === permission.parentName);

    if (parentPermission && parentPermission.parentName) {
      let margin = 20;
      return (margin += this.findMargin(permissions, parentPermission));
    }

    return parentPermission ? 20 : 0;
  }

  getPermissions(groups: PermissionGroupDto[]): PermissionGrantInfoDto[] {
    return groups.reduce((acc, val) => [...acc, ...val.permissions], []);
  }

  someComplete(name: string): boolean {
    return this.selectedGroupPermissions.some(data => !data.isGranted) && !this.selectThisTab;
  }

  setAll(completed: boolean, group: PermissionGroupDto) {
    this.onClickDeSelectAll(completed, group);
  }

  onClickDeSelectAll(value: boolean, group: PermissionGroupDto) {
    group.permissions.forEach(permission => {
      const index = this.permissions.findIndex(per => per.name === permission.name);
      this.permissions = [
        ...this.permissions.slice(0, index),
        { ...this.permissions[index], isGranted: value },
        ...this.permissions.slice(index + 1),
      ];
    });
    this.selectThisTab = value;
    if (!value) {
      this.selectAllTabIntermediate = true;
      this.selectAllTab = false;
    } else {
      this.setGrantCheckboxState();
    }
  }

  updateAllComplete(permission: PermissionGrantInfoDto, value: boolean) {
    this.onClickCheckbox(permission, value);
  }

  public getRolePermissions(): string[] {
    const permissions = this.permissions
      .filter(per => per.isGranted && per?.parentName !== null)
      .map(data => data.name);
    return [...new Set([...permissions])];
  }
}
