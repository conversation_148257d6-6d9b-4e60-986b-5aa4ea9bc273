import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ReportDashboardComponent } from './components/dashboard/report-dashboard.component';
import { PriceReportComponent } from './components/price-report/price-report.component';
import { StockReportComponent } from './components/stock-report/stock-report.component';
import { AccountsReportsComponent } from '../accounts/components/account-reports/accounts-reports.component';
import { PermissionGuard } from '../../core/core/guards/permission.guard';
import { AccountListReportsComponent } from '../accounts/components/account-list-reports/account-list-reports.component';
import { AccountStatementReportsComponent } from '../accounts/components/account-statement-reports/account-statement-reports.component';
import { InventoryReportComponent } from './components/inventory-report/inventory-report.component';
import { AccountingReportComponent } from './components/accounting-report/accounting-reports.component';
import { ZatcaInvoiceStatusComponent } from '../../shared/components/zatca-invoice-status/zatca-invoice-status.component';
import { SalesReportComponent } from './components/sales-report/sales-reports.component';
import { PurchaseReportsComponent } from './components/purchase-reports/purchase-reports.component';
import { ReportSetupComponent } from './components/report-setup/report-setup.component';
import { InventoryReportsComponent } from './components/inventory-reports/inventory-reports.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'searchReports',
    pathMatch: 'full',
  },
  {
    path: 'searchReports',
    component: ReportDashboardComponent,
    data: {
      title: 'Reports Dashboard',
      urls: [{ title: 'Main Dashboard', url: '/dashboard' }],
      allowedPermissions: ['ReportsManagement', 'AllPermissions'],
    },
  },
  {
    path: 'stockReports',
    component: StockReportComponent,
    data: {
      title: 'Stock Reports',
      urls: [{ title: 'Reports Dashboard', url: '/reports' }],
      allowedPermissions: ['ReportsManagement.StockValueReports', 'AllPermissions'],
    },
  },
  {
    path: 'priceReports',
    component: PriceReportComponent,
    data: {
      title: 'Price Reports',
      urls: [{ title: 'Reports Dashboard', url: '/reports' }],
      allowedPermissions: ['ReportsManagement.PriceReports', 'AllPermissions'],
    },
  },
  {
    path: 'inventoryReports',
    component: InventoryReportsComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Inventory Reports',
      urls: [{ title: 'Reports Dashboard', url: '/reports' }],
      allowedPermissions: ['Accounting.Accounts', 'AllPermissions'],
    },
  },
  {
    path: 'salesReports',
    component: SalesReportComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Sales Reports',
      urls: [{ title: 'Reports Dashboard', url: '/reports' }],
      allowedPermissions: ['Accounting.Accounts', 'AllPermissions'],
    },
  },
  {
    path: 'reportsSetup',
    component: ReportSetupComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Reports Setup',
      urls: [{ title: 'Reports Dashboard', url: '/reports' }],
      allowedPermissions: ['Accounting.Accounts', 'AllPermissions'],
    },
  },
  {
    path: 'accountingReports',
    component: AccountingReportComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Accounting Reports',
      urls: [{ title: 'Reports Dashboard', url: '/reports' }],
      allowedPermissions: ['Accounting.Accounts', 'AllPermissions'],
    },
  },
  {
    path: 'accountsReports',
    component: AccountsReportsComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Accounts Reports',
      urls: [{ title: 'Reports Dashboard', url: '/reports' }],
      allowedPermissions: ['Accounting.Accounts', 'AllPermissions'],
    },
  },
  {
    path: 'accountsList',
    component: AccountListReportsComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Accounts List Reports',
      urls: [{ title: 'Accounts reports', url: '/reports/accountsReports' }],
      allowedPermissions: ['Accounting.Accounts', 'AllPermissions'],
    },
  },
  {
    path: 'accountsStatement',
    component: AccountStatementReportsComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Accounts Statement Reports',
      urls: [{ title: 'Accounts reports', url: '/reports/accountsReports' }],
      allowedPermissions: ['Accounting.Accounts', 'AllPermissions'],
    },
  },
  {
    path: 'zatcaReports',
    component: ZatcaInvoiceStatusComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Zatca einvoice status reports',
      urls: [{ title: 'Zatca reports', url: '/reports/zatcaReports' }],
      allowedPermissions: [],
    },
  },
  {
    path: 'purchaseReports',
    component: PurchaseReportsComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Purchase Reports',
      urls: [{ title: 'Reports Dashboard', url: '/reports' }],
      allowedPermissions: ['Accounting.Accounts', 'AllPermissions'],
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ReportingRoutingModule {}
