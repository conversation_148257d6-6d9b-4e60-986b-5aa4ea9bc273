import { HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { BranchApiService } from 'src/app/core/api/company/branch-api.service';
import { Branch } from '../../catalog/models/branch';
import { BranchParams } from '../models/branchParams';
import { ILoginBranches } from 'src/app/modules/core/core/models/login';

@Injectable()
export class BranchService {
  constructor(private branchApiService: BranchApiService) {}

  getAllBranches(branchParams: BranchParams): Observable<Branch[]> {
    const params = this.getParams(branchParams);
    return this.branchApiService.getAllBranches(params).pipe(map((response: Branch[]) => response));
  }
  getAllBranchesWithYear(branchParams: BranchParams): Observable<Branch[]> {
    const params = this.getParams(branchParams);
    return this.branchApiService
      .getAllBranchesWithYear(params)
      .pipe(map((response: Branch[]) => response));
  }
  getAllLoggedBranches(): Observable<ILoginBranches[]> {
    return this.branchApiService
      .getAllLoggedInBranches()
      .pipe(map((response: ILoginBranches[]) => response));
  }

  getBranchById(branchParams: BranchParams, branchId: string): Observable<Branch> {
    const params = this.getParams(branchParams);
    return this.branchApiService
      .getBranchById(params, branchId)
      .pipe(map((response: Branch) => response));
  }

  createBranch(branchParams: BranchParams, branch: Branch): Observable<any> {
    const params = this.getParams(branchParams);
    return this.branchApiService
      .createBranch(params, branch)
      .pipe(map((response: any) => response));
  }

  updateBranch(branchParams: BranchParams, branchId: string, branch: Branch): Observable<any> {
    const params = this.getParams(branchParams);
    return this.branchApiService
      .updateBranch(params, branchId, branch)
      .pipe(map((response: any) => response));
  }

  deleteBranch(branchId: string) {
    const params = new HttpParams();
    return this.branchApiService
      .deleteBranch(params, branchId)
      .pipe(map((response: Branch[]) => response));
  }

  importData(formData: FormData) {
    const params = new HttpParams();
    return this.branchApiService.importBranch(params, formData).pipe(
      tap((result: ArrayBuffer) => {
        const fileURL = URL.createObjectURL(new Blob([result]));
        const link = document.createElement('a');
        link.href = fileURL;
        link.setAttribute('download', 'Branch_List.xls');
        document.body.appendChild(link);
        link.click();
      })
    );
  }

  getParams(branchParams: BranchParams): HttpParams {
    let params = new HttpParams();
    if (branchParams.searchString)
      params = params.append('searchString', branchParams.searchString);
    if (branchParams.pageNumber)
      params = params.append('pageNumber', branchParams.pageNumber.toString());
    if (branchParams.pageSize) params = params.append('pageSize', branchParams.pageSize.toString());
    if (branchParams.orderBy) params = params.append('orderBy', branchParams.orderBy.toString());
    return params;
  }
}
