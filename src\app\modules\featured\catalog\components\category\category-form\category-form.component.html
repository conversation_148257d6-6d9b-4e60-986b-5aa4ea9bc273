<form *ngIf="!loading" [formGroup]="categoryForm" autocomplete="off">
  <mat-card appearance="outlined">
    <mat-card-title>{{ formTitle | translate }}</mat-card-title>
    <div class="row no-gutters">
      <div class="col-md-4 col-lg-4 col-sm-4 p-2">
        <mat-label> {{ 'productCategory.nameArabic' | translate }}</mat-label>

        <mat-form-field class="w-100" [subscriptSizing]="'fixed'">
          <input matInput formControlName="nameArabic" />
          <mat-error
            *ngIf="
              categoryForm.controls['nameArabic'].hasError('required') &&
              categoryForm.controls['nameArabic'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>
      <div class="col-md-4 col-lg-4 col-sm-4 p-2">
        <mat-label> {{ 'productCategory.nameEnglish' | translate }}</mat-label>

        <mat-form-field class="w-100" [subscriptSizing]="'fixed'">
          <input matInput formControlName="nameEnglish" />
          <mat-error
            *ngIf="
              categoryForm.controls['nameEnglish'].hasError('required') &&
              categoryForm.controls['nameEnglish'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>

      <div class="col-md-4 col-lg-4 col-sm-4 p-2">
        <mat-label>{{ 'productCategory.parentCategory' | translate }}</mat-label>

        <mat-form-field class="w-100" [subscriptSizing]="'fixed'">
          <mat-select formControlName="parentCategoryId">
            <mat-option
              *ngFor="let parentCategory of parentCategories"
              [value]="parentCategory.categoryId"
              >{{ parentCategory | localized }}</mat-option
            >
          </mat-select>
          <mat-error
            *ngIf="
              categoryForm?.controls['parentCategory']?.hasError('required') &&
              categoryForm?.controls['parentCategory']?.touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>
    </div>
  </mat-card>
  <div class="text-center">
    <button
      class="m-l-10"
      *ngIf="!isEditMode"
      (click)="onSubmit($event)"
      mat-stroked-button
      color="primary">
      {{ 'common.submit' | translate }}
    </button>
    <button
      class="m-l-10"
      *ngIf="isEditMode"
      (click)="onSubmit($event)"
      mat-stroked-button
      color="primary">
      {{ 'common.submit' | translate }}
    </button>
    <button
      class="m-l-10"
      *ngIf="!isEditMode"
      [routerLink]="['../']"
      mat-stroked-button
      color="warn">
      {{ 'common.cancel' | translate }}
    </button>
    <button
      class="m-l-10"
      *ngIf="isEditMode"
      [routerLink]="['../../']"
      mat-stroked-button
      color="warn">
      {{ 'common.cancel' | translate }}
    </button>
  </div>
</form>
