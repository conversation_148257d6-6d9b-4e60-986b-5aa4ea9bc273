import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { SupplierParams } from '../../models/params/supplierParams';
import { ISupplier } from '../../interfaces/supplier';

@Injectable({
  providedIn: 'root',
})
export class SupplierService {
  baseUrl = environment.apiUrl + 'purchase/suppliers';

  constructor(private http: HttpClient) {}

  getAllSuppliers(params: SupplierParams) {
    const getSupplierParams = this.getParams(params);
    return this.http.get(this.baseUrl + '/pages', { params: getSupplierParams });
  }

  getSupplierById(id: string) {
    return this.http.get<ISupplier>(this.baseUrl + `/${id}`);
  }

  createSupplier(supplier: ISupplier) {
    return this.http.post(this.baseUrl, supplier);
  }

  updateSupplier(supplier: ISupplier, id: string) {
    return this.http.put(this.baseUrl + '/' + id, supplier);
  }

  deleteSupplier(id: string) {
    return this.http.delete(this.baseUrl + `/${id}`);
  }

  getParams(supplierParams: SupplierParams): HttpParams {
    let httpParams = new HttpParams();
    if (supplierParams?.searchString)
      httpParams = httpParams.append('searchString', supplierParams.searchString);
    if (supplierParams?.pageNumber)
      httpParams = httpParams.append('pageNumber', supplierParams.pageNumber.toString());
    if (supplierParams?.pageSize)
      httpParams = httpParams.append('pageSize', supplierParams.pageSize.toString());
    if (supplierParams?.orderBy)
      httpParams = httpParams.append('orderBy', supplierParams.orderBy.toString());
    return httpParams;
  }
}
