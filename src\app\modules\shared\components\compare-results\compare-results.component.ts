import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-compare-results',
  templateUrl: './compare-results.component.html',
  styleUrls: ['./compare-results.component.scss'],
})
export class CompareResultsComponent {
  constructor(
    public dialogRef: MatDialogRef<CompareResultsComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit(): void {
    console.log(this.data);
  }
  onNoClick(): void {
    this.dialogRef.close();
  }
}
