import { Component } from '@angular/core';
import { ProductModuleService } from '../../../home/<USER>/product-module.service';
import { DashboardModulesHolder } from 'src/app/modules/shared/components/dashboard-modules-holder/modulesHolder';

@Component({
  selector: 'app-ownerdashboard',
  templateUrl: './ownerdashboard.component.html',
  styleUrls: ['./ownerdashboard.component.scss'],
})
export class OwnerdashboardComponent {
  productModulesList: DashboardModulesHolder[] = [
    {
      moduleName: 'navigationMenus.tenantConfiguration',
      moduleDescription: 'Manage Sales.',
      modulePermission: ['Provider.TenantManagement', 'AllPermissions'],
      moduleImage: 'store',
      moduleRouterLink: '../tenants',
      moduleButtonAction: 'POS',
      moduleType: 'subModule',
    },
    {
      moduleName: 'navigationMenus.zatcaListings',
      moduleDescription: 'Manage Sales.',
      modulePermission: ['Provider.TenantManagement', 'AllPermissions'],
      moduleImage: 'store',
      moduleRouterLink: '../tenants/zatcalistings',
      moduleButtonAction: 'POS',
      moduleType: 'subModule',
    },
  ];

  constructor() {}

  ngOnInit(): void {}
}
