// Variables
$invoice-border-color: #cccccc;
$invoice-bg-color: #e6f2ff;
$invoice-blue-font: #0056b3;
$report-font-family: Arial, sans-serif;
$report-font-size: 12px;
$border-thickness: 1px;
$border-color: #333;
$margin-sections: 2px;

// Base container
.invoice-container {
  width: 100%;
  max-width: 210mm;
  margin: 0 auto;
  padding: 10px;
  font-family: $report-font-family;
  font-size: $report-font-size;
  color: #333;
  background-color: white;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  position: relative;

  @media print {
    box-shadow: none;
    margin: 0;
    padding: 0;
    width: 100%;
    background-color: white !important;
    overflow: visible;
  }

  // Items Table
  .items-table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
    border: $border-thickness solid $invoice-border-color;

    th,
    td {
      border: $border-thickness solid $invoice-border-color;
      text-align: center;
      vertical-align: middle;
      padding: 1px 2px;
      font-weight: bold;
      line-height: 1.1;
      word-break: break-word;
      white-space: normal;
      overflow-wrap: break-word;
    }

    // Column widths
    th,
    td {
      &:nth-child(1) {
        width: 14%;
      }
      &:nth-child(2) {
        width: 24%;
      }
      &:nth-child(3) {
        width: 6%;
      }
      &:nth-child(4) {
        width: 11%;
      }
      &:nth-child(5) {
        width: 11%;
      }
      &:nth-child(6) {
        width: 11%;
      }
      &:nth-child(7) {
        width: 6%;
      }
      &:nth-child(8) {
        width: 11%;
      }
      &:nth-child(9) {
        width: 11%;
      }
    }

    // Header styles
    th {
      background-color: $invoice-bg-color;
      color: $invoice-blue-font;
      border: $border-thickness solid $invoice-border-color;

      .header-bilingual {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1px;

        .arabic,
        .en {
          font-weight: bold;
          display: block;
          width: 100%;
          line-height: 1;
        }
      }

      .header-currency-badge {
        display: inline-block;
        margin-top: 2px;
        padding: 0;
        background-color: transparent;
        border: none;

        .sar-symbol {
          width: 9px;
          height: 9px;
          vertical-align: middle;
        }
      }
    }

    .right-align {
      text-align: right;
      padding-right: 4px;
    }

    .left-align {
      text-align: left;
      padding-left: 4px;
    }

    .desc-arabic,
    .unit-arabic {
      display: block;
      text-align: right;
      direction: rtl;
      margin-bottom: 1px;
      line-height: 1.1;
      word-break: break-word;
      white-space: normal;
      overflow-wrap: break-word;
    }

    .desc-english,
    .unit-english {
      display: block;
      text-align: left;
      direction: ltr;
      line-height: 1.1;
      word-break: break-word;
      white-space: normal;
      overflow-wrap: break-word;
    }
  }

  .header-bilingual {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .header-currency-badge {
    display: inline-block;
    margin-top: 2px;
    padding: 0;
    background-color: transparent;
    border: none;

    .sar-symbol {
      width: 9px;
      height: 9px;
      vertical-align: middle;
    }
  }
}

// Common text styles
.text-element {
  font-family: $report-font-family !important;
  font-size: $report-font-size !important;
}

.invoice-header,
.header-layout,
.english-address,
.arabic-address,
.logo,
.invoice-title,
.title-table,
.client-info-table,
.client-details-table,
.meta-details-table,
.qr-code-container,
.invoice-items,
.invoice-footer,
.footer-table,
.invoice-notes,
.totals-table,
.total-in-words,
.invoice-footer-info,
.footer-info-table,
.signatures-table,
.generation-info-left,
.generation-info-center,
.generation-info-right,
.arabic,
.client-name-arabic,
.client-name-english,
.desc-arabic,
.desc-english,
.unit-arabic,
.unit-english {
  @extend .text-element;
}

.invoice-header {
  margin-top: $margin-sections;
  margin-bottom: $margin-sections;
}

.header-layout {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border-bottom: $border-thickness solid $invoice-border-color;
  margin-top: $margin-sections;
  margin-bottom: $margin-sections;
  padding-bottom: 10px;
}

.address {
  flex: 1;

  h3 {
    color: $invoice-blue-font !important;
    font-weight: bold !important;
    font-size: 16px !important;
  }

  &.english-address {
    padding-right: 15px;
    text-align: left;

    h3 {
      color: $invoice-blue-font;
      font-weight: bold;
      margin: 0 0 5px 0;
    }

    p {
      margin: 0 0 3px 0;
      line-height: 1.5;
    }
  }

  &.arabic-address {
    padding-left: 15px;
    text-align: right;
    direction: rtl;

    h3 {
      color: $invoice-blue-font;
      font-weight: bold;
      margin: 0 0 5px 0;
    }

    p {
      margin: 0 0 3px 0;
      line-height: 1.5;
    }
  }

  .company-info {
    font-weight: bold !important;
    color: #333 !important;
  }

  .contact-detail {
    font-weight: normal !important;
    color: #555 !important;
  }
}

.logo {
  width: 100px;
  flex: 0 0 100px;
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    width: 80px;
    height: auto;
    max-height: 80px;
    object-fit: contain;
  }
}

.title-table {
  width: 100%;
  border-collapse: collapse;
  border: $border-thickness solid $invoice-border-color;

  .title-cell {
    text-align: center;
    padding: 4px;
    background-color: $invoice-bg-color;
    border-bottom: $border-thickness solid $invoice-border-color;

    .title-text {
      color: $invoice-blue-font;
      font-weight: bold;
      margin: 0 5px;
      font-size: 14px;
    }
  }

  .info-row {
    .info-cell {
      padding: 2px 5px;
      border-right: $border-thickness solid $invoice-border-color;

      &:last-child {
        border-right: none;
      }

      &.equal-width {
        width: 50%;
      }

      .info-content {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .left-content {
          font-weight: bold;
          text-align: left;
          flex: 1;
        }

        .right-content {
          font-weight: bold;
          text-align: right;
          min-width: 40%;
        }
      }

      .doc-container {
        width: 100%;
        line-height: 1.2;
        padding: 2px 0;

        .doc-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          margin-bottom: 0;

          .doc-label {
            font-weight: bold;
            text-align: left;
            white-space: nowrap;
          }

          .doc-value {
            font-weight: bold;
            text-align: left;
            flex: 1;
            word-break: break-word;
            white-space: normal;
            padding: 0 5px;
          }

          .doc-arabic-label {
            font-weight: bold;
            text-align: right;
            direction: rtl;
            width: 18%;
            white-space: nowrap;
          }
        }

        .doc-date {
          font-weight: bold;
          font-size: 11px;
          color: #555;
          display: block;
          margin-top: 0;
          padding-left: 12%;
          line-height: 1.1;
        }
      }
    }
  }
}

.client-info-table {
  width: 100%;
  border-collapse: collapse;
  border: $border-thickness solid $invoice-border-color;
  margin-top: $margin-sections;
  margin-bottom: $margin-sections;

  .client-section {
    width: 70%;
    vertical-align: top;
    border-right: $border-thickness solid $invoice-border-color;
  }

  .meta-section {
    width: 30%;
    vertical-align: top;
  }
}

.client-details-table,
.meta-details-table {
  width: 100%;
  border-collapse: collapse;

  tr {
    border-bottom: 1px solid #eee;

    &:last-child {
      border-bottom: none;
    }
  }

  td {
    padding: 2px 3px;
    line-height: 1.1;
    word-break: break-word;
    white-space: normal;
    overflow-wrap: break-word;

    &.label {
      font-weight: bold;
      width: 25%;
      background-color: #f9f9f9;
      text-align: left;
    }

    &.value {
      width: 45%;
      font-weight: bold;

      .client-name-arabic {
        display: block;
        text-align: right;
        direction: rtl;
        margin-bottom: 2px;
        word-break: break-word;
        white-space: normal;
        font-size: 14px !important;
      }

      .client-name-english {
        display: block;
        text-align: left;
        direction: ltr;
        font-weight: bold;
        word-break: break-word;
        white-space: normal;
        font-size: 14px !important;
      }
    }

    &.arabic {
      text-align: right;
      direction: rtl;
      width: 30%;
      background-color: #f9f9f9;
      font-weight: bold;
    }
  }
}

.qr-code-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  text-align: center;
  page-break-inside: avoid;
  break-inside: avoid;
}

::ng-deep .qr-code-container qrcode {
  display: block;
  width: 60%;
  max-width: 60%;
  height: auto;
  margin: 0 auto;
  page-break-inside: avoid;
  break-inside: avoid;

  img {
    display: block;
    width: 100%;
    height: auto;
    max-width: 100%;
    margin: 0 auto;
    page-break-inside: avoid;
    break-inside: avoid;
  }
}

.invoice-items {
  margin-top: $margin-sections;
  margin-bottom: $margin-sections;
}

.invoice-footer {
  margin-top: $margin-sections;
  margin-bottom: $margin-sections;
}

.footer-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: $margin-sections;
  margin-bottom: $margin-sections;

  .notes-section {
    width: 34%;
    vertical-align: top;
    padding: 8px;
    font-size: 12px;
  }

  .totals-section {
    width: 60%;
    vertical-align: top;
  }
}

.invoice-notes {
  .notes-content {
    margin-bottom: 4px;
    border: $border-thickness solid $invoice-border-color;
    border-radius: 3px;
    background-color: #f9f9f9;
    padding: 4px;
  }

  .notes-pre {
    font-family: inherit;
    font-weight: bold;
    white-space: pre-wrap;
    margin: 0;
    padding: 0;
    background-color: transparent;
    border: none;
    line-height: 1.4;
    overflow: auto;
  }

  p {
    margin-bottom: 4px;
  }

  .due-date {
    margin-top: 10px;
    border-top: 1px solid #eee;
    padding-top: 5px;

    p {
      margin: 0;
      font-weight: bold;
    }
  }
}

.totals-table {
  width: 100%;
  border-collapse: collapse;
  border: $border-thickness solid $invoice-border-color;

  tr {
    border-bottom: $border-thickness solid $invoice-border-color;

    &:last-child {
      border-bottom: none;
    }

    &.total-row {
      font-weight: bold;
      background-color: $invoice-bg-color;

      td {
        background-color: $invoice-bg-color !important;

        &.grand-total {
          font-size: 14px;
        }
      }
    }
  }

  td {
    padding: 2px 3px;
    word-break: break-word;
    white-space: normal;
    overflow-wrap: break-word;

    &.label {
      text-align: left;
      width: 25%;
      background-color: #f9f9f9;
      font-weight: bold;
    }

    &.value {
      width: 30%;
      font-weight: bold;
      text-align: right;

      .amount-container {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 2px;

        .amount {
          display: block;
          width: 100%;
          text-align: right;
        }

        .currency-badge {
          display: inline-flex;
          align-items: center;
          order: -1;
          margin-right: 2px;
          background-color: transparent;
          border: none;
          padding: 0;

          .sar-symbol {
            width: 1em;
            height: 1em;
            object-fit: contain;
          }
        }
      }
    }

    &.arabic {
      text-align: right;
      direction: rtl;
      width: 45%;
      background-color: #f9f9f9;
      font-weight: bold;
    }
  }
}

.total-in-words {
  padding: 6px;
  background-color: #f9f9f9;
  font-weight: bold;
  border: $border-thickness solid $invoice-border-color;
  border-top: none;

  .arabic-words {
    text-align: right;
    direction: rtl;
    margin-bottom: 2px;
  }

  .english-words {
    text-align: left;
  }
}

.invoice-footer-info {
  margin-top: $margin-sections;
  margin-bottom: $margin-sections;
}

.footer-info-table {
  width: 100%;
  border-collapse: collapse;
  border: $border-thickness solid $invoice-border-color;
  background-color: #f9f9f9;

  .signature-section {
    padding: 5px;
  }
}

.signatures-table {
  width: 100%;
  border-collapse: collapse;

  td {
    width: 33.33%;
    text-align: center;
    padding: 2px;
    vertical-align: top;
    word-break: break-word;
    white-space: normal;
    overflow-wrap: break-word;
  }

  .qr-code-cell {
    text-align: center;
    vertical-align: middle;

    .footer-qr-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;

      .footer-qr-image {
        width: 60px;
        height: 60px;
        object-fit: contain;
      }
    }
  }

  .prepared-by {
    border-left: $border-thickness solid $invoice-border-color;
    border-right: $border-thickness solid $invoice-border-color;
  }

  .signature-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2px;
    border-bottom: $border-thickness solid #eee;
    padding-bottom: 2px;
  }

  .signature-label {
    font-weight: bold;
    color: #333;
    text-align: left;
  }

  .signature-value {
    margin-bottom: 0;
    font-weight: bold;
    text-align: center;
  }

  .signature-label-ar {
    direction: rtl;
    font-weight: bold;
    text-align: right;
  }

  .signature-line {
    width: 80%;
    height: 1px;
    border-bottom: $border-thickness solid #333;
    margin: 8px auto 2px;
  }

  .page-number {
    margin-top: 10px;
    color: #555;
  }
}

.generation-info-left {
  text-align: left;
  color: #555;
  width: 40%;
}

.generation-info-center {
  text-align: center;
  color: #555;
  width: 20%;
  font-weight: bold;
}

.generation-info-right {
  text-align: right;
  color: #555;
  width: 40%;
}

.arabic {
  direction: rtl;
}

.amount-container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 2px;

  .currency-badge {
    display: inline-flex;
    align-items: center;
    order: -1;
    margin-right: 2px;
    background-color: transparent;
    border: none;
    padding: 0;

    .sar-symbol {
      width: 1em;
      height: 1em;
      object-fit: contain;
    }
  }

  .amount {
    font-variant-numeric: tabular-nums;
  }
}

[dir='rtl'] {
  .amount-container {
    .currency-badge {
      order: 1;
      margin-right: 0;
      margin-left: 2px;
    }
  }

  .header-currency-badge {
    right: auto;
    left: 2px;
  }
}

.action-buttons {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  padding: 16px;

  button {
    display: flex;
    align-items: center;
    gap: 8px;

    mat-icon {
      margin-right: 4px;
    }
  }

  @media print {
    display: none !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    visibility: hidden !important;
    position: absolute !important;
  }
}

@media print {
  html,
  body {
    height: auto !important;
    overflow: visible !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color-adjust: exact !important;
  }

  app-header,
  app-footer,
  .mat-toolbar,
  .mat-sidenav,
  .mat-drawer,
  .mat-drawer-container,
  .mat-drawer-content,
  .action-buttons,
  nav,
  footer,
  button {
    display: none !important;
    height: 0 !important;
    max-height: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    border: 0 !important;
    visibility: hidden !important;
    position: absolute !important;
    overflow: hidden !important;
  }

  body {
    margin: 0 !important;
    padding: 0 !important;
    background-color: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  .invoice-container {
    page-break-inside: avoid;
    page-break-after: auto;
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    max-width: none !important;
    background-color: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    overflow: visible !important;
    height: auto !important;
  }

  .header-layout,
  .invoice-client-info,
  .invoice-footer,
  .invoice-footer-info {
    page-break-inside: avoid !important;
  }

  table {
    page-break-inside: avoid !important;
  }

  tr {
    page-break-inside: avoid !important;
    page-break-after: auto !important;
  }

  thead {
    display: table-header-group !important;
  }

  tfoot {
    display: table-footer-group !important;
  }

  @page {
    size: A4;
    margin: 0;
    padding: 0;
  }

  .items-table {
    width: 100% !important;
    border-collapse: collapse !important;
    border: $border-thickness solid $invoice-border-color !important;
    margin-bottom: 0 !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;

    th,
    td {
      border: $border-thickness solid $invoice-border-color !important;
      line-height: 1.1 !important;
      word-break: break-word !important;
      white-space: normal !important;
      overflow-wrap: break-word !important;
      -webkit-print-color-adjust: exact !important;
      print-color-adjust: exact !important;
    }

    th {
      background-color: $invoice-bg-color !important;
      color: $invoice-blue-font !important;
      border: $border-thickness solid $invoice-border-color !important;
      position: static !important;

      .header-bilingual {
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        gap: 2px !important;
      }

      .header-currency-badge {
        display: inline-block !important;
        margin-top: 2px !important;
        padding: 0 !important;
        background-color: transparent !important;
        border: none !important;
      }
    }

    .right-align {
      text-align: right !important;
      padding-right: 4px !important;
    }

    .left-align {
      text-align: left !important;
      padding-left: 4px !important;
    }
  }

  .title-table .title-cell {
    background-color: $invoice-bg-color !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  .client-info-table,
  .client-details-table,
  .meta-details-table,
  .footer-info-table {
    border-collapse: collapse;
    background-color: #f9f9f9 !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  .client-details-table td.label,
  .client-details-table td.arabic,
  .meta-details-table td.label,
  .footer-info-table td.signature-section {
    border-collapse: collapse;
    background-color: #f9f9f9 !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  .totals-table {
    border: $border-thickness solid $invoice-border-color !important;
    background-color: transparent !important;

    tr.total-row {
      background-color: $invoice-bg-color !important;

      td {
        background-color: $invoice-bg-color !important;
      }
    }
  }

  .invoice-notes .notes-content {
    background-color: #f9f9f9 !important;
    border: $border-thickness solid $invoice-border-color !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  .qr-code-container {
    background-color: #f9f9f9 !important;
    border: $border-thickness solid #eee !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    page-break-inside: avoid !important;
    break-inside: avoid !important;
  }

  ::ng-deep .qr-code-container qrcode {
    display: block !important;
    width: 60% !important;
    max-width: 60% !important;
    height: auto !important;
    margin: 0 auto !important;
    page-break-inside: avoid !important;
    break-inside: avoid !important;

    img {
      display: block !important;
      width: 100% !important;
      height: auto !important;
      max-width: 100% !important;
      margin: 0 auto !important;
      page-break-inside: avoid !important;
      break-inside: avoid !important;
    }
  }

  .mat-mdc-menu-panel,
  .cdk-overlay-container,
  [hidden],
  .hidden-print {
    display: none !important;
    height: 0 !important;
    max-height: 0 !important;
    visibility: hidden !important;
    position: absolute !important;
    overflow: hidden !important;
  }
}
