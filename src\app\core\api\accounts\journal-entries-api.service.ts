import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class JournalEntriesApiService {
  baseUrl = environment.apiUrl + 'company/account/jentry';

  constructor(private http: HttpClient) {}

  getJournalById(journalId: number) {
    return this.http.get(this.baseUrl + '/' + journalId);
  }

  deleteEntry(entryId: string, params: HttpParams) {
    return this.http.delete(this.baseUrl + '/' + entryId, { params: params });
  }

  updateJournalEntries(accountId: number, journalEntries: any) {
    return this.http.put(this.baseUrl + '/' + accountId, journalEntries);
  }

  createJournalEntries(journalEntries: any) {
    return this.http.post(this.baseUrl, journalEntries);
  }

  getAllJournalEntries(params: HttpParams) {
    return this.http.get(this.baseUrl + '/pages', { params: params });
  }

  postJournalById(journalId: number) {
    return this.http.put(this.baseUrl + '/post/' + journalId, { withCredentials: true });
  }
}
