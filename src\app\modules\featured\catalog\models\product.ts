export interface Product {
  itemId: number;
  alternateItems: number[];
  brand: string;
  freeEndDate: Date;
  freeStartDate: Date;
  hasExpirationDate: boolean;
  daysToAlert: number;
  isActive: boolean;
  isItemFree: boolean;
  isPrintFrozen: boolean;
  isSaleFrozen: boolean;
  isTransferFrozen: boolean;
  isGeneralSalesPolicy: boolean;
  isItemWeighed: boolean;
  categoryId: number;
  itemCode: string;
  color: string;
  label: string;
  size: string;
  itemType: number;
  maximumQuantity: number;
  minimumQuantity: number;
  nameArabic: string;
  nameEnglish: string;
  notes: string;
  partNumber: string;
  reasonToFreezePrint: string;
  reasonToFreezeSale: string;
  vat: number;
  warehouseIds: number[];
  //itemUnitResponses?: ProductUnits[];
  itemUnitRequests?: ProductUnits[];
  base64EncodedImageString?: unknown;
}

export interface ProductUnits {
  yearId: number;
  itemId: number;
  branchId: number;
  companyId: number;
  itemUnitId: number;
  unitBarcode: string;
  unitId: number;
  costPrice: number;
  discount: number;
  isGeneralDscntMethod: boolean;
  openPurchasePrice: number;
  purchasePrice: number;
  isGeneralProfitMethod: boolean;
  profit: number;
  retailPrice: number;
  wholesalePrice: number;
  distributorPrice: number;
  transportCost: number;
  avgPurchasePrice: number;
}

export interface IProductSearch {
  searchTimeStamp: Date;
  totalRecordsCount: number;
  totalPages: number;
  morePages: boolean;
  inventories: IInventory[];
}

export interface IInventory {
  warehouseId: number;
  warehouseName: string;
  itemId: number;
  itemName: string;
  itemCode: string;
  nameEnglish: string;
  nameArabic: string;
  partNumber: string;
  category: string;
  vat: number;
  itemUnitId: number;
  unitBarcode: string;
  unitName: string;
  currentQty: number;
  companyId: number;
  branchId: number;
  itemLocation: null;
  lastInventoryCheckDate: null;
  openQty: number;
  reservedQty: number;
  retailPrice: number;
  wholesalePrice: number;
  distributorPrice: number;
  purchasePrice: number;
  avgPurchasePrice: number;
  openPurchasePrice: number;
  yearId: number;
  discount: number;
  isGeneralDscntMethod: boolean;
  totalQuantityPerUnit: number;
  profit: number;
  isGeneralProfitMethod: boolean;
  quantity: number;
  factorRefUom: number;
  totalQuantity: number;
}
