import { IAddress } from 'src/app/modules/shared/components/address/address';

export interface IDistributor {
  distributorId: number;
  accountId: number;
  parentAccountId: number;
  accountNumber: number;
  branchId: number;
  companyId: number;
  nameArabic: string;
  nameEnglish: string;
  isHidden: boolean;
  isFreezed: boolean;
  distributorNote: string;
  hasCashPrivilege: boolean;
  hasCreditPrivilege: boolean;
  hasCardPrivilege: boolean;
  hasTransferPrivilege: boolean;
  discountPercent: number;
  paymentToleranceDays: number;
  allowedBalance: number;
  yearlyTarget: number;
  commission: number;
  phoneNumber: string;
  emailId: string;
  vatNumber: string;
  address?: IAddress;
}

export interface IDistributorResponse {
  searchTimeStamp: Date;
  totalRecordsCount: number;
  totalPages: number;
  morePages: boolean;
  distributors: IDistributor[];
}
