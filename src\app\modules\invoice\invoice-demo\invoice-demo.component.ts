import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { SalesService } from 'src/app/core/api/trading/sales.service';
import { ReportInvoice } from '../../shared/components/invoice-template/invoice.model';

@Component({
  selector: 'app-invoice-demo',
  templateUrl: './invoice-demo.component.html',
  styleUrls: ['./invoice-demo.component.scss'],
})
export class InvoiceDemoComponent implements OnInit {
  isLoading = true;
  error = false;
  invoiceData: ReportInvoice;

  constructor(private route: ActivatedRoute, private salesService: SalesService) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      const documentId = params['documentId'];
      const transactionType = params['transactionType'];
      if (documentId && transactionType) {
        this.fetchSalesDataByDocument(documentId, transactionType);
      } else {
        console.log('No documentId/transactionType or invoiceId provided, using default data');
        this.isLoading = false;
      }
    });
  }

  private fetchSalesDataByDocument(documentId: string, transactionType: string): void {
    this.isLoading = true;

    const params = {
      documentId: documentId,
      transactionType: transactionType,
    };

    console.log('Fetching sales data with params:', params);

    this.salesService.getSalesByDocumentUuid(params).subscribe(
      (salesData: ReportInvoice) => {
        console.log('Sales data fetched successfully:', salesData);
        this.invoiceData = salesData;
        this.isLoading = false;
      },
      (error: any) => {
        console.error('Error fetching sales data:', error);
        this.error = true;
        this.isLoading = false;
      }
    );
  }
}
