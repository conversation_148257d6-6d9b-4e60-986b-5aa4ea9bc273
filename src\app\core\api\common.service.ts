import { Injectable } from '@angular/core';
import { Howl, Howler } from 'howler';
import {
  FormArray,
  AbstractControl,
  FormGroup,
  FormControl,
  ValidatorFn,
  ValidationErrors,
} from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root',
})
export class CommonService {
  constructor(private toastr: ToastrService, private translate: TranslateService) {
    //
  }

  public playSuccessSound() {
    this.genericSuccessToaster();
    const sound = new Howl({
      src: ['assets/success_sound.wav'],
    });
    Howler.volume(1);
    sound.play();
  }

  public playErrorSound() {
    const sound = new Howl({
      src: ['assets/form_error_sound.wav'],
    });
    Howler.volume(1);
    sound.play();
  }

  scrollTo(el: Element): void {
    if (el) {
      el.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }

  scrollToError(): void {
    const firstElementWithError = document.querySelector('.ng-invalid[formControlName]');
    this.scrollTo(firstElementWithError);
    this.playErrorSound();
  }

  deepCloneFormArray(formArray: FormArray): FormArray {
    const clonedArray = new FormArray([]);

    formArray.controls.forEach((control: AbstractControl) => {
      const clonedControl = this.cloneControl(control);
      clonedArray.push(clonedControl);
    });

    return clonedArray;
  }

  cloneControl(control: AbstractControl): AbstractControl {
    if (control instanceof FormArray) {
      return this.deepCloneFormArray(control);
    } else if (control instanceof FormGroup) {
      const clonedGroup = new FormGroup({});

      Object.keys(control.controls).forEach(key => {
        const clonedControl = this.cloneControl(control.get(key));
        clonedGroup.addControl(key, clonedControl);
      });

      return clonedGroup;
    } else {
      return new FormControl(control.value, control.validator, control.asyncValidator);
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  isEqual(array1: any, array2: any): boolean {
    return JSON.stringify(array1) === JSON.stringify(array2) ? true : false;
  }

  showToaster(message: string): void {
    this.toastr.clear();
    this.toastr.info(message);
  }

  genericSuccessToaster(): void {
    this.translate.get('common.success_operation').subscribe((message: string) => {
      this.toastr.clear();
      this.toastr.success(message);
    });
  }

  genericErrorToaster(message: string): void {
    this.toastr.clear();
    this.toastr.error(message);
  }

  vatNumberValidator(): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } | null => {
      const value = control.value;

      // Check if value is not a number
      if (isNaN(value)) {
        return { notNumeric: { value } };
      }

      // Convert to string to perform length check
      const valueStr = value.toString();

      // If value is 0, it's valid
      if (valueStr === '0') {
        return null;
      }

      // Check if length is exactly 15
      if (valueStr.length !== 15) {
        return { invalidLength: { value } };
      }

      return null;
    };
  }

  minDigitsValidator(minDigits: number): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } | null => {
      const value = control.value;
      if (value != null && value.toString().length < minDigits) {
        return { minDigits: { value: control.value } };
      }
      return null;
    };
  }

  atLeastOnePrivilegeValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const hasTransferPrivilege = control.get('hasTransferPrivilege')?.value;
      const hasCardPrivilege = control.get('hasCardPrivilege')?.value;
      const hasCashPrivilege = control.get('hasCashPrivilege')?.value;
      const hasCreditPrivilege = control.get('hasCreditPrivilege')?.value;

      const atLeastOneSelected =
        hasTransferPrivilege || hasCardPrivilege || hasCashPrivilege || hasCreditPrivilege;

      return atLeastOneSelected ? null : { atLeastOneRequired: true };
    };
  }

  /**
   * Opens the Invoice Demo component in a new browser window for printing
   * @param documentId - The document ID to fetch invoice data
   * @param transactionType - The transaction type (SALES, CREDIT_NOTE, etc.)
   * @param windowFeatures - Optional window features configuration
   */
  openInvoiceInNewWindow(
    documentId: number | string,
    transactionType = 'SALES',
    windowFeatures?: {
      width?: number;
      height?: number;
      resizable?: boolean;
      scrollbars?: boolean;
      toolbar?: boolean;
      menubar?: boolean;
    }
  ): Window | null {
    // Default window features
    const defaultFeatures = {
      width: 1200,
      height: 800,
      resizable: true,
      scrollbars: true,
      toolbar: false,
      menubar: false,
      ...windowFeatures,
    };

    // Construct the URL for the Invoice Demo component
    const baseUrl = window.location.origin;
    const invoiceUrl = `${baseUrl}/invoice/demo?documentId=${documentId}&transactionType=${transactionType}`;

    console.log('Opening Invoice Demo URL:', invoiceUrl);

    // Build window features string
    const featuresString = [
      `width=${defaultFeatures.width}`,
      `height=${defaultFeatures.height}`,
      `resizable=${defaultFeatures.resizable ? 'yes' : 'no'}`,
      `scrollbars=${defaultFeatures.scrollbars ? 'yes' : 'no'}`,
      `toolbar=${defaultFeatures.toolbar ? 'yes' : 'no'}`,
      `menubar=${defaultFeatures.menubar ? 'yes' : 'no'}`,
    ].join(',');

    // Open the Invoice Demo component in a new window
    const newWindow = window.open(invoiceUrl, '_blank', featuresString);

    if (!newWindow) {
      console.error('Failed to open new window - popup might be blocked');
      // Fallback to current window navigation if popup is blocked
      window.open(invoiceUrl, '_blank');
      return null;
    } else {
      // Focus the new window
      newWindow.focus();
      return newWindow;
    }
  }

  /**
   * Legacy method for backward compatibility - opens invoice with invoiceId
   * @param invoiceId - The legacy invoice ID
   */
  openInvoiceDemoLegacy(invoiceId: string): Window | null {
    const baseUrl = window.location.origin;
    const invoiceUrl = `${baseUrl}/invoice/demo?invoiceId=${invoiceId}`;

    console.log('Opening Invoice Demo (Legacy) URL:', invoiceUrl);

    const newWindow = window.open(
      invoiceUrl,
      '_blank',
      'width=1200,height=800,resizable=yes,scrollbars=yes,toolbar=no,menubar=no'
    );

    if (!newWindow) {
      console.error('Failed to open new window - popup might be blocked');
      window.open(invoiceUrl, '_blank');
      return null;
    } else {
      newWindow.focus();
      return newWindow;
    }
  }
}
