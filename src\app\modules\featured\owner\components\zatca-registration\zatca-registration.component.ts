import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators, AbstractControl } from '@angular/forms';
import { ZatcaRegistrationService } from '../../services/zatca-registration.service';
import { CommonService } from 'src/app/core/api/common.service';
import { ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { Directionality } from '@angular/cdk/bidi';
import { ConfirmDialogComponent } from 'src/app/modules/shared/components/confirm-dialog/confirm-dialog.component';
import { ToastrService } from 'ngx-toastr';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { TenantsService } from '../../services/tenants.service';
import { IBranchListResponse, ITenant } from '../../tenant';
import { v4 as uuidv4 } from 'uuid';
import { environment } from 'src/environments/environment';
@Component({
  selector: 'app-zatca-registration',
  templateUrl: './zatca-registration.component.html',
  styleUrls: ['./zatca-registration.component.scss'],
})
export class ZatcaRegistrationComponent implements OnInit {
  registrationForm: FormGroup;
  tenantId: string;
  mainTenantId: string;
  branches: IBranchListResponse[];
  environments: string[] = ['TEST', 'PROD', 'SANDBOX'];

  constructor(
    private fb: FormBuilder,
    private registrationService: ZatcaRegistrationService,
    private commonService: CommonService,
    private route: ActivatedRoute,
    private translateService: TranslateService,
    private direction: Directionality,
    private toastr: ToastrService,
    private dialog: MatDialog,
    private tenantService: TenantsService
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      this.tenantId = params['id'];
      this.getTenant(this.tenantId);
      //this.getBranchesforTenant(this.tenantId);
    });
    this.registrationForm = this.fb.group({
      otp: ['', Validators.required],
      commonName: ['', Validators.required],
      serialNumber: ['', [Validators.required, this.serialNumberValidator]],
      organizationIdentifier: [
        { value: null, disabled: true },
        [(Validators.required, Validators.minLength(15), Validators.maxLength(15))],
      ],
      organizationUnitName: ['', Validators.required],
      organizationName: ['', Validators.required],
      countryName: [{ value: 'SA', disabled: true }],
      invoiceType: [{ value: '1100', disabled: true }],
      location: ['KSA-Riyadh', Validators.required],
      industry: ['', Validators.required],
      branchId: ['', Validators.required],
      environment: ['', Validators.required],
    });
    this.generateAndPopulateSerialNo();
  }

  getTenant(customerId: string): void {
    this.tenantService.getTenantsById(customerId).subscribe(
      response => {
        this.mainTenantId = response.tenantId;
        this.getBranchesforTenant(this.mainTenantId);
        this.updateForms(response);
      },
      error => console.log(error)
    );
  }

  getBranchesforTenant(customerId: string): void {
    this.tenantService.getTenantsBranches(customerId).subscribe(
      response => {
        this.branches = response;
      },
      error => console.log(error)
    );
  }

  updateForms(data: ITenant): void {
    this.registrationForm.patchValue({
      commonName: data.commercialRegistrationNo,
      organizationName: data.tenantName,
      organizationIdentifier: data.vatNumber,
    });
    const value = this.registrationForm.get('organizationIdentifier').value;
    if (value && value.length >= 11 && value[10] === '1') {
      // fill with commercialRegistrationNo
      this.registrationForm.get('organizationUnitName').setValue(data.commercialRegistrationNo);
      this.registrationForm.get('organizationUnitName').disable();
    } else {
      this.registrationForm.get('organizationUnitName').enable();
      this.registrationForm.get('organizationUnitName').setValue(null);
    }
    this.registrationForm.get('organizationUnitName').updateValueAndValidity();
  }

  serialNumberValidator(control: AbstractControl): { [key: string]: boolean } | null {
    const validFormat = /^\d+-.*\|\d+-.*\|\d+-[a-fA-F0-9-]+$/;
    if (!validFormat.test(control.value)) {
      return { invalidSerialNumber: true };
    }
    return null;
  }

  vatNumberValidator(control: AbstractControl): { [key: string]: boolean } | null {
    const vatNumber = control.value;
    if (
      vatNumber.length !== 15 ||
      vatNumber[0] !== '3' ||
      vatNumber[vatNumber.length - 1] !== '3'
    ) {
      return { invalidVatNumber: true };
    }
    return null;
  }

  onSubmit(event: Event): void {
    console.log('....');
    event.preventDefault();
    this.registrationForm.markAllAsTouched();
    if (this.registrationForm.valid) {
      this.onPostClick();
    } else {
      this.commonService.playErrorSound();
    }
  }

  submitForm(): void {
    const formData = this.registrationForm.getRawValue();
    console.log('Form is valid', formData);
    this.registrationService.register(this.mainTenantId, formData).subscribe(
      response => {
        console.log('Registration successful', response);
        //this.toastr.success('Tenant created successfully');
        this.commonService.playSuccessSound();
      },
      error => {
        console.error('Registration failed', error);
      }
    );
  }

  onPostClick() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.minWidth = '600px';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = true;
    dialogConfig.direction = this.direction.value;
    dialogConfig.data = 'Are you sure you want to proceed with Zatca Certificate Proces';
    const dialogRef = this.dialog.open(ConfirmDialogComponent, dialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      console.log('result', result);
      if (result) {
        this.submitForm();
      }
    });
  }
  generateSerialNumber(): string {
    return `1-TST|2-TST|3-${uuidv4()}`;
  }
  generateAndPopulateSerialNo(): void {
    const newSerialNo = this.generateSerialNumber();
    this.registrationForm.get('serialNumber')?.setValue(newSerialNo);
  }
}
