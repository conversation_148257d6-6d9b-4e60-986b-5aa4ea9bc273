<app-dialog-header></app-dialog-header>

<!-- search field -->
<div class="row no-gutters m-t-10 p-4">
  <div class="col-md-6 col-lg-6 col-sm-12 d-flex">
    <mat-form-field class="w-100">
      <input #searchInput matInput autocomplete="off" />
      <i-tabler class="icon-16" matPrefix name="search"></i-tabler>
      <a class="cursor-pointer" *ngIf="searchInput?.value" (click)="clearSearchInput()" matSuffix>
        <i-tabler class="icon-16 error" name="X"></i-tabler>
      </a>
    </mat-form-field>
  </div>
</div>
<!-- search field -->
<div class="dialog-container table-responsive m-t-10">
  <table class="w-100 p-4" #table [dataSource]="dataSource" mat-table matSort>
    <ng-container matColumnDef="itemCode">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'productSearch.itemCode' | translate }}
      </th>
      <td class="f-w-600" *matCellDef="let element" mat-cell>
        {{ element.itemCode }}
      </td>
    </ng-container>

    <ng-container matColumnDef="discount">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'productSearch.discount' | translate }}
      </th>
      <td class="f-w-600" *matCellDef="let element" mat-cell>
        <div class="d-flex">
          <i-tabler
            class="icon-14"
            *ngIf="element.isGeneralDscntMethod"
            name="percentage"></i-tabler>
          <i-tabler class="icon-14" *ngIf="!element.isGeneralDscntMethod" name="cash"></i-tabler>
          {{ element.discount }}
        </div>
      </td>
    </ng-container>

    <ng-container matColumnDef="itemName">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'productSearch.itemName' | translate }}
      </th>
      <td class="f-w-600" *matCellDef="let element" mat-cell>
        {{ element.itemName }}
      </td>
    </ng-container>

    <ng-container matColumnDef="warehouseName">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'productSearch.warehouseName' | translate }}
      </th>
      <td class="f-w-600" *matCellDef="let element" mat-cell>
        {{ element.warehouseName }}
      </td>
    </ng-container>

    <ng-container matColumnDef="category">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'productSearch.category' | translate }}
      </th>
      <td class="f-w-600" *matCellDef="let element" mat-cell>
        {{ element.category }}
      </td>
    </ng-container>

    <ng-container matColumnDef="unitName">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'productSearch.unitName' | translate }}
      </th>
      <td class="f-w-600" *matCellDef="let element" mat-cell>
        {{ element.unitName }}
      </td>
    </ng-container>

    <ng-container matColumnDef="currentQty">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'productSearch.currentQty' | translate }}
      </th>
      <td class="f-w-600" *matCellDef="let element" mat-cell>
        {{ element.totalQuantityPerUnit }}
      </td>
    </ng-container>

    <ng-container matColumnDef="distributorPrice">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'productSearch.distributorPrice' | translate }}
      </th>
      <td class="f-w-600" *matCellDef="let element" mat-cell>
        {{ element.distributorPrice }}
      </td>
    </ng-container>

    <ng-container matColumnDef="purchasePrice">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'productSearch.purchasePrice' | translate }}
      </th>
      <td class="f-w-600" *matCellDef="let element" mat-cell>
        {{ element.purchasePrice }}
      </td>
    </ng-container>

    <ng-container matColumnDef="wholesalePrice">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'productSearch.wholesalePrice' | translate }}
      </th>
      <td class="f-w-600" *matCellDef="let element" mat-cell>
        {{ element.wholesalePrice }}
      </td>
    </ng-container>

    <ng-container matColumnDef="retailPrice">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'productSearch.retailPrice' | translate }}
      </th>
      <td class="f-w-600" *matCellDef="let element" mat-cell>
        {{ element.retailPrice }}
      </td>
    </ng-container>

    <ng-container matColumnDef="openPurchasePrice">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'productSearch.openPurchasePrice' | translate }}
      </th>
      <td class="f-w-600" *matCellDef="let element" mat-cell>
        {{ element.openPurchasePrice }}
      </td>
    </ng-container>

    <ng-container matColumnDef="avgPurchasePrice">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'productSearch.avgPurchasePrice' | translate }}
      </th>
      <td class="f-w-600" *matCellDef="let element" mat-cell>
        {{ element.avgPurchasePrice }}
      </td>
    </ng-container>

    <tr *matHeaderRowDef="displayedSearchColumns; sticky: true" mat-header-row></tr>
    <tr
      class="cursor-pointer"
      *matRowDef="let row; columns: displayedSearchColumns"
      [rowModel]="row"
      [matTable]="table"
      [focusFirstOption]="true"
      [selectOnFocus]="true"
      [ngClass]="{ selected: selection.isSelected(row) }"
      [appMatTableKeyboardNavigation]="selection"
      (rowSelected)="selectRow($event)"
      (click)="selectRow(row)"
      mat-row></tr>
  </table>
</div>

<div class="spinner-container" *ngIf="isLoading">
  <mat-spinner class="spinner-wrapper" diameter="30"></mat-spinner>
</div>
