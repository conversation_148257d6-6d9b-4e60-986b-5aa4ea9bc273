import {
  Component,
  Input,
  ElementRef,
  EventEmitter,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  NG_VALUE_ACCESSOR,
  FormControl,
  Validators,
} from '@angular/forms';
import {
  MatAutocomplete,
  MatAutocompleteSelectedEvent,
  MatAutocompleteTrigger,
} from '@angular/material/autocomplete';
import { MatSort } from '@angular/material/sort';
import { MatTable, MatTableDataSource } from '@angular/material/table';
import { debounceTime, filter, finalize, switchMap, tap } from 'rxjs/operators';
import { Account } from '../../models/account';
import { ChartOfAccountsService } from '../../services/chart-of-accounts.service';
import { AccountParams } from '../../models/accountParams';
import { AccountsListWindowComponent } from '../accounts-list-window/accounts-list-window.component';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { Directionality } from '@angular/cdk/bidi';
import { LocalStorageService } from 'src/app/modules/core/core/services/local-storage.service';
import { TranslateService } from '@ngx-translate/core';
import { IAccountDetails } from 'src/app/core/interfaces/sales';
@Component({
  selector: 'app-account-auto-search',
  templateUrl: './account-auto-search.component.html',
  styleUrls: ['./account-auto-search.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: AccountAutoSearchComponent,
    },
  ],
})
export class AccountAutoSearchComponent implements OnInit {
  @Input() disabled: boolean;
  @Input() label: string;
  @Input() control: FormControl;
  @Input() otherControl: FormControl;
  @Input() placeholder: string;
  @Input() accountGroup: string;
  @Input() isDepreciating: boolean;
  @Input() isContraAccount: boolean;
  @Input() accountType: string;
  @Input() filterTypes: string[];
  @Input() placeHolderText: string[];
  @Input() searchStringLength: number;
  @Input() showSearch = false;
  @Input() newAccountSettings: boolean;
  @Input() formTouched = false;
  @ViewChild('userInput') userInput: ElementRef;
  @ViewChild(MatTable) table: MatTable<Account>;
  @ViewChild(MatAutocompleteTrigger) autoCompleteTrigger: MatAutocompleteTrigger;
  @ViewChild(MatAutocomplete) autoComplete: MatAutocomplete;
  @ViewChild(MatSort) sort: MatSort;
  @Output() accountSelected: EventEmitter<Account> = new EventEmitter<Account>();
  searchBoxForm: UntypedFormGroup;
  searchPlaceholder: string;
  touched = false;
  public accountValueSet = false;
  public isAccountLoading = false;
  public accounts: Account[] = [];
  public resultNotFound = false;
  accountSearchSource: MatTableDataSource<Account>;
  displayedSearchColumns: string[] = ['accountNumber-nameArabic'];
  constructor(
    private formBuilder: UntypedFormBuilder,
    private accountService: ChartOfAccountsService,
    private dialog: MatDialog,
    private direction: Directionality,
    private localStorage: LocalStorageService,
    private translate: TranslateService
  ) {}

  ngOnInit(): void {
    this.searchBoxForm = this.formBuilder.group({
      searchString: [null, Validators.required],
    });
    this.searchBoxForm
      .get('searchString')
      .valueChanges.pipe(
        filter(value => {
          this.accountValueSet = false;
          if (!!value && typeof value !== 'object') {
            console.log('search Value', value);
            return value !== null && value.trim().length >= this.searchStringLength;
          } else {
            if (!this.disabled) {
              this.control.setValue(null);
              this.accountSearchSource.data = null;
              this.resultNotFound = false;
            }
            return false;
          }
        })
      )
      .pipe(
        debounceTime(500),
        tap(
          () => (
            (this.accounts = []),
            (this.accountSearchSource = null),
            (this.resultNotFound = false),
            (this.isAccountLoading = true)
          )
        ),
        switchMap(value => {
          const params = new AccountParams();
          params.searchString = value;
          if (this.accountGroup) {
            params.accountGroup = this.accountGroup;
          }
          if (this.isDepreciating) {
            params.isDepreciating = this.isDepreciating;
          }
          if (this.isContraAccount) {
            params.isContraAccount = this.isContraAccount;
          }
          if (this.accountType) {
            params.accountType = this.accountType;
          }
          return this.accountService
            .getAllChartOfAccounts(params)
            .pipe(finalize(() => (this.isAccountLoading = false)));
        })
      )
      .subscribe(
        searchResult => (
          (this.accounts = searchResult.accounts),
          (this.resultNotFound = this.accounts?.length > 0 ? false : true),
          (this.accountSearchSource = new MatTableDataSource<Account>(this.accounts)),
          console.log('set', this.accountSearchSource),
          setTimeout(() => {
            this.accountSearchSource.sort = this.sort;
          })
        )
      );
    if (this.disabled) {
      this.searchBoxForm.disable();
      this.searchBoxForm
        .get('searchString')
        .setValue(this.control.value, { emitEvent: false, onlySelf: false });
    }
  }

  ngAfterViewInit() {
    // Clear the input and emit when a selection is made
    // this.autoCompleteTrigger.autocomplete.optionSelected.subscribe(option => {
    //   console.log('select event triggered..');
    //   option.option.deselect;
    //   this.accountSearchSource = null;
    //   this.onSelection(option.option.value);
    // });
  }
  // onSelection(selectedAccount: Account) {
  //   console.log('onSelection we need to pass event', selectedAccount.accountId, this.autoComplete);
  //   this.searchBoxForm.get('searchString').patchValue(null, { emitEvent: false });
  //   this.accountSelected.emit(selectedAccount);
  //   this.searchBoxForm.controls['searchString'].setValue(
  //     this.getAccountName(selectedAccount) + '-' + selectedAccount.accountNumber
  //   );
  //   this.control.setValue(selectedAccount.accountId);
  //   if (this.otherControl) this.otherControl.setValue(selectedAccount.costCentreId);
  // }

  resetSearchBox(): void {
    this.searchBoxForm.reset();
    this.searchBoxForm.updateValueAndValidity();
  }

  onViewAccountsList(event: Event) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '90vw';
    dialogConfig.position = {
      top: '50px',
    };
    dialogConfig.maxHeight = '90vh';
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.data = {
      header: 'Accounts List',
      data: this.searchBoxForm.controls['searchString'].value,
    };
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(AccountsListWindowComponent, dialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (!dialogConfig.data.data) {
        this.searchBoxForm.controls['searchString'].setValue('');
      } else if (typeof dialogConfig.data.data === 'string') {
        this.searchBoxForm.controls['searchString'].setValue(dialogConfig.data.data);
      } else {
        this.control.setValue(this.setValue(dialogConfig.data.data));
        if (this.otherControl) this.otherControl.setValue(dialogConfig.data.data?.costCentreId);
        this.newAccountSettings
          ? (this.searchBoxForm.controls['searchString'].setValue(
              this.setValue(dialogConfig.data.data),
              { emitEvent: false, onlySelf: true }
            ),
            this.accountSelected.emit(dialogConfig.data.data))
          : this.searchBoxForm.controls['searchString'].setValue(dialogConfig.data.data);
      }
      this.accountValueSet = true;
    });
  }

  setValue(data: Account) {
    const accountConfig: IAccountDetails = {
      accountId: data.accountId,
      accountNumber: data.accountNumber,
      nameArabic: data.nameArabic,
      nameEnglish: data.nameEnglish,
    };
    return accountConfig;
  }

  writeValue(obj: any): void {}
  registerOnChange(fn: any): void {
    this.searchBoxForm.valueChanges.subscribe(fn);
  }
  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }
  setDisabledState?(isDisabled: boolean): void {}

  onTouched() {}

  getAccountName(account: Account) {
    if (this.localStorage.getItem('locale') === 'AR' && account.nameArabic !== null) {
      return account.nameArabic;
    } else if (this.localStorage.getItem('locale') === 'EN' && account.nameEnglish !== null) {
      return account.nameEnglish;
    } else if (account.nameEnglish !== null) {
      return account.nameEnglish;
    } else if (account.nameArabic !== null) {
      return account.nameArabic;
    } else {
      return '';
    }
  }

  clearSelection() {
    this.accountSearchSource = null;
    this.searchBoxForm.get('searchString').patchValue(null, { emitEvent: false });
    this.resultNotFound = false;
    this.autoCompleteTrigger.closePanel();
  }

  public setFocus() {
    this.userInput.nativeElement.focus();
  }

  displayFn(user: any) {
    if (user) {
      const currentLanguage = this.translate.currentLang;
      const name = currentLanguage === 'ar' ? user.nameArabic : user.nameEnglish;
      return user ? `${user.accountNumber}-${name}` : undefined;
    }
  }

  optionSelected(event: MatAutocompleteSelectedEvent): void {
    console.log('see the selected data', event);
    if (event.option.select) {
      const data = this.setValue(event.option.value);
      this.onSelection(data);
    }
  }

  onSelection(selectedCustomer: any) {
    console.log('onSelection', selectedCustomer);
    this.searchBoxForm.controls['searchString'].patchValue(selectedCustomer, {
      emitEvent: true,
    });
    this.control.setValue(selectedCustomer);
    this.accountSelected.emit(selectedCustomer);
  }

  markAsTouched() {
    this.searchBoxForm.markAllAsTouched();
  }
}
