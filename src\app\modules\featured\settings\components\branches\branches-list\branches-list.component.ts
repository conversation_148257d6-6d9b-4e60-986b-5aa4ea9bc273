import { Directionality } from '@angular/cdk/bidi';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/core/api/common.service';
import { Branch } from '../../../../catalog/models/branch';
import { BranchParams } from '../../../models/branchParams';
import { BranchService } from '../../../services/branch.service';
import { DeleteConfirmationComponent } from 'src/app/modules/shared/components/delete-confirmation/delete-confirmation.component';

@Component({
  selector: 'app-branches-list',
  templateUrl: './branches-list.component.html',
  styleUrls: ['./branches-list.component.scss'],
})
export class BranchesListComponent implements OnInit {
  @ViewChild('filter') filter: ElementRef;
  @ViewChild(MatPaginator) set matPaginator(paginator: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = paginator;
    }
  }
  @ViewChild(MatSort) set matSort(sort: MatSort) {
    if (this.dataSource) {
      this.dataSource.sort = sort;
    }
  }
  @ViewChild('searchInput') searchInput: ElementRef;
  branches: Branch[];
  displayedColumns: string[];
  dataSource: MatTableDataSource<Branch>;
  branchParams = new BranchParams();
  formTitle: string;
  constructor(
    private commonService: CommonService,
    private branchService: BranchService,
    private toastr: ToastrService,
    private dialog: MatDialog,
    private direction: Directionality,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.initColumns();
    this.formTitle = this.route.snapshot.data['title'];
    this.getBranches();
  }

  initColumns(): void {
    this.displayedColumns = ['action', 'nameEnglish', 'nameArabic', 'vatNumber'];
  }

  getBranches(): void {
    this.branchService.getAllBranches(this.branchParams).subscribe(result => {
      this.branches = result;
      this.dataSource = new MatTableDataSource<Branch>(this.branches);
    });
  }

  applyFilter(filterValue: string): void {
    filterValue = filterValue.trim();
    filterValue = filterValue.toLowerCase();
    this.dataSource.filter = filterValue;
  }

  clearSearchInput() {
    this.searchInput.nativeElement.value = '';
    this.getBranches();
  }

  deleteBranch(branchId: string) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.panelClass = 'green_theme';
    dialogConfig.minWidth = '600px';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(DeleteConfirmationComponent, dialogConfig);
    dialogRef.componentInstance.accepted.subscribe(result => {
      console.log('delete itemcode', result);
      this.branchService.deleteBranch(branchId).subscribe(() => {
        //this.toastr.success('Branch deleted Successfully');
        this.commonService.playSuccessSound();
        this.getBranches();
      });
    });
  }
}
