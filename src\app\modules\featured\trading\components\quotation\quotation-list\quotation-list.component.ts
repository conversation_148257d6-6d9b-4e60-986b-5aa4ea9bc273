import { Directionality } from '@angular/cdk/bidi';
import { Component, ElementRef, ViewChild } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { CommonService } from 'src/app/core/api/common.service';
import { SalesService } from 'src/app/core/api/trading/sales.service';
import { IInvoice, ISalesResponse, SaletransactionTypes } from 'src/app/core/interfaces/sales';
import { SalesParams } from 'src/app/core/models/params/salesParams';
import { MultilingualService } from 'src/app/modules/core/core/services/multilingual.service';
import { ReportService } from 'src/app/modules/featured/settings/services/report.service';
import { DeleteConfirmationComponent } from 'src/app/modules/shared/components/delete-confirmation/delete-confirmation.component';
import { PrintDialogComponent } from 'src/app/modules/shared/components/print-dialog/print-dialog.component';
import { SalesNotesComponent } from '../../sales/sales-notes/sales-notes.component';

@Component({
  selector: 'app-quotation-list',
  templateUrl: './quotation-list.component.html',
  styleUrls: ['./quotation-list.component.scss'],
})
export class QuotationListComponent {
  @ViewChild('filter') filter: ElementRef;
  @ViewChild('searchInput') searchInput: ElementRef;
  @ViewChild(MatPaginator) set matPaginator(paginator: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = paginator;
    }
  }
  @ViewChild(MatSort) set matSort(sort: MatSort) {
    if (this.dataSource) {
      this.dataSource.sort = sort;
    }
  }
  quotations: IInvoice[];
  displayedColumns: string[];
  dataSource: MatTableDataSource<IInvoice>;
  isLoading = true;
  isReportPulling = false;
  constructor(
    public salesService: SalesService,
    public dialog: MatDialog,
    public reportService: ReportService,
    private direction: Directionality,
    private translateService: MultilingualService,
    private commonService: CommonService
  ) {}

  ngOnInit(): void {
    this.getAllSalesQuotations();
    this.initColumns();
  }

  getAllSalesQuotations(): void {
    const salesParams = new SalesParams();
    salesParams.transactionType = SaletransactionTypes.quotation;
    this.salesService.getAllSales(salesParams).subscribe((result: ISalesResponse) => {
      console.log(result, result.transactions);
      this.quotations = result.transactions;
      this.isLoading = false;
      this.dataSource = new MatTableDataSource<IInvoice>(this.quotations);
    });
  }

  initColumns(): void {
    this.displayedColumns = this.translateService.updateDisplayedColumns([
      'action',
      'documentNumber',
      'issueDate',
      'invoiceTotal',
      'accountNumber',
      'nameArabic',
      'nameEnglish',
      'vatNumber',
      'phoneNumber',
      'isPosted',
    ]);
  }

  applyFilter(filterValue: string): void {
    filterValue = filterValue.trim();
    filterValue = filterValue.toLowerCase();
    this.dataSource.filter = filterValue;
  }

  clearSearchInput() {
    this.searchInput.nativeElement.value = '';
    this.getAllSalesQuotations();
  }

  openSalesNotes(invoiceId: number): void {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '90vw';
    dialogConfig.position = {
      top: '50px',
    };
    dialogConfig.maxHeight = '90vh';
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    dialogConfig.data = {
      invoiceId: invoiceId,
    };
    this.dialog.open(SalesNotesComponent, dialogConfig);
  }

  deleteEntry(id: string) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '600px';
    dialogConfig.panelClass = 'green_theme';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(DeleteConfirmationComponent, dialogConfig);
    dialogRef.componentInstance.accepted.subscribe(result => {
      console.log('delete quotations', result);
      const salesParams = new SalesParams();
      salesParams.transactionType = SaletransactionTypes.quotation;
      this.salesService.deleteSalesQuotation(id).subscribe(result => {
        console.log(result);
        if (result) {
          this.getAllSalesQuotations();
        }
      });
    });
  }

  /**
   * Shows the print dialog for the selected invoice
   */
  private showPrintDialog(invoiceData: IInvoice): void {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.position = { top: '10vh' };
    dialogConfig.minWidth = '400px';
    dialogConfig.maxWidth = '500px';
    dialogConfig.panelClass = 'green_theme';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = true;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(PrintDialogComponent, dialogConfig);
    dialogRef.componentInstance.printRequested.subscribe((shouldPrint: boolean) => {
      if (shouldPrint) {
        this.commonService.openInvoiceInNewWindow(
          invoiceData.documentId,
          invoiceData.transactionType
        );
      }
    });
  }

  printQuotation(quotation: IInvoice): void {
    this.showPrintDialog(quotation);
  }
}
