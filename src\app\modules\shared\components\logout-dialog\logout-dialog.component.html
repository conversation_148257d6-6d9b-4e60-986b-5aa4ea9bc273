<app-dialog-header></app-dialog-header>
<h2 class="text-center text-warning">
  {{ 'dialogs.logout.message' | translate }}
</h2>

<div mat-dialog-actions align="center">
  <button [mat-dialog-close]="true" mat-button cdkFocusInitial mat-flat-button color="primary">
    {{ 'common.buttons.logout' | translate }}
  </button>
  <button (click)="onNoClick()" mat-stroked-button color="warn">
    {{ 'common.buttons.logoutCancel' | translate }}
  </button>
</div>
