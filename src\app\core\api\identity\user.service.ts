import { HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { UserApiService } from 'src/app/core/api/identity/user-api.service';
import { IResult } from 'src/app/core/models/wrappers/IResult';
import { IUser } from '../../interfaces/user';
import { UserParams } from '../../models/params/userParams';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  constructor(private api: UserApiService) {}

  getAllUsers(UserParams: UserParams): Observable<IUser[]> {
    let params = new HttpParams();
    if (UserParams.searchString) params = params.append('searchString', UserParams.searchString);
    if (UserParams.pageNumber)
      params = params.append('pageNumber', UserParams.pageNumber.toString());
    if (UserParams.pageSize) params = params.append('pageSize', UserParams.pageSize.toString());
    if (UserParams.orderBy) params = params.append('orderBy', UserParams.orderBy.toString());
    return this.api.getAllUsers(params).pipe(map((response: IUser[]) => response));
  }

  getUserById(id: string): Observable<IUser> {
    return this.api.getUserById(id).pipe(map((response: IUser) => response));
  }

  updateUser(User: IUser, userId: string): Observable<IUser> {
    return this.api.updateUser(User, userId).pipe(map((response: IUser) => response));
  }

  createUser(User: IUser): Observable<IResult<IUser>> {
    return this.api.createUser(User).pipe(map((response: IResult<IUser>) => response));
  }

  deleteUser(id: string): Observable<IResult<string>> {
    return this.api.deleteUser(id).pipe(map((response: IResult<string>) => response));
  }
}
