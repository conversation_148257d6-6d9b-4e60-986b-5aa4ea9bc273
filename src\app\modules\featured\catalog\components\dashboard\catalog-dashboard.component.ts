import { Component } from '@angular/core';
import { DashboardModulesHolder } from 'src/app/modules/shared/components/dashboard-modules-holder/modulesHolder';

@Component({
  selector: 'app-catalog-dashboard',
  templateUrl: './catalog-dashboard.component.html',
  styleUrls: ['./catalog-dashboard.component.scss'],
})
export class CatalogDashboardComponent {
  productModulesList: DashboardModulesHolder[] = [
    {
      moduleName: 'navigationMenus.products',
      moduleDescription: 'Manage Products.',
      modulePermission: ['Product', 'AllPermissions'],
      moduleImage: 'inventory',
      moduleRouterLink: '../products',
      moduleButtonAction: 'POS',
      moduleType: 'subModule',
    },
    {
      moduleName: 'navigationMenus.units',
      moduleDescription: 'Manage Product Units.',
      modulePermission: ['Units', 'AllPermissions'],
      moduleImage: 'ad_units',
      moduleRouterLink: '../units',
      moduleButtonAction: 'BackOffice',
      moduleType: 'subModule',
    },
    {
      moduleName: 'navigationMenus.catagories',
      moduleDescription: 'Manage Category.',
      modulePermission: ['Categories', 'AllPermissions'],
      moduleImage: 'category',
      moduleRouterLink: '../category',
      moduleButtonAction: 'BackOffice',
      moduleType: 'subModule',
    },
    {
      moduleName: 'navigationMenus.stock',
      moduleDescription: 'Manage and update stocks.',
      modulePermission: ['InventoryManagement', 'AllPermissions'],
      moduleImage: 'price_change',
      moduleRouterLink: '/stocks',
      moduleButtonAction: 'BackOffice',
      moduleType: 'mainModule',
    },
  ];
}
