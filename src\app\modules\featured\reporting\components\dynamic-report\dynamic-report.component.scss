// Report Selection
.report-selection {
  padding: 24px;

  h2 {
    margin-bottom: 24px;
    color: #333;
  }

  .report-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;

    .report-card {
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid transparent;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        border-color: #1976d2;
      }

      mat-card-title {
        color: #1976d2;
        font-weight: 500;
      }

      mat-card-subtitle {
        color: #666;
        margin-top: 8px;
      }
    }
  }
}

// Selected Report
.selected-report {
  padding: 24px;

  .report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e0e0e0;

    .report-info {
      h2 {
        margin: 0;
        color: #333;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-style: italic;
      }
    }

    .report-actions {
      display: flex;
      gap: 8px;
    }
  }
}

// Filters Card
.filters-card {
  margin-bottom: 24px;

  mat-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .filter-actions {
      margin-left: auto;
    }
  }

  .filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    margin-bottom: 24px;

    .filter-field {
      &.advanced {
        opacity: 0.8;

        mat-form-field {
          border-left: 3px solid #ff9800;
          padding-left: 8px;
        }
      }
    }
  }

  .form-actions {
    display: flex;
    gap: 16px;
    justify-content: flex-start;
    padding-top: 16px;
    border-top: 1px solid #e0e0e0;
  }
}

// Column Selection
.column-selection {
  margin-bottom: 24px;

  mat-form-field {
    width: 100%;
    max-width: 400px;
  }
}

// Results Card
.results-card {
  .no-data {
    text-align: center;
    padding: 48px 24px;
    color: #666;

    mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    p {
      font-size: 16px;
      margin: 0;
    }
  }

  .loading {
    text-align: center;
    padding: 48px 24px;

    mat-spinner {
      margin-bottom: 16px;
    }

    p {
      color: #666;
      margin: 0;
    }
  }

  .table-container {
    .results-table {
      width: 100%;

      th {
        background-color: #f5f5f5;
        font-weight: 600;
        color: #333;
      }

      td {
        padding: 12px 8px;
        border-bottom: 1px solid #e0e0e0;
      }

      tr:hover {
        background-color: #f9f9f9;
      }
    }

    mat-paginator {
      border-top: 1px solid #e0e0e0;
      margin-top: 16px;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .report-selection {
    padding: 16px;

    .report-cards {
      grid-template-columns: 1fr;
    }
  }

  .selected-report {
    padding: 16px;

    .report-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }
  }

  .filters-grid {
    grid-template-columns: 1fr !important;
  }

  .form-actions {
    flex-direction: column;

    button {
      width: 100%;
    }
  }
}

// Material Design Overrides
mat-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

mat-form-field {
  width: 100%;

  .mat-mdc-form-field-subscript-wrapper {
    margin-top: 4px;
  }
}

// Loading States
.mat-mdc-button:disabled {
  opacity: 0.6;
}

// Advanced Filter Styling
.filter-field.advanced {
  position: relative;

  &::before {
    content: 'Advanced';
    position: absolute;
    top: -8px;
    right: 8px;
    background: #ff9800;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 4px;
    z-index: 1;
  }
}
