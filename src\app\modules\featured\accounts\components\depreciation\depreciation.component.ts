import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { PaginatedFilter } from 'src/app/core/interfaces/PaginatedFilter';
import { Depreciation } from '../../models/depreciation';
import { DepreciationParams } from '../../models/depreciationParams';
import { DepreciationService } from '../../services/depreciation.service';
import { DepreciationSearchBoxComponent } from '../depreciation-search-box/depreciation-search-box.component';
import { depreciationMethodTypes } from 'src/app/core/configs/dropDownConfig';
import { AccountBasic } from '../../models/account';
import { LocalStorageService } from 'src/app/modules/core/core/services/local-storage.service';

@Component({
  selector: 'app-depreciation',
  templateUrl: './depreciation.component.html',
  styleUrls: ['./depreciation.component.scss'],
})
export class DepreciationComponent implements OnInit {
  @ViewChild('searchBoxForm') searchBoxForm: DepreciationSearchBoxComponent;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  displayedColumns: string[];
  dataSource: MatTableDataSource<any>;
  depreciationForm: UntypedFormGroup;
  depreciations: Depreciation[];
  isAdvancedSearchEnabled = false;
  resultsLength: any;
  filteredDataTriggered = false;
  isLoading: boolean;

  // accounts: Account[];
  constructor(
    private depreciationService: DepreciationService,
    private formBuilder: UntypedFormBuilder,
    private localStorage: LocalStorageService
  ) {}

  ngOnInit(): void {
    this.depreciationForm = this.formBuilder.group({
      depreciationMethod: [null],
      searchBoxForm: [],
    });
    this.initColumns();
    this.getAllDepreciation();
  }

  initColumns(): void {
    this.displayedColumns = [
      'action',
      'assetAccount',
      'cumulativeDepreciationAccount',
      'depreciationExpenseAccount',
      'depreciationPct',
      'depreciationMethod',
    ];
  }

  depreciationMethods = depreciationMethodTypes;

  getFilterData(event?: Event, pageEvent?: PaginatedFilter) {
    event?.preventDefault();
    const depreciationParams: DepreciationParams = new DepreciationParams();
    depreciationParams.searchString =
      this.searchBoxForm.searchBoxForm.controls['searchString'].value;
    depreciationParams.searchType = this.searchBoxForm.searchBoxForm.controls['searchType'].value;
    depreciationParams.pageNumber = pageEvent?.pageNumber ?? 0;
    depreciationParams.pageSize = pageEvent?.pageSize ?? 10;
    depreciationParams.depreciationMethod =
      this.depreciationForm.controls['depreciationMethod'].value;
    this.depreciationService.getAllDepreciation(depreciationParams).subscribe(result => {
      this.filteredDataTriggered = true;
      this.depreciations = null;
      this.depreciations = result.depreciations;
      setTimeout(() => {
        this.dataSource = new MatTableDataSource<Depreciation>(this.depreciations);
        this.resultsLength = result.totalRecordsCount;
      });
    });
  }

  clearFilters(event: Event) {
    event.preventDefault();
    this.depreciationForm.markAsUntouched();
    this.depreciationForm.markAsPristine();
    this.depreciationForm.reset();
    this.searchBoxForm.resetSearchBox();
    this.getFilterData();
  }

  getAllDepreciation(): void {
    const despreciationParams: DepreciationParams = new DepreciationParams();
    despreciationParams.pageNumber = 1;
    despreciationParams.pageSize = 10;
    this.depreciationService.getAllDepreciation(despreciationParams).subscribe(result => {
      this.depreciations = result.depreciations;
      this.dataSource = new MatTableDataSource<Depreciation>(this.depreciations);
      this.dataSource.paginator = this.paginator;
      this.resultsLength = result.totalRecordsCount;
      this.isLoading = false;
    });
  }
  getAccountName(account: AccountBasic): string {
    if (account && this.localStorage.getItem('locale') === 'AR' && account.nameArabic !== null) {
      return account.nameArabic + '-' + account.accountNumber;
    } else if (
      account &&
      this.localStorage.getItem('locale') === 'EN' &&
      account.nameEnglish !== null
    ) {
      return account.nameEnglish + '-' + account.accountNumber;
    } else if (account && account.nameEnglish !== null) {
      return account.nameEnglish + '-' + account.accountNumber;
    } else if (account && -account.nameArabic !== null) {
      return account.nameArabic + '-' + account.accountNumber;
    } else {
      return '';
    }
  }
}
