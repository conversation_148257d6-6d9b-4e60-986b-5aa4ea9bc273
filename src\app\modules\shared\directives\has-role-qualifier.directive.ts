import { Directive, Input, TemplateRef, ViewContainerRef } from '@angular/core';
import { AuthService } from '../../core/core/services/auth.service';

@Directive({
  selector: '[appHasRoleQualifier]',
})
export class HasRoleQualifierDirective {
  @Input() appHasRoleQualifier: string[];

  constructor(
    private viewContainerRef: ViewContainerRef,
    private templateRef: TemplateRef<any>,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    const isAuthorized = this.authService.hasRoleQualifier(this.appHasRoleQualifier);
    if (!isAuthorized) {
      this.viewContainerRef.clear();
    } else {
      this.viewContainerRef.createEmbeddedView(this.templateRef);
    }
  }
}
