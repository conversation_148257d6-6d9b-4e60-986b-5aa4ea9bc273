/* eslint-disable @typescript-eslint/ban-types */
import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import {
  ISaleDetails,
  ISalesResponse,
  SalesIntegratedCreateResponse,
} from '../../interfaces/sales';
import { ReportInvoice } from 'src/app/modules/shared/components/invoice-template/invoice.model';
import { createParamsFromObject } from '../../utils/date-utils';
import { v4 as uuidv4 } from 'uuid';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { SalesNotesComponent } from 'src/app/modules/featured/trading/components/sales/sales-notes/sales-notes.component';
import { Direction } from '@angular/cdk/bidi';

@Injectable({
  providedIn: 'root',
})
export class SalesService {
  baseUrl = environment.apiUrl + 'trading/sales';
  salesIntegrated = environment.apiUrl + `trading/sales/integrated`;

  constructor(private http: HttpClient, private dialog: MatDialog) {}

  createSales(sales: ISaleDetails) {
    sales.documentUuid = uuidv4();
    return this.http.post(this.baseUrl, sales);
  }

  createSalesIntegrated(sales: ISaleDetails, params?: Object) {
    const getSalesParams = this.getParams(params);
    sales.documentUuid = uuidv4();
    return this.http.post<SalesIntegratedCreateResponse>(this.salesIntegrated, sales, {
      params: getSalesParams,
    });
  }

  sendToZatcaInvoice(params: Object) {
    const getSalesParams = this.getParams(params);
    return this.http.post<SalesIntegratedCreateResponse>(
      this.salesIntegrated,
      {},
      { params: getSalesParams }
    );
  }

  getAllSales(params: Object) {
    const getSalesParams = this.getParams(params);
    return this.http.get<ISalesResponse>(this.baseUrl + '/pages', { params: getSalesParams });
  }

  getSalesById(id: string, params: Object) {
    const getSalesParams = this.getParams(params);
    return this.http.get<ISaleDetails>(this.baseUrl + `/${id}`, { params: getSalesParams });
  }

  getSalesNotesById(params: Object) {
    const getSalesParams = this.getParams(params);
    return this.http.get<ISalesResponse>(this.baseUrl + '/pages', { params: getSalesParams });
  }

  getSalesByDocumentUuid(params: Object) {
    const getSalesParams = this.getParams(params);
    return this.http.get<ReportInvoice>(this.baseUrl + `/document`, {
      params: getSalesParams,
    });
  }

  editSalesQuotation(quotation: ISaleDetails, id: string) {
    return this.http.put(this.baseUrl + `/${id}`, quotation);
  }

  postSalesQuotation(quotation: ISaleDetails, id: string) {
    return this.http.post(this.baseUrl + `/${id}`, quotation);
  }

  deleteSalesQuotation(id: string) {
    return this.http.delete(this.baseUrl + `/${id}`);
  }

  getParams(salesParams: Object): HttpParams {
    return createParamsFromObject(salesParams);
  }

  openSalesNotes(invoiceId: number, direction: Direction): void {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '90vw';
    dialogConfig.position = {
      top: '50px',
    };
    dialogConfig.maxHeight = '90vh';
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = direction;
    dialogConfig.data = {
      invoiceId: invoiceId,
      salesProcessing: true,
    };
    this.dialog.open(SalesNotesComponent, dialogConfig);
  }
}
