import { Component } from '@angular/core';
import { DashboardModulesHolder } from 'src/app/modules/shared/components/dashboard-modules-holder/modulesHolder';

@Component({
  selector: 'app-financedashboard',
  templateUrl: './financedashboard.component.html',
  styleUrls: ['./financedashboard.component.scss'],
})
export class FinancedashboardComponent {
  productModulesList: DashboardModulesHolder[] = [
    {
      moduleName: 'navigationMenus.ucvoucher',
      moduleDescription: 'Manage Sales.',
      modulePermission: ['TradeManagement', 'AllPermissions'],
      moduleImage: 'store',
      moduleRouterLink: '../ucvoucher',
      moduleButtonAction: 'POS',
      moduleType: 'subModule',
    },
  ];
}
