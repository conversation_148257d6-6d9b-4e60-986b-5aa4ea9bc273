import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionGuard } from '../../core/core/guards/permission.guard';
import { PosComponent } from '../../shared/components/pos/pos.component';
import { CustomerFormComponent } from './components/customers/customer-form/customer-form.component';
import { CustomerListComponent } from './components/customers/customer-list/customer-list.component';
import { DistributorFormComponent } from './components/distributors/distributor-form/distributor-form.component';
import { DistributorListComponent } from './components/distributors/distributor-list/distributor-list.component';
import { PurchaseFormComponent } from './components/purchase/purchase-form/purchase-form.component';
import { PurchaseListComponent } from './components/purchase/purchase-list/purchase-list.component';
import { PurchasepartialnoteComponent } from './components/purchase/purchasepartialnote/purchasepartialnote.component';
import { QuotationFormComponent } from './components/quotation/quotation-form/quotation-form.component';
import { QuotationListComponent } from './components/quotation/quotation-list/quotation-list.component';
import { SaleDebitNoteComponent } from './components/sales/sale-debit-note/sale-debit-note.component';
import { SaleFormComponent } from './components/sales/sale-form/sale-form.component';
import { SaleListComponent } from './components/sales/sale-list/sale-list.component';
import { SalePartialNoteComponent } from './components/sales/sale-partial-note/sale-partial-note.component';
import { SupplierFormComponent } from './components/suppliers/supplier-form/supplier-form.component';
import { SupplierListComponent } from './components/suppliers/supplier-list/supplier-list.component';
import { TradingDashboardComponent } from './components/trading-dashboard/trading-dashboard.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'dashboard',
    pathMatch: 'full',
  },
  {
    path: 'dashboard',
    component: TradingDashboardComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Trading Features',
      urls: [{ title: 'Main Dashboard', url: '/dashboard' }],
      allowedPermissions: ['TradeManagement', 'AllPermissions'],
    },
  },
  {
    path: 'sales',
    component: SaleListComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'sales.salesList',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Sales.View', 'AllPermissions'],
    },
  },
  {
    path: 'sales/view/:id',
    component: SaleFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'sales.viewSales',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Sales.View', 'AllPermissions'],
      mode: 'view',
    },
  },
  {
    path: 'sales/create',
    component: SaleFormComponent,
    canActivate: [PermissionGuard],
    // canDeactivate: [hasUnsavedChangesGuard],
    data: {
      title: 'sales.createSales',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Sales.Create', 'AllPermissions'],
      mode: 'create',
    },
  },
  {
    path: 'quotations',
    component: QuotationListComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'salesQuotes.quotationList',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Quotation.View', 'AllPermissions'],
      mode: 'view',
    },
  },
  {
    path: 'quotations/create',
    component: QuotationFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'salesQuotes.createQuotation',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Quotation.Create', 'AllPermissions'],
      mode: 'create',
    },
  },
  {
    path: 'quotations/post/:id',
    component: QuotationFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'salesQuotes.postQuotation',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Quotation.Post', 'AllPermissions'],
      mode: 'post',
    },
  },
  {
    path: 'quotations/view/:id',
    component: QuotationFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'salesQuotes.viewQuotation',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Quotation.View', 'AllPermissions'],
      mode: 'view',
    },
  },
  {
    path: 'quotations/edit/:id',
    component: QuotationFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'salesQuotes.updateQuotation',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Quotation.Update', 'AllPermissions'],
      mode: 'edit',
    },
  },
  {
    path: 'quotations/delete/:id',
    component: QuotationFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'salesQuotes.deleteQuotation',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Quotation.Delete', 'AllPermissions'],
      mode: 'delete',
    },
  },
  {
    path: 'purchases',
    component: PurchaseListComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'purchase.purchaseList',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Purchase', 'AllPermissions'],
      mode: 'view',
    },
  },
  {
    path: 'purchases/view/:id',
    component: PurchaseFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'sales.viewSales',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Purchase.View', 'AllPermissions'],
      mode: 'view',
    },
  },
  {
    path: 'purchases/edit/:id',
    component: PurchaseFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'purchase.edit',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      mode: 'edit',
    },
  },
  {
    path: 'purchases/create',
    component: PurchaseFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'purchase.register_purchase',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Purchase.Create', 'AllPermissions'],
      mode: 'create',
    },
  },
  {
    path: 'purchases/creditnote/:id',
    component: PurchaseFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'purchase.newfullCreditNote',
      urls: [{ title: 'Trading Dashboard', url: '/trading/dashboard' }],
      allowedPermissions: ['Purchase.Create', 'AllPermissions'],
      mode: 'fullCreditNote',
    },
  },
  {
    path: 'purchases/partialcreditnote/:id',
    component: PurchasepartialnoteComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'purchase.newpartialCreditNote',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Purchase.Create', 'AllPermissions'],
      mode: 'partialCreditNote',
    },
  },
  {
    path: 'sales/creditnote/:id',
    component: SaleFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'sales.createSalesCreditNote',
      urls: [{ title: 'Trading Dashboard', url: '/trading/dashboard' }],
      allowedPermissions: ['Sales.Create', 'AllPermissions'],
      mode: 'fullCreditNote',
    },
  },
  {
    path: 'sales/partialcreditnote/:id',
    component: SalePartialNoteComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'sales.createSalesPartialNote',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Sales.Create', 'AllPermissions'],
      mode: 'partialCreditNote',
    },
  },
  {
    path: 'sales/debitnote/:id',
    component: SaleDebitNoteComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'sales.createSalesDebitNote',
      urls: [{ title: 'Trading Dashboard', url: '/trading/dashboard' }],
      allowedPermissions: ['Sales.Create', 'AllPermissions'],
      mode: 'debitNote',
    },
  },
  {
    path: 'customers',
    component: CustomerListComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'customer.customerList',
      urls: [{ title: 'Trading Dashboard', url: '/trading/dashboard' }],
      allowedPermissions: ['Customer', 'AllPermissions'],
    },
  },
  {
    path: 'customers/create',
    component: CustomerFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'common.field.accountDetails',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Customer.Create', 'AllPermissions'],
      mode: 'create',
    },
  },
  {
    path: 'customers/edit/:id',
    component: CustomerFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'common.field.accountDetails',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Customer.Update', 'AllPermissions'],
      mode: 'edit',
    },
  },
  {
    path: 'customers/view/:id',
    component: CustomerFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'common.field.accountDetails',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Customer.View', 'AllPermissions'],
      mode: 'view',
    },
  },
  {
    path: 'suppliers',
    component: SupplierListComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'supplier.supplierList',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Supplier', 'AllPermissions'],
    },
  },
  {
    path: 'suppliers/create',
    component: SupplierFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'common.field.accountDetails',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Supplier.Create', 'AllPermissions'],
      mode: 'create',
    },
  },
  {
    path: 'suppliers/edit/:id',
    component: SupplierFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'common.field.accountDetails',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Supplier.Update', 'AllPermissions'],
      mode: 'edit',
    },
  },
  {
    path: 'suppliers/view/:id',
    component: SupplierFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'common.field.accountDetails',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Supplier.View', 'AllPermissions'],
      mode: 'view',
    },
  },
  {
    path: 'distributors',
    component: DistributorListComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'distributor.distributorList',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Distributor', 'AllPermissions'],
    },
  },
  {
    path: 'distributors/create',
    component: DistributorFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'common.field.accountDetails',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Distributor.Create', 'AllPermissions'],
    },
  },
  {
    path: 'distributors/edit/:id',
    component: DistributorFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'common.field.accountDetails',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Distributor.Update', 'AllPermissions'],
    },
  },
  {
    path: 'distributors/view/:id',
    component: DistributorFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'common.field.accountDetails',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Distributor.View', 'AllPermissions'],
    },
  },
  {
    path: 'pos',
    component: PosComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class TradingRoutingModule {}
