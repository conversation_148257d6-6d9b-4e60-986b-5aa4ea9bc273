@use 'variables';

$utilities: (
  'border-radius': (
    property: border-radius,
    class: rounded,
    values: variables.$radius
  ),
  'border-top-left-radius': (
    property: border-top-left-radius,
    class: r-t-l,
    values: variables.$radius
  ),
  'border-top-right-radius': (
    property: border-top-right-radius,
    class: r-t-r,
    values: variables.$radius
  ),
  'border-bottom-right-radius': (
    property: border-bottom-right-radius,
    class: r-b-r,
    values: variables.$radius
  ),
  'border-bottom-left-radius': (
    property: border-bottom-left-radius,
    class: r-b-l,
    values: variables.$radius
  ),
  'border-top-radius': (
    property: border-top-left-radius border-top-right-radius,
    class: r-t,
    values: variables.$radius
  ),
  'border-bottom-radius': (
    property: border-bottom-left-radius border-bottom-right-radius,
    class: r-b,
    values: variables.$radius
  ),
  'border-left-radius': (
    property: border-top-left-radius border-bottom-left-radius,
    class: r-l,
    values: variables.$radius
  ),
  'border-right-radius': (
    property: border-top-right-radius border-bottom-right-radius,
    class: r-r,
    values: variables.$radius
  )
);
