import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { CostCentresApiService } from 'src/app/core/api/accounts/cost-centres-api.service';
import { CostCentre } from '../../models/costCentre';

@Component({
  selector: 'app-cost-centres',
  templateUrl: './cost-centres.component.html',
  styleUrls: ['./cost-centres.component.scss'],
})
export class CostCentresComponent implements OnInit {
  @ViewChild(MatPaginator) set matPaginator(paginator: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = paginator;
    }
  }
  @ViewChild(MatSort) set matSort(sort: MatSort) {
    if (this.dataSource) {
      this.dataSource.sort = sort;
    }
  }
  @ViewChild('searchInput') searchInput: ElementRef;
  costCentres: CostCentre[];
  displayedColumns: string[];
  dataSource: MatTableDataSource<CostCentre>;
  isLoading = true;
  constructor(public costcentreApiService: CostCentresApiService, public dialog: MatDialog) {}

  ngOnInit(): void {
    this.getCostCentres();
    this.initColumns();
  }

  getCostCentres(): void {
    this.costcentreApiService.getAllCostCentres().subscribe((result: CostCentre[]) => {
      this.costCentres = result;
      this.isLoading = false;
      this.dataSource = new MatTableDataSource<CostCentre>(this.costCentres);
    });
  }

  initColumns(): void {
    this.displayedColumns = ['action', 'accountNumber', 'nameArabic', 'nameEnglish'];
  }

  applyFilter(filterValue: string): void {
    filterValue = filterValue.trim();
    filterValue = filterValue.toLowerCase();
    this.dataSource.filter = filterValue;
  }

  clearSearchInput() {
    this.searchInput.nativeElement.value = '';
    this.getCostCentres();
  }
}
