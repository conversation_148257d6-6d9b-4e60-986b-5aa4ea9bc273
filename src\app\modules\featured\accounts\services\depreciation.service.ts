import { HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { DepreciationApiService } from 'src/app/core/api/accounts/depreciation-api.service';
import { Depreciation } from '../models/depreciation';
import { DepreciationParams } from '../models/depreciationParams';
import { DepreciationResponse } from '../models/depreciationResponse';

@Injectable({
  providedIn: 'root',
})
export class DepreciationService {
  constructor(private depreciationApiService: DepreciationApiService) {}

  getAllDepreciation(depreciationParams: DepreciationParams): Observable<any> {
    let params = new HttpParams();
    // if (depreciationParams.depreciationMethod) {
    //   params = params.append('depreciationMethod', depreciationParams.depreciationMethod);
    // }
    if (depreciationParams.searchString) {
      params = params.append('searchString', depreciationParams.searchString);
    }
    if (depreciationParams.searchString && depreciationParams.searchType) {
      if (depreciationParams.searchType == 'assetAccountId') {
        depreciationParams.searchType = 'assetAccountId';
      }
      params = params.append('searchType', depreciationParams.searchType);
    }
    if (depreciationParams.pageNumber) {
      params = params.append('pageNumber', depreciationParams.pageNumber.toString());
    }
    if (depreciationParams.pageSize) {
      params = params.append('pageSize', depreciationParams.pageSize.toString());
    }

    return this.depreciationApiService
      .getAllDepreciation(params)
      .pipe(map((response: any) => response));
  }

  createDepreciation(depreciation: any) {
    return this.depreciationApiService
      .createDepreciation(depreciation)
      .pipe(map((response: any) => response));
  }

  getDepreciationById(depreciationId: string): Observable<Depreciation> {
    return this.depreciationApiService
      .getDepreciationById(depreciationId)
      .pipe(map((response: any) => response));
  }

  updateDepreciation(depreciationId: string, depreciation: any) {
    return this.depreciationApiService
      .updateDepreciation(depreciationId, depreciation)
      .pipe(map((response: any) => response));
  }
}
