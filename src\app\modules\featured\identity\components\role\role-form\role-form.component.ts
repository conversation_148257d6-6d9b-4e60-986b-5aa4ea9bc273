import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { IRole } from '../../../../../../core/interfaces/role';
import { RoleService } from '../../../services/role.service';
import { PermissionComponent } from '../../permission/permission.component';
import { Action } from 'rxjs/internal/scheduler/Action';
import { ActionType } from 'src/app/core/enums/actionType';
import { PermissionService } from '../../../services/permission.service';
import { PermissionTabsComponent } from '../../permission-tabs/permission-tabs.component';
import { CommonService } from 'src/app/core/api/common.service';

@Component({
  selector: 'app-role-form',
  templateUrl: './role-form.component.html',
  styleUrls: ['./role-form.component.scss'],
})
export class RoleFormComponent implements OnInit, OnDestroy {
  @ViewChild('apppermissions') apppermissions: PermissionTabsComponent;
  roles: IRole = <IRole>{};
  roleForm: UntypedFormGroup;
  formTitle: string;
  loading = true;
  id: number;
  mode: ActionType;
  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private roleService: RoleService,
    private toastr: ToastrService,
    private fb: UntypedFormBuilder,
    private authService: AuthService,
    private permissionService: PermissionService,
    private commonService: CommonService
  ) {}

  get isEditMode(): boolean {
    return this.mode === ActionType.edit;
  }
  get isViewMode(): boolean {
    return this.mode === ActionType.view;
  }

  get isCreateMode(): boolean {
    return this.mode === ActionType.create;
  }

  ngOnInit() {
    this.formTitle = this.route.snapshot.data['title'];
    this.mode = this.route.snapshot.data['mode'];
    this.route.params.subscribe(params => {
      this.id = +params['id'];
      if (this.id && Number.isInteger(this.id)) {
        this.getRoles(this.id);
      } else {
        this.roles = null;
        this.initializeForm();
      }
    });
  }

  get adminPermissions() {
    return this.authService.getJwtPermissions;
  }

  getRoles(id: number): void {
    this.roleService.getRoleById(id).subscribe(result => {
      console.log(result);
      this.roles = result;
      console.log(this.roles);

      this.initializeForm(this.roles);
    });
  }

  initializeForm(role?: IRole) {
    this.roleForm = this.fb.group({
      name: [role?.name, Validators.required],
      description: [role?.description, Validators.required],
      permissions: [role?.permissions],
    });
    this.loading = false;
    // disbale if user is trying to edit his own permissions via URL
    if (this.isViewMode || this.id === this.authService.getUserId) {
      this.roleForm.disable();
    }
  }

  onSubmit(event: Event) {
    event.preventDefault();
    this.roleForm.markAllAsTouched();
    if (this.roleForm && this.roleForm?.valid) {
      this.roleForm.get('permissions').setValue(this.apppermissions.getRolePermissions());
      if (!this.isEditMode) {
        this.roleService.createRole(this.roleForm.value).subscribe(response => {
          //this.toastr.success('Role and Permissions added Successfully');
          this.commonService.playSuccessSound();
          this.router.navigate(['../'], { relativeTo: this.route });
        });
      } else {
        this.roleService.updateRole(this.roleForm.value, this.id).subscribe(response => {
          //this.toastr.success('Role and Permissions updated Successfully');
          this.commonService.playSuccessSound();
          this.router.navigate(['../../'], { relativeTo: this.route });
        });
      }
    }
  }

  ngOnDestroy(): void {
    this.roles = null;
  }
}
