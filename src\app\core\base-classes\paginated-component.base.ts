import { Directive, OnInit } from '@angular/core';
import { PageEvent } from '@angular/material/paginator';
import { Observable } from 'rxjs';
import { PaginatedFilter } from '../interfaces/PaginatedFilter';

/**
 * Base class for components that implement server-side pagination
 * Components can extend this class to get common pagination functionality
 */
@Directive()
export abstract class PaginatedComponentBase implements OnInit {
  // Pagination properties
  pageSize = 10;
  pageIndex = 0;
  totalItems = 0;
  pageSizeOptions: number[] = [5, 10, 25, 50, 100];

  /**
   * Initialize pagination settings
   * Override this method in child components if needed
   */
  ngOnInit(): void {
    this.initPagination();
  }

  /**
   * Initialize pagination with default values
   * Override this method in child components to set custom initial values
   */
  protected initPagination(): void {
    this.pageSize = 10;
    this.pageIndex = 0;
    this.totalItems = 0;
  }

  /**
   * Handle page change events from mat-paginator
   * @param event The PageEvent from mat-paginator
   */
  onPageChange(event: PageEvent): void {
    this.pageSize = event.pageSize;
    this.pageIndex = event.pageIndex;

    const paginatedFilter = this.createPaginatedFilter();
    this.loadPagedData(paginatedFilter);
  }

  /**
   * Create a PaginatedFilter object from current pagination state
   * @returns PaginatedFilter object with current pagination settings
   */
  protected createPaginatedFilter(): PaginatedFilter {
    return {
      pageNumber: this.pageIndex + 1, // API uses 1-based indexing
      pageSize: this.pageSize,
    };
  }

  /**
   * Update pagination state from API response
   * @param totalCount Total number of items
   */
  protected updatePaginationState(totalCount: number): void {
    this.totalItems = totalCount;
  }

  /**
   * Load data with pagination
   * This method must be implemented by child components
   * @param filter The pagination filter to apply
   */
  protected abstract loadPagedData(filter: PaginatedFilter): void;
}
