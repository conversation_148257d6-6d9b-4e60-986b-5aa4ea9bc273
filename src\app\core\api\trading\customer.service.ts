import { HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { CustomerApiService } from 'src/app/core/api/trading/customer-api.service';
import { ICustomer } from '../../interfaces/customer';
import { CustomerParams } from '../../models/params/customerParams';

@Injectable({
  providedIn: 'root',
})
export class CustomerService {
  private customerSelectionSubject = new BehaviorSubject<any>(null); // Use a more specific type if possible
  customerSelection$ = this.customerSelectionSubject.asObservable();
  constructor(private api: CustomerApiService) {}

  setCustomerSelection(selection: any) {
    console.log('sasasasa', selection);
    this.customerSelectionSubject.next(selection);
  }

  getCustomers(CustomerParams: CustomerParams): Observable<any> {
    let params = new HttpParams();
    if (CustomerParams.searchString)
      params = params.append('searchString', CustomerParams.searchString);
    if (CustomerParams.pageNumber)
      params = params.append('pageNumber', CustomerParams.pageNumber.toString());
    if (CustomerParams.pageSize)
      params = params.append('pageSize', CustomerParams.pageSize.toString());
    if (CustomerParams.orderBy)
      params = params.append('orderBy', CustomerParams.orderBy.toString());
    return this.api.getAlls(params).pipe(map((response: ICustomer) => response));
  }

  getCustomerById(id: string): Observable<ICustomer> {
    return this.api.getById(id).pipe(map((response: ICustomer) => response));
  }

  createCustomer(Customer: any): Observable<ICustomer> {
    return this.api.create(Customer).pipe(map((response: ICustomer) => response));
  }

  updateCustomer(Customer: any, id: string): Observable<any> {
    return this.api.update(Customer, id).pipe(map((response: ICustomer) => response));
  }

  deleteCustomer(id: string): Observable<string> {
    return this.api.delete(id).pipe(map((response: string) => response));
  }
}
