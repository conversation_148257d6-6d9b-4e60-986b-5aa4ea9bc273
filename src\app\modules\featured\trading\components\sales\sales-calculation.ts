import { UntypedFormArray } from '@angular/forms';
import { IInventory } from '../../../catalog/models/product';

export class SalesCalculation {
  items: UntypedFormArray;
  orderTax = 0;
  discounts = 0;
  totals = 0;
  itemTotals = 0;
  totalVats = 0;
  totalExcVatDiscs = 0;
  grandTotals = 0;
  constructor() {
    // to be added later
  }

  formItems(items: UntypedFormArray) {
    this.resetCounts();
    this.items = items;
  }

  get grandTotal(): number {
    return +this.grandTotals.toFixed(2);
  }

  get totalExcVatDisc(): number {
    return +this.totalExcVatDiscs.toFixed(2);
  }

  get totalVat(): number {
    return +this.totalVats.toFixed(2);
  }

  get total(): number {
    return +this.totals.toFixed(2);
  }

  get discount(): number {
    return +this.discounts.toFixed(2);
  }

  get itemTotal(): number {
    return +this.itemTotals;
  }

  // subtotal
  countSubTotal(
    quantity: number,
    price: number,
    discount: number,
    discountType: boolean,
    vatValue: number
  ): number {
    return +(
      quantity *
      (this.priceMinusDiscount(price, discount, discountType) + vatValue)
    ).toFixed(2);
  }

  countSubTotalExcluded(quantity: number, price: number) {
    return +(quantity * price).toFixed(2);
  }

  // grand VAT
  countGrandVat(): number {
    const result = new Map<number, number>();
    this.totalVats = 0;

    const vatSums = new Map<number, number>();

    for (const control of this.items.controls) {
      const controlVat = control.get('vat').value;
      const subTotalVat = control.value.subTotalVat;

      if (vatSums.has(controlVat)) {
        vatSums.set(controlVat, vatSums.get(controlVat) + subTotalVat);
      } else {
        vatSums.set(controlVat, subTotalVat);
      }

      this.totalVats += subTotalVat;
    }

    for (const [vat, sum] of vatSums) {
      // Round the sum to 2 decimal places
      const roundedSum = Math.round(sum * 100) / 100;
      result.set(vat, roundedSum);
    }

    //this.totalVats = Math.round(this.totalVats * 100) / 100; // Round final sum
    console.error(result, this.totalVats.toFixed(2));
    return this.totalVats;
  }

  countTotal(): number {
    this.totals = 0;
    for (const item of this.items.controls) {
      this.totals += item.value.subtotal;
    }
    console.log('countTotal', this.totals);
    return this.totals;
  }

  countItemTotal(): number {
    this.itemTotals = 0;
    for (const item of this.items.controls) {
      this.itemTotals += item.value.quantity;
    }
    console.log('countItemTotal', this.itemTotals);
    return this.itemTotals;
  }

  countDiscount(): number {
    this.discounts = 0;
    for (const item of this.items.controls) {
      this.discounts +=
        (item.value.isGeneralDscntMethod
          ? (+item.value.discount / 100) * +item.value.price
          : +item.value.discount) * +item.value.quantity;
    }
    console.log('countDiscount', this.discounts);
    return this.discounts;
  }

  countPurchaseDiscount(): number {
    this.discounts = 0;
    for (const item of this.items.controls) {
      this.discounts +=
        (item.value.isGeneralDscntMethod
          ? (+item.value.discount / 100) * +item.value.purchasePrice
          : +item.value.discount) * +item.value.quantity;
    }
    console.log('countPurchaseDiscount', this.discounts);
    return this.discounts;
  }

  countGrandTotal(): void {
    this.grandTotals = +this.totals - 0;
    console.log('countGrandTotal', this.grandTotals);
  }

  CountTotalExclVatDisc() {
    this.totalExcVatDiscs = 0;
    for (let t = 0; t < this.items.length; t++) {
      this.totalExcVatDiscs += +(
        this.items?.value[t].price * this.items?.value[t].quantity
      ).toFixed(2);
    }
    console.log('CountTotalExclVatDisc', this.totalExcVatDiscs);
  }

  CountTotalExclVatDiscPurchase() {
    this.totalExcVatDiscs = 0;
    for (let t = 0; t < this.items.length; t++) {
      this.totalExcVatDiscs += +(
        this.items?.value[t].purchasePrice * this.items?.value[t].quantity
      ).toFixed(2);
    }
    console.log('CountTotalExclVatDisc', this.totalExcVatDiscs);
  }

  // price without discount and vat into consideration
  priceMinusDiscount(price: number, discount: number, discountType: boolean): number {
    let discountValue = 0;
    if (discountType) {
      discountValue = price - price * (discount / 100);
    } else {
      discountValue = price - discount;
    }
    console.log('priceMinusDiscount', price, discount, discountType, discountValue);
    return discountValue;
  }

  // subtotal vat
  subTotalVat(price: number, tax: number, discount: number, discountType: boolean): number {
    console.log('subTotalVat', price, tax, discount, discountType);
    return +((this.priceMinusDiscount(price, discount, discountType) * tax) / 100);
  }

  vatAmount(price: number, discount: number, discountType: boolean, vat: number) {
    console.log('vatAmount', price, discount, discountType, vat);
    return +(this.priceMinusDiscount(price, discount, discountType) * (vat / 100)).toFixed(2);
  }

  updateAllRelevantCounts(): void {
    console.log(this.items);
    this.countTotal();
    this.countItemTotal();
    this.countGrandTotal();
    this.countGrandVat();
    this.countDiscount();
    this.CountTotalExclVatDisc();
  }

  updateAllRelevantPurchaseCounts(): void {
    console.error(this.items);
    this.countTotal();
    this.countItemTotal();
    this.countGrandTotal();
    this.countGrandVat();
    this.countPurchaseDiscount();
    this.CountTotalExclVatDiscPurchase();
  }

  updateTotalOnly(): void {
    this.countTotal();
    this.countGrandTotal();
  }

  resetCounts(): void {
    this.orderTax = 0;
    this.discounts = 0;
    this.totals = 0;
    this.itemTotals = 0;
    this.totalVats = 0;
    this.totalExcVatDiscs = 0;
    this.grandTotals = 0;
  }

  getFieldValue(product: IInventory, field: any): string {
    if (!product || !field) return '';

    const value = product[field.value];
    return field.convert ? field.convert[value] : value;
  }
}
