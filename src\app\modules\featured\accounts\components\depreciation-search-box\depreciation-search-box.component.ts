import { Component, Input, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'app-depreciation-search-box',
  templateUrl: './depreciation-search-box.component.html',
  styleUrls: ['./depreciation-search-box.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: DepreciationSearchBoxComponent,
    },
  ],
})
export class DepreciationSearchBoxComponent implements OnInit {
  @Input() filterTypes: string[];
  searchBoxForm: UntypedFormGroup;
  searchPlaceholder: string;
  touched = false;
  defaultSearchType = 'supplierName';

  constructor(private formBuilder: UntypedFormBuilder) {}

  ngOnInit(): void {
    this.searchBoxForm = this.formBuilder.group({
      searchString: [],
      searchType: [this.defaultSearchType],
    });
    this.updatePlaceHolder(this.defaultSearchType);
    this.searchBoxForm;
    this.searchBoxForm?.controls['searchType'].valueChanges.subscribe(selection => {
      this.updatePlaceHolder(selection);
    });
  }

  updatePlaceHolder(selection) {
    switch (selection) {
      case 'supplierName':
        this.searchPlaceholder = 'Search by Supplier Name';
        break;
      case 'assetAccountId':
        this.searchPlaceholder = 'Search by Asset Account Id';
        break;
      case 'assetLocation':
        this.searchPlaceholder = 'Search by Asset Location';
        break;
      default:
        this.searchPlaceholder = 'Search by Supplier Name';
    }
  }

  resetSearchBox(): void {
    this.searchBoxForm.reset();
    this.searchBoxForm.controls['searchType'].setValue(this.defaultSearchType);
    this.searchBoxForm.updateValueAndValidity();
  }
  writeValue(obj: any): void {
    obj && this.searchBoxForm.setValue(obj, { emitEvent: false });
  }
  registerOnChange(fn: any): void {
    this.searchBoxForm.valueChanges.subscribe(fn);
  }
  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }
  setDisabledState?(isDisabled: boolean): void {}

  onTouched() {}
}
