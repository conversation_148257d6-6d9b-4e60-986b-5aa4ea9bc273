import { HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { ReportApiService } from 'src/app/core/api/report/report-api.service';
import {
  AccountingReportParams,
  PurchaseReportParams,
  ReportParams,
  SalesReportParams,
  TransactionParams,
} from '../models/reportParams';
import { map } from 'rxjs-compat/operator/map';
import { IReportType } from '../../catalog/models/reports';

@Injectable()
export class ReportService {
  constructor(private reportApiService: ReportApiService) {}

  getStockReports(reportParams: ReportParams): Observable<any> {
    const params = this.getParams(reportParams);
    let reportType;
    if (reportParams.type) {
      reportType = 'application/' + reportParams.type;
    } else {
      reportType = 'application/pdf';
    }
    return this.reportApiService.getStockReports(params).pipe(
      tap((result: Array<PERSON>uffer) => {
        const file = new Blob([result], { type: reportType });
        const fileURL = URL.createObjectURL(file);
        window.open(fileURL);
      })
    );
  }

  getInventoryJasperReports(reportParams: ReportParams): Observable<any> {
    const params = this.getParams(reportParams);
    let reportType;
    if (reportParams.type) {
      reportType = 'application/' + reportParams.type;
    } else {
      reportType = 'application/pdf';
    }

    return this.reportApiService
      .getInventoryJasperReports(params, reportParams.jasperEndPoint)
      .pipe(
        tap((result: ArrayBuffer) => {
          const file = new Blob([result], { type: reportType });
          const fileURL = URL.createObjectURL(file);
          window.open(fileURL);
        })
      );
  }
  getPriceReports(reportParams: ReportParams): Observable<any> {
    const params = this.getParams(reportParams);
    let reportType;
    if (reportParams.type) {
      reportType = 'application/' + reportParams.type;
    } else {
      reportType = 'application/pdf';
    }

    return this.reportApiService.getPriceReports(params).pipe(
      tap((result: ArrayBuffer) => {
        const file = new Blob([result], { type: reportType });
        const fileURL = URL.createObjectURL(file);
        window.open(fileURL);
      })
    );
  }

  getInventoryReports(reportParams: ReportParams): Observable<any> {
    const params = this.getParams(reportParams);
    return this.reportApiService.getInventoryReports(params, reportParams.endPoint);
  }
  getAllReportType(type: number): Observable<any> {
    let params: HttpParams = new HttpParams();
    params = params.append('type', type);
    return this.reportApiService.getAllReportType(params);
  }
  getAccountingJasperReports(reportParams: AccountingReportParams): Observable<any> {
    const params = this.getAccountingReportParams(reportParams);
    let reportType;
    if (reportParams.type) {
      reportType = 'application/' + reportParams.type;
    } else {
      reportType = 'application/pdf';
    }

    return this.reportApiService
      .getAccountingJasperReports(params, reportParams.jasperEndPoint)
      .pipe(
        tap((result: ArrayBuffer) => {
          const file = new Blob([result], { type: reportType });
          const fileURL = URL.createObjectURL(file);
          window.open(fileURL);
        })
      );
  }
  getAccountingReports(reportParams: AccountingReportParams): Observable<any> {
    const params = this.getAccountingReportParams(reportParams);
    return this.reportApiService.getAccountingReports(params, reportParams.endPoint);
  }
  getSalesReports(reportParams: SalesReportParams): Observable<any> {
    const params = this.getSalesReportParams(reportParams);
    return this.reportApiService.getSalesReports(params, reportParams.endPoint);
  }
  createReportSetup(reportSetupParams: any) {
    let params: HttpParams = new HttpParams();
    params = params.append('branchId', reportSetupParams.branchId.toString());
    return this.reportApiService.createReportSetup(params, reportSetupParams);
  }

  getReportSetup(params: HttpParams) {
    return this.reportApiService.getReportSetup(params);
  }
  getAccountingReportParams(reportParams: AccountingReportParams) {
    let params: HttpParams = new HttpParams();
    params = params.append('branchId', reportParams.branchId.toString());
    params = params.append('yearId', reportParams.yearId);
    if (reportParams.reportType) params = params.append('reportType', reportParams.reportType);
    if (reportParams.searchString)
      params = params.append('searchString', reportParams.searchString);
    if (reportParams.searchType) params = params.append('searchType', reportParams.searchType);
    if (reportParams.accountGroup)
      params = params.append('accountGroup', reportParams.accountGroup);
    if (reportParams.businessGroup)
      params = params.append('businessGroup', reportParams.businessGroup);
    if (reportParams.accountType) params = params.append('accountType', reportParams.accountType);

    if (reportParams.startingAccountNo) {
      params = params.append('accountNumberFrom', reportParams.startingAccountNo);
    }
    if (reportParams.endingAccountNo) {
      params = params.append('accountNumberTo', reportParams.endingAccountNo);
    }

    if (reportParams.startingDate) params = params.append('dateFrom', reportParams.startingDate);

    if (reportParams.date) params = params.append('date', reportParams.date);

    if (reportParams.endingDate) params = params.append('dateTo', reportParams.endingDate);
    if (reportParams.sortFields) {
      params = params.append('sortFields', reportParams.sortFields.toString());
    }
    if (reportParams.sortBy) {
      params = params.append('sortBy', reportParams.sortBy);
    }
    if (reportParams.sortDir) {
      params = params.append('sortDir', reportParams.sortDir);
    }

    if (reportParams.templateId) params = params.append('templateId', reportParams.templateId);
    if (reportParams.type) params = params.append('type', reportParams.type);
    if (reportParams.page) params = params.append('pageNumber', reportParams.page);
    if (reportParams.pageSize) params = params.append('pageSize', reportParams.pageSize);
    return params;
  }

  getSalesReportParams(reportParams: SalesReportParams) {
    let params: HttpParams = new HttpParams();
    params = params.append('branchId', reportParams.branchId.toString());
    params = params.append('yearId', reportParams.yearId);
    if (reportParams.reportType) params = params.append('reportType', reportParams.reportType);
    if (reportParams.searchString)
      params = params.append('searchString', reportParams.searchString);
    if (reportParams.searchType) params = params.append('searchType', reportParams.searchType);

    if (reportParams.issueDateFrom)
      params = params.append('issueDateFrom', reportParams.issueDateFrom);

    if (reportParams.issueDateTo) params = params.append('issueDateTo', reportParams.issueDateTo);
    if (reportParams.sortFields) {
      params = params.append('sortFields', reportParams.sortFields.toString());
    }
    if (reportParams.sortBy) {
      params = params.append('sortBy', reportParams.sortBy);
    }
    if (reportParams.sortDir) {
      params = params.append('sortDir', reportParams.sortDir);
    }
    if (reportParams.categoryId)
      params = params.append('categoryIds', reportParams.categoryId.toString());
    if (reportParams.warehouseId)
      params = params.append('warehouseIds', reportParams.warehouseId.toString());
    if (reportParams.transactionType)
      params = params.append('transactionType', reportParams.transactionType);
    if (reportParams.invoiceStatus)
      params = params.append('invoiceStatus', reportParams.invoiceStatus);
    if (reportParams.documentNumber)
      params = params.append('documentNumber', reportParams.documentNumber);

    if (reportParams.settlementStatus)
      params = params.append('settlementStatus', reportParams.settlementStatus);
    if (reportParams.templateId) params = params.append('templateId', reportParams.templateId);
    if (reportParams.type) params = params.append('type', reportParams.type);
    if (reportParams.page) params = params.append('pageNumber', reportParams.page);
    if (reportParams.pageSize) params = params.append('pageSize', reportParams.pageSize);
    return params;
  }

  getParams(reportParams: ReportParams): HttpParams {
    let params = new HttpParams();

    params = params.append('branchId', reportParams.branchId.toString());

    if (reportParams.warehouseId && reportParams.warehouseId.length > 0) {
      params = params.append('warehouseIds', reportParams.warehouseId.toString());
    }
    if (reportParams.categoryId && reportParams.categoryId.length > 0) {
      params = params.append('categoryIds', reportParams.categoryId.toString());
    }
    if (reportParams.unitId) {
      params = params.append('unitIds', reportParams.unitId.toString());
    }
    if (reportParams.type) {
      params = params.append('type', reportParams.type);
    }
    if (reportParams.searchString)
      params = params.append('searchString', reportParams.searchString);

    if (reportParams.showDetailed) {
      params = params.append('showDetailed', reportParams.showDetailed);
    }
    if (reportParams.showGrouped) {
      params = params.append('showGrouped', reportParams.showGrouped);
    }
    if (reportParams.reportType) {
      params = params.append('reportType', reportParams.reportType);
    }
    if (reportParams.sortFields) {
      params = params.append('sortFields', reportParams.sortFields.toString());
    }
    if (reportParams.sortBy) {
      params = params.append('sortBy', reportParams.sortBy);
    }
    if (reportParams.sortDir) {
      params = params.append('sortDir', reportParams.sortDir);
    }
    if (reportParams.templateId) {
      params = params.append('templateId', reportParams.templateId);
    }
    if (reportParams.yearId) params = params.append('yearId', reportParams.yearId);
    if (reportParams.page) params = params.append('pageNumber', reportParams.page);
    params = params.append('pageSize', reportParams.pageSize);
    if (reportParams.reportType == 'Item Movement')
      params = params.append('itemId', reportParams.itemId);
    if (reportParams.dateFrom) params = params.append('dateFrom', reportParams?.dateFrom);
    if (reportParams.dateTo) params = params.append('dateTo', reportParams?.dateTo);
    if (reportParams.date) params = params.append('date', reportParams?.date);

    return params;
  }

  getTransactionParams(transactionParams: TransactionParams): HttpParams {
    let params = new HttpParams();
    params = params.append('type', transactionParams.type);
    params = params.append('transactionId', transactionParams.transactionId);
    params = params.append('transactionType', transactionParams.transactionType);
    return params;
  }

  getTransactionReports(transactionParams: TransactionParams): Observable<any> {
    console.log('Report Service ');
    const params = this.getTransactionParams(transactionParams);
    let reportType;
    if (transactionParams.type) {
      reportType = 'application/' + transactionParams.type;
    } else {
      reportType = 'application/pdf';
    }
    console.log(' Report Service ' + params);

    return this.reportApiService.getTransactionReport(params).pipe(
      tap((result: ArrayBuffer) => {
        const file = new Blob([result], { type: reportType });
        const fileURL = URL.createObjectURL(file);
        window.open(fileURL);
      })
    );
  }

  getPurchaseReportParams(reportParams: PurchaseReportParams) {
    let params: HttpParams = new HttpParams();
    params = params.append('branchId', reportParams.branchId.toString());
    params = params.append('yearId', reportParams.yearId);
    if (reportParams.reportType) params = params.append('reportType', reportParams.reportType);
    if (reportParams.searchString)
      params = params.append('searchString', reportParams.searchString);
    if (reportParams.searchType) params = params.append('searchType', reportParams.searchType);

    if (reportParams.issueDateFrom) params = params.append('dateFrom', reportParams.issueDateFrom);

    if (reportParams.issueDateTo) params = params.append('dateTo', reportParams.issueDateTo);
    if (reportParams.sortFields) {
      params = params.append('sortFields', reportParams.sortFields.toString());
    }
    if (reportParams.sortBy) {
      params = params.append('sortBy', reportParams.sortBy);
    }
    if (reportParams.sortDir) {
      params = params.append('sortDir', reportParams.sortDir);
    }
    if (reportParams.categoryId)
      params = params.append('categoryIds', reportParams.categoryId.toString());
    if (reportParams.warehouseId)
      params = params.append('warehouseIds', reportParams.warehouseId.toString());
    if (reportParams.transactionType)
      params = params.append('transactionType', reportParams.transactionType);
    if (reportParams.invoiceStatus)
      params = params.append('invoiceStatus', reportParams.invoiceStatus);
    if (reportParams.settlementStatus)
      params = params.append('settlementStatus', reportParams.settlementStatus);
    if (reportParams.documentNumber)
      params = params.append('documentNumber', reportParams.documentNumber);
    if (reportParams.templateId) params = params.append('templateId', reportParams.templateId);
    if (reportParams.type) params = params.append('type', reportParams.type);
    if (reportParams.page) params = params.append('pageNumber', reportParams.page);
    if (reportParams.pageSize) params = params.append('pageSize', reportParams.pageSize);
    return params;
  }

  getPurchaseReports(reportParams: SalesReportParams): Observable<any> {
    const params = this.getPurchaseReportParams(reportParams);
    return this.reportApiService.getPurchaseReports(params, reportParams.endPoint);
  }
}
