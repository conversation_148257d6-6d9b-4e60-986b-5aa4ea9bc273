<form [formGroup]="form" (ngSubmit)="onSubmit()" autocomplete="off">
  <div class="blank-layout-container justify-content-center align-items-center">
    <div class="position-relative row w-100 h-100 bg-gredient justify-content-center">
      <div class="col-lg-4 d-flex align-items-center">
        <mat-card class="cardWithShadow boxed-auth">
          <mat-progress-bar *ngIf="isBeingLoggedIn" mode="indeterminate"></mat-progress-bar>
          <mat-card-content class="p-32">
            <div class="text-center">
              <a>
                <img
                  class="align-middle m-2 white-background"
                  src="assets/images/sawami_logo.png"
                  alt="logo" />
              </a>
            </div>

            <div class="or-border m-t-20 m-b-10">
              <div class="stacked-title">
                <div class="en-title">Sawami WebApp</div>
                <div class="ar-title">سوامي ويب آب</div>
              </div>
            </div>

            <div class="row no-gutters" *ngIf="!showBranchSelectionForm">
              <div class="col-12 m-t-10" *ngIf="form.get('userType').value">
                <div class="bilingual-label">
                  <span class="en-text">Tenant Code</span>
                  <span class="ar-text">رمز المشترك</span>
                </div>

                <mat-form-field class="w-100 dense-0" [subscriptSizing]="'fixed'">
                  <input [formControl]="form.controls['tenantId']" matInput autofocus />
                  <mat-error
                    *ngIf="
                      form.controls['tenantId'].hasError('required') &&
                      form.controls['tenantId'].touched
                    ">
                    Tenant Code is required
                  </mat-error>
                </mat-form-field>
              </div>
              <div class="col-12 m-t-10">
                <div class="bilingual-label">
                  <span class="en-text">Username</span>
                  <span class="ar-text">أسم المستخدم</span>
                </div>

                <mat-form-field class="w-100 dense-0" [subscriptSizing]="'fixed'">
                  <input [formControl]="form.controls['username']" matInput />
                  <mat-error
                    *ngIf="
                      form.controls['username'].hasError('required') &&
                      form.controls['username'].touched
                    ">
                    Username is required
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="col-12">
                <div class="bilingual-label">
                  <span class="en-text">Password</span>
                  <span class="ar-text">الكلمة السرية</span>
                </div>

                <mat-form-field class="w-100 dense-0">
                  <input
                    [type]="passwordhide ? 'password' : 'text'"
                    [formControl]="form.controls['password']"
                    matInput />
                  <i-tabler
                    class="icon-16 m-r-4 op-4"
                    (click)="passwordhide = !passwordhide; (false)"
                    matSuffix
                    name="{{ passwordhide ? 'eye-off' : 'eye' }}"></i-tabler>
                  <mat-error
                    *ngIf="
                      form.controls['password'].hasError('required') &&
                      form.controls['password'].touched
                    ">
                    Password is required
                  </mat-error>
                </mat-form-field>
              </div>
              <div class="col-12">
                <button class="w-100 m-t-10" mat-raised-button color="primary" type="submit">
                  <div class="bilingual-button">
                    <span class="en-text">Sign In</span>
                    <span class="ar-text">تسجيل الدخول</span>
                  </div>
                </button>
              </div>
            </div>
            <div class="row no-gutters" *ngIf="showBranchSelectionForm">
              <div class="col-12">
                <div class="bilingual-label">
                  <span class="en-text">Branch</span>
                  <span class="ar-text">الفرع</span>
                </div>

                <mat-form-field class="w-100 dense-0" [subscriptSizing]="'fixed'">
                  <mat-select [formControl]="branchId" autofocus>
                    <mat-option
                      *ngFor="let branch of branchList; let i = index"
                      [value]="branch.branchId"
                      >{{ branch.branchName }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
              <div class="col-12">
                <div class="bilingual-label">
                  <span class="en-text">Year</span>
                  <span class="ar-text">السنة</span>
                </div>

                <mat-form-field class="w-100 dense-0" [subscriptSizing]="'fixed'">
                  <mat-select [formControl]="yearId">
                    <mat-option *ngFor="let year of yearList" [value]="year"
                      >{{ year.year }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
              <div class="col-12">
                <button
                  class="w-100 m-t-10"
                  [disabled]="!branchId?.valid && !yearId?.valid"
                  (click)="onSubmitBranchSelection($event)"
                  mat-raised-button
                  color="primary">
                  <div class="bilingual-button">
                    <span class="en-text">Login</span>
                    <span class="ar-text">دخول</span>
                  </div>
                </button>
              </div>
            </div>

            <!-- Language selector removed -->

            <!-- Copyright Footer (English only) -->
            <div class="copyright-footer-actions">
              <div class="copyright-text">&copy; 2025 Sawami Information Technology</div>
              <div class="copyright-subtext">All rights reserved.</div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </div>
</form>
