<mat-card
  class="module-card"
  appearance="outlined"
  style="margin-bottom: 0px !important; padding-bottom: 4px !important">
  <mat-card-header>
    <mat-card-title>
      {{ title | translate }}
    </mat-card-title>
    <mat-card-title>{{ subtitle | translate }}</mat-card-title>
  </mat-card-header>
  <div style="margin: 0 26px 0 16px">
    <div class="left">
      <a
        *ngIf="onCreateForm.observers.length > 0"
        (click)="openCreateForm()"
        mat-stroked-button
        color="primary">
        <mat-icon>add</mat-icon> {{ 'components.table.register' | translate }} {{ title }}
      </a>
      <a
        *ngIf="onReload.observers.length > 0"
        (click)="handleReload()"
        mat-stroked-button
        color="accent">
        <mat-icon>refresh</mat-icon> {{ 'components.table.reload' | translate }}
      </a>
    </div>
    <div class="right" *ngIf="customActionData">
      <a (click)="handleCustomAction()" mat-stroked-button color="{{ customActionData.color }}">
        <mat-icon>{{ customActionData.icon }}</mat-icon> {{ customActionData.title }}
      </a>
    </div>
    <div class="right" *ngIf="onFilter.observers.length > 0">
      <mat-form-field class="w-100">
        <input
          [(ngModel)]="searchString"
          (keyup)="handleFilter()"
          matInput
          name="searchString"
          type="text"
          autocomplete="off"
          placeholder="{{ 'components.table.filter' | translate }} {{ title | translate }}" />
      </mat-form-field>
    </div>
  </div>
  <!-- Table -->
  <table
    [dataSource]="tableDataSource"
    (matSortChange)="handleSort($event)"
    fixedLayout="true"
    mat-table
    mat-table-stripped
    matSort>
    <ng-container *ngFor="let tableColumn of columns" [matColumnDef]="tableColumn.name">
      <!-- if showable column header -->
      <ng-container
        *ngIf="tableColumn.isShowable || tableColumn.dataKey === 'action'; else notShowable">
        <!-- if sortable column header -->
        <ng-container *ngIf="tableColumn.isSortable; else notSortable">
          <th
            *matHeaderCellDef
            [mat-sort-header]="tableColumn.name"
            [arrowPosition]="tableColumn.position === 'right' ? 'before' : 'after'"
            mat-header-cell>
            <ng-container *ngIf="tableColumn.dataKey !== 'selected'">{{
              tableColumn.name
            }}</ng-container>
            <ng-container *ngIf="tableColumn.dataKey === 'selected'">
              <mat-checkbox
                [checked]="isAllSelected()"
                [indeterminate]="!isAllSelected()"
                (change)="$event ? masterToggle() : null">
              </mat-checkbox>
            </ng-container>
          </th>
        </ng-container>
        <!-- else not sortable -->
        <ng-template #notSortable>
          <th
            *matHeaderCellDef
            [class.text-right]="tableColumn.position === 'right'"
            mat-header-cell>
            <ng-container *ngIf="tableColumn.dataKey !== 'selected'">{{
              tableColumn.name
            }}</ng-container>
            <ng-container *ngIf="tableColumn.dataKey === 'selected'">
              <mat-checkbox
                [checked]="isAllSelected()"
                [indeterminate]="!isAllSelected()"
                (change)="$event ? masterToggle() : null">
              </mat-checkbox>
            </ng-container>
          </th>
        </ng-template>
      </ng-container>

      <ng-template #notShowable>
        <th *matHeaderCellDef hidden mat-header-cell></th>
      </ng-template>

      <!-- column data -->
      <!-- if showable column data -->
      <ng-container
        *ngIf="tableColumn.isShowable || tableColumn.dataKey === 'action'; else notShowableData">
        <td
          *matCellDef="let element"
          [class.text-right]="tableColumn.position === 'right'"
          [ngClass]="{ 'text-right': tableColumn.dataKey === 'action' }"
          mat-cell>
          <ng-container *ngIf="tableColumn.isShowable && tableColumn.dataKey !== 'selected'">
            <span *ngIf="tableColumn.dataKey !== 'isAlert'">
              {{ element | dataPropertyGetter : tableColumn.dataKey }}
            </span>
            <span *ngIf="tableColumn.dataKey === 'tax'"> ({{ element.taxMethod }})</span>
            <span *ngIf="tableColumn.dataKey === 'isAlert'"
              >{{ element.isAlert ? '&#10060;' : '&#9989;' }} ({{ element.alertQuantity }})</span
            >
          </ng-container>

          <ng-container *ngIf="tableColumn.dataKey === 'selected'">
            <mat-checkbox
              [checked]="element.selected"
              (click)="$event.stopPropagation()"
              (change)="element.selected = !element.selected">
            </mat-checkbox>
          </ng-container>
          <ng-container *ngIf="tableColumn.dataKey === 'action'">
            <button
              *ngIf="customActionOneData"
              (click)="openCustomActionOne(element)"
              mat-stroked-button
              matTooltip="{{ customActionOneData.title }}"
              color="{{ customActionOneData.color }}">
              <mat-icon>{{ customActionOneData.icon }}</mat-icon>
            </button>
            <button
              *ngIf="onView.observers.length > 0"
              (click)="openViewForm(element)"
              mat-stroked-button
              color="primary">
              <mat-icon>remove_red_eye</mat-icon>
            </button>
            <button
              *ngIf="onEditForm.observers.length > 0"
              (click)="openEditForm(element)"
              matTooltip="Edit"
              mat-stroked-button
              color="accent">
              <mat-icon>mode_edit</mat-icon>
            </button>
            <button
              *ngIf="onDelete.observers.length > 0"
              (click)="openDeleteConfirmationDialog(element.id)"
              matTooltip="Delete"
              mat-stroked-button
              color="danger">
              <mat-icon>delete</mat-icon>
            </button>
          </ng-container>
        </td>
      </ng-container>

      <ng-template #notShowableData>
        <th *matCellDef="let element" hidden mat-cell></th>
      </ng-template>
    </ng-container>

    <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
    <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
  </table>
  <!-- Pagination -->
  <mat-paginator
    *ngIf="totalCount > 0"
    [length]="totalCount"
    [pageSize]="pageSize"
    [pageSizeOptions]="[5, 10, 25, 100]"
    (page)="onPageChange($event)"
    showFirstLastButtons>
  </mat-paginator>
</mat-card>
