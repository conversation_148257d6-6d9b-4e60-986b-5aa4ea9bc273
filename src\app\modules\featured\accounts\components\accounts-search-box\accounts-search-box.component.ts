import { Component, Input, OnInit } from '@angular/core';
import {
  <PERSON><PERSON><PERSON><PERSON>Accessor,
  UntypedFormBuilder,
  UntypedFormGroup,
  NG_VALUE_ACCESSOR,
} from '@angular/forms';

@Component({
  selector: 'app-accounts-search-box',
  templateUrl: './accounts-search-box.component.html',
  styleUrls: ['./accounts-search-box.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: AccountsSearchBoxComponent,
    },
  ],
})
export class AccountsSearchBoxComponent implements OnInit {
  @Input() filterTypes: string[];
  searchBoxForm: UntypedFormGroup;
  searchPlaceholder: string;
  touched = false;
  defaultSearchType = 'accountNumber';

  constructor(private formBuilder: UntypedFormBuilder) {}

  ngOnInit(): void {
    this.searchBoxForm = this.formBuilder.group({
      searchString: [],
      searchType: [this.defaultSearchType],
    });
    this.updatePlaceHolder(this.defaultSearchType);
    this.searchBoxForm &&
      this.searchBoxForm?.controls['searchType'].valueChanges.subscribe(selection => {
        this.updatePlaceHolder(selection);
      });
  }

  updatePlaceHolder(selection) {
    switch (selection) {
      case 'nameArabic':
        this.searchPlaceholder = 'searchPanel.searchByArabicName';
        break;
      case 'nameEnglish':
        this.searchPlaceholder = 'searchPanel.searchByEnglishName';
        break;
      case 'accountNumber':
        this.searchPlaceholder = 'searchPanel.searchByAccountNumber';
        break;
      default:
        this.searchPlaceholder = 'searchPanel.searchByAccountNumber';
    }
  }

  resetSearchBox(): void {
    this.searchBoxForm.reset();
    this.searchBoxForm.controls['searchType'].setValue(this.defaultSearchType);
    this.searchBoxForm.updateValueAndValidity();
  }

  writeValue(obj: any): void {
    obj && this.searchBoxForm.setValue(obj, { emitEvent: false });
  }
  registerOnChange(fn: any): void {
    this.searchBoxForm.valueChanges.subscribe(fn);
  }
  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }
  setDisabledState?(isDisabled: boolean): void {}

  onTouched() {}
}
