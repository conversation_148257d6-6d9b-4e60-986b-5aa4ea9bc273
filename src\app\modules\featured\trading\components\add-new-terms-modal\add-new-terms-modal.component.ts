import { Component } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-add-new-terms-modal',
  templateUrl: './add-new-terms-modal.component.html',
  styleUrls: ['./add-new-terms-modal.component.scss'],
})
export class AddNewTermsModalComponent {
  newSectionName = '';
  newSectionTerms = '';

  constructor(private dialogRef: MatDialogRef<AddNewTermsModalComponent>) {}

  onAddNewSection() {
    if (this.newSectionName && this.newSectionTerms) {
      this.dialogRef.close({
        name: this.newSectionName,
        clause: this.newSectionTerms,
      });
    }
  }

  onCancel() {
    this.dialogRef.close();
  }
}
