import { LOCALE_ID, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { OwnerRoutingModule } from './owner-routing.module';
import { TenantListingComponent } from './components/tenant-listing/tenant-listing.component';
import { TenantConfigurationComponent } from './components/tenant-configuration/tenant-configuration.component';
import { OwnerdashboardComponent } from './components/ownerdashboard/ownerdashboard.component';
import { FlexLayoutModule } from '@angular/flex-layout';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MaterialModule } from '../../material/material.module';
import { SharedModule } from '../../shared/shared.module';
import { ProductModuleService } from '../home/<USER>/product-module.service';
import { TenantsService } from './services/tenants.service';
import { ZatcaRegistrationComponent } from './components/zatca-registration/zatca-registration.component';
import { ZatcaListingsComponent } from './components/zatca-listings/zatca-listings.component';

@NgModule({
  declarations: [
    TenantListingComponent,
    TenantConfigurationComponent,
    OwnerdashboardComponent,
    ZatcaRegistrationComponent,
    ZatcaListingsComponent,
  ],
  imports: [
    CommonModule,
    OwnerRoutingModule,
    SharedModule,
    MaterialModule,
    TranslateModule,
    FlexLayoutModule,
  ],
  providers: [ProductModuleService, TenantsService, { provide: LOCALE_ID, useValue: 'en' }],
})
export class OwnerModule {
  constructor(private translateService: TranslateService) {
    this.translateService.use('en');
  }
}
