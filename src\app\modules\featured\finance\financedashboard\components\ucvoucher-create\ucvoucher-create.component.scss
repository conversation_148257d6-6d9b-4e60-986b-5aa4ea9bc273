@import 'variables';

table {
  width: 100%;
  overflow: auto !important;
}

tr.mat-mdc-footer-row {
  font-weight: bold;
}

.mdc-data-table__cell,
.mdc-data-table__header-cell {
  padding: 0 1px 0 0px;
}

.mat-column-accountId {
  min-width: 160px !important;
}

.mat-column-distributorAccountId {
  max-width: 100px !important;
}

.mat-column-costCentreId {
  max-width: 100px !important;
}

.mat-column-accountName {
  max-width: 100px !important;
}

.mat-column-notes {
  max-width: 100px !important;
}

.mat-column-issueType {
  max-width: 100px !important;
}

.mat-column-amount {
  max-width: 100px !important;
}

.mat-column-price {
  max-width: 120px !important;
}

.mat-column-vatAmount {
  max-width: 120px !important;
}

.mat-column-discount {
  max-width: 120px !important;
}

.mat-column-subtotal {
  max-width: 120px !important;
}

.mat-column-notes {
  max-width: 120px !important;
}

mat-option .mat-mdc-row {
  display: flex;
}

.mat-mdc-header-cell {
  text-align: center !important;
}

.mat-mdc-cell {
  text-align: center;
  justify-content: center;
}

.no-wrap {
  white-space: normal;
}

.summary-table {
  width: 100%;
  border-collapse: collapse;
}

.summary-item {
  padding: 4px;
  font-size: 16px;
  border: 1px solid $borderColor;
  text-align: right; /* Align labels (text) to the right */
}

// .value-column {
//   text-align: center; /* Align values to the center */
// }

.no-divider {
  border: none; /* Remove border for the last row */
}

// .summary-table td:nth-last-child(-n + 2) {
//   text-align: center;
// }
