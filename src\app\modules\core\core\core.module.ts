import { CommonModule, DatePipe } from '@angular/common';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { NgModule, Optional, SkipSelf } from '@angular/core';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { BusyService } from 'src/app/modules/core/core/services/busy.service';
import { LocalStorageService } from 'src/app/modules/core/core/services/local-storage.service';
import { MultilingualService } from 'src/app/modules/core/core/services/multilingual.service';
import { AuthGuard } from './guards/auth.guard';
import { DashBoardGuard } from './guards/dashboard.guard';
import { PermissionGuard } from './guards/permission.guard';
import { RoleGuard } from './guards/role.guard';
import { ErrorInterceptor } from './interceptors/error.interceptor';
import { JwtInterceptor } from './interceptors/jwt.interceptor';
import { LoadingInterceptor } from './interceptors/loading.interceptor';
import { EventBusService } from './services/event-bus.service';

const guards = [AuthGuard, PermissionGuard, DashBoardGuard, RoleGuard];
const services = [
  AuthService,
  LocalStorageService,
  MultilingualService,
  BusyService,
  EventBusService,
];

@NgModule({
  declarations: [],
  imports: [CommonModule, HttpClientModule],
  providers: [
    guards,
    services,
    { provide: HTTP_INTERCEPTORS, useClass: JwtInterceptor, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: LoadingInterceptor, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptor, multi: true },
    DatePipe,
  ],
})
export class CoreModule {
  constructor(@Optional() @SkipSelf() parentModule: CoreModule) {
    if (parentModule) {
      throw new Error('Core is already loaded. Import it in the AppModule only');
    }
  }
}
