import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class DepreciationApiService {
  baseUrl = environment.apiUrl + 'company/accounts/depreciations';

  constructor(private http: HttpClient) {}

  getAllDepreciation(params: HttpParams) {
    return this.http.get(this.baseUrl + '/' + 'pages', { params: params });
  }

  createDepreciation(depreciation: any) {
    return this.http.post(this.baseUrl, depreciation);
  }

  getDepreciationById(depreciationId: string) {
    return this.http.get(this.baseUrl + '/' + depreciationId);
  }

  updateDepreciation(depreciationId: string, depreciation: any) {
    return this.http.put(this.baseUrl + '/' + depreciationId, depreciation);
  }
}
