import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { Account } from '../../models/account';
import { ChartOfAccountsService } from '../../services/chart-of-accounts.service';
import { AccountParams } from '../../models/accountParams';
import { PaginatedFilter } from 'src/app/core/interfaces/PaginatedFilter';
import { HttpParams } from '@angular/common/http';
import * as moment from 'moment';
import { AccountsProsearchBoxComponent } from '../accounts-prosearch-box/accounts-prosearch-box.component';
import { AccountsSearchBoxComponent } from '../accounts-search-box/accounts-search-box.component';
import { AccountAutoSearchComponent } from '../accountAutoSearch/account-auto-search.component';
import { accountGroups, accountTypes, businessGroups } from 'src/app/core/configs/dropDownConfig';

@Component({
  selector: 'app-chart-of-accounts',
  templateUrl: './account-statement-reports.component.html',
  styleUrls: ['./account-statement-reports.component.scss'],
  providers: [
    AccountAutoSearchComponent, // added class in the providers
  ],
})
export class AccountStatementReportsComponent implements OnInit {
  @ViewChild('fromAccountSearchBoxForm') fromAccountSearchBoxForm: AccountsProsearchBoxComponent;
  @ViewChild('toAccountSearchBoxForm') toAccountSearchBoxForm: AccountsProsearchBoxComponent;
  @ViewChild('searchBoxForm') searchBoxForm: AccountsSearchBoxComponent;
  accounts: Account[];
  isLoading = true;
  filterForm: UntypedFormGroup;
  isAdvancedSearchEnabled = false;
  accountGroups = accountGroups;

  accountTypes = accountTypes;

  businessGroups = businessGroups;

  constructor(
    private chartOfAccountsService: ChartOfAccountsService,
    private fb: UntypedFormBuilder,
    private accountSearchComponent: AccountAutoSearchComponent
  ) {}

  ngOnInit(): void {
    this.filterForm = this.fb.group({
      accountGroup: [null],
      businessGroup: [null],
      accountType: [null],
      searchBoxForm: [],
      reportType: ['PDF'],
      startingAccountNo: [],
      endingAccountNo: [],
      startingDate: [],
      endingDate: [],
    });
    this.getAccounts();
  }

  getAccounts(): void {
    const accountParams: AccountParams = new AccountParams();
    accountParams.pageNumber = 1;
    accountParams.pageSize = 10;
    this.chartOfAccountsService.getAllChartOfAccounts(accountParams).subscribe(result => {
      this.accounts = result.accounts;
      this.isLoading = false;
    });
  }

  getReport(event?: Event, pageEvent?: PaginatedFilter) {
    event?.preventDefault();
    console.log(this.filterForm);
    this.filterForm.markAllAsTouched();
    this.isLoading = true;
    let params: HttpParams = new HttpParams();
    if (this.filterForm.controls['reportType'].value)
      params = params.append('type', this.filterForm.controls['reportType'].value);
    if (this.searchBoxForm.searchBoxForm.controls['searchString'].value)
      params = params.append(
        'searchString',
        this.searchBoxForm.searchBoxForm.controls['searchString'].value
      );
    if (this.searchBoxForm.searchBoxForm.controls['searchType'].value)
      params = params.append(
        'searchType',
        this.searchBoxForm.searchBoxForm.controls['searchType'].value
      );
    if (this.filterForm.controls['accountGroup'].value)
      params = params.append('accountGroup', this.filterForm.controls['accountGroup'].value);
    if (this.filterForm.controls['businessGroup'].value)
      params = params.append('businessGroup', this.filterForm.controls['businessGroup'].value);
    if (this.filterForm.controls['accountType'].value)
      params = params.append('accountType', this.filterForm.controls['accountType'].value);

    if (this.filterForm.controls['startingAccountNo'].value) {
      const parts = this.filterForm.controls['startingAccountNo'].value.searchString.split('-');
      const fromAccountNo = parts[0];
      params = params.append('accountNumberFrom', fromAccountNo);
    }
    if (this.filterForm.controls['endingAccountNo'].value) {
      const parts = this.filterForm.controls['endingAccountNo'].value.searchString.split('-');
      const toAccountNo = parts[0];
      params = params.append('accountNumberTo', toAccountNo);
    }

    if (this.filterForm.controls['startingDate'].value)
      params = params.append(
        'dateFrom',
        moment(this.filterForm.controls['startingDate'].value).format('DD-MM-YYYY')
      );
    if (this.filterForm.controls['endingDate'].value)
      params = params.append(
        'dateTo',
        moment(this.filterForm.controls['endingDate'].value).format('DD-MM-YYYY')
      );
    if (this.filterForm.controls['date'].value)
      params = params.append(
        'date',
        moment(this.filterForm.controls['date'].value).format('DD-MM-YYYY')
      );
    this.chartOfAccountsService
      .getAccountStatementReport(params)
      .subscribe(result => {
        console.log('Success....');
      })
      .add(() => (this.isLoading = false));
  }

  onFromAccountSelection(selectedAccount: Account) {
    this.fromAccountSearchBoxForm.searchBoxForm.controls['searchString'].setValue(
      selectedAccount.accountNumber +
        '-' +
        this.accountSearchComponent.getAccountName(selectedAccount)
    );
    this.fromAccountSearchBoxForm.accountValueSet = true;
  }

  onToAccountSelection(selectedAccount: Account) {
    this.toAccountSearchBoxForm.searchBoxForm.controls['searchString'].setValue(
      selectedAccount.accountNumber +
        '-' +
        this.accountSearchComponent.getAccountName(selectedAccount)
    );
    this.toAccountSearchBoxForm.accountValueSet = true;
  }

  clearFilters(event: Event) {
    event.preventDefault();
    this.filterForm.markAsUntouched();
    this.filterForm.markAsPristine();
    this.filterForm.reset();
    this.searchBoxForm.resetSearchBox();
    this.getReport();
  }
}
