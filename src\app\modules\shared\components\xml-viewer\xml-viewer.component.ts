import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import xmlFormat from 'xml-formatter';
import { IXmlViewer } from './xml-viewer';

@Component({
  selector: 'app-xml-viewer',
  templateUrl: './xml-viewer.component.html',
  styleUrls: ['./xml-viewer.component.scss'],
})
export class XmlViewerComponent {
  formattedXmlContent: string;
  constructor(@Inject(MAT_DIALOG_DATA) public data: IXmlViewer) {
    this.formattedXmlContent = xmlFormat(this.data.xmlData, { indentation: '  ' });
    console.log('xml', this.formattedXmlContent);
  }

  downloadXml(): void {
    const blob = new Blob([this.data.xmlData], { type: 'application/xml' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = this.data.documentUUID;
    a.click();
    window.URL.revokeObjectURL(url);
  }
}
