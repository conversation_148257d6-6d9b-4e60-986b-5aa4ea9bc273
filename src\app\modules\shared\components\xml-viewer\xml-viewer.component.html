<!-- <div class="direction bg-primary">
  <h2 class="text-centered" mat-dialog-title>
    {{ 'xmlViewer.xmlInvoice' | translate }}
  </h2>
  <button class="close-button" mat-dialog-close mat-icon-button>
    <i-tabler class="icon-20 text-error" name="X"></i-tabler>
  </button>
</div> -->

<app-dialog-header [title]="'xmlViewer.xmlInvoice' | translate"></app-dialog-header>

<div class="text-center m-b-10">
  <button (click)="downloadXml(); $event.preventDefault()" mat-flat-button color="warn">
    {{ 'xmlViewer.download' | translate }}
  </button>
</div>
<div mat-dialog-content>
  <pre>{{ formattedXmlContent }}</pre>
</div>
