<app-dialog-header *ngIf="modalData?.data"></app-dialog-header>

<ng-container>
  <mat-card appearance="outlined">
    <form [ngClass]="{ readOnly: isViewMode }" [formGroup]="supplierForm" autocomplete="off">
      <mat-card-title>{{ 'supplier.supplierTitle' | translate }}</mat-card-title>
      <div class="row no-gutters">
        <!-- parentAccountId  -->
        <div class="p-2 col-md-4 col-sm-6">
          <mat-label>{{ 'supplier.parentId' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <mat-select formControlName="parentAccountId">
              <mat-option
                *ngFor="let parentAccount of parentAccounts"
                [value]="parentAccount.accountId">
                {{ parentAccount | localized }}
              </mat-option>
            </mat-select>
            <mat-error
              *ngIf="
                supplierForm?.controls['parentAccountId'].hasError('required') &&
                supplierForm?.controls['parentAccountId'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </mat-form-field>
        </div>
        <!-- nameArabic  -->
        <div class="p-2 col-md-4 col-sm-6">
          <mat-label>{{ 'customer.name' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input type="text" maxlength="40" matInput formControlName="nameArabic" />
            <mat-error
              *ngIf="
                supplierForm?.controls['nameArabic'].hasError('required') &&
                supplierForm?.controls['nameArabic'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </mat-form-field>
        </div>
        <!-- nameEnglish -->
        <div class="p-2 col-md-4 col-sm-6">
          <mat-label>{{ 'customer.englishName' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input type="text" maxlength="40" matInput formControlName="nameEnglish" />
            <mat-error
              *ngIf="
                supplierForm?.controls['nameEnglish'].hasError('required') &&
                supplierForm?.controls['nameEnglish'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </mat-form-field>
        </div>
      </div>
      <div class="row no-gutters">
        <!-- accountNumber  -->
        <div class="p-2 col-md-3 col-sm-6">
          <mat-label>{{ 'common.field.accountNumber' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input readonly type="text" maxlength="40" matInput formControlName="accountNumber" />
          </mat-form-field>
        </div>
        <!-- vatNumber -->
        <div class="p-2 col-md-3 col-sm-6">
          <mat-label>{{ 'common.field.vatNo' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input type="text" maxlength="40" matInput formControlName="vatNumber" />
            <mat-error
              *ngIf="
                supplierForm?.controls['vatNumber'].hasError('required') &&
                supplierForm?.controls['vatNumber'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </mat-form-field>
        </div>
        <!-- phoneNumber -->
        <div class="p-2 col-md-3 col-sm-6">
          <mat-label>{{ 'common.field.phone' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input type="text" maxlength="40" matInput formControlName="phoneNumber" />
            <mat-error
              *ngIf="
                supplierForm?.controls['phoneNumber'].hasError('required') &&
                supplierForm?.controls['phoneNumber'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </mat-form-field>
        </div>
        <div class="p-2 col-md-3 col-sm-6">
          <mat-label>{{ 'common.field.email' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input type="text" maxlength="40" matInput formControlName="emailId" />
            <mat-error
              *ngIf="
                supplierForm.controls['emailId'].hasError('required') &&
                supplierForm.controls['emailId'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
            <mat-error
              *ngIf="
                supplierForm.controls['emailId'].errors?.email &&
                supplierForm.controls['emailId'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </mat-form-field>
        </div>
      </div>
      <mat-card-title>{{ 'common.field.transactionDetails' | translate }}</mat-card-title>
      <div class="row no-gutters">
        <!-- discountPercent -->
        <div class="p-2 col-md-3 col-sm-6">
          <mat-label>{{ 'common.field.percDiscount' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input type="text" maxlength="40" matInput formControlName="discountPercent" />
            <mat-error
              *ngIf="
                supplierForm?.controls['discountPercent'].hasError('required') &&
                supplierForm?.controls['discountPercent'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </mat-form-field>
        </div>
        <!-- allowedBalance -->
        <div class="p-2 col-md-3 col-sm-6">
          <mat-label>{{ 'common.field.allowedBalance' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input type="number" matInput formControlName="allowedBalance" />
            <mat-error
              *ngIf="
                supplierForm?.controls['allowedBalance'].hasError('required') &&
                supplierForm?.controls['allowedBalance'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </mat-form-field>
        </div>
        <!-- paymentToleranceDays -->
        <div class="p-2 col-md-3 col-sm-6">
          <mat-label>{{ 'common.field.allowedDays' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input type="text" maxlength="40" matInput formControlName="paymentToleranceDays" />
            <mat-error
              *ngIf="
                supplierForm?.controls['paymentToleranceDays'].hasError('required') &&
                supplierForm?.controls['paymentToleranceDays'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </mat-form-field>
        </div>
        <!-- commission -->
        <div class="p-2 col-md-3 col-sm-6">
          <mat-label>{{ 'common.field.commission' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input type="number" matInput formControlName="commission" />
            <mat-error
              *ngIf="
                supplierForm?.controls['commission'].hasError('required') &&
                supplierForm?.controls['commission'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </mat-form-field>
        </div>
        <!-- yearlyTarget -->
        <div class="p-2 col-md-3 col-sm-6">
          <mat-label>{{ 'common.field.yrlyTarget' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input type="number" matInput formControlName="yearlyTarget" />
            <mat-error
              *ngIf="
                supplierForm?.controls['yearlyTarget'].hasError('required') &&
                supplierForm?.controls['yearlyTarget'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </mat-form-field>
        </div>
      </div>
      <div class="row no-gutters">
        <div class="p-2 col-md-2 col-lg-2 col-sm-3">
          <mat-slide-toggle [labelPosition]="'before'" formControlName="isFreezed">{{
            'common.field.freezed' | translate
          }}</mat-slide-toggle>
        </div>
        <div class="p-2 col-md-2 col-lg-2 col-sm-3">
          <mat-slide-toggle [labelPosition]="'before'" formControlName="hasCashPrivilege">{{
            'paymentsType.cash' | translate
          }}</mat-slide-toggle>
        </div>
        <div class="p-2 col-md-2 col-lg-2 col-sm-3">
          <mat-slide-toggle [labelPosition]="'before'" formControlName="hasCreditPrivilege">{{
            'paymentsType.credit' | translate
          }}</mat-slide-toggle>
        </div>
        <div class="p-2 col-md-2 col-lg-2 col-sm-3">
          <mat-slide-toggle [labelPosition]="'before'" formControlName="hasTransferPrivilege">{{
            'paymentsType.bank' | translate
          }}</mat-slide-toggle>
        </div>
        <div class="p-2 col-md-2 col-lg-2 col-sm-3">
          <mat-slide-toggle [labelPosition]="'before'" formControlName="hasCardPrivilege">{{
            'paymentsType.card' | translate
          }}</mat-slide-toggle>
        </div>
      </div>
      <div
        class="row no-gutters"
        *ngIf="supplierForm.errors?.atLeastOneRequired && supplierForm?.touched">
        <mat-error> {{ 'paymentsType.paymentSelectionError' | translate }} </mat-error>
      </div>
      <app-address #addressForm [noValidators]="true" formControlName="address"></app-address>
    </form>
    <div class="text-center" *ngIf="!modalData?.isViewMode">
      <button
        class="m-l-10"
        *ngIf="isCreateMode"
        (click)="onSubmit($event); (false)"
        type="button"
        mat-stroked-button
        color="primary">
        {{ 'common.buttons.submit' | translate }}
      </button>
      <button
        class="m-l-10"
        *ngIf="isCreateMode"
        [routerLink]="['../']"
        type="button"
        mat-stroked-button
        color="warn">
        {{ 'common.buttons.cancel' | translate }}
      </button>
      <button
        class="m-l-10"
        *ngIf="isEditMode"
        (click)="onSubmit($event); (false)"
        type="button"
        mat-flat-button
        color="primary">
        {{ 'common.buttons.save' | translate }}
      </button>
      <button
        class="m-l-10"
        *ngIf="isEditMode"
        [routerLink]="['../../']"
        type="button"
        mat-stroked-button
        color="warn">
        {{ 'common.buttons.cancel' | translate }}
      </button>
      <button
        class="m-l-10"
        *ngIf="isViewMode"
        [routerLink]="['../../']"
        type="button"
        mat-stroked-button
        color="primary">
        {{ 'common.buttons.back' | translate }}
      </button>
    </div>
  </mat-card>
</ng-container>
