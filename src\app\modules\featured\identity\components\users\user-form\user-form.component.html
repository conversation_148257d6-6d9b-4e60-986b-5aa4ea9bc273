<form *ngIf="!loading" [formGroup]="userForm">
  <mat-card [ngClass]="{ readOnly: isViewMode }" appearance="outlined">
    <mat-card-title>{{ formTitle | translate }}</mat-card-title>

    <div class="row no-gutters">
      <div class="col-md-6 col-sm-6 p-2">
        <mat-label>{{ 'users.firstName' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <input [formControl]="userForm.controls['firstName']" matInput />
          <mat-error
            *ngIf="
              userForm.controls['firstName'].hasError('required') &&
              userForm.controls['firstName'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
          <mat-error
            *ngIf="
              userForm.controls['firstName'].hasError('minlength') &&
              userForm.controls['firstName'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
          <mat-error
            *ngIf="
              userForm.controls['firstName'].hasError('maxlength') &&
              userForm.controls['firstName'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>

      <div class="col-md-6 col-sm-6 col-sm-6 p-2">
        <mat-label>{{ 'users.lastName' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <input [formControl]="userForm.controls['lastName']" matInput />
          <mat-error
            *ngIf="
              userForm.controls['lastName'].hasError('required') &&
              userForm.controls['lastName'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
          <mat-error
            *ngIf="
              userForm.controls['lastName'].hasError('minlength') &&
              userForm.controls['lastName'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
          <mat-error
            *ngIf="
              userForm.controls['lastName'].hasError('maxlength') &&
              userForm.controls['lastName'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>

      <div class="col-md-3 col-lg-3 col-sm-6 p-2">
        <mat-label>{{ 'users.userName' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <input
            [maxLength]="15"
            [disabled]="isEditMode"
            [formControl]="userForm.controls['username']"
            matInput />
          <mat-error
            *ngIf="
              userForm.controls['username'].hasError('required') &&
              userForm.controls['username'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
          <mat-error
            *ngIf="
              userForm.controls['username'].hasError('maxlength') &&
              userForm.controls['username'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>

      <div class="col-md-3 col-lg-3 col-sm-6 p-2" *ngIf="isEditMode">
        <mat-label>{{ 'users.userPasswordReset' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <input [formControl]="userForm.controls['password']" matInput />
          <mat-error
            *ngIf="
              userForm.controls['password'].hasError('minlength') &&
              userForm.controls['password'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
          <mat-error
            *ngIf="
              userForm.controls['password'].hasError('maxlength') &&
              userForm.controls['password'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>

      <div class="col-md-3 col-lg-3 col-sm-6 p-2">
        <mat-label>{{ 'users.userEmail' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <input [formControl]="userForm.controls['emailId']" matInput type="text" />
          <mat-error
            *ngIf="
              userForm.controls['emailId'].errors?.email && userForm.controls['emailId'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>

      <div class="col-md-3 col-lg-3 col-sm-6 p-2">
        <mat-label>{{ 'users.userPhone' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <input [formControl]="userForm.controls['phoneNumber']" matInput type="number" />
          <mat-error
            *ngIf="
              userForm.controls['phoneNumber'].hasError('required') &&
              userForm.controls['phoneNumber'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
          <mat-error
            *ngIf="
              userForm.controls['phoneNumber'].hasError('minlength') &&
              userForm.controls['phoneNumber'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>

      <div class="col-md-3 col-lg-3 col-sm-6 p-2 align-toggle">
        <mat-slide-toggle
          [labelPosition]="'after'"
          [formControl]="userForm.controls['isPosUser']"
          (change)="onUserTypeChange()"
          >IsPosUser</mat-slide-toggle
        >
      </div>

      <div class="col-md-3 col-lg-3 col-sm-6 p-2" *ngIf="!userForm.get('isPosUser').value">
        <mat-label>{{ 'users.userRoles' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <mat-select [formControl]="userForm.controls['roleIds']" multiple>
            <mat-option *ngFor="let role of rolesList" [value]="role.id">
              {{ role?.name }}
            </mat-option>
          </mat-select>
          <mat-error
            *ngIf="
              userForm?.controls['roleIds'].hasError('required') &&
              userForm?.controls['roleIds'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>

      <div class="col-md-3 col-lg-3 col-sm-6 p-2" *ngIf="userForm.get('isPosUser').value">
        <mat-label>{{ 'users.userRoles' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <mat-select [formControl]="userForm.controls['roleIds']">
            <mat-option *ngFor="let role of rolesList" [value]="role.id">
              {{ role?.name }}
            </mat-option>
          </mat-select>
          <mat-error
            *ngIf="
              userForm?.controls['roleIds'].hasError('required') &&
              userForm?.controls['roleIds'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>

      <div class="col-md-3 col-lg-3 col-sm-6 p-2" *ngIf="!userForm.get('isPosUser').value">
        <mat-label>{{ 'users.userBranches' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <mat-select [formControl]="userForm.controls['branchIds']" multiple>
            <mat-option *ngFor="let branch of branchesList" [value]="branch.branchId"
              >{{ branch | localized }}
            </mat-option>
          </mat-select>
          <mat-error
            *ngIf="
              userForm?.controls['branchIds'].hasError('required') &&
              userForm?.controls['branchIds'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>

      <div class="col-md-3 col-lg-3 col-sm-6 p-2" *ngIf="userForm.get('isPosUser').value">
        <mat-label>{{ 'users.userBranches' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <mat-select [formControl]="userForm.controls['branchIds']">
            <mat-option *ngFor="let branch of branchesList" [value]="branch.branchId"
              >{{ branch | localized }}
            </mat-option>
          </mat-select>
          <mat-error
            *ngIf="
              userForm?.controls['branchIds'].hasError('required') &&
              userForm?.controls['branchIds'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>

      <div class="col-md-3 col-lg-3 col-sm-6 p-2">
        <mat-label>{{ 'users.userDefaultBranch' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <mat-select [formControl]="userForm.controls['defaultBranchId']">
            <mat-option *ngFor="let branch of defaultBranchesList" [value]="branch.branchId"
              >{{ branch | localized }}
            </mat-option>
          </mat-select>
          <mat-error
            *ngIf="
              userForm?.controls['defaultBranchId'].hasError('required') &&
              userForm?.controls['defaultBranchId'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>

      <div class="col-md-3 col-lg-3 col-sm-6 p-2" *ngIf="!userForm.get('isPosUser').value">
        <mat-label>{{ 'users.userWarehouse' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <mat-select [formControl]="userForm.controls['warehouseIds']" multiple>
            <mat-option *ngFor="let branch of wareHouseList" [value]="branch.warehouseId"
              >{{ branch | localized }} / {{ branch.branchNameArabic }}
            </mat-option>
          </mat-select>
          <mat-error
            *ngIf="
              userForm?.controls['warehouseIds'].hasError('required') &&
              userForm?.controls['warehouseIds'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>

      <div class="col-md-3 col-lg-3 col-sm-6 p-2" *ngIf="userForm.get('isPosUser').value">
        <mat-label>{{ 'users.userWarehouse' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <mat-select [formControl]="userForm.controls['warehouseIds']">
            <mat-option *ngFor="let branch of wareHouseList" [value]="branch.warehouseId"
              >{{ branch | localized }} / {{ branch.branchNameArabic }}
            </mat-option>
          </mat-select>
          <mat-error
            *ngIf="
              userForm?.controls['warehouseIds'].hasError('required') &&
              userForm?.controls['warehouseIds'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>

      <div class="col-md-3 col-lg-3 col-sm-6 p-2 align-toggle">
        <mat-slide-toggle [labelPosition]="'after'" [formControl]="userForm.controls['active']">{{
          'common.active' | translate
        }}</mat-slide-toggle>
      </div>
      <div class="col-12 p-2" *ngIf="isCreateMode">
        <p class="text-primary">{{ 'users.defaultPassword' | translate }} : password</p>
      </div>
    </div>
  </mat-card>
  <div class="text-center">
    <ng-container *ngIf="isCreateMode">
      <button
        class="m-l-10"
        *ngIf="isCreateMode"
        (click)="onSubmit($event); (false)"
        type="button"
        mat-stroked-button
        color="primary">
        {{ 'common.submit' | translate }}
      </button>
      <button
        class="m-l-10"
        *ngIf="isCreateMode"
        [routerLink]="['../']"
        type="button"
        mat-stroked-button
        color="warn">
        {{ 'common.cancel' | translate }}
      </button>
    </ng-container>

    <ng-container *ngIf="isEditMode">
      <button
        class="m-l-10"
        (click)="onSubmit($event); (false)"
        type="button"
        mat-stroked-button
        color="primary">
        {{ 'common.submit' | translate }}
      </button>
      <button
        class="m-l-10"
        *ngIf="isEditMode"
        [routerLink]="['../../']"
        type="button"
        mat-stroked-button
        color="warn">
        {{ 'common.cancel' | translate }}
      </button>
    </ng-container>

    <ng-container *ngIf="isViewMode">
      <button
        class="m-l-10"
        [routerLink]="['../../']"
        type="button"
        mat-stroked-button
        color="primary">
        {{ 'common.buttons.back' | translate }}
      </button>
    </ng-container>
  </div>
</form>
