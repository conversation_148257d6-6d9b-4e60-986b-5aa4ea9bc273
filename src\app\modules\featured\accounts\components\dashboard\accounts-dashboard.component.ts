import { Component, OnInit } from '@angular/core';
import { DashboardModulesHolder } from 'src/app/modules/shared/components/dashboard-modules-holder/modulesHolder';

@Component({
  selector: 'app-accounts-dashboard',
  templateUrl: './accounts-dashboard.component.html',
  styleUrls: ['./accounts-dashboard.component.scss'],
})
export class AccountsDashboardComponent {
  productModulesList: DashboardModulesHolder[] = [
    {
      moduleName: 'navigationMenus.chartOfAccounts',
      moduleDescription: 'Manage chart of accounts.',
      modulePermission: ['ChartOfAccounts', 'AllPermissions'],
      moduleImage: 'inventory',
      moduleRouterLink: '../chartOfAccounts',
      moduleButtonAction: 'BackOffice',
      moduleType: 'subModule',
    },
    {
      moduleName: 'navigationMenus.journalEntries',
      moduleDescription: 'Find Journal entries.',
      modulePermission: ['JournalEntries', 'AllPermissions'],
      moduleImage: 'inventory',
      moduleRouterLink: '../journalEntries',
      moduleButtonAction: 'BackOffice',
      moduleType: 'subModule',
    },
    {
      moduleName: 'navigationMenus.fixedAsset',
      moduleDescription: 'Manage and update Fixed Asset',
      modulePermission: ['Depreciation', 'AllPermissions'],
      moduleImage: 'inventory',
      moduleRouterLink: '../fixedAsset',
      moduleButtonAction: 'BackOffice',
      moduleType: 'mainModule',
    },
    {
      moduleName: 'navigationMenus.accountReports',
      moduleDescription: 'Find reports for accounts.',
      modulePermission: ['ChartOfAccounts', 'AllPermissions'],
      moduleImage: 'inventory',
      moduleRouterLink: '../accountsReports',
      moduleButtonAction: 'BackOffice',
      moduleType: 'subModule',
    },
    {
      moduleName: 'navigationMenus.costCenter',
      moduleDescription: 'Manage Cost Centres.',
      modulePermission: ['ChartOfAccounts', 'AllPermissions'],
      moduleImage: 'inventory',
      moduleRouterLink: '../costCentres',
      moduleButtonAction: 'BackOffice',
      moduleType: 'subModule',
    },
    {
      moduleName: 'navigationMenus.accountBookSetup',
      moduleDescription: 'Configure new account.',
      modulePermission: ['ChartOfAccounts', 'AllPermissions'],
      moduleImage: 'inventory',
      moduleRouterLink: '../accountSetup',
      moduleButtonAction: 'BackOffice',
      moduleType: 'subModule',
    },
  ];
}
