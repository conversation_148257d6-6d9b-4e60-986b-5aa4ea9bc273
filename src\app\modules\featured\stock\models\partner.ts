export interface Partner {
  parentAccountId: number;
  nameArabic: string;
  nameEnglish: string;
  partnerBranchId: number;
}

export interface PartnerList {
  branchPartnerResponses: BranchPartnerResponse[];
}

export interface BranchPartnerResponse {
  id: number;
  nameArabic: string;
  nameEnglish: string;
  accountNumber: number;
  accountId: number;
  parentAccountId: number;
  branchId: number;
  partnerBranchId: number;
  partnerBranchNameArabic: string;
  partnerBranchNameEnglish: string;
}
