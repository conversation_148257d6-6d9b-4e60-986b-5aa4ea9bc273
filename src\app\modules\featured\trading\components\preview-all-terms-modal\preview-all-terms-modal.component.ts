import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-preview-all-terms-modal',
  templateUrl: './preview-all-terms-modal.component.html',
  styleUrls: ['./preview-all-terms-modal.component.scss'],
})
export class PreviewAllTermsModalComponent {
  constructor(
    @Inject(MAT_DIALOG_DATA) public sections: { name: string; clause: string }[],
    private dialogRef: MatDialogRef<PreviewAllTermsModalComponent>
  ) {}

  onClose() {
    this.dialogRef.close();
  }
}
