import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { IDistributor, IDistributorResponse } from '../../interfaces/distributor';
import { DistributorParams } from '../../models/params/distributorParams';

@Injectable({
  providedIn: 'root',
})
export class DistributorService {
  baseUrl = environment.apiUrl + 'sales/distributors';

  constructor(private http: HttpClient) {}

  getAllDistributors(params: DistributorParams) {
    const getDistributorParams = this.getParams(params);
    return this.http.get<IDistributorResponse>(this.baseUrl + '/pages', {
      params: getDistributorParams,
    });
  }

  getDistributorById(id: string) {
    return this.http.get<IDistributor>(this.baseUrl + `/${id}`);
  }

  createDistributor(supplier: IDistributor) {
    return this.http.post(this.baseUrl, supplier);
  }

  updateDistributor(supplier: IDistributor, id: string) {
    return this.http.put(this.baseUrl + '/' + id, supplier);
  }

  deleteDistributor(id: string) {
    return this.http.delete(this.baseUrl + `/${id}`);
  }

  getParams(distributorParams: DistributorParams): HttpParams {
    let httpParams = new HttpParams();
    if (distributorParams?.searchString)
      httpParams = httpParams.append('searchString', distributorParams.searchString);
    if (distributorParams?.pageNumber)
      httpParams = httpParams.append('pageNumber', distributorParams.pageNumber.toString());
    if (distributorParams?.pageSize)
      httpParams = httpParams.append('pageSize', distributorParams.pageSize.toString());
    if (distributorParams?.orderBy)
      httpParams = httpParams.append('orderBy', distributorParams.orderBy.toString());
    return httpParams;
  }
}
