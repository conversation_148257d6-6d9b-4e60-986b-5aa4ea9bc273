import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnInit,
} from '@angular/core';
import {
  ControlValueAccessor,
  NG_VALUE_ACCESSOR,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';

@Component({
  selector: 'app-searchbox',
  templateUrl: './searchbox.component.html',
  styleUrls: ['./searchbox.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: SearchboxComponent,
    },
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SearchboxComponent implements OnInit, ControlValueAccessor {
  @Input() filterTypes: string[];
  @Input() nameEnglish = true;
  @Input() nameArabic = true;
  @Input() accountNo = false;
  @Input() accountName = false;
  @Input() itemCode = true;
  @Input() defaultSearchType = 'itemCode';
  @Input() validatorApplied = false;
  searchBoxForm: UntypedFormGroup;
  searchPlaceholder: string;
  touched = false;

  constructor(private formBuilder: UntypedFormBuilder, private cdr: ChangeDetectorRef) {}

  ngOnInit() {
    this.searchBoxForm = this.formBuilder.group({
      searchString: [
        '',
        Validators.compose(
          this.validatorApplied ? [Validators.required, Validators.minLength(3)] : []
        ),
      ],
      searchType: [
        this.defaultSearchType,
        Validators.compose(this.validatorApplied ? [Validators.required] : []),
      ],
    });
    this.updatePlaceHolder(this.defaultSearchType);
    this.searchBoxForm &&
      this.searchBoxForm?.controls['searchType'].valueChanges.subscribe(selection => {
        this.updatePlaceHolder(selection);
      });
  }

  resetSearchBox(): void {
    this.searchBoxForm.reset();
    this.searchBoxForm.controls['searchType'].setValue(this.defaultSearchType);
    this.searchBoxForm.updateValueAndValidity();
  }

  markAllAsTouched(): void {
    this.searchBoxForm.markAllAsTouched();
    this.cdr.markForCheck();
  }

  get isValid(): boolean {
    return this.searchBoxForm.valid;
  }

  updatePlaceHolder(selection) {
    switch (selection) {
      case 'nameArabic':
        this.searchPlaceholder = 'searchPanel.searchByArabicName';
        break;
      case 'nameEnglish':
        this.searchPlaceholder = 'searchPanel.searchByEnglishName';
        break;
      case 'itemCode':
        this.searchPlaceholder = 'searchPanel.searchByCode';
        break;
      case 'accountNo':
        this.searchPlaceholder = 'searchPanel.searchByAccountNo';
        break;
      case 'accountName':
        this.searchPlaceholder = 'searchPanel.searchByAccountName';
        break;
      default:
        this.searchPlaceholder = 'searchPanel.searchByCode';
    }
  }

  writeValue(obj: any): void {
    obj && this.searchBoxForm.setValue(obj, { emitEvent: false });
  }
  registerOnChange(fn: any): void {
    this.searchBoxForm.valueChanges.subscribe(fn);
  }
  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }
  setDisabledState?(isDisabled: boolean): void {}

  onTouched() {}
}
