import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { CustomValidators } from 'ngx-custom-validators';
import { ImageCroppedEvent } from 'ngx-image-cropper';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { AddressComponent } from 'src/app/modules/shared/components/address/address.component';
import { BranchParams } from '../../../models/branchParams';
import { StoreParams } from '../../../models/storeParams';
import { BranchService } from '../../../services/branch.service';
import { ActionType } from 'src/app/core/enums/actionType';
import { StoreService } from 'src/app/core/api/store.service';
import { CommonService } from 'src/app/core/api/common.service';

@Component({
  selector: 'app-store-form',
  templateUrl: './store-form.component.html',
  styleUrls: ['./store-form.component.scss'],
})
export class StoreFormComponent implements OnInit {
  @ViewChild('addressForm') addressForm: AddressComponent;
  storeForm: UntypedFormGroup;
  imageChangedEvent: any = '';
  croppedImage: any = '';
  showCropper = false;
  defaultImage = 'assets/images/users/default.png';
  file: File;
  formTitle: string;
  branches = [];
  storeId: string;
  mode: ActionType;
  dummyAddressData = {
    buildingNumber: '1234', // 4 digits minimum
    streetName: 'Main Street',
    district: 'Downtown District',
    additionalNumber: 'A1',
    postalCode: '12345', // 5 digits minimum
    city: 'New York',
    country: 'United States',
    address2: 'Apt 2B',
    shortAddress: 'MainSt',
    addressId: null,
  };
  constructor(
    private branchService: BranchService,
    private authService: AuthService,
    private toastr: ToastrService,
    private storeService: StoreService,
    private fb: UntypedFormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private commonService: CommonService
  ) {}

  get isEditMode(): boolean {
    return this.mode === ActionType.edit;
  }
  get isViewMode(): boolean {
    return this.mode === ActionType.view;
  }

  get isCreateMode(): boolean {
    return this.mode === ActionType.create;
  }

  ngOnInit(): void {
    this.mode = this.route.snapshot.data['mode'];
    this.formTitle = this.route.snapshot.data['title'];
    this.route.params.subscribe(params => {
      this.storeId = params['id'];
      if (this.storeId) {
        this.getStores(this.storeId);
      }
    });
    this.initializeForm();
    this.getAllBranches();
    if (this.isCreateMode) {
      this.storeForm.get('address').patchValue(this.dummyAddressData);
    }
  }

  getStores(storeId: string): void {
    this.storeService.getById(new StoreParams(), storeId).subscribe(
      response => {
        this.patchData(response);
      },
      error => console.log(error)
    );
  }

  patchData(data): void {
    const storeFormData = {
      nameArabic: data?.nameArabic,
      nameEnglish: data?.nameEnglish,
      phoneNumber: data?.phoneNumber,
      emailId: data?.emailId,
      branchId: data?.branchId,
      address: data?.address,
      isPos: data?.isPos,
    };
    this.storeForm.patchValue(storeFormData);
    // check if in view mode
    if (this.isViewMode) {
      this.storeForm.disable();
      this.addressForm.disableForm();
    }
  }

  initializeForm(data?: any) {
    this.storeForm = this.fb.group({
      nameArabic: ['', Validators.compose([Validators.required])],
      nameEnglish: ['', Validators.compose([Validators.required])],
      phoneNumber: ['', Validators.compose([Validators.required])],
      emailId: ['', Validators.compose([CustomValidators.email])],
      createdBy: this.authService.getUserId,
      branchId: ['', Validators.compose([Validators.required])],
      address: [],
      isPos: [false],
    });
  }

  fileChangeEvent(event: any): void {
    const mimeType = event.target.files[0].type;
    if (mimeType.match(/image\/*/) == null) {
      return;
    }
    this.file = event.target.files[0];
    this.showCropper = true;
    this.imageChangedEvent = event;
  }
  imageCropped(event: ImageCroppedEvent) {
    this.croppedImage = event.base64;
  }
  imageLoaded() {
    // show cropper
  }
  cropperReady() {
    // cropper ready
  }
  loadImageFailed() {
    // show message
  }

  getAllBranches(): void {
    this.branchService.getAllBranches(new BranchParams()).subscribe(result => {
      this.branches = result;
    });
  }

  onSubmit(event: Event) {
    event.preventDefault();
    this.addressForm.markFormAsTouched();
    this.storeForm.markAllAsTouched();
    if (this.storeForm && this.storeForm?.valid && this.addressForm.isValid()) {
      if (!this.isEditMode) {
        this.storeService.create(new StoreParams(), this.storeForm.value).subscribe(response => {
          //this.toastr.success('WareHouse added Successfully');
          this.commonService.playSuccessSound();
          this.router.navigate(['../'], { relativeTo: this.route });
        });
      } else {
        this.storeService
          .update(new StoreParams(), this.storeId, this.storeForm.value)
          .subscribe(response => {
            //this.toastr.success('WareHouse updated Successfully');
            this.commonService.playSuccessSound();
            this.router.navigate(['../../'], { relativeTo: this.route });
          });
      }
    } else {
      this.commonService.scrollToError();
    }
  }
}
