import { SelectionModel } from '@angular/cdk/collections';
import { Component, ElementRef, ViewChild } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { Subject } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { SupplierService } from 'src/app/core/api/trading/supplier.service';
import { ISupplier, ISupplierResponse } from 'src/app/core/interfaces/supplier';
import { SupplierParams } from 'src/app/core/models/params/supplierParams';

@Component({
  selector: 'app-supplier-get-all',
  templateUrl: './supplier-get-all.component.html',
  styleUrls: ['./supplier-get-all.component.scss'],
})
export class SupplierGetAllComponent {
  @ViewChild(MatSort) sort: MatSort;
  @ViewChild('filter') filter: ElementRef;
  @ViewChild('searchInput') searchInput: ElementRef;
  constructor(
    private customerService: SupplierService,
    public dialogRef: MatDialogRef<SupplierGetAllComponent>
  ) {
    this.searchSubject.pipe(debounceTime(300)).subscribe(searchTerm => {
      this.applyFilter(searchTerm);
    });
  }
  displayedSearchColumns: string[] = [
    'nameArabic',
    'nameEnglish',
    'vatNumber',
    'accountNumber',
    'phoneNumber',
    'emailId',
  ];
  public isLoading = false;
  public suppliers: ISupplier[] = [];
  public resultNotFound = false;
  dataSource: MatTableDataSource<ISupplier>;
  selection = new SelectionModel<ISupplier>();
  private searchSubject = new Subject<string>();

  ngOnInit(): void {
    this.getAllSuppliers();
  }

  getAllSuppliers() {
    this.isLoading = true;
    const params = new SupplierParams();
    params.pageSize = *********;
    this.customerService
      .getAllSuppliers(params)
      .subscribe(
        (searchResult: ISupplierResponse) => (
          (this.isLoading = false),
          (this.suppliers = searchResult.suppliers),
          (this.resultNotFound = this.suppliers?.length > 0 ? false : true),
          (this.dataSource = new MatTableDataSource<ISupplier>(this.suppliers)),
          (this.selection = new SelectionModel<ISupplier>(true))
        )
      );
  }

  applyFilter(filterValue: string): void {
    filterValue = filterValue.trim();
    filterValue = filterValue.toLowerCase();
    this.dataSource.filter = filterValue;
    if (this.dataSource.filteredData.length > 0) {
      this.selection.clear(); // Clear previous selection
      this.selection.select(this.dataSource.filteredData[0]); // Select the first record
    }
  }

  clearSearchInput() {
    this.searchInput.nativeElement.value = '';
    this.applyFilter('');
  }

  selectRow(customer: ISupplier) {
    console.log('selected supplier', customer);
    this.dialogRef.close(customer);
  }

  onSearchInput(value: string): void {
    this.searchSubject.next(value);
  }
}
