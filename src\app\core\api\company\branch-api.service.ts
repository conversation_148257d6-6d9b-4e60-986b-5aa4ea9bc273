import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Branch } from 'src/app/modules/featured/catalog/models/branch';
import { environment } from '../../../../environments/environment';

@Injectable()
export class BranchApiService {
  baseUrl = environment.apiUrl + 'company/branches';
  apiBaseUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

  getAllBranches(params: HttpParams) {
    return this.http.get(this.baseUrl, { params: params });
  }

  getAllBranchesWithYear(params: HttpParams) {
    return this.http.get(this.apiBaseUrl + 'auth/users/branches-years', { params: params });
  }

  getAllLoggedInBranches() {
    return this.http.get(this.baseUrl);
  }

  getBranchById(params: HttpParams, id: string) {
    return this.http.get(this.baseUrl + '/' + id, { params: params });
  }

  createBranch(params: HttpParams, branch: Branch) {
    return this.http.post(this.baseUrl, branch, { params: params });
  }

  updateBranch(params: HttpParams, branchId: string, branch: any) {
    return this.http.put(this.baseUrl + '/' + branchId, branch, { params: params });
  }

  deleteBranch(params: HttpParams, branchId: string) {
    return this.http.delete(this.baseUrl + '/' + branchId, { params: params });
  }

  importBranch(params: HttpParams, formData: FormData) {
    return this.http.post(this.baseUrl + '/import', formData, {
      withCredentials: true,
      responseType: 'arraybuffer',
      params: params,
    });
  }
}
