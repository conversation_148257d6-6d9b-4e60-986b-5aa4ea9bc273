export interface SetupRequest {
  cashSalesAcctId: number;
  creditSalesAcctId: number;
  cardSalesAcctId: number;
  wireTrnsfrSalesAcctId: number;
  salesVatAcctId: number;
  salesDscntAcctId: number;
  salesReturnAcctId: number;
  roundingDscntAcctId: number;
  cashPrchsAcctId: number;
  creditPrchsAcctId: number;
  cardPrchsAcctId: number;
  wireTrnsfrPrchsAcctId: number;
  prchsVatAcctId: number;
  prchsDscntAcctId: number;
  prchsReturnAcctId: number;
  trnsfrInAcctId: number;
  trnsfrOutAcctId: number;
  trnsfrInReturnAcctId: number;
  trnsfrOutReturnAcctId: number;
  badInvDebitAcctId: number;
  badInvCreditAcctId: number;
  shrtgInvDebitAcctId: number;
  shrtgInvCreditAcctId: number;
  srplsInvDebitAcctId: number;
  srplsInvCreditAcctId: number;
  cogsAcctId: number;
  cogsEndAcctId: number;
}
