/* eslint-disable @typescript-eslint/ban-types */
import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { createParamsFromObject } from 'src/app/core/utils/date-utils';
import { environment } from 'src/environments/environment';
import { IBranchListResponse, ITenant, ITenantConfig, ITenants } from '../tenant';

@Injectable()
export class TenantsService {
  baseUrl = environment.apiUrl + 'admin/tenants';
  branchesUrl = environment.apiUrl + 'company/branches/tenant';

  constructor(private http: HttpClient) {}

  getAllTenants() {
    // const getTenantsParams = this.getParams(params);
    return this.http.get<ITenants>(this.baseUrl + '/pages');
  }

  getTenantsById(id: string) {
    return this.http.get<ITenant>(this.baseUrl + `/${id}`);
  }

  getTenantsConfigById(id: string) {
    return this.http.get<ITenantConfig>(this.baseUrl + `/config/${id}`);
  }

  getTenantsBranches(id: string) {
    return this.http.get<IBranchListResponse[]>(this.branchesUrl + `/${id}`);
  }

  createTenants(tenants: ITenant) {
    return this.http.post(this.baseUrl, tenants);
  }

  createTenantsConfig(tenants: ITenantConfig, id: number) {
    return this.http.post(this.baseUrl + `/config/${id}`, tenants);
  }

  editTenants(tenants: ITenantConfig, id: string) {
    return this.http.put(this.baseUrl + `/config/${id}`, tenants);
  }

  editTenantsBasic(tenants: ITenant, id: string) {
    return this.http.put(this.baseUrl + `/${id}`, tenants);
  }

  getParams(salesParams: Object): HttpParams {
    return createParamsFromObject(salesParams);
  }
}
