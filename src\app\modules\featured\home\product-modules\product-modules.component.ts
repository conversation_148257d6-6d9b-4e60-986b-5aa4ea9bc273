import { Component, OnInit } from '@angular/core';
import { DashboardModulesHolder } from 'src/app/modules/shared/components/dashboard-modules-holder/modulesHolder';
import { ProductModuleService } from './product-module.service';

@Component({
  selector: 'app-product-modules',
  templateUrl: './product-modules.component.html',
  styleUrls: ['./product-modules.component.scss'],
})
export class ProductModulesComponent implements OnInit {
  productModulesList: DashboardModulesHolder[] = [];

  constructor(private productModuleService: ProductModuleService) {
    this.productModulesList = this.productModuleService.getPosApps();
  }

  ngOnInit(): void {}
}
