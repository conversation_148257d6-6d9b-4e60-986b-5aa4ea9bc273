import { Directionality } from '@angular/cdk/bidi';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { Units } from '../../models/units';
import { DeleteConfirmationComponent } from 'src/app/modules/shared/components/delete-confirmation/delete-confirmation.component';
import { UnitService } from 'src/app/core/api/unit.service';
import { CommonService } from 'src/app/core/api/common.service';

@Component({
  selector: 'app-units',
  templateUrl: './units.component.html',
  styleUrls: ['./units.component.scss'],
})
export class UnitsComponent implements OnInit {
  @ViewChild('filter') filter: ElementRef;
  @ViewChild('searchInput') searchInput: ElementRef;
  @ViewChild(MatPaginator) set matPaginator(paginator: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = paginator;
    }
  }
  @ViewChild(MatSort) set matSort(sort: MatSort) {
    if (this.dataSource) {
      this.dataSource.sort = sort;
    }
  }
  units: Units[];
  displayedColumns: string[];
  dataSource: MatTableDataSource<Units>;
  isLoading = true;
  constructor(
    public unitApiService: UnitService,
    private authService: AuthService,
    public dialog: MatDialog,
    private toastr: ToastrService,
    private direction: Directionality,
    private commonService: CommonService
  ) {}

  ngOnInit(): void {
    this.getUnits();
    this.initColumns();
  }

  getUnits(): void {
    this.unitApiService.getAllUnits().subscribe((result: Units[]) => {
      this.units = result;
      this.isLoading = false;
      this.dataSource = new MatTableDataSource<Units>(this.units);
    });
  }

  initColumns(): void {
    this.displayedColumns = ['action', 'name', 'type', 'factorRefUom'];
  }

  applyFilter(filterValue: string): void {
    filterValue = filterValue.trim();
    filterValue = filterValue.toLowerCase();
    this.dataSource.filter = filterValue;
  }

  clearSearchInput() {
    this.searchInput.nativeElement.value = '';
    this.getUnits();
  }

  deleteUnit(unitId: string) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '600px';
    dialogConfig.panelClass = 'green_theme';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(DeleteConfirmationComponent, dialogConfig);
    dialogRef.componentInstance.accepted.subscribe(() => {
      this.unitApiService.deleteUnit(unitId).subscribe(result => {
        this.commonService.playSuccessSound();
        this.getUnits();
        this.isLoading = false;
      });
    });
  }
}
