import { Directionality } from '@angular/cdk/bidi';
import { Component, QueryList, ViewChild, ViewChildren } from '@angular/core';
import {
  FormControl,
  UntypedFormArray,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { forkJoin, of } from 'rxjs';
import { CostCentresApiService } from 'src/app/core/api/accounts/cost-centres-api.service';
import { CommonService } from 'src/app/core/api/common.service';
import { DistributorService } from 'src/app/core/api/trading/distributor.service';
import { PaymentService } from 'src/app/core/api/trading/payment.service';
import { issueType } from 'src/app/core/configs/dropDownConfig';
import { ActionType } from 'src/app/core/enums/actionType';
import { IDistributor } from 'src/app/core/interfaces/distributor';
import { IPaymentViewData } from 'src/app/core/interfaces/payment';
import {
  IAccountDetails,
  PaymentVoucher,
  VoucherItems,
  customePayment,
  voucherType,
} from 'src/app/core/interfaces/sales';
import { DistributorParams } from 'src/app/core/models/params/distributorParams';
import { convertDateForBE } from 'src/app/core/utils/date-utils';
import { ConfirmDialogComponent } from 'src/app/modules/shared/components/confirm-dialog/confirm-dialog.component';
import { StandardPaymentsComponent } from 'src/app/modules/shared/components/standard-payments/standard-payments.component';
import { AccountAutoSearchComponent } from '../../../../accounts/components/accountAutoSearch/account-auto-search.component';
import { CostCentre } from '../../../../accounts/models/costCentre';

@Component({
  selector: 'app-ucvoucher-create',
  templateUrl: './ucvoucher-create.component.html',
  styleUrls: ['./ucvoucher-create.component.scss'],
})
export class UcvoucherCreateComponent {
  @ViewChild('paymentForm') private paymentForm: StandardPaymentsComponent;
  @ViewChildren(AccountAutoSearchComponent)
  autoSearchComponents: QueryList<AccountAutoSearchComponent>;
  customePaymentSelection: customePayment = <customePayment>{};
  paymentViewData: IPaymentViewData = <IPaymentViewData>{};
  loading = true;
  displayRemoveIcon = false;
  formTitle: string;
  dataSource: MatTableDataSource<any>;
  voucherRows: UntypedFormArray;
  voucherForm: UntypedFormGroup;
  issueTypes = issueType;

  columnsToDisplay: string[] = [
    'action',
    'accountId',
    'amount',
    'distributorAccountId',
    'costCentreId',
    'issueType',
    'notes',
  ];

  mode: ActionType;
  processType: string;
  voucherID: string | null = null;
  totalPaidAmount = 0;
  //
  distributorAccounts: IDistributor[] = [];
  costCentreAccounts: CostCentre[] = [];
  voucherData: PaymentVoucher;

  constructor(
    private route: ActivatedRoute,
    private formBuilder: UntypedFormBuilder,
    private costCentresApiService: CostCentresApiService,
    private distributorService: DistributorService,
    private commonService: CommonService,
    private direction: Directionality,
    private dialog: MatDialog,
    private paymentService: PaymentService,
    private toastr: ToastrService
  ) {
    this.customePaymentSelection.bankType = true;
    this.customePaymentSelection.cardType = true;
    this.customePaymentSelection.cashType = true;
    this.customePaymentSelection.creditType = false;
  }

  get IsViewMode() {
    return this.mode === ActionType.view;
  }

  ngOnInit(): void {
    this.loading = true;
    this.mode = this.route.snapshot.data['mode'];
    this.formTitle = this.route.snapshot.data['title'];
    this.processType = this.route.snapshot.data['process'];
    this.route.params.subscribe(params => {
      this.voucherID = params['id'];
      this.getAllDropDownDataNew();
    });
    // as per req: show only one drop down value preset
    if (this.processType === 'receipt') {
      this.issueTypes = issueType.filter(data => data.value === 'GENERAL');
    }
  }

  getAllDropDownDataNew(): void {
    const voucherData = this.voucherID
      ? this.paymentService.getPaymentVouchersByID(this.voucherID)
      : of(null);
    const distributorAccounts = this.distributorService.getAllDistributors(new DistributorParams());
    const costCentreAccounts = this.costCentresApiService.getAllCostCentres();
    forkJoin([distributorAccounts, costCentreAccounts, voucherData]).subscribe(results => {
      console.log(results);
      this.distributorAccounts = results[0]?.distributors;
      this.costCentreAccounts = results[1];
      if (results[2]) {
        this.voucherData = results[2];
      }
      this.initializeForm(this.voucherData);
      this.loading = false;
    });
  }

  initializeForm(data?: PaymentVoucher) {
    this.voucherRows = this.formBuilder.array([]);
    this.voucherForm = this.formBuilder.group({
      voucherDate: [new Date(), Validators.compose([Validators.required])],
      voucherEntries: this.voucherRows,
    });
    if (data) {
      this.getdata(data);
      this.voucherForm.get('voucherDate').setValue(data.voucherDate);
      this.paymentViewData = data.payments;
      this.paymentViewData.isViewMode = true;
    } else {
      this.addEntryRow();
    }
    if (this.IsViewMode) {
      this.voucherForm.disable();
    }
  }

  getdata(data) {
    data.items.forEach((data: VoucherItems) => {
      console.log(data);
      const itemFormGroup = this.formBuilder.group({
        id: null,
        invoiceId: [data?.id],
        invoiceNumber: [data?.invoiceNumber ?? null],
        invoiceDate: [data?.invoiceDate ?? null],
        unpaidAmount: [0],
        paidAmount: [data.paidAmount],
        notes: [data?.notes ?? null],
        distributorAccountId: [data?.distributorAccountId ?? null],
        costCentreId: [data?.costCentreId ?? null],
        account: [data?.account ?? null],
        issueType: data?.issueType?.toUpperCase() ?? 'GENERAL', //this.data.voucherType === voucherType.PurchasePayable,
      });
      console.log(itemFormGroup);

      this.voucherRows.push(itemFormGroup);
      this.updateActionButtonStatus();
    });
    this.getTotal();
  }

  getAccountIdPlaceholder(control: any): string {
    return '';
  }
  accountSelected(account: IAccountDetails, formcontrol: FormControl, index: number): void {
    console.log('account selected', account);
    const accountConfig: IAccountDetails = {
      accountId: account.accountId,
      accountNumber: account.accountNumber,
      nameArabic: account.nameArabic,
      nameEnglish: account.nameEnglish,
    };
    formcontrol.get('account').setValue(accountConfig);
    const inputs = document.querySelectorAll('.next');
    const currentIndex = inputs.length - 5;
    const nextIndex = currentIndex;
    if (nextIndex < inputs.length) {
      const nextInput = inputs[nextIndex] as HTMLInputElement;
      setTimeout(() => nextInput.focus());
    }
  }

  addEntryRow(data?) {
    console.log('Data:', data);
    const row = this.formBuilder.group({
      account: [data && data?.account, Validators.required],
      paidAmount: [data?.paidAmount ?? 0, Validators.required],
      notes: [data && data?.notes],
      issueType: [data?.entryId ?? 'GENERAL'],
      distributorAccountId: [data && data?.distributorAccountId],
      costCentreId: [data && data?.costCentreId],
    });
    this.voucherRows.push(row);
    this.updateActionButtonStatus();
  }

  updateActionButtonStatus() {
    this.dataSource = new MatTableDataSource(
      (this.voucherForm.get('voucherEntries') as UntypedFormArray).controls
    );

    if ((this.voucherForm.get('voucherEntries') as UntypedFormArray).length === 1) {
      this.displayRemoveIcon = false;
    } else {
      this.displayRemoveIcon = true;
    }
  }

  getTotal() {
    this.totalPaidAmount = 0;
    const controls = this.voucherForm.get('voucherEntries') as UntypedFormArray;
    for (const control of controls.controls) {
      const paidAmount = control.get('paidAmount').value;
      this.totalPaidAmount += paidAmount;
    }
  }

  onSubmit(event: Event): void {
    event.preventDefault();
    console.log('form submitted', this.voucherForm);
  }

  deleteEntry(element: any, index: number) {
    console.log(index);
    const myFormArray = this.voucherForm.get('voucherEntries') as UntypedFormArray;
    myFormArray.removeAt(index);
    this.updateActionButtonStatus();
    this.getTotal();
  }

  jumpToNext(event: Event, index: number) {
    event.preventDefault();
    const nextField = document.querySelectorAll('.next');
    const currentIndex = Array.from(nextField).indexOf(event.target as HTMLElement);
    const nextIndex = currentIndex + 1;
    if (nextIndex < nextField.length) {
      const nextElement = nextField[nextIndex] as HTMLElement;
      nextElement.focus();
    } else {
      this.addEntryRow();
      setTimeout(() => {
        this.jumpToNext(event, index);
      }, 300);
    }
  }

  submitVoucher(event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    this.voucherForm.markAllAsTouched();
    this.runFunctionInAutoSearchComponents();
    this.paymentForm.paymentFormIsAllValidPurchase();
    console.log(
      this.voucherForm.valid,
      this.voucherForm.getRawValue(),
      this.paymentForm.paymentFormIsAllValidPurchase()
    );
    const details: PaymentVoucher = {
      voucherDate: convertDateForBE(this.voucherForm.get('voucherDate')?.value),
      voucherType:
        this.processType === 'payment' ? voucherType.GeneralPayable : voucherType.GeneralReceivable,
      items: this.voucherForm.get('voucherEntries').getRawValue(),
      payments: this.paymentForm.paymentForm.getRawValue(),
      paymentAccountNumber: null,
    };
    console.log('final structure', details);
    if (this.voucherForm.valid && this.paymentForm.paymentFormIsAllValid()) {
      this.confirmationModal(details);
    } else {
      this.commonService.playErrorSound();
    }
  }

  runFunctionInAutoSearchComponents() {
    // Check if autoSearchComponents is initialized
    if (this.autoSearchComponents) {
      this.autoSearchComponents.forEach(component => {
        component.markAsTouched(); // Call the function in each component
      });
    }
  }

  confirmationModal(details: PaymentVoucher) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.minWidth = '600px';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    dialogConfig.data = 'common.confirmAction';
    const dialogRef = this.dialog.open(ConfirmDialogComponent, dialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      console.log('result', result);
      if (result) {
        // make api call
        this.paymentService.createSalesVoucher(details).subscribe(() => {
          console.log('sales service response', result);
          //this.toastr.success('Processed successfully!!');
          this.commonService.playSuccessSound();
          this.voucherRows = null;
          this.totalPaidAmount = 0;
          this.paymentForm.resetForm();
          this.voucherForm.reset();
          this.initializeForm();
        });
      }
    });
  }
}
