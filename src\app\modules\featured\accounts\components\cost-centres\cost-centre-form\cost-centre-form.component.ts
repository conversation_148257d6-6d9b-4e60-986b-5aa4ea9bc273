import { Component, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { CostCentresApiService } from 'src/app/core/api/accounts/cost-centres-api.service';
import { ActionType } from 'src/app/core/enums/actionType';
import { IActionEventType } from 'src/app/core/interfaces/actionEventType';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { CostCentre } from '../../../models/costCentre';
import { CommonService } from 'src/app/core/api/common.service';

@Component({
  selector: 'app-cost-centre-form',
  templateUrl: './cost-centre-form.component.html',
  styleUrls: ['./cost-centre-form.component.scss'],
})
export class CostCentreFormComponent implements OnInit {
  costCentre: CostCentre;
  costCentreForm: UntypedFormGroup;
  formTitle: string;
  loading = true;
  editedCostCentresId = '';
  mode: ActionType;

  constructor(
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute,
    private costCentresApiService: CostCentresApiService,
    private toastr: ToastrService,
    private fb: UntypedFormBuilder,
    private commonService: CommonService
  ) {}

  get IsViewMode(): boolean {
    return this.mode === ActionType.view;
  }

  ngOnInit() {
    this.mode = this.route.snapshot.data['mode'];
    this.formTitle = this.route.snapshot.data['title'];
    this.route.params.subscribe(params => {
      const id = params['id'];
      if (id) {
        this.editedCostCentresId = id;
        this.getCostCentres(id);
      } else {
        this.editedCostCentresId = null;
        this.initializeForm();
        this.loading = false;
      }
    });
  }

  setPageDisabled(): void {
    if (this.mode === ActionType.view) {
      this.costCentreForm.disable();
    }
  }

  getCostCentres(id: string): void {
    this.costCentresApiService.getCostCentreById(id).subscribe(result => {
      console.log(result);
      this.costCentre = result;
      this.initializeForm(this.costCentre);
      this.loading = false;
    });
  }

  initializeForm(costCentres?: CostCentre) {
    this.costCentreForm = this.fb.group({
      accountNumber: [costCentres?.accountNumber ?? null, Validators.required],
      nameArabic: [costCentres?.nameArabic ?? null, Validators.required],
      nameEnglish: [costCentres?.nameEnglish ?? null, Validators.required],
    });
    this.setPageDisabled();
  }

  submitSales(event: IActionEventType): void {
    event.event.preventDefault();
    if (event.actionType === ActionType.create) {
      this.costCentresApiService
        .createCostCentres(this.costCentreForm.value)
        .subscribe(response => {
          //this.toastr.success('Cost Centre added Successfully');
          this.commonService.playSuccessSound();
          this.router.navigate(['../'], { relativeTo: this.route });
        });
    }
    if (event.actionType === ActionType.edit) {
      this.costCentresApiService
        .updateCostCentres(this.costCentreForm.value, this.editedCostCentresId)
        .subscribe(response => {
          //this.toastr.success('Cost Centre updated Successfully');
          this.commonService.playSuccessSound();
          this.router.navigate(['../../'], { relativeTo: this.route });
        });
    }
  }
}
