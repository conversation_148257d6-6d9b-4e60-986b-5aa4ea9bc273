<ng-container *ngIf="!loading">
  <!--Entry section -->
  <mat-card appearance="outlined">
    <form [ngClass]="{ readOnly: IsViewMode }" [formGroup]="voucherForm" autocomplete="off">
      <mat-card-title>{{ formTitle | translate }}</mat-card-title>
      <div class="row no-gutters">
        <div class="col-md-2 col-lg-2 col-sm-3 p-2">
          <mat-label>{{ 'ucvoucher.voucherDate' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input [matDatepicker]="picker1" matInput formControlName="voucherDate" />
            <mat-error
              *ngIf="
                voucherForm?.controls['voucherDate'].hasError('required') &&
                voucherForm?.controls['voucherDate'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
            <mat-datepicker-toggle [for]="picker1" matSuffix> </mat-datepicker-toggle>
            <mat-datepicker #picker1></mat-datepicker>
          </mat-form-field>
        </div>
      </div>

      <div class="table-responsive">
        <table
          class="w-100"
          [dataSource]="dataSource"
          mat-table
          formArrayName="voucherEntries"
          multiTemplateDataRows>
          <ng-container matColumnDef="accountId">
            <th *matHeaderCellDef mat-header-cell>{{ 'ucvoucher.accountNo' | translate }}</th>
            <td *matCellDef="let element; let i = dataIndex" [formGroup]="element" mat-cell>
              <app-account-auto-search
                [control]="element.get('account')"
                [showSearch]="IsViewMode ? false : true"
                [disabled]="IsViewMode ? true : false"
                [newAccountSettings]="true"
                [accountType]="DETAILED"
                (accountSelected)="accountSelected($event, element, i)"
                label=""
                searchStringLength="3">
              </app-account-auto-search>
            </td>
          </ng-container>

          <ng-container matColumnDef="distributorAccountId">
            <th *matHeaderCellDef mat-header-cell>
              {{ 'ucvoucher.distributorAccountId' | translate }}
            </th>
            <td *matCellDef="let element; let i = dataIndex" [formGroup]="element" mat-cell>
              <mat-form-field class="w-100">
                <mat-select
                  class="next"
                  (selectionChange)="jumpToNext($event, i)"
                  formControlName="distributorAccountId">
                  <mat-option
                    *ngFor="let distributorAccount of distributorAccounts"
                    [value]="distributorAccount.accountId">
                    {{ distributorAccount | localized }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </td>
          </ng-container>

          <ng-container matColumnDef="costCentreId">
            <th *matHeaderCellDef mat-header-cell>
              {{ 'ucvoucher.costCentreId' | translate }}
            </th>
            <td *matCellDef="let element; let i = dataIndex" [formGroup]="element" mat-cell>
              <mat-form-field class="w-100">
                <mat-select
                  class="next"
                  (selectionChange)="jumpToNext($event, i)"
                  formControlName="costCentreId">
                  <mat-option
                    *ngFor="let costCentreAccount of costCentreAccounts"
                    [value]="costCentreAccount.costCentreId">
                    {{ costCentreAccount | localized }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </td>
          </ng-container>

          <ng-container matColumnDef="issueType">
            <th *matHeaderCellDef mat-header-cell>{{ 'ucvoucher.issueType' | translate }}</th>
            <td *matCellDef="let element; let i = dataIndex" [formGroup]="element" mat-cell>
              <mat-form-field class="w-100">
                <mat-select
                  class="next"
                  (selectionChange)="jumpToNext($event, i)"
                  formControlName="issueType">
                  <mat-option *ngFor="let issueType of issueTypes" [value]="issueType.value">
                    {{ issueType.display | translate }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </td>
          </ng-container>

          <ng-container matColumnDef="amount">
            <th *matHeaderCellDef mat-header-cell>{{ 'ucvoucher.paidAmount' | translate }}</th>
            <td *matCellDef="let element; let i = dataIndex" [formGroup]="element" mat-cell>
              <mat-form-field class="w-100">
                <input
                  class="next"
                  (keydown.enter)="jumpToNext($event, i)"
                  (input)="getTotal()"
                  type="number"
                  matInput
                  formControlName="paidAmount" />
                <mat-error *ngIf="element['controls']['paidAmount']?.hasError('required')">
                  {{ 'common.required' | translate }}
                </mat-error>
              </mat-form-field>
            </td>
          </ng-container>

          <ng-container matColumnDef="notes">
            <th *matHeaderCellDef mat-header-cell>{{ 'ucvoucher.notes' | translate }}</th>
            <td *matCellDef="let element; let i = dataIndex" [formGroup]="element" mat-cell>
              <mat-form-field class="w-100">
                <textarea
                  class="next"
                  #streetName
                  #autosize="cdkTextareaAutosize"
                  (keydown.enter)="jumpToNext($event, i)"
                  maxlength="90"
                  type="text"
                  matInput
                  formControlName="notes"
                  cdkTextareaAutosize
                  cdkAutosizeMaxRows="5"></textarea>
              </mat-form-field>
            </td>
          </ng-container>

          <!-- Action section-->
          <ng-container matColumnDef="action">
            <th *matHeaderCellDef mat-header-cell>{{ 'common.action' | translate }}</th>

            <td *matCellDef="let element; let i = dataIndex; let last = last" mat-cell>
              <ng-container *ngIf="!IsViewMode">
                <a class="m-r-10 cursor-pointer" *ngIf="last">
                  <i-tabler class="icon-16" (click)="addEntryRow(); (false)" name="plus"></i-tabler>
                </a>
                <a class="cursor-pointer" *ngIf="displayRemoveIcon">
                  <i-tabler
                    class="icon-16"
                    (click)="deleteEntry(element, i)"
                    name="trash"></i-tabler>
                </a>
              </ng-container>
            </td>
          </ng-container>
          <tr *matHeaderRowDef="columnsToDisplay" mat-header-row></tr>
          <tr *matRowDef="let entry; columns: columnsToDisplay" mat-row></tr>
        </table>
      </div>

      <div class="row flex-wrap m-t-10">
        <div class="col-lg-6 col-md-6 order-md-first">
          <app-standard-payments
            class="m-t-10"
            #paymentForm
            [paymentViewData]="paymentViewData"
            [grandTotal]="totalPaidAmount"
            [customePaymentSelection]="customePaymentSelection"
            [hideFractionAllowed]="true"
            [singlePaymentsAllowed]="true"></app-standard-payments>
        </div>
        <div class="col-lg-6 col-md-6 order-md-last">
          <table class="summary-table">
            <tr>
              <td class="summary-item">
                <span class="f-s-20 text-primary">{{ 'ucvoucher.voucherTotal' | translate }} </span>
              </td>
              <td class="summary-item value-column f-s-20 text-primary">
                {{ totalPaidAmount }}
              </td>
            </tr>
          </table>
        </div>
      </div>
    </form>
  </mat-card>
  <div class="text-center">
    <button
      class="m-l-10"
      *ngIf="!IsViewMode"
      (click)="submitVoucher($event); (false)"
      type="button"
      mat-stroked-button
      color="primary">
      {{ 'common.submit' | translate }}
    </button>
    <button
      class="m-l-10"
      *ngIf="!IsViewMode"
      [routerLink]="['../']"
      type="button"
      mat-stroked-button
      color="warn">
      {{ 'common.cancel' | translate }}
    </button>
    <button
      class="m-l-10"
      *ngIf="IsViewMode"
      [routerLink]="['../../']"
      type="button"
      mat-stroked-button
      color="primary">
      {{ 'common.cancel' | translate }}
    </button>
  </div>
</ng-container>
