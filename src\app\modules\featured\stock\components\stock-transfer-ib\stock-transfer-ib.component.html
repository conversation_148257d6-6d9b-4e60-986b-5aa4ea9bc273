<mat-card appearance="outlined">
  <mat-card-content>
    <!-- action bar -->
    <app-create-action
      *appHasPermission="['Transfer.Create', 'AllPermissions']"
      [link]="'ibtransfer'"
      [label]="'stockTransfer.transferCreate' | translate"></app-create-action>
    <!-- action bar -->

    <!-- search field -->
    <div class="row no-gutters">
      <div class="col-md-6 col-lg-6 col-sm-12 d-flex">
        <mat-form-field class="w-100">
          <input
            #searchInput
            (keyup)="applyFilter($event.target.value)"
            matInput
            autocomplete="off" />
          <i-tabler class="icon-16" matPrefix name="search"></i-tabler>
          <a
            class="cursor-pointer"
            *ngIf="searchInput?.value"
            (click)="clearSearchInput()"
            matSuffix>
            <i-tabler class="icon-16 error" name="X"></i-tabler>
          </a>
        </mat-form-field>
      </div>
    </div>

    <!-- search field -->
    <mat-card-title class="m-t-10">{{ 'stockTransfer.outgoingStock' | translate }}</mat-card-title>
    <div class="table-responsive">
      <table class="w-100" [dataSource]="dataSource" mat-table matSort>
        <ng-container matColumnDef="issueDate">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'stockTransfer.issueDate' | translate }}
          </th>
          <td class="f-s-14" *matCellDef="let element" mat-cell>
            {{ element.issueDate | date : 'yyyy-MM-dd' }}
          </td>
        </ng-container>
        <ng-container matColumnDef="documentNumberIn">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'stockTransfer.docno' | translate }}
          </th>
          <td class="f-s-14" *matCellDef="let element" mat-cell>
            {{ element.documentNumberIn }}
          </td>
        </ng-container>
        <ng-container matColumnDef="branchIdIn">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'stockTransfer.branchin' | translate }}
          </th>
          <td
            class="f-s-14"
            *matCellDef="let element"
            [data]="element.branchIdIn"
            [copyValue]="element.branchIdIn"
            appHyphen
            appCopyClick
            mat-cell>
            {{ element.branchIdIn }}
          </td>
        </ng-container>
        <ng-container matColumnDef="grandTotal">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'stockTransfer.grandTotal' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ element.grandTotal | translate }}
          </td>
        </ng-container>
        <ng-container matColumnDef="accountBasic">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'stockTransfer.accno' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ element.accountBasic.accountNumber }}
          </td>
        </ng-container>
        <ng-container matColumnDef="isExternalTransfer">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'stockTransfer.transferType' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{
              (element.isExternalTransfer ? 'common.buttons.yes' : 'common.buttons.no') | translate
            }}
          </td>
        </ng-container>
        <ng-container matColumnDef="action">
          <th *matHeaderCellDef mat-header-cell>
            {{ 'common.action' | translate }}
          </th>
          <td class="f-s-14" *matCellDef="let element" mat-cell>
            <button
              class="d-flex justify-content-center"
              [matMenuTriggerFor]="menu1"
              mat-icon-button>
              <i-tabler class="icon-20 d-flex" name="dots-vertical"></i-tabler>
            </button>
            <mat-menu class="cardWithShadow" #menu1="matMenu">
              <button
                *appHasPermission="['Transfer.View', 'AllPermissions']"
                [routerLink]="['ibtransfer/view', element.id]"
                mat-menu-item>
                <div class="d-flex align-items-center">
                  <i-tabler class="icon-16 m-r-4" name="eye"></i-tabler>
                  <span>{{ 'common.viewAction' | translate }}</span>
                </div>
              </button>
              <!-- <ng-container>
                <button
                  *appHasPermission="['Ucvoucher.Update', 'AllPermissions']"
                  [routerLink]="['edit', element.paymentId]"
                  mat-menu-item>
                  <div class="d-flex align-items-center">
                    <i-tabler class="icon-16 m-r-4" name="edit"></i-tabler>
                    <span>{{ 'common.editAction' | translate }}</span>
                  </div>
                </button>
              </ng-container> -->

              <!-- <ng-container>
                <button *appHasPermission="['Ucvoucher.Delete', 'AllPermissions']" mat-menu-item>
                  <div class="d-flex align-items-center">
                    <i-tabler class="icon-16 m-r-4" name="trash"></i-tabler>
                    <span>{{ 'common.deleteAction' | translate }}</span>
                  </div>
                </button>
              </ng-container> -->
            </mat-menu>
          </td>
        </ng-container>
        <tr class="mat-row" *matNoDataRow>
          <td class="f-s-14" class="text-center" [attr.colspan]="displayedColumns.length">
            {{ 'common.noDataFound' | translate }}
          </td>
        </tr>
        <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
        <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
      </table>
    </div>
    <mat-paginator
      *ngIf="dataSource?.filteredData?.length > 0"
      [length]="dataSource.filteredData.length"
      [pageIndex]="0"
      [pageSize]="10"
      [pageSizeOptions]="[5, 10, 20]"></mat-paginator> </mat-card-content
></mat-card>
