import { Directionality } from '@angular/cdk/bidi';
import { Injectable, EventEmitter } from '@angular/core';

@Injectable()
export class CustomDirectionality extends Directionality {
  override value: 'ltr' | 'rtl' = 'ltr';
  override change: EventEmitter<'ltr' | 'rtl'> = new EventEmitter<'ltr' | 'rtl'>();

  setDirection(dir: 'ltr' | 'rtl') {
    if (this.value !== dir) {
      this.value = dir;
      this.change.emit(dir);
    }
  }
}
