import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IUser } from 'src/app/core/interfaces/user';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class UserApiService {
  baseUrl = environment.apiUrl + 'auth/users';

  constructor(private http: HttpClient) {}

  getAllUsers(params: HttpParams) {
    return this.http.get(this.baseUrl, { params: params });
  }

  getUserById(id: string) {
    return this.http.get<IUser>(this.baseUrl + '/' + id);
  }

  createUser(user: IUser) {
    return this.http.post(this.baseUrl, user);
  }

  updateUser(user: IUser, userId: string) {
    return this.http.put(this.baseUrl + '/' + userId, user);
  }

  deleteUser(id: string) {
    return this.http.delete(this.baseUrl + '/' + id);
  }
}
