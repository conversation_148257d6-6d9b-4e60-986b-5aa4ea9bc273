import { AfterViewInit, Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { IRole } from '../../../../../../core/interfaces/role';
import { RoleParams } from '../../../../../../core/models/params/roleParams';
import { RoleService } from '../../../services/role.service';
import { ActivatedRoute } from '@angular/router';
import { AuthService } from '../../../../../core/core/services/auth.service';

@Component({
  selector: 'app-role-list',
  templateUrl: './role-list.component.html',
  styleUrls: ['./role-list.component.scss'],
})
export class RoleListComponent implements OnInit {
  @ViewChild('filter') filter: ElementRef;
  @ViewChild('searchInput') searchInput: ElementRef;
  @ViewChild(MatPaginator) set matPaginator(paginator: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = paginator;
    }
  }
  @ViewChild(MatSort) set matSort(sort: MatSort) {
    if (this.dataSource) {
      this.dataSource.sort = sort;
    }
  }
  roles: IRole[];
  displayedColumns: string[];
  dataSource: MatTableDataSource<IRole>;
  roleParams = new RoleParams();
  isLoading = true;
  formTitle: string;
  constructor(
    public dialog: MatDialog,
    public roleService: RoleService,
    private route: ActivatedRoute,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.formTitle = this.route.snapshot.data['title'];
    this.getRoles();
    this.initColumns();
  }

  get getUserId() {
    return this.authService.getUserId;
  }

  getRoles(): void {
    this.roleService.getRoles(this.roleParams).subscribe(result => {
      this.roles = result;
      this.isLoading = false;
      this.dataSource = new MatTableDataSource<IRole>(this.roles);
    });
  }

  initColumns(): void {
    this.displayedColumns = ['action', 'id', 'name', 'description'];
  }

  pageChanged(pageEvent: PageEvent): void {
    this.roleParams.pageNumber = pageEvent.pageIndex + 1 ?? 1;
    this.roleParams.pageSize = pageEvent.pageSize ?? 10;
    this.getRoles();
  }

  filterParams(): RoleParams {
    this.roleParams.searchString = this.filter.nativeElement.value.toLocaleLowerCase();
    this.roleParams.pageNumber = 0;
    this.roleParams.pageSize = 0;
    return this.roleParams;
  }

  applyFilter(filterValue: string): void {
    filterValue = filterValue.trim();
    filterValue = filterValue.toLowerCase();
    this.dataSource.filter = filterValue;
  }

  clearSearchInput() {
    this.searchInput.nativeElement.value = '';
    this.getRoles();
  }
}
