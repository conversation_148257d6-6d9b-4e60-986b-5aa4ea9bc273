import { Component, EventEmitter, Input, Output } from '@angular/core';
import { ActionType } from 'src/app/core/enums/actionType';
import { IActionEventType } from 'src/app/core/interfaces/actionEventType';

@Component({
  selector: 'app-action-buttons',
  templateUrl: './action-buttons.component.html',
  styleUrls: ['./action-buttons.component.scss'],
})
export class ActionButtonsComponent {
  @Input() mode: ActionType;
  @Input() processType = 'sales';
  @Output() submitAction: EventEmitter<IActionEventType> = new EventEmitter();
  actionType = ActionType;
  constructor() {
    //
  }

  onSubmit(event: IActionEventType): void {
    event?.event.preventDefault();
    this.submitAction.emit(event);
  }
}
