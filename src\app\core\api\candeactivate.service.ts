import { Directionality } from '@angular/cdk/bidi';
import { Injectable } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { Observable, of } from 'rxjs';
import { UnsavedChangesDialogComponent } from 'src/app/modules/shared/components/unsaved-changes-dialog/unsaved-changes-dialog.component';

@Injectable({
  providedIn: 'root',
})
export class CandeactivateService {
  constructor(private dialog: MatDialog, private direction: Directionality) {}

  canDeactivate(form: FormGroup): Observable<boolean> {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '600px';
    dialogConfig.maxWidth = '600px';
    dialogConfig.panelClass = 'green_theme';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    if (form.dirty || form.touched) {
      const dialogRef = this.dialog.open(UnsavedChangesDialogComponent, dialogConfig);
      dialogRef.componentInstance.accepted.subscribe(value => {
        return value === true;
      });
    }
    return of(true);
  }
}
