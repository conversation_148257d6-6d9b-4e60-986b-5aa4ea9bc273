import { Component, OnInit } from '@angular/core';
import { DashboardModulesHolder } from 'src/app/modules/shared/components/dashboard-modules-holder/modulesHolder';

@Component({
  selector: 'app-identity-dashboard',
  templateUrl: './identity-dashboard.component.html',
  styleUrls: ['./identity-dashboard.component.scss'],
})
export class IdentityDashboardComponent implements OnInit {
  productModulesList: DashboardModulesHolder[] = [
    {
      moduleName: 'navigationMenus.rolesPermissions',
      moduleDescription: 'Manage Company Profile.',
      modulePermission: ['Role', 'AllPermissions'],
      moduleImage: 'accessibility',
      moduleRouterLink: '../roles',
      moduleButtonAction: 'POS',
      moduleType: 'subModule',
    },
    {
      moduleName: 'navigationMenus.users',
      moduleDescription: 'Manage Company Profile.',
      modulePermission: ['User', 'AllPermissions'],
      moduleImage: 'accessibility',
      moduleRouterLink: '../users',
      moduleButtonAction: 'POS',
      moduleType: 'subModule',
    },
  ];
  constructor() {}

  ngOnInit(): void {}
}
