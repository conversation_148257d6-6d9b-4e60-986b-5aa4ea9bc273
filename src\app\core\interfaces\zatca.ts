export interface IZatcaEInvoices {
  searchTimestamp: Date;
  totalRecordsCount: number;
  totalPages: number;
  morePages: boolean;
  invoices: ZatacaInvoices[];
}

export interface ZatacaInvoices {
  id: number;
  documentUuid: string;
  invoiceCounterValue: number;
  documentNumber: string;
  documentType: DocumentType;
  invoiceType: InvoiceType;
  issueDate: Date;
  status: number;
}

export enum DocumentType {
  Sales = 'SALES',
  CreditNote = 'CREDITNOTE',
  DebitNote = 'DEBITNOTE',
}

export enum InvoiceType {
  Standard = 'STANDARD',
  Simplified = 'SIMPLIFIED',
}

export interface InvoiceParams {
  invoiceType: 'SIMPLIFIED' | 'STANDARD';
  issueDateFrom: Date;
  issueDateTo: Date;
  stage?: 'EINVOICE_SAVED' | 'NOT_SUBMITTED' | 'REJECTED' | 'CLEARED' | 'CLEARED_WITH_WARNINGS';
}
