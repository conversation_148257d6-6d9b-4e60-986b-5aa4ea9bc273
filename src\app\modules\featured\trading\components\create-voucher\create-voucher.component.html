<app-dialog-header></app-dialog-header>
<mat-card appearance="outlined">
  <mat-card-content>
    <mat-card-title>{{ 'voucher.create' | translate }}</mat-card-title>
    <form [formGroup]="salesForm">
      <div class="row no-gutters">
        <div class="col-md-4 col-lg-4 p-2">
          <mat-label>{{ 'voucher.accountNo' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input #arabic type="text" readonly matInput formControlName="accountNumber" />
          </mat-form-field>
        </div>
        <div class="col-md-4 col-lg-4 p-2">
          <mat-label>{{ 'voucher.name' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input #arabic type="text" matInput formControlName="name" />
          </mat-form-field>
        </div>
        <div class="col-md-2 col-lg-2 p-2">
          <mat-label>{{ 'voucher.voucherDate' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input [matDatepicker]="invoiceDatePicker" formControlName="voucherDate" matInput />
            <mat-datepicker-toggle [for]="invoiceDatePicker" matSuffix></mat-datepicker-toggle>
            <mat-datepicker #invoiceDatePicker></mat-datepicker>
          </mat-form-field>
        </div>
        <div class="col-md-2 col-lg-2 p-2">
          <mat-slide-toggle [labelPosition]="'after'" formControlName="fullPayment">{{
            'voucher.fullPayment' | translate
          }}</mat-slide-toggle>
        </div>
      </div>

      <div class="table-responsive">
        <table class="w-100" [dataSource]="dataSource" mat-table matSort formArrayName="items">
          <ng-container matColumnDef="invoiceNumber">
            <th *matHeaderCellDef mat-header-cell>
              {{ 'voucher.invoiceNo' | translate }}
            </th>
            <td *matCellDef="let element; let i = index" mat-cell>
              {{ element.get('invoiceNumber')?.value }}
            </td>
          </ng-container>
          <ng-container matColumnDef="invoiceDate">
            <th *matHeaderCellDef mat-header-cell>
              {{ 'voucher.invoiceDate' | translate }}
            </th>
            <td *matCellDef="let element; let i = index" mat-cell>
              {{ element.get('invoiceDate')?.value | date : 'yyyy-MM-dd' }}
            </td>
          </ng-container>
          <ng-container matColumnDef="unpaidAmount">
            <th *matHeaderCellDef mat-header-cell>
              {{ 'voucher.invoiceAmount' | translate }}
            </th>
            <td *matCellDef="let element; let i = index" mat-cell>
              {{ element.get('unpaidAmount')?.value }}
            </td>
          </ng-container>
          <ng-container matColumnDef="paidAmount">
            <th *matHeaderCellDef mat-header-cell>
              {{ 'voucher.paidAmount' | translate }}
            </th>
            <td *matCellDef="let element; let i = index" [formGroupName]="i" mat-cell>
              <mat-form-field class="w-100 d-flex">
                <input
                  class="next"
                  [min]="0"
                  [readonly]="salesForm.get('fullPayment').value ?? false"
                  formControlName="paidAmount"
                  matInput
                  type="number" />
                <mat-error
                  class="text-error-wrap"
                  *ngIf="element.get('paidAmount').hasError('balanceGreaterThanTotal')">
                  {{ 'voucher.voucherAmountError' | translate }}
                </mat-error>
                <mat-error class="text-error-wrap" *ngIf="element.get('paidAmount').hasError('gt')">
                  Invalid Amount
                </mat-error>
              </mat-form-field>
            </td>
          </ng-container>
          <ng-container matColumnDef="notes">
            <th *matHeaderCellDef mat-header-cell>
              {{ 'voucher.notes' | translate }}
            </th>
            <td *matCellDef="let element; let i = index" [formGroupName]="i" mat-cell>
              <mat-form-field class="w-100 d-flex">
                <input class="next" formControlName="notes" matInput type="text" />
              </mat-form-field>
            </td>
          </ng-container>
          <ng-container matColumnDef="distributorAccountId">
            <th *matHeaderCellDef mat-header-cell>
              {{ 'voucher.distributor' | translate }}
            </th>
            <td *matCellDef="let element; let i = index" [formGroupName]="i" mat-cell>
              <mat-form-field class="w-100 d-flex">
                <mat-select formControlName="distributorAccountId">
                  <mat-option>{{ 'common.reset' | translate }}</mat-option>
                  <mat-option
                    *ngFor="let distributorAccount of distributorAccounts"
                    [value]="distributorAccount.accountId">
                    {{ distributorAccount | localized }}
                  </mat-option>
                </mat-select>
                <!-- <mat-error
                  *ngIf="
                    element.get('distributorAccountId').hasError('required') &&
                    element.get('paidAmount').touched
                  "
                  >{{ 'common.required' | translate }}</mat-error
                > -->
              </mat-form-field>
            </td>
          </ng-container>
          <ng-container matColumnDef="costCentreId">
            <th *matHeaderCellDef mat-header-cell>
              {{ 'voucher.costCenter' | translate }}
            </th>
            <td *matCellDef="let element; let i = index" [formGroupName]="i" mat-cell>
              <mat-form-field class="w-100 d-flex">
                <mat-select formControlName="costCentreId">
                  <mat-option>{{ 'common.reset' | translate }}</mat-option>
                  <mat-option
                    *ngFor="let costCentreAccount of costCentreAccounts"
                    [value]="costCentreAccount.costCentreId">
                    {{ costCentreAccount | localized }}
                  </mat-option>
                </mat-select>
                <!-- <mat-error
                  *ngIf="
                    element.get('costCentreId').hasError('required') &&
                    element.get('costCentreId').touched
                  "
                  >{{ 'common.required' | translate }}</mat-error
                > -->
              </mat-form-field>
            </td>
          </ng-container>

          <tr class="mat-row" *matNoDataRow>
            <td class="text-center" [attr.colspan]="displayedColumns.length">
              {{ 'common.noDataFound' | translate }}
            </td>
          </tr>
          <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
          <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
        </table>
      </div>
      <div class="row flex-wrap m-t-10">
        <div class="col-lg-6 col-md-6 order-md-first">
          <app-standard-payments
            class="m-t-10"
            #paymentForm
            [paymentViewData]="paymentViewData"
            [grandTotal]="getTotalBalanceAmount()"
            [customePaymentSelection]="customePaymentSelection"
            [hideFractionAllowed]="true"
            [singlePaymentsAllowed]="true"></app-standard-payments>
        </div>
        <div class="col-lg-6 col-md-6 order-md-last">
          <app-voucher-summary
            #vouchersummary
            [invoiceTotal]="getTotalInvoiceTotal()"
            [paidInvoiceTotal]="getTotalBalanceAmount()">
          </app-voucher-summary>
        </div>
      </div>
    </form>
    <div class="text-center">
      <button
        class="m-l-10"
        (click)="submitVoucher($event); (false)"
        type="button"
        mat-stroked-button
        color="primary">
        {{ 'common.submit' | translate }}
      </button>
      <button class="m-l-10" mat-dialog-close type="button" mat-stroked-button color="warn">
        {{ 'common.cancel' | translate }}
      </button>
    </div>
  </mat-card-content>
</mat-card>
