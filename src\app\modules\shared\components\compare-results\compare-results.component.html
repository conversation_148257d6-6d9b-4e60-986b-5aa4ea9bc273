<app-dialog-header></app-dialog-header>
<h2 class="text-center text-warning">{{ data.message | translate }}</h2>
<mat-card>
  <div class="table-responsive">
    <table class="w-100" [dataSource]="data.compareData" mat-table>
      <!-- Code Column -->
      <ng-container matColumnDef="itemCode">
        <th class="text-center" *matHeaderCellDef mat-header-cell>
          {{ 'compareTable.code' | translate }}
        </th>
        <td class="text-center" *matCellDef="let row" mat-cell>{{ row.itemCode }}</td>
      </ng-container>

      <!-- Name Column -->
      <ng-container matColumnDef="itemName">
        <th class="text-center" *matHeaderCellDef mat-header-cell>
          {{ 'compareTable.name' | translate }}
        </th>
        <td class="text-center" *matCellDef="let row" mat-cell>{{ row.itemName }}</td>
      </ng-container>

      <!-- Unit Column -->
      <ng-container matColumnDef="unitName">
        <th class="text-center" *matHeaderCellDef mat-header-cell>
          {{ 'compareTable.unit' | translate }}
        </th>
        <td class="text-center" *matCellDef="let row" mat-cell>{{ row.product.unitName }}</td>
      </ng-container>

      <!-- Warehouse Column -->
      <ng-container matColumnDef="warehouseName">
        <th class="text-center" *matHeaderCellDef mat-header-cell>
          {{ 'compareTable.warehouse' | translate }}
        </th>
        <td class="text-center" *matCellDef="let row" mat-cell>{{ row.warehouseName }}</td>
      </ng-container>

      <!-- Quantity Column -->
      <ng-container matColumnDef="quantity">
        <th class="text-center" *matHeaderCellDef mat-header-cell>
          {{
            (data?.purchase ? 'compareTable.purchasedQty' : 'compareTable.existingQty') | translate
          }}
        </th>
        <td class="text-center text-primary" *matCellDef="let row" mat-cell>
          {{ row.returnableQty }}
        </td>
      </ng-container>

      <!-- Updated Quantity Column -->
      <ng-container matColumnDef="updatedQty">
        <th class="text-center" *matHeaderCellDef mat-header-cell>
          {{ 'compareTable.updatedQty' | translate }}
        </th>
        <td class="text-center text-warning" *matCellDef="let row" mat-cell>
          {{ row.quantity }}
        </td>
      </ng-container>

      <tr
        *matHeaderRowDef="[
          'itemCode',
          'itemName',
          'unitName',
          'warehouseName',
          'quantity',
          'updatedQty'
        ]"
        mat-header-row></tr>
      <tr
        *matRowDef="
          let row;
          columns: ['itemCode', 'itemName', 'unitName', 'warehouseName', 'quantity', 'updatedQty']
        "
        mat-row></tr>
    </table>
  </div>
</mat-card>
<mat-dialog-actions align="center">
  <button [mat-dialog-close]="true" mat-stroked-button cdkFocusInitial color="primary">
    {{ 'common.confirm' | translate }}
  </button>
  <button (click)="onNoClick()" mat-stroked-button color="warn">
    {{ 'common.cancel' | translate }}
  </button>
</mat-dialog-actions>
