import { Directionality } from '@angular/cdk/bidi';
import { SelectionModel } from '@angular/cdk/collections';
import { Component, Inject, Input, Optional, ViewChild } from '@angular/core';
import { FormControl, FormGroup, UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { MatDateRangeInput } from '@angular/material/datepicker';
import { MAT_DIALOG_DATA, MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { PurchaseService } from 'src/app/core/api/trading/purchase.service';
import { SalesService } from 'src/app/core/api/trading/sales.service';
import { salesPaymentStatus } from 'src/app/core/configs/dropDownConfig';
import { IPurchaseInvoice, IPurchaseResponse } from 'src/app/core/interfaces/purchase';
import {
  IInvoice,
  ISalesResponse,
  SaletransactionTypes,
  voucherType,
} from 'src/app/core/interfaces/sales';
import { getFormValueExcludeSearchBox } from 'src/app/core/utils/date-utils';
import { SearchboxComponent } from 'src/app/modules/shared/components/searchbox/searchbox.component';
import { Branch } from '../../../catalog/models/branch';
import { BulkEditData } from '../../../catalog/models/bulkedit';
import { Category } from '../../../catalog/models/category';
import { CreateVoucherComponent } from '../create-voucher/create-voucher.component';
import { VoucherListInvoiceComponent } from '../../../finance/financedashboard/components/voucher-list-invoice/voucher-list-invoice.component';
import { MultilingualService } from 'src/app/modules/core/core/services/multilingual.service';

@Component({
  selector: 'app-sales-purchase-receivable',
  templateUrl: './sales-purchase-receivable.component.html',
  styleUrls: ['./sales-purchase-receivable.component.scss'],
})
export class SalesPurchaseReceivableComponent {
  @Input() payables = false;
  @ViewChild('searchBoxForm') searchBoxForm: SearchboxComponent;
  @ViewChild(MatPaginator) set matPaginator(paginator: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = paginator;
    }
  }
  @ViewChild(MatSort) set matSort(sort: MatSort) {
    if (this.dataSource) {
      this.dataSource.sort = sort;
    }
  }
  invoices: IInvoice[] | IPurchaseInvoice[] = [];
  dataSource: MatTableDataSource<IInvoice | IPurchaseInvoice>;
  disabledSelection = false;
  loading = true;
  categoryList: Category[];
  displayedColumns = [];
  branchList: Branch[];
  warehouseList: any;
  allWarehouses: any;
  unitList: any;
  filterForm: UntypedFormGroup;
  isReportPulling = false;
  isAdvancedSearchEnabled = false;
  isBranchNotSelected = true;
  status = salesPaymentStatus;
  todayDate: Date = new Date();
  range = new FormGroup({
    startDate: new FormControl<Date | null>(null),
    endDate: new FormControl<Date | null>(new Date()),
  });
  picker: MatDateRangeInput<Date>;
  openSubscription: any;
  selection = new SelectionModel<any>(true, []);
  constructor(
    @Optional() @Inject(MAT_DIALOG_DATA) public data: BulkEditData,
    private fb: UntypedFormBuilder,
    private dialog: MatDialog,
    private direction: Directionality,
    private salesService: SalesService,
    private purchaseService: PurchaseService,
    private translateService: MultilingualService
  ) {
    this.filterForm = this.fb.group({
      invoiceDateFrom: [new Date()],
      invoiceDateTo: [new Date()],
      settlementStatus: ['UNPAID'],
      searchBoxForm: [],
    });
  }

  /** Whether the number of selected elements matches the total number of rows. */
  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  toggleAllRows() {
    if (this.isAllSelected()) {
      this.selection.clear();
      return;
    }

    this.selection.select(...this.dataSource.data);
  }

  ngOnInit(): void {
    this.loading = true;
    this.dataSource = new MatTableDataSource<IInvoice | IPurchaseInvoice>(this.invoices);
    this.displayedColumns = this.translateService.updateDisplayedColumns([
      'select',
      'documentNumber',
      'issueDate',
      'invoiceTotal',
      'unpaidAmount',
      'accountNumber',
      'nameArabic',
      'nameEnglish',
      'vatNumber',
      'phoneNumber',
      'returnStatus',
      'voucherStatus',
    ]);
    this.filterForm.get('settlementStatus').valueChanges.subscribe(change => {
      this.selection.clear();
      if (change === 'FULLY_PAID') {
        this.disabledSelection = true;
      } else {
        this.disabledSelection = false;
      }
    });
  }

  clearFilters(event: Event) {
    console.log('clear filter');
    event.preventDefault();
    this.filterForm.markAsUntouched();
    this.filterForm.markAsPristine();
    this.filterForm.reset();
    this.searchBoxForm.resetSearchBox();
    this.resetForm();
  }

  resetForm() {
    this.filterForm.get('invoiceDateFrom').setValue(new Date());
    this.filterForm.get('invoiceDateTo').setValue(new Date());
    this.filterForm.get('settlementStatus').setValue('UNPAID');
  }

  openDialog(event: Event): void {
    event.preventDefault();
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '90vw';
    dialogConfig.position = {
      top: '50px',
    };
    dialogConfig.maxHeight = '90vh';
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    dialogConfig.data = {
      selectedData: this.selection.selected,
      voucherType: this.payables ? voucherType.PurchasePayable : voucherType.SalesReceivable,
    };
    const dialog = this.dialog.open(CreateVoucherComponent, dialogConfig);
    dialog.afterClosed().subscribe(() => {
      this.selection.clear();
      this.dataSource.data = [];
      this.search();
    });
  }

  getAllSales(): void {
    const salesParams = this.getParams();
    console.log(salesParams, this.filterForm);
    this.salesService.getAllSales(salesParams).subscribe((result: ISalesResponse) => {
      console.log(result, result.transactions);
      this.invoices = result.transactions;
      this.dataSource = new MatTableDataSource<IInvoice>(this.invoices);
    });
  }

  getAllPurchases(): void {
    const salesParams = this.getParams();
    console.log(salesParams, this.filterForm);
    this.purchaseService.getAllPurchaseSales(salesParams).subscribe((result: IPurchaseResponse) => {
      console.log(result, result.transactions);
      this.invoices = result.transactions;
      this.dataSource = new MatTableDataSource<IPurchaseInvoice>(this.invoices);
    });
  }

  getParams() {
    const data = Object.assign(
      {
        pageSize: 99999,
        invoiceStatus: 'NOTRETURNED',
        transactionType: this.payables ? SaletransactionTypes.purchase : SaletransactionTypes.sales,
      },
      ...[getFormValueExcludeSearchBox(this.filterForm), this.searchBoxForm.searchBoxForm.value]
    );
    return data;
  }

  search(event?: Event): void {
    event?.preventDefault();
    this.filterForm.markAllAsTouched();
    if (this.filterForm.valid && this.searchBoxForm.isValid) {
      if (this.payables) {
        this.getAllPurchases();
      } else {
        this.getAllSales();
      }
    } else {
      console.log('error');
    }
  }

  openVoucherList(data: IInvoice) {
    console.log(data);
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '90vw';
    dialogConfig.position = {
      top: '50px',
    };
    dialogConfig.maxHeight = '90vh';
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    dialogConfig.data = {
      id: data.id,
      voucherType: this.payables ? voucherType.PurchasePayable : voucherType.SalesReceivable,
    };
    const dialog = this.dialog.open(VoucherListInvoiceComponent, dialogConfig);
    dialog.afterClosed().subscribe(() => {
      //
    });
  }
}
