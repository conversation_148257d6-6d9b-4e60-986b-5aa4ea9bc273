import { Directive, HostListener, Input } from '@angular/core';
import { NgControl } from '@angular/forms';

@Directive({
  selector: '[appMaxlength]',
})
export class MaxlengthDirective {
  @Input('appMaxlength') maxLength: number | undefined;

  constructor(private control: NgControl) {}

  @HostListener('input', ['$event.target.value'])
  onInput(value: string): void {
    if (this.maxLength != null && value.length > this.maxLength) {
      this.control.control?.setValue(value.slice(0, this.maxLength));
      this.control.control?.markAsTouched();
    }
  }
}
