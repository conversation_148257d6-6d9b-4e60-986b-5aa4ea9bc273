import { IAccountDetails } from './sales';

export interface IPaymentDetails {
  bankType?: boolean;
  bankAccountId?: null;
  bankAmount?: null | number;
  cashType?: boolean;
  cashAccountId?: null;
  cashAmount?: null | number;
  cardType?: boolean;
  cardAccountId?: null;
  cardAmount?: null | number;
  creditType?: boolean;
  creditDueDate?: null;
  creditAmount?: null | number;
  halala?: boolean;
  fractionAmount?: number;
  grandTotal?: number;
  totalExclVatDiscount?: number;
  totalVat?: number;
  balanceAmount?: number;
  changeAmount?: number;
  totalDiscount?: number;
}

export interface IPaymentViewData extends IPaymentDetails {
  isViewMode?: boolean;
}

export interface VoucherInvoice {
  paymentId: number;
  voucherNumber: null;
  voucherDate: Date;
  invoiceNumber: string;
  invoiceDate: Date;
  paidAmount: number;
  notes: null;
  // nameEnglish?: string;
  // nameArabic?: string;
  // accountNumber?: string;
  account?: IAccountDetails;
}
