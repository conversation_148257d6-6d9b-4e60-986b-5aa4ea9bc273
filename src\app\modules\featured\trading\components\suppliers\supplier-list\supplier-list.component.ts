import { Directionality } from '@angular/cdk/bidi';
import { AfterViewInit, Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/core/api/common.service';
import { SupplierService } from 'src/app/core/api/trading/supplier.service';
import { ISupplier, ISupplierResponse } from 'src/app/core/interfaces/supplier';
import { SupplierParams } from 'src/app/core/models/params/supplierParams';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { DeleteConfirmationComponent } from 'src/app/modules/shared/components/delete-confirmation/delete-confirmation.component';

@Component({
  selector: 'app-supplier-list',
  templateUrl: './supplier-list.component.html',
  styleUrls: ['./supplier-list.component.scss'],
})
export class SupplierListComponent implements OnInit, AfterViewInit {
  @ViewChild('filter') filter: ElementRef;
  @ViewChild('searchInput') searchInput: ElementRef;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  suppliers: ISupplier[];
  displayedColumns: string[];
  dataSource: MatTableDataSource<ISupplier>;
  isLoading = true;
  constructor(
    public supplierService: SupplierService,
    private authService: AuthService,
    public dialog: MatDialog,
    private toastr: ToastrService,
    private direction: Directionality,
    private commonService: CommonService
  ) {}

  ngOnInit(): void {
    this.getSuppliers();
    this.initColumns();
  }

  ngAfterViewInit(): void {
    if (this.dataSource) {
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
    }
  }

  getSuppliers(): void {
    const supplierParams = new SupplierParams();
    this.supplierService.getAllSuppliers(supplierParams).subscribe((result: ISupplierResponse) => {
      console.log(result, result.suppliers);
      this.suppliers = result.suppliers;
      this.isLoading = false;
      this.dataSource = new MatTableDataSource<ISupplier>(this.suppliers);
      this.dataSource.paginator = this.paginator;
    });
  }

  initColumns(): void {
    this.displayedColumns = [
      'action',
      'accountNumber',
      'nameArabic',
      'nameEnglish',
      'vatNumber',
      'phoneNumber',
    ];
  }

  applyFilter(filterValue: string): void {
    filterValue = filterValue.trim();
    filterValue = filterValue.toLowerCase();
    this.dataSource.filter = filterValue;
  }

  clearSearchInput() {
    this.searchInput.nativeElement.value = '';
    this.getSuppliers();
  }

  deleteUnit(customerId?: string) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '600px';
    dialogConfig.panelClass = 'green_theme';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(DeleteConfirmationComponent, dialogConfig);
    dialogRef.componentInstance.accepted.subscribe(result => {
      this.supplierService.deleteSupplier(customerId).subscribe(result => {
        this.commonService.playSuccessSound();
        this.getSuppliers();
        this.isLoading = false;
      });
    });
  }
}
