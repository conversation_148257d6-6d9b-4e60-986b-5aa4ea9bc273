import { Component, OnInit, ElementRef, ViewChild, ViewChildren, QueryList } from '@angular/core';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort, Sort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';

import { HttpParams } from '@angular/common/http';
import { PaginatedFilter } from 'src/app/core/interfaces/PaginatedFilter';
import { AccountsSearchBoxComponent } from '../../../accounts/components/accounts-search-box/accounts-search-box.component';
import { Account } from '../../../accounts/models/account';
import { AccountParams } from '../../../accounts/models/accountParams';
import { ChartOfAccountsService } from '../../../accounts/services/chart-of-accounts.service';

import * as moment from 'moment';
import { accountGroups, accountTypes, businessGroups } from 'src/app/core/configs/dropDownConfig';
import { AccountAutoSearchComponent } from '../../../accounts/components/accountAutoSearch/account-auto-search.component';
import { IReportType } from '../../../catalog/models/reports';
import { ReportService } from '../../../settings/services/report.service';
import { forkJoin } from 'rxjs';
import { AccountingReportParams } from '../../../settings/models/reportParams';
import { BranchService } from '../../../settings/services/branch.service';
import { BranchParams } from '../../../settings/models/branchParams';
import { Branch } from '../../../catalog/models/branch';
import { CookieService } from 'ngx-cookie-service';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { MatSelect } from '@angular/material/select';
import { MultilingualService } from 'src/app/modules/core/core/services/multilingual.service';
import { debounceTime } from 'rxjs/operators';
import { StoreService } from 'src/app/core/api/store.service';
import { StoreParams } from '../../../settings/models/storeParams';
import { SettingsModule } from './../../../settings/settings.module';

@Component({
  selector: 'app-chart-of-accounts',
  templateUrl: './accounting-reports.component.html',
  styleUrls: ['./accounting-reports.component.scss'],
})
export class AccountingReportComponent implements OnInit {
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  dataSource: MatTableDataSource<any>;
  isLoading = true;

  filtersForm: UntypedFormGroup;
  isAdvancedSearchEnabled = false;
  accountGroups = accountGroups;
  pdfUrl = '';
  accountTypes = accountTypes;
  businessGroups = businessGroups;
  reportTypeList: IReportType[];
  reportData: any[] = [];
  selectedReportType: string;
  selectedReportEndPoint: string;
  selectedJasperReportEndPoint: string;
  displayedColumns: string[] = [];
  allColumns: string[] = [];
  branchList: Branch[];
  totalItems = 0;
  pageSize = 100;
  currentPage = 0;
  totalPages = 0;
  morePages = false;
  isAdvancedSearchVisible = false;
  yearIdList: any;
  allWarehouses: any;
  warehouseList: any;
  filteredYearIdList: any;
  accountColumns: any;
  columnNames: { [key: string]: string } = {
    accountNumber: 'Account Number',
    accountNature: 'Account Nature',
    itemId: 'Item ID',
    itemName: 'Item Name',
    itemCode: 'Item Code',
    nameEnglish: 'Name (English)',
    nameArabic: 'Name (Arabic)',
    branchId: 'Branch Id',
    accountType: 'Account Type',
    accountGroup: 'Account Group',
    businessGroup: 'Business Group',
  };
  loading = true;
  request: any;
  @ViewChildren(MatSelect) matSelects!: QueryList<MatSelect>;
  searchConfigs: any;
  formName = 'accountingReportSearch';
  templateId: number;
  constructor(
    private fb: UntypedFormBuilder,
    private reportService: ReportService,
    private branchService: BranchService,
    private cookieService: CookieService,
    private authService: AuthService,
    private translateService: MultilingualService,
    private storeService: StoreService
  ) {}

  ngOnInit(): void {
    this.dataSource = new MatTableDataSource(this.reportData);
    this.dataSource.paginator = this.paginator;
    this.getAllDropDownData();
  }

  filterYearIdForSelectedBranch() {
    this.filteredYearIdList = this.yearIdList
      .filter(year => this.filtersForm.controls['branchId'].value === year.branchId)
      .flatMap(filteredYear => filteredYear.years);
  }

  getAllDropDownData(): void {
    const warehouses = this.storeService.getAllStores(new StoreParams());
    const reportTypes = this.reportService.getAllReportType(2);
    const branches = this.branchService.getAllBranchesWithYear(new BranchParams());
    const yearIds = this.authService.getUserBranchesYearId(this.authService.getCompanyID);
    forkJoin([reportTypes, branches, yearIds, warehouses]).subscribe(results => {
      this.reportTypeList = results[0];
      this.branchList = results[1];
      this.yearIdList = results[2];
      this.allWarehouses = results[3];
      this.loading = false;
      this.searchConfigs = this.reportTypeList[0].searchConfigs;
    });
  }

  getReportData(event?: Event, pageEvent?: PaginatedFilter) {
    event?.preventDefault();
    this.filtersForm.markAllAsTouched();
    this.isLoading = true;
    const params: AccountingReportParams = <AccountingReportParams>{};
    params.templateId = this.templateId;
    params.reportType = this.selectedReportType;
    params.accountGroup = this.filtersForm?.controls['accountGroup']?.value;

    params.businessGroup = this.filtersForm?.controls['businessGroup']?.value;
    params.accountType = this.filtersForm?.controls['accountType']?.value;
    params.branchId = this.filtersForm?.controls['branchId']?.value;
    params.yearId = this.filtersForm?.controls['yearId']?.value;
    if (this.filtersForm?.controls['fromAccount']?.value) {
      const parts = this.filtersForm.controls['fromAccount'].value.searchString.split('-');
      const fromAccountNo = parts[0];
      params.startingAccountNo = fromAccountNo;
    }
    if (this.filtersForm?.controls['toAccount']?.value) {
      const parts = this.filtersForm.controls['toAccount'].value.searchString.split('-');
      const toAccountNo = parts[0];
      params.endingAccountNo = toAccountNo;
    }

    if (this.filtersForm?.controls['dateFrom']?.value)
      params.startingDate = moment(this.filtersForm.controls['dateFrom'].value).format(
        'YYYY-MM-DD'
      );
    if (this.filtersForm?.controls['dateTo']?.value)
      params.endingDate = moment(this.filtersForm.controls['dateTo'].value).format('YYYY-MM-DD');

    if (this.filtersForm?.controls['date']?.value)
      params.endingDate = moment(this.filtersForm.controls['date'].value).format('YYYY-MM-DD');

    // Pagination parameters
    params.page = this.currentPage + 1; // Convert 0-based index to 1-based page
    params.pageSize = this.pageSize;
    params.endPoint = this.selectedReportEndPoint;
    params.jasperEndPoint = this.selectedJasperReportEndPoint;
    const requestPayload = {
      ...(this.request || {}),
      ...params,
    };
    this.request = requestPayload;
    console.log(requestPayload);
    this.reportService
      .getAccountingReports(requestPayload)
      .subscribe(
        (result: any) => {
          console.log(result);
          this.reportData = result.reportData;
          if (this.displayedColumns.length === 0) {
            this.displayedColumns = this.translateService.updateDisplayedColumns(
              result?.columnsToDisplay
            );
            this.allColumns = this.displayedColumns;
          }
          this.dataSource = new MatTableDataSource(this.reportData);
          // Set page properties
          this.totalItems = result.totalRecordsCount;
          this.totalPages = result.totalPages;
          this.morePages = result.morePages;
          this.selectedReportType = this.request.reportType;
        },
        error => {
          console.log(error);
        }
      )
      .add(() => (this.isLoading = false));
  }

  clearFilters(event: Event) {
    event.preventDefault();
    this.filtersForm.markAsUntouched();
    this.filtersForm.markAsPristine();
    this.filtersForm.reset();
    this.getReportData();
  }

  getColumnDisplayName(key: string): string {
    return this.columnNames[key] || key;
  }
  downloadPdf() {
    this.request.type = 'PDF';

    this.reportService.getAccountingJasperReports(this.request).subscribe(
      (result: any) => {
        console.log('Success...');
      },
      error => {
        console.log(error);
      }
    );
  }

  toggleAdvancedSearch() {
    this.isAdvancedSearchVisible = !this.isAdvancedSearchVisible;
  }
  onSortChange(sortState: Sort) {
    this.request.sortBy = sortState.active;
    this.request.sortDir = sortState.direction;
    this.request.sortFields = `${sortState.active}:${sortState.direction}`;

    this.getReportData(); // Re-fetch the report data with sorting
  }

  onPageChange(event: PageEvent) {
    this.pageSize = event.pageSize;
    this.currentPage = event.pageIndex;
    this.getReportData();
  }
  getSelectForField(field: string): MatSelect | undefined {
    return this.matSelects?.toArray().find(matSelect => matSelect.ngControl?.name === field);
  }

  subscribeToFormChanges(): void {
    this.filtersForm.get('branchId')!.valueChanges.subscribe(value => {
      this.filterYearIdForSelectedBranch();
    });

    this.filtersForm.valueChanges.subscribe(data => {
      if (data) {
        this.warehouseList = this.allWarehouses.filter(item => {
          return this.filtersForm.get('branchId').value === item.branchId;
        });
      }
    });
  }
  createForm() {
    this.filtersForm = this.fb.group({});
    this.searchConfigs.forEach(config => {
      const validators = config.mandatory ? [Validators.required] : [];
      this.filtersForm.addControl(config.backendParam, this.fb.control(null, validators));
      if (config.type === 'dateRange') {
        this.filtersForm.addControl(
          config.backendParam + 'From',
          this.fb.control(null, validators)
        );
        this.filtersForm.addControl(config.backendParam + 'To', this.fb.control(null, validators));
      }
    });
    this.subscribeToFormChanges();
    const branchIdFromCookie = +this.cookieService.get('branchId');
    const yearIdFromCookie = +this.cookieService.get('yearId');
    this.filtersForm.patchValue({ branchId: branchIdFromCookie, yearId: yearIdFromCookie });
    this.filterYearIdForSelectedBranch();
  }

  getListForField(field: string) {
    switch (field) {
      case 'branchId':
        return this.branchList;
      case 'yearId':
        return this.filteredYearIdList;
      case 'accountGroup':
        return this.accountGroups;
      case 'accountType':
        return this.accountTypes;
      case 'businessGroup':
        return this.businessGroups;
      case 'reportType':
        return this.reportTypeList;
      default:
        return [];
    }
  }

  selectReportType(report: any) {
    this.selectedReportType = report.name;
    this.selectedReportEndPoint = report.endPoint;
    this.selectedJasperReportEndPoint = report.endPoint;
    const reportType = this.reportTypeList.find(report => report.name === this.selectedReportType);
    this.templateId = reportType.id;
    this.searchConfigs = reportType.searchConfigs;
    this.createForm();
  }
  resetReportType() {
    this.selectedReportType = null;
    this.reportData = [];
    this.request = {};
  }
  transformReportName(name: string): string {
    return name ? name.replace(/ /g, '') : '';
  }

  getAccountColumnsDisplay(columnName: string[]): string[] {
    console.log(columnName);
    return columnName;
  }
  updateDisplayedColumns(selectedColumns: string[]) {
    this.displayedColumns = selectedColumns.length ? selectedColumns : this.allColumns;
  }
}
