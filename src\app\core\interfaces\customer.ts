import { IAddress } from 'src/app/modules/shared/components/address/address';
import { ISupplier } from './supplier';

export interface ICustomer {
  customerId: number;
  accountId: number;
  branchId: number;
  companyId: number;
  accountNumber: number;
  nameArabic: string;
  nameEnglish: string;
  isHidden: boolean;
  isFreezed: boolean;
  hasCashPrivilege: boolean;
  hasCreditPrivilege: boolean;
  hasCardPrivilege: boolean;
  hasTransferPrivilege: boolean;
  discountPercent: number;
  paymentToleranceDays: number;
  allowedBalance: number;
  distributorAccountNumber: number;
  phoneNumber: string;
  emailId: string;
  vatNumber: string;
  parentAccountId: number;
  yearlyTarget: number;
  commission: number;
  address: IAddress;
  costCentreId: number;
  distributorAccountId: number;
  identification?: string;
  identificationCode?: string;
  customerNote?: null;
  balance?: null;
}

export interface ICustomerSearch {
  searchTimeStamp: Date;
  totalRecordsCount: number;
  totalPages: number;
  morePages: boolean;
  customers: ICustomer[];
}

export interface ICustomerView {
  isViewMode?: boolean;
  existingClient?: boolean;
  customer?: ICustomer | ISupplier | null;
}

export interface ICustomerModalData {
  header?: string;
  data: ICustomer | ISupplier | null;
  isViewMode: boolean;
}
