import { Component, Inject, OnInit, Optional, ViewChild } from '@angular/core';
import {
  UntypedForm<PERSON>rray,
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { forkJoin } from 'rxjs';
import { CommonService } from 'src/app/core/api/common.service';
import { PaginatedFilter } from 'src/app/core/interfaces/PaginatedFilter';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { LocalStorageService } from 'src/app/modules/core/core/services/local-storage.service';
import { BranchParams } from 'src/app/modules/featured/settings/models/branchParams';
import { BranchService } from 'src/app/modules/featured/settings/services/branch.service';
import { SearchboxComponent } from 'src/app/modules/shared/components/searchbox/searchbox.component';
import { CategoryService } from '../../../../../core/api/category.service';
import { ProductService } from '../../../../../core/api/product.service';
import { Branch } from '../../../catalog/models/branch';
import { BulkEditData } from '../../../catalog/models/bulkedit';
import { Category } from '../../../catalog/models/category';
import { CategoryParams } from '../../../catalog/models/categoryParams';
import { UnitPriceAdjustmentsParams } from '../../../catalog/models/unitPriceAdjustmentParams';
import { Inventory } from '../../models/unitPrice';

@Component({
  selector: 'app-unit-price-update',
  templateUrl: './unit-price-update.component.html',
  styleUrls: ['./unit-price-update.component.scss'],
})
export class UnitPriceUpdateComponent implements OnInit {
  @ViewChild('searchBoxForm') searchBoxForm: SearchboxComponent;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  productForm: UntypedFormGroup = Object.create(null);
  imageChangedEvent: any = '';
  croppedImage: any = '';
  showCropper = false;
  defaultImage = 'assets/images/company-placeholder-image.png';
  loading = true;
  title: string;
  companyError = false;
  displayRemoveIcon = false;
  loaded = false;
  tableData = new MatTableDataSource<any>();
  adjustmentRows: UntypedFormArray = this.formBuilder.array([]);
  adjustmentForm: UntypedFormGroup = this.formBuilder.group({
    InventoryOpeningRequest: this.adjustmentRows,
  });
  warehouse = new UntypedFormControl([]);
  categoryList: Category[];
  branchList: Branch[];
  units: any;
  products: Inventory[];
  submitted = false;
  postSaved = false;
  totalRecords = 0;
  pageSize: number;

  allComplete = false;

  displayedColumns: string[];

  editedUnitId = '';
  formTitle: string;
  isEditMode = false;
  parentCategories: any;
  subCategories: any;
  filterForm: UntypedFormGroup;
  isViewMode = false;
  isAdvancedSearchEnabled = false;
  constructor(
    @Optional() @Inject(MAT_DIALOG_DATA) public data: BulkEditData,
    private branchService: BranchService,
    private router: Router,
    private dialog: MatDialog,
    private route: ActivatedRoute,
    private categoryService: CategoryService,
    private formBuilder: UntypedFormBuilder,
    private toastr: ToastrService,
    private authService: AuthService,
    private fb: UntypedFormBuilder,
    private productService: ProductService,
    private localStorage: LocalStorageService,
    private commonService: CommonService
  ) {}

  ngOnInit(): void {
    this.loading = true;
    if (this.data) {
      this.getModalData();
    } else {
      this.getAllDropDownData();
      this.initializeFilterForm();
    }
    this.displayedColumns = [
      'itemCode',
      'nameArabic',
      'unitName',
      'wholesalePrice',
      'distributorPrice',
      'retailPrice',
      'openPurchasePrice',
      'purchasePrice',
      'costPrice',
    ];
  }

  ngAfterViewInit(): void {
    if (this.tableData) {
      this.tableData.paginator = this.paginator;
      this.tableData.sort = this.sort;
    }
  }

  initializeFilterForm() {
    this.filterForm = this.fb.group({
      categoryId: [[]],
      searchBoxForm: [],
    });
  }

  getParentName(parentId: number) {
    return this.parentCategories.filter(data => data.categoryId === parentId)[0]?.nameArabic;
  }

  getAllDropDownData() {
    const category = this.categoryService.getAllCategories(new CategoryParams());
    const branches = this.branchService.getAllBranches(new BranchParams());
    forkJoin([category, branches]).subscribe(results => {
      this.categoryList = results[0];
      this.branchList = results[1];
      this.splitCategoryandParentCategory();
      this.loading = false;
    });
  }

  splitCategoryandParentCategory() {
    this.parentCategories = this.categoryList.filter(data => data.parentCategoryId === null);
    this.subCategories = this.categoryList.filter(data => data.parentCategoryId !== null);
  }

  deleteEntry(id: number) {
    this.adjustmentRows.removeAt(id);
    this.tableData.data.splice(id, 1);
    this.tableData = this.tableData;
    setTimeout(() => {
      this.tableData.paginator = this.paginator;
      this.tableData.sort = this.sort;
    });
  }

  addUnitsPriceRow(data) {
    const row = this.formBuilder.group({
      branchId: [data && data?.branchId],
      companyId: [data && data?.companyId],
      itemLocation: [data && data?.itemLocation],
      itemUnitId: [data && data?.itemUnitId],
      itemId: [data && data?.itemId],
      status: [data && data?.status],
      warehouseId: [data && data?.warehouseId],
      yearId: [data && data?.yearId],
      unitName: [data && data?.unitName],
      itemCode: [data && data?.itemCode],
      itemName: [data && data?.itemName],
      costPrice: [(data && data?.costPrice) || 0, Validators.required],
      openPurchasePrice: [(data && data?.openPurchasePrice) || 0, Validators.required],
      purchasePrice: [(data && data?.purchasePrice) || 0, Validators.required],
      transportCost: [(data && data?.transportCost) || 0, Validators.required],
      retailPrice: [(data && data?.retailPrice) || 0, Validators.required],
      wholesalePrice: [(data && data?.wholesalePrice) || 0, Validators.required],
      distributorPrice: [(data && data?.distributorPrice) || 0, Validators.required],
    });
    this.adjustmentRows.push(row);
  }

  markFormArrayTouched() {
    const formData = (this.adjustmentForm.get('InventoryOpeningRequest') as UntypedFormArray)
      .controls;
    formData.forEach(formArrayData => {
      Object.keys(formArrayData['controls']).forEach(key => {
        formArrayData.get(key).markAsTouched();
      });
    });
  }

  getFilterData(event?: Event, pageEvent?: PaginatedFilter) {
    event?.preventDefault();
    this.filterForm.markAllAsTouched();
    if (this.filterForm.valid && this.searchBoxForm.isValid) {
      this.loading = true;
      const params: UnitPriceAdjustmentsParams = <UnitPriceAdjustmentsParams>{};
      console.log(this.filterForm.controls);
      params.categoryIds = this.filterForm.controls['categoryId'].value;
      params.searchString = this.searchBoxForm.searchBoxForm.controls['searchString'].value;
      params.searchType = this.searchBoxForm.searchBoxForm.controls['searchType'].value;
      params.pageSize = pageEvent ? pageEvent.pageSize : 10;
      params.pageNumber = pageEvent ? pageEvent.pageNumber : 1;
      this.productService.getUnitsByCriteria(params).subscribe(result => {
        this.products = null;
        this.products = result.itemUnits;
        setTimeout(() => {
          this.tableData = new MatTableDataSource(this.products);
          this.totalRecords = result.totalRecordsCount;
          this.tableData.sort = this.sort;
        });
        this.products.forEach(data => this.addUnitsPriceRow(data));
        this.loading = false;
        this.submitted = true;
      });
    } else {
      this.commonService.scrollToError();
    }
  }

  getModalData(event?: Event, pageEvent?: PaginatedFilter) {
    const params: UnitPriceAdjustmentsParams = <UnitPriceAdjustmentsParams>{};
    params.itemCode = this.data.selectedRecords.toString();
    params.pageSize = pageEvent ? pageEvent.pageSize : 10;
    params.pageNumber = pageEvent ? pageEvent.pageNumber : 1;
    this.productService.getUnitsByCriteria(params).subscribe(result => {
      this.products = null;
      this.products = result.itemUnits;
      setTimeout(() => {
        this.tableData = new MatTableDataSource(this.products);
        this.totalRecords = result.totalRecordsCount;
        this.tableData.sort = this.sort;
      });
      this.products.forEach(data => this.addUnitsPriceRow(data));
      this.loading = false;
      this.submitted = true;
    });
  }

  clearFilters(event: Event) {
    event.preventDefault();
    this.filterForm.markAsUntouched();
    this.filterForm.markAsPristine();
    this.filterForm.reset();
    this.searchBoxForm.resetSearchBox();
  }

  onSubmit(event: Event) {
    console.log('adjustmentForm', this.adjustmentForm.value['InventoryOpeningRequest']);
    this.submitted = true;
    event.preventDefault();
    this.adjustmentForm.markAllAsTouched();
    this.markFormArrayTouched();
    if (
      this.adjustmentForm.valid &&
      (this.adjustmentForm.get('InventoryOpeningRequest') as UntypedFormArray)?.controls?.length > 0
    ) {
      // add logic to update
      this.productService
        .updateUnitPrices(this.adjustmentForm.value['InventoryOpeningRequest'])
        .subscribe(response => {
          //this.toastr.success('Unit Prices Updated Successfully');
          this.commonService.playSuccessSound();
        });
    } else {
      this.commonService.scrollToError();
    }
  }

  getActualIndex(index: number) {
    if (this.paginator) {
      return index + this.paginator.pageSize * this.paginator.pageIndex;
    }
    return index;
  }

  onPageChange(pageEvent: PageEvent) {
    const event: PaginatedFilter = {
      pageNumber: pageEvent.pageIndex + 1 ?? 1,
      pageSize: pageEvent.pageSize ?? 10,
    };
    this.getFilterData(null, event);
  }
}
