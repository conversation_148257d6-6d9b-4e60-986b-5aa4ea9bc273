import { Injectable } from '@angular/core';
import { DashboardModulesHolder } from 'src/app/modules/shared/components/dashboard-modules-holder/modulesHolder';
import { productModulesList } from './product-main-modules-data';

@Injectable()
export class ProductModuleService {
  public productModules = productModulesList;
  public getPosApps(): DashboardModulesHolder[] {
    return this.productModules;
  }
}
