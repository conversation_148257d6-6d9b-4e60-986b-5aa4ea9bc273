<div class="row no-gutters">
  <div
    class="col-sm-12 col-md-4 col-lg-4 p-2 custom-card"
    *ngFor="let module of modules | hasRoleQualifier">
    <ng-container *ngIf="module?.moduleType === 'mainModule' || module?.moduleType === 'subModule'">
      <mat-card
        class="justify-content-between align-items-center cursor-pointer bg-primary"
        *appHasPermission="module?.modulePermission"
        routerLink="{{ module.moduleRouterLink }}">
        <div class="align-items-center">
          <mat-card-title class="align-items-center text-white">
            <p>{{ module.moduleName | translate }}</p>
          </mat-card-title>
        </div>
      </mat-card>
    </ng-container>
  </div>
</div>
