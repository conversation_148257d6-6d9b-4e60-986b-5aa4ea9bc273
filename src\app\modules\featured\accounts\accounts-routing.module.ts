import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DashBoardGuard } from '../../core/core/guards/dashboard.guard';
import { PermissionGuard } from '../../core/core/guards/permission.guard';
import { AccountSetupComponent } from './components/account-setup/account-setup.component';
import { ChartOfAccountsFormComponent } from './components/chart-of-accounts/chart-of-accounts-form/chart-of-accounts-form.component';
import { ChartOfAccountsComponent } from './components/chart-of-accounts/chart-of-accounts.component';
import { CostCentreFormComponent } from './components/cost-centres/cost-centre-form/cost-centre-form.component';
import { CostCentresComponent } from './components/cost-centres/cost-centres.component';
import { AccountsDashboardComponent } from './components/dashboard/accounts-dashboard.component';
import { DepreciationFormComponent } from './components/depreciation/depreciation-form/depreciation-form.component';
import { DepreciationComponent } from './components/depreciation/depreciation.component';
import { FixedAssetDashboardComponent } from './components/fixed-asset-dashboard/fixed-asset-dashboard.component';
import { JournalEntriesFormComponent } from './components/journal-entries/journal-entries-form/journal-entries-form.component';
import { JournalEntriesComponent } from './components/journal-entries/journal-entries.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'dashboard',
    pathMatch: 'full',
  },
  {
    path: 'dashboard',
    component: AccountsDashboardComponent,
    canActivate: [DashBoardGuard],
    data: {
      title: 'Accounts Management',
      urls: [{ title: 'Main Dashboard', url: '/dashboard' }],
      allowedPermissions: ['AccountsManagement', 'AllPermissions'],
    },
  },
  {
    path: 'chartOfAccounts',
    component: ChartOfAccountsComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'caccounts.listAccounts',
      urls: [{ title: 'Accounts', url: '/accounts/dashboard' }],
      allowedPermissions: ['ChartOfAccounts', 'AllPermissions'],
    },
  },
  {
    path: 'chartOfAccounts/create',
    component: ChartOfAccountsFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'caccounts.createAccount',
      urls: [{ title: 'Chart Of Accounts', url: '/accounts/chartOfAccounts' }],
      allowedPermissions: ['ChartOfAccounts.Create', 'AllPermissions'],
    },
  },
  {
    path: 'chartOfAccounts/edit/:id',
    component: ChartOfAccountsFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'caccounts.editAccount',
      urls: [{ title: 'Chart Of Accounts', url: '/accounts/chartOfAccounts' }],
      allowedPermissions: ['ChartOfAccounts.Update', 'AllPermissions'],
    },
  },
  {
    path: 'chartOfAccounts/view/:id',
    component: ChartOfAccountsFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'caccounts.viewAccount',
      urls: [{ title: 'Chart Of Accounts', url: '/accounts/chartOfAccounts' }],
      allowedPermissions: ['ChartOfAccounts.View', 'AllPermissions'],
    },
  },
  {
    path: 'accountSetup',
    component: AccountSetupComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Accounts Set-Up',
      urls: [{ title: 'Accounts dashboard', url: '/accounts/dashboard' }],
      allowedPermissions: ['ChartOfAccounts.Create', 'AllPermissions'],
    },
  },
  {
    path: 'costCentres',
    component: CostCentresComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'costCenter.listings',
      urls: [{ title: 'dashBoardModules.acountingMgt', url: '/accounts/dashboard' }],
      allowedPermissions: ['ChartOfAccounts.Create', 'AllPermissions'],
    },
  },
  {
    path: 'costCentres/create',
    component: CostCentreFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'costCenter.create',
      urls: [{ title: 'costCenter.listings ', url: '/accounts/costCentres' }],
      allowedPermissions: ['ChartOfAccounts.Create', 'AllPermissions'],
      mode: 'create',
    },
  },
  {
    path: 'costCentres/view/:id',
    component: CostCentreFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'costCenter.view',
      urls: [{ title: 'costCenter.listings ', url: '/accounts/costCentres' }],
      allowedPermissions: ['ChartOfAccounts.View', 'AllPermissions'],
      mode: 'view',
    },
  },
  {
    path: 'costCentres/edit/:id',
    component: CostCentreFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'costCenter.edit',
      urls: [{ title: 'costCenter.listings', url: '/accounts/costCentres' }],
      allowedPermissions: ['ChartOfAccounts.Update', 'AllPermissions'],
      mode: 'edit',
    },
  },
  {
    path: 'journalEntries',
    component: JournalEntriesComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'journals.listJournals',
      urls: [{ title: 'dashBoardModules.acountingMgt', url: '/accounts/dashboard' }],
      allowedPermissions: ['ChartOfAccounts', 'AllPermissions'],
    },
  },
  {
    path: 'journalEntries/create',
    component: JournalEntriesFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'journals.create',
      urls: [{ title: 'journals.listJournals', url: '/accounts/journalEntries' }],
      allowedPermissions: ['JournalEntries.Create', 'AllPermissions'],
      mode: 'create',
    },
  },
  {
    path: 'journalEntries/edit/:id',
    component: JournalEntriesFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'journals.editJournal',
      urls: [{ title: 'journals.listJournals', url: '/accounts/journalEntries' }],
      allowedPermissions: ['JournalEntries.Update', 'AllPermissions'],
      mode: 'edit',
    },
  },
  {
    path: 'journalEntries/view/:id',
    component: JournalEntriesFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'journals.viewJournal',
      urls: [{ title: 'journals.listJournals', url: '/accounts/journalEntries' }],
      allowedPermissions: ['JournalEntries.View', 'AllPermissions'],
      mode: 'view',
    },
  },
  {
    path: 'depreciation',
    component: DepreciationComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'depreciation.listing',
      urls: [{ title: 'Accounting Dashboard', url: '/accounts/dashboard' }],
      allowedPermissions: ['Depreciation', 'AllPermissions'],
    },
  },
  {
    path: 'depreciation/create',
    component: DepreciationFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'depreciation.depreciationCreate',
      urls: [{ title: 'Depreciation Dashboard', url: '/accounts/depreciation' }],
      allowedPermissions: ['Depreciation.Create', 'AllPermissions'],
    },
  },
  {
    path: 'depreciation/edit/:id',
    component: DepreciationFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'depreciation.deprciationEdit',
      urls: [{ title: 'Depreciation', url: '/accounts/depreciation' }],
      allowedPermissions: ['Depreciation.Update', 'AllPermissions'],
    },
  },
  {
    path: 'depreciation/view/:id',
    component: DepreciationFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'depreciation.depriciationDetails',
      urls: [{ title: 'Depreciation', url: '/accounts/depreciation' }],
      allowedPermissions: ['Depreciation.View', 'AllPermissions'],
    },
  },
  {
    path: 'fixedAsset',
    component: FixedAssetDashboardComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Fixed Asset Management',
      urls: [{ title: 'Accounts', url: '/accounts/dashboard' }],
      allowedPermissions: ['FixetAssetManagement', 'AllPermissions'],
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AccountsRoutingModule {}
