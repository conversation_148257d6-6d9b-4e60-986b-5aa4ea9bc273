import { Component, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { BranchService } from 'src/app/modules/featured/settings/services/branch.service';
import { Units } from '../../../models/units';
import { UnitService } from 'src/app/core/api/unit.service';
import { CommonService } from 'src/app/core/api/common.service';

@Component({
  selector: 'app-units-form',
  templateUrl: './units-form.component.html',
  styleUrls: ['./units-form.component.scss'],
})
export class UnitsFormComponent implements OnInit {
  units: Units;
  unitForm: UntypedFormGroup;
  formTitle: string;
  isEditMode = false;
  branches = [];
  loading = true;
  editedUnitId = '';
  unitFactorTypes = [
    {
      display: 'Count',
      value: 'Count',
    },
    {
      display: 'Volume',
      value: 'Volume',
    },
    {
      display: 'Weight',
      value: 'Weight',
    },
    {
      display: 'Length',
      value: 'Length',
    },
  ];
  constructor(
    private authService: AuthService,
    private branchService: BranchService,
    private router: Router,
    private route: ActivatedRoute,
    private unitApiService: UnitService,
    private toastr: ToastrService,
    private fb: UntypedFormBuilder,
    private commonService: CommonService
  ) {}

  ngOnInit() {
    this.formTitle = this.route.snapshot.data['title'];
    this.route.params.subscribe(params => {
      const id = params['id'];
      if (id) {
        this.editedUnitId = id;
        this.isEditMode = true;
        this.getUnits(id);
      } else {
        this.editedUnitId = null;
        this.isEditMode = false;
        this.initializeForm();
        this.loading = false;
      }
    });
  }

  getUnits(id: string): void {
    this.unitApiService.getUnitById(id).subscribe(result => {
      console.log(result);
      this.units = result;
      this.initializeForm(this.units);
      this.loading = false;
    });
  }

  initializeForm(units?: Units) {
    this.unitForm = this.fb.group({
      name: ['', Validators.required],
      factorRefUom: ['', Validators.required],
      type: ['Count', Validators.required],
    });

    if (units) {
      this.unitForm.patchValue({
        name: units.name,
        factorRefUom: units.factorRefUom,
        type: units.type,
      });
    }
  }

  onSubmit(event: Event) {
    event.preventDefault();
    this.unitForm.markAllAsTouched();
    if (this.unitForm && this.unitForm?.valid) {
      if (!this.isEditMode) {
        this.unitApiService.createUnit(this.unitForm.value).subscribe(response => {
          //this.toastr.success('Category added Successfully');
          this.commonService.playSuccessSound();
          this.router.navigate(['../'], { relativeTo: this.route });
        });
      } else {
        this.unitApiService
          .updateUnit(this.unitForm.value, this.editedUnitId)
          .subscribe(response => {
            //this.toastr.success('Category updated Successfully');
            this.commonService.playSuccessSound();
            this.router.navigate(['../../'], { relativeTo: this.route });
          });
      }
    } else {
      this.commonService.scrollToError();
    }
  }
}
