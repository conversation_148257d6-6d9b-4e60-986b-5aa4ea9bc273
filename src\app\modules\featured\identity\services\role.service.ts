import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { IRole } from '../../../../core/interfaces/role';
import { RoleParams } from '../../../../core/models/params/roleParams';

@Injectable({
  providedIn: 'root',
})
export class RoleService {
  baseUrl = environment.apiUrl + 'auth/roles';

  constructor(private http: HttpClient) {}

  getRoles(params: RoleParams) {
    const getRoleParams = this.getParams(params);
    return this.http.get<IRole[]>(this.baseUrl, { params: getRoleParams });
  }

  getRoleById(id: number) {
    return this.http.get<IRole>(this.baseUrl + `/${id}`);
  }

  createRole(role: IRole) {
    return this.http.post(this.baseUrl, role);
  }

  updateRole(role: IRole, roleId: number) {
    return this.http.put(this.baseUrl + `/${roleId}`, role);
  }

  deleteRole(id: string) {
    return this.http.delete(this.baseUrl + id);
  }

  getParams(roleParams: RoleParams): HttpParams {
    let httpParams = new HttpParams();
    if (roleParams?.searchString)
      httpParams = httpParams.append('searchString', roleParams.searchString);
    if (roleParams?.pageNumber)
      httpParams = httpParams.append('pageNumber', roleParams.pageNumber.toString());
    if (roleParams?.pageSize)
      httpParams = httpParams.append('pageSize', roleParams.pageSize.toString());
    if (roleParams?.orderBy)
      httpParams = httpParams.append('orderBy', roleParams.orderBy.toString());
    return httpParams;
  }
}
