export interface Inventories {
  searchTimeStamp: Date;
  totalRecordsCount: number;
  totalPages: number;
  morePages: boolean;
  inventories: Inventory[];
}

export interface Inventory {
  warehouseId: number;
  warehouseName: string;
  itemId: number;
  itemName: string;
  itemCode: string;
  itemUnitId: number;
  unitBarcode: string;
  unitName: string;
  currentQty: number;
  companyId: number;
  branchId: number;
  itemLocation: string;
  lastInventoryCheckDate: Date;
  openQty: number;
  reservedQty: number;
  yearId: number;
}
