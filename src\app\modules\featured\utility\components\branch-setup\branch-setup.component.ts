import { Component, ElementRef, Inject, OnInit, Optional, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { tap } from 'rxjs/operators';
import { PaginatedFilter } from 'src/app/core/interfaces/PaginatedFilter';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { SearchboxComponent } from 'src/app/modules/shared/components/searchbox/searchbox.component';
import { BulkEditData } from '../../../catalog/models/bulkedit';
import { CategoryService } from '../../../../../core/api/category.service';
import { ProductService } from '../../../../../core/api/product.service';
import { BranchService } from '../../../settings/services/branch.service';
import { BranchParams } from '../../../settings/models/branchParams';
import { Branch } from '../../../catalog/models/branch';
import { BranchSetupApiService } from '../../services/branch-setup-api.service';
import { HttpParams } from '@angular/common/http';
import { CommonService } from 'src/app/core/api/common.service';

@Component({
  selector: 'branch-setup',
  templateUrl: './branch-setup.component.html',
  styleUrls: ['./branch-setup.component.scss'],
})
export class BranchSetupComponent implements OnInit {
  @ViewChild('searchBoxForm') searchBoxForm: SearchboxComponent;
  branchSetupForm: UntypedFormGroup;
  branchList: Branch[];
  constructor(
    @Optional() @Inject(MAT_DIALOG_DATA) public data: BulkEditData,
    private branchService: BranchService,
    private authService: AuthService,
    private toastr: ToastrService,
    private fb: UntypedFormBuilder,
    private branchSetupApiService: BranchSetupApiService,
    private commonService: CommonService
  ) {}

  ngOnInit(): void {
    this.branchSetupForm = this.fb.group({
      targetBranchId: ['', Validators.required],
      sourceBranchId: ['', Validators.required],
    });
    setTimeout(() => {
      this.getAllBranch();
    }, 5000);
  }

  getAllBranch(): void {
    this.branchService.getAllBranches(new BranchParams()).subscribe((response: Branch[]) => {
      this.branchList = response;
    });
  }

  onFormSubmit(event?: Event, pageEvent?: PaginatedFilter) {
    event.preventDefault();
    this.branchSetupForm.markAllAsTouched();
    if (this.branchSetupForm && this.branchSetupForm?.valid) {
      let params = new HttpParams();
      params = params.append('branchIdFrom', this.branchSetupForm.value.sourceBranchId.toString());
      params = params.append('branchIdTo', this.branchSetupForm.value.targetBranchId.toString());

      this.branchSetupApiService.copyAllAccounts(params).subscribe(res => {
        //this.toastr.success('Accounts Copied Successfully');
        this.commonService.playSuccessSound();
      });
    }
  }
}
