import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SharedModule } from '../../shared/shared.module';
import { MaterialModule } from '../../material/material.module';
import { TranslateModule } from '@ngx-translate/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { UtilityDashboardComponent } from './components/dashboard/utility-dashboard.component';
import { UtilityRoutingModule } from './utility-routing.module';
import { DataImportComponent } from './components/data-import/data-import.component';
import { BranchSetupComponent } from './components/branch-setup/branch-setup.component';
import { BranchSetupApiService } from './services/branch-setup-api.service';

@NgModule({
  declarations: [UtilityDashboardComponent, DataImportComponent, BranchSetupComponent],
  imports: [
    CommonModule,
    UtilityRoutingModule,
    SharedModule,
    MaterialModule,
    TranslateModule,
    FlexLayoutModule,
  ],
  providers: [BranchSetupApiService],
})
export class UtilityModule {}
