.main-container {
  width: 100%;
  height: 100%;
}

.filter-card {
  box-sizing: border-box;
  padding: 10px;
  margin: 0;
  overflow-y: auto;
  overflow-x: auto;
  background-color: #dfebf2 !important;
  width: 100%;
  border-right: 1px solid #ccc;
}
.filter-card-content {
  max-width: 100%;
}

.result-card {
  box-sizing: border-box;
  padding: 10px;
  margin: 0;

  overflow-x: auto;
  background-color: #edf4f7 !important;
  width: 100%;
}
.column {
  display: flex;
  flex-wrap: wrap;
  margin: 10px;
}

.table-container {
  height: calc(100vh - 150px);
  overflow: auto;
  position: relative;
}

.mat-table {
  width: 100%;
}
.table-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
  background-color: transparent !important;
  border-bottom: none;
}

.table-header .actions {
  display: flex;
  gap: 8px;
}
.table-header button {
  color: #1e88e5 !important;
}
.mat-icon {
  color: #1e88e5 !important;
}

.pagination-container {
  background-color: #f5f5f5;
}

.report-search-heading {
  color: #06769a !important;
  padding: 10px;
  overflow-y: auto;
  overflow-x: auto;
}

.filter-card,
.result-card {
  margin-bottom: 0;
  border-radius: 0;
}

.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  font-size: 18px;
  color: #999;
  font-weight: 500;
  background-color: transparent !important;
  padding: 20px;
  border: none;
  text-align: center;
}

.edit-report-type {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f5f5f5;
  border-radius: 5px;
  gap: 10px;
  width: 100%;
  padding: 10px 10px;
}

.report-type {
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  color: #1e88e5 !important;
  text-transform: uppercase;
  white-space: nowrap;
}

.report-type-buttons {
  display: flex;
  gap: 10px;
  margin-top: 5px;
  margin-bottom: 10px;
}
.filter-data {
  margin-left: auto;
  display: flex;
  flex-shrink: 0;
}
.report-type-container {
  display: flex;
  align-items: center;
  flex-grow: 1;
  justify-content: center;
  position: relative;
  left: 50%;
  transform: translate(-50%);
}
.sticky-table {
  width: 100%;
  overflow: auto;
  position: relative;
}

.sticky-header {
  position: sticky;
  top: 0;
  z-index: 2;
  background: white;
}

.mat-header-cell {
  position: sticky !important;
  top: 0;
  z-index: 3;
  background: #f5f5f5;
  font-weight: bold;
}
