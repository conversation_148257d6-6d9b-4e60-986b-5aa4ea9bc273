export interface Journal {
  journalId: number;
  journalNumber: number;
  yearId: number;
  companyId: number;
  branchId: number;
  journalDate: Date;
  journalType: string;
  journalCreationType: string;
  isPosted: boolean;
  description: string;
  arabicDescription: string;
  transactionLineRespons?: TransactionLine[];
  transactionLineRequests?: TransactionLine[];
}

export interface TransactionLine {
  entryId: number;
  accountId: number;
  account: number;
  debitAmount: number;
  creditAmount: number;
  description: string;
  description2: string;
}
