import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ProductModulesComponent } from './product-modules/product-modules.component';

const routes: Routes = [
  {
    path: '',
    children: [
      {
        path: '',
        component: ProductModulesComponent,
        data: {
          title: 'POS Features Dashboard',
        },
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class HomeRoutingModule {}
