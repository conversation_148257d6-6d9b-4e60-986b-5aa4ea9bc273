import { IInventory } from 'src/app/modules/featured/catalog/models/product';
import { IPaymentDetails } from './payment';
import { ISupplier } from './supplier';
import { ICustomer } from './customer';
import { ISalesItem, SaletransactionTypes, salesNotesType } from './sales';

export interface IPurchaseDetails {
  issueDate?: string;
  orderNumber?: string | null;
  documentNumber?: string | null;
  orderDate?: string | null;
  invoiceStatus?: string | null;
  invoiceDiscount?: number | null;
  notes?: string;
  items?: IPurchaseItem[];
  existingClient?: boolean;
  supplier?: ISupplier | null;
  payments?: IPaymentDetails;
  //
  referenceDocumentId?: number | null;
  referenceDocumentNumber?: string | null;
  referenceDocumentDate?: Date | string | null;
  vendorInvoiceNumber?: string | null;
  transactionType?: SaletransactionTypes;
  costCentreId?: number | null;
  distributorAccountId?: number | null;
}

export interface IPurchaseItem {
  retailPrice: number;
  wholesalePrice: number;
  distributorPrice: number;
  purchasePrice: number;
  profit: number;
  isGeneralProfitMethod?: boolean;
  itemId?: number;
  itemCode?: string;
  quantity?: number;
  itemUnitId?: number;
  price?: number;
  subtotal?: number;
  vat?: number;
  product?: IInventory;
  discount?: number;
  isGeneralDscntMethod?: boolean;

  priceType?: number;
  warehouseName?: string;
  itemName?: string;
  unitName?: string;
  notes?: null;
  subTotalVat?: number;
  warehouseId?: number;
  vatAmount?: number;

  // for notes
  returnableQty?: number;
  transactionItemId?: number;
}

export interface IPurchaseResponse {
  searchTimeStamp: Date;
  totalRecordsCount: number;
  totalPages: number;
  morePages: boolean;
  transactions: IPurchaseInvoice[];
}

export interface IPurchaseInvoice {
  id: number;
  branchId: number;
  yearId: number;
  documentNumber: string;
  issuedTime: null;
  issueDate: Date;
  vendorInvoiceNumber: null;
  orderId: number;
  orderDate: Date;
  invoiceTotal: number;
  totalVat: number;
  returnStatus: string;
  supplierType: number;
  // accountNumber: number;
  name: null;
  vatNumber: null;
  phoneNumber: null;
  distributorAccountId: number;
  supplierDiscount: number;
  costCentreId: number;
  referenceDocumentId: null;
  // nameEnglish: string;
  // nameArabic: string;
  paymentMethod: number;
  settlementStatus: number;
  unpaidAmount: number;
  //
  referenceDocumentDate?: string | null;
  referenceDocumentNumber?: string | null;
  transactionType?: SaletransactionTypes;
  account?: IAccountDetails;
}

export interface IAccountDetails {
  accountId: number;
  accountNumber: number;
  nameArabic: string;
  nameEnglish: string;
}

export interface IInvoiceAdjustmentDetails {
  invoiceAdjustmentMaster: InvoiceAdjustmentMaster;
  items: ISalesItem[];
  payments: IPaymentDetails;
  customers: ICustomer;
}

export interface InvoiceAdjustmentMaster {
  noteInvoiceId: number;
  noteType: salesNotesType;
  noteIssueDate: string;
  noteRemark: string;
  documentType?: DocumentType;
}
