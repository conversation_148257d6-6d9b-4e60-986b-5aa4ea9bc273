<form [formGroup]="searchBoxForm" autocomplete="off">
  <mat-label>{{ label }}</mat-label>
  <mat-form-field class="w-100">
    <!-- <mat-icon
          class="custom-icon"
          *ngIf="!disabled"
          (click)="onViewAccountsList($event); (false)"
          (keydown.enter)="onViewAccountsList($event); (false)"
          tabindex="0"
          matSuffix
          >filter</mat-icon
        > -->
    <a
      class="custom-icon"
      *ngIf="showSearch"
      (click)="onViewAccountsList($event); (false)"
      (keydown.enter)="onViewAccountsList($event); (false)"
      tabindex="0"
      matSuffix>
      <i-tabler class="icon-16" name="database-search"></i-tabler>
    </a>
    <input
      class="next"
      #userInput
      [matAutocomplete]="auto"
      matInput
      type="text"
      placeholder="{{ placeholder }}"
      formControlName="searchString" />
    <mat-error
      *ngIf="
        searchBoxForm.controls['searchString']?.hasError('required') &&
        searchBoxForm.controls['searchString'].touched
      ">
      {{ 'common.required' | translate }}
    </mat-error>
    <mat-progress-bar *ngIf="isAccountLoading" mode="query"></mat-progress-bar>
    <mat-autocomplete
      class="bigger-mat-ac"
      #autoComplete
      #auto="matAutocomplete"
      [displayWith]="displayFn.bind(this)"
      (optionSelected)="optionSelected($event)"
      autoActiveFirstOption>
      <ng-container *ngIf="accountSearchSource?.data?.length && !accountValueSet">
        <mat-option *ngFor="let account of accountSearchSource.data" [value]="account">
          {{ getAccountName(account) + '-' + account.accountNumber }}
        </mat-option>
      </ng-container>
      <mat-option *ngIf="resultNotFound"> No records Found </mat-option>
      <!-- <ng-container *ngIf="resultNotFound">
        <div class="no-row-center text-info">
          {{ 'No account Found' }}
        </div>
      </ng-container> -->
    </mat-autocomplete>
  </mat-form-field>
</form>
