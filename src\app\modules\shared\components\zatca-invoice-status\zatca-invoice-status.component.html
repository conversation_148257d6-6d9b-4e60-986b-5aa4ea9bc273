<!-- Filter Place Holders -->
<mat-card appearance="outlined">
  <form [formGroup]="filterForm" autocomplete="off">
    <div class="row no-gutters">
      <!-- <div class="p-2 col-md-5">
        <app-searchbox
          #searchBoxForm
          [accountNo]="true"
          [accountName]="false"
          [nameEnglish]="true"
          [nameArabic]="true"
          [itemCode]="false"
          [defaultSearchType]="'accountNo'"
          [validatorApplied]="true"
          formControlName="searchBoxForm"></app-searchbox>
      </div> -->
      <div class="p-2 col-lg-4 col-md-4 col-sm-6">
        <mat-label>{{ 'voucher.status' | translate }}</mat-label>

        <mat-form-field class="w-100" [subscriptSizing]="'fixed'">
          <mat-select [mode]="mode" appPreselectOption formControlName="invoiceType">
            <mat-option *ngFor="let stat of invoiceType" [value]="stat.value">{{
              stat.display | translate
            }}</mat-option>
          </mat-select>
          <mat-error
            *ngIf="
              filterForm?.controls['invoiceType'].hasError('required') &&
              filterForm?.controls['invoiceType'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>
      <div class="p-2 col-lg-4 col-md-4 col-sm-6">
        <mat-label>{{ 'voucher.status' | translate }}</mat-label>

        <mat-form-field class="w-100" [subscriptSizing]="'fixed'">
          <mat-select [mode]="mode" appPreselectOption formControlName="stage">
            <mat-option *ngFor="let stat of invoiceStages" [value]="stat.value">{{
              stat.display | translate
            }}</mat-option>
          </mat-select>
          <mat-error
            *ngIf="
              filterForm?.controls['stage'].hasError('required') &&
              filterForm?.controls['stage'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>
      <div class="p-2 col-lg-4 col-md-4 col-sm-6">
        <mat-label>{{ 'voucher.dateRange' | translate }}</mat-label>

        <mat-form-field class="w-100" [subscriptSizing]="'fixed'">
          <mat-date-range-input [rangePicker]="picker" [max]="todayDate" disabled>
            <input
              matStartDate
              disabled
              formControlName="issueDateFrom"
              placeholder="{{ 'voucher.startDate' | translate }}" />
            <input
              matEndDate
              disabled
              formControlName="issueDateTo"
              placeholder="{{ 'voucher.endDate' | translate }}" />
          </mat-date-range-input>
          <mat-datepicker-toggle [for]="picker" matIconSuffix></mat-datepicker-toggle>
          <mat-date-range-picker #picker disabled="false"></mat-date-range-picker>

          <mat-error *ngIf="filterForm.controls.issueDateFrom.hasError('matStartDateInvalid')"
            >Invalid start date</mat-error
          >
          <mat-error *ngIf="filterForm.controls.issueDateTo.hasError('matEndDateInvalid')"
            >Invalid end date</mat-error
          >
        </mat-form-field>
      </div>
    </div>
    <button (click)="search($event); (false)" mat-raised-button color="primary">
      {{ 'searchPanel.searchString' | translate }}
    </button>
    <button class="m-l-10" (click)="clearFilters($event); (false)" mat-stroked-button>
      {{ 'searchPanel.clear' | translate }}
    </button>
  </form>
</mat-card>
<!-- Filter Place Holders -->

<button
  *ngIf="selection.selected.length"
  (click)="sendToZatca($event)"
  mat-raised-button
  color="warn">
  {{ 'zatcaInvoiceListings.reportToZatca' | translate }}
</button>
<div class="table-responsive">
  <table class="w-100" [dataSource]="dataSource" mat-table matSort>
    <!-- Checkbox Column -->
    <ng-container matColumnDef="select">
      <th *matHeaderCellDef mat-header-cell></th>
      <td *matCellDef="let row" mat-cell>
        <mat-checkbox
          [disabled]="disabledSelection || row?.stage === 'CLEARED'"
          [color]="'primary'"
          (change)="onRowSelect(row, $event)"
          (click)="$event.stopPropagation()">
        </mat-checkbox>
      </td>
    </ng-container>
    <ng-container matColumnDef="issueDate">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'zatcaInvoiceListings.issueDate' | translate }}
      </th>
      <td *matCellDef="let element" mat-cell>
        {{ element.issueDate | date : 'yyyy-MM-dd' }}
      </td>
    </ng-container>
    <ng-container matColumnDef="documentType">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'zatcaInvoiceListings.documentType' | translate }}
      </th>
      <td *matCellDef="let element" mat-cell>
        <span
          class="status-text rounded"
          [ngClass]="{
          'bg-light-success': element.documentType === 'SALES',
          'bg-light-primary': element.documentType === 'CREDIT_NOTE',
          'bg-light-warning': element.documentType === 'DEBIT_NOTE',
        }">
          {{ 'zatcaInvoiceStatus.' + element?.documentType | translate }}
        </span>
      </td>
    </ng-container>
    <ng-container matColumnDef="invoiceType">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'zatcaInvoiceListings.invoiceType' | translate }}
      </th>
      <td *matCellDef="let element" mat-cell>
        <span
          class="status-text rounded"
          [ngClass]="{
          'bg-light-primary': element.invoiceType === 'SIMPLIFIED',
          'bg-light-accent': element.invoiceType === 'STANDARD',
        }">
          {{ 'zatcaInvoiceStatus.' + element?.invoiceType | translate }}
        </span>
      </td>
    </ng-container>
    <ng-container matColumnDef="documentNumber">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'zatcaInvoiceListings.documentNumber' | translate }}
      </th>
      <td *matCellDef="let element" [data]="element?.documentNumber" mat-cell appHyphen>
        {{ element?.documentNumber }}
      </td>
    </ng-container>

    <ng-container matColumnDef="stage">
      <th *matHeaderCellDef mat-header-cell>
        {{ 'zatcaInvoiceListings.status' | translate }}
      </th>
      <td class="text-center" *matCellDef="let element" mat-cell>
        <div class="status-container">
          <span
            class="status-text rounded"
            [ngClass]="{
              'bg-light-error': element.stage === 'REJECTED',
              'bg-light-accent': element.stage === 'EINVOICE_SAVED',
              'bg-light-success': element.stage === 'CLEARED'
            }">
            {{ element?.stage ? ('zatcaInvoiceStatus.' + element?.stage | translate) : '-' }}
          </span>
          <div class="button-container">
            <a
              (click)="
                getZatcaErrorForInvoice(element.documentUuid); (false); $event.preventDefault()
              "
              mat-icon-button
              matTooltip="Invoice Zatca Status"
              matTooltipClass="text-uppercase"
              matTooltipPosition="above">
              <i-tabler class="icon-12 theme-icon" name="exclamation-circle"></i-tabler>
            </a>
            <a
              (click)="getXmlForInvoice(element.documentUuid); (false); $event.preventDefault()"
              mat-icon-button
              matTooltip="Invoice XML"
              matTooltipClass="text-uppercase"
              matTooltipPosition="above">
              <i-tabler class="icon-12 theme-icon" name="file-type-xml"></i-tabler>
            </a>
          </div>
        </div>
      </td>
    </ng-container>
    <tr class="mat-row" *matNoDataRow>
      <td class="text-center" [attr.colspan]="displayedColumns.length">
        {{ 'common.noDataFound' | translate }}
      </td>
    </tr>
    <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
    <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
  </table>
</div>
