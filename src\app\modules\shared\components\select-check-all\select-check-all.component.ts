import { Component, Input, ViewChild } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatOption } from '@angular/material/core';
import { MatSelect } from '@angular/material/select';

@Component({
  selector: 'app-select-check-all',
  templateUrl: './select-check-all.component.html',
  styleUrls: ['./select-check-all.component.scss'],
})
export class SelectCheckAllComponent {
  @Input() model: UntypedFormControl;
  @Input() values: MatSelect;
  @Input() text = 'Select All';

  isChecked(): boolean {
    return (
      this.model.value &&
      this.values.options.length &&
      this.model.value.length === this.values.options.length
    );
  }

  isIndeterminate(): boolean {
    return (
      this.model.value &&
      this.values.options.length &&
      this.model.value.length &&
      this.model.value.length < this.values.options.length
    );
  }

  toggleSelection(change: Mat<PERSON>heckboxChange): void {
    if (change.checked) {
      console.log(this.values);
      this.values.options.forEach((item: MatOption) => item.select());
    } else {
      console.log(this.values);
      this.values.options.forEach((item: MatOption) => item.deselect());
    }
  }
}
