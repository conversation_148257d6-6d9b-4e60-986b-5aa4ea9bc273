<form [formGroup]="searchBoxForm" autocomplete="off">
  <!-- <div class="row no-gutters"> -->
  <!-- <div class="p-2 col-lg-3 col-md-3 col-sm-4 d-flex align-items-end"> -->
  <mat-label>{{ label }}</mat-label>

  <mat-form-field class="w-100">
    <mat-label>{{ reportLabel }}</mat-label>
    <a
      class="custom-icon"
      (click)="onViewAccountsList($event); (false)"
      (keydown.enter)="onViewAccountsList($event); (false)"
      tabindex="0"
      matSuffix>
      <i-tabler class="icon-16" name="database-search"></i-tabler>
    </a>
    <input
      #userInput
      [matAutocomplete]="auto"
      matInput
      type="text"
      placeholder="Enter atleast 3 characters"
      formControlName="searchString" />
    <mat-progress-bar *ngIf="isAccountLoading" mode="query"></mat-progress-bar>
    <mat-autocomplete
      class="bigger-mat-ac"
      #autoComplete
      #auto="matAutocomplete"
      autoActiveFirstOption>
      <ng-container *ngIf="accountSearchSource?.data?.length && !accountValueSet">
        <mat-option *ngFor="let account of accountSearchSource.data" [value]="account">
          {{ getAccountName(account) + '-' + account.accountNumber }}
        </mat-option>
        <!-- <div class="table-responsive">
              <table
                class="table product-list"
                #searchTable
                [dataSource]="accountSearchSource"
                mat-table
                matSort>
                <ng-container matColumnDef="accountNumber-nameArabic">
                  <th
                    class="w-40"
                    class="font-14"
                    *matHeaderCellDef
                    mat-header-cell
                    mat-sort-header></th>
                  <td class="w-40" *matCellDef="let element" mat-cell>
                    {{ element.accountNumber + '-' + element.nameArabic }}
                  </td>
                </ng-container>
                <tr *matHeaderRowDef="displayedSearchColumns; " mat-header-row></tr>
                <mat-option
                  class="search"
                  *matRowDef="let element; columns: displayedSearchColumns"
                  [value]="element">
                  <tr class="mat-row" mat-row></tr>
                </mat-option>
              </table>
            </div> -->
      </ng-container>
      <ng-container *ngIf="resultNotFound && userInput.value.length >= 3 && !accountValueSet">
        <div class="no-row-center text-info">
          {{ 'No account Found' }}
        </div>
      </ng-container>
    </mat-autocomplete>
  </mat-form-field>
  <!-- </div> -->
  <!-- </div> -->
</form>
