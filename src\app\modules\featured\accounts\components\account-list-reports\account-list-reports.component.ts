import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { Account } from '../../models/account';
import { ChartOfAccountsService } from '../../services/chart-of-accounts.service';
import { AccountParams } from '../../models/accountParams';
import { PaginatedFilter } from 'src/app/core/interfaces/PaginatedFilter';
import { AccountsSearchBoxComponent } from '../accounts-search-box/accounts-search-box.component';
import { HttpParams } from '@angular/common/http';

@Component({
  selector: 'app-chart-of-accounts',
  templateUrl: './account-list-reports.component.html',
  styleUrls: ['./account-list-reports.component.scss'],
})
export class AccountListReportsComponent implements OnInit {
  @ViewChild('searchBoxForm') searchBoxForm: AccountsSearchBoxComponent;
  accounts: Account[];
  isLoading = true;
  filterForm: UntypedFormGroup;
  isAdvancedSearchEnabled = false;
  accountGroups = [
    {
      display: 'Assets',
      value: 'ASSETS',
    },
    {
      display: 'Liabilities',
      value: 'LIABILITIES',
    },
    {
      display: 'Equity',
      value: 'EQUITY',
    },
    {
      display: 'Revenues',
      value: 'REVENUES',
    },
    {
      display: 'Expenses',
      value: 'EXPENSES',
    },
  ];

  accountTypes = [
    {
      display: 'General',
      value: 'GENERAL',
    },
    {
      display: 'Detailed',
      value: 'DETAILED',
    },
  ];

  businessGroups = [
    {
      display: 'General',
      value: 'GENERAL',
    },
    {
      display: 'Cashier',
      value: 'CASHIER',
    },
    {
      display: 'Bank',
      value: 'BANK',
    },
    {
      display: 'Customer',
      value: 'CUSTOMER',
    },
    {
      display: 'Supplier',
      value: 'SUPPLIER',
    },
    {
      display: 'Branch',
      value: 'BRANCH',
    },
    {
      display: 'Distributor',
      value: 'DISTRIBUTOR',
    },
  ];

  constructor(
    private chartOfAccountsService: ChartOfAccountsService,
    private fb: UntypedFormBuilder
  ) {}

  ngOnInit(): void {
    this.filterForm = this.fb.group({
      accountGroup: [null],
      businessGroup: [null],
      accountType: [null],
      searchBoxForm: [],
      reportType: ['PDF'],
      startingAccountNo: [],
      endingAccountNo: [],
    });
    this.getAccounts();
  }

  getAccounts(): void {
    const accountParams: AccountParams = new AccountParams();
    accountParams.pageNumber = 1;
    accountParams.pageSize = 10;
    this.chartOfAccountsService.getAllChartOfAccounts(accountParams).subscribe(result => {
      this.accounts = result.accounts;
      this.isLoading = false;
    });
  }

  getReport(event?: Event, pageEvent?: PaginatedFilter) {
    event?.preventDefault();
    console.log(this.filterForm);
    this.filterForm.markAllAsTouched();
    this.isLoading = true;
    let params: HttpParams = new HttpParams();
    if (this.filterForm.controls['reportType'].value)
      params = params.append('type', this.filterForm.controls['reportType'].value);
    if (this.searchBoxForm.searchBoxForm.controls['searchString'].value)
      params = params.append(
        'searchString',
        this.searchBoxForm.searchBoxForm.controls['searchString'].value
      );
    if (this.searchBoxForm.searchBoxForm.controls['searchType'].value)
      params = params.append(
        'searchType',
        this.searchBoxForm.searchBoxForm.controls['searchType'].value
      );
    if (this.filterForm.controls['accountGroup'].value)
      params = params.append('accountGroup', this.filterForm.controls['accountGroup'].value);
    if (this.filterForm.controls['businessGroup'].value)
      params = params.append('businessGroup', this.filterForm.controls['businessGroup'].value);
    if (this.filterForm.controls['accountType'].value)
      params = params.append('accountType', this.filterForm.controls['accountType'].value);
    if (this.filterForm.controls['startingAccountNo'].value)
      params = params.append(
        'accountNumberFrom',
        this.filterForm.controls['startingAccountNo'].value
      );
    if (this.filterForm.controls['endingAccountNo'].value)
      params = params.append('accountNumberTo', this.filterForm.controls['endingAccountNo'].value);

    this.chartOfAccountsService
      .getAccountListReport(params)
      .subscribe(result => {
        console.log('Success....');
      })
      .add(() => (this.isLoading = false));
  }

  clearFilters(event: Event) {
    event.preventDefault();
    this.filterForm.markAsUntouched();
    this.filterForm.markAsPristine();
    this.filterForm.reset();
    this.searchBoxForm.resetSearchBox();
    //this.getReport();
  }
}
