import { Directive, HostListener, ElementRef, Input } from '@angular/core';

@Directive({
  selector: '[appInputFormat]',
})
export class InputFormatDirective {
  @Input('appInputFormat') format;

  constructor(private elementRef: ElementRef) {}

  @HostListener('input') onInput() {
    const value: string = this.elementRef.nativeElement.value;
    if (this.format == 'capital-case') {
      const words = value.split(' ');
      this.elementRef.nativeElement.value = words
        .map(word => {
          return word[0].toUpperCase() + word.substring(1).toLowerCase();
        })
        .join(' ');
    } else if (this.format == 'upper-case') {
      this.elementRef.nativeElement.value = value.toUpperCase();
    } else if (this.format == 'lower-case') {
      this.elementRef.nativeElement.value = value.toLowerCase();
    }
  }
}
