<ng-container>
  <mat-card appearance="outlined">
    <form [formGroup]="filterForm" autocomplete="off">
      <div class="row no-gutters">
        <div class="p-2 col-md-4">
          <!-- main search box panel -->
          <app-accounts-search-box #searchBoxForm formControlName="searchBoxForm">
          </app-accounts-search-box>
        </div>
        <div class="p-2 col-lg-4 col-md-4 col-sm-4 inline align-items-end">
          <app-accounts-prosearch-box
            #fromAccountSearchBoxForm
            [label]="'accountStatReport.toAccount' | translate"
            (accountSelected)="onFromAccountSelection($event)"
            formControlName="startingAccountNo">
          </app-accounts-prosearch-box>
        </div>
        <div class="p-2 col-lg-4 col-md-4 col-sm-4 inline align-items-end">
          <app-accounts-prosearch-box
            #toAccountSearchBoxForm
            [label]="'accountStatReport.dateFrom' | translate"
            (accountSelected)="onToAccountSelection($event)"
            formControlName="endingAccountNo">
          </app-accounts-prosearch-box>
        </div>
        <div class="p-2 col-lg-2 col-md-2 col-sm-4 align-items-end">
          <mat-label>{{ 'accountStatReport.dateFrom' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input [matDatepicker]="picker1" matInput formControlName="startingDate" />
            <mat-datepicker-toggle [for]="picker1" matSuffix> </mat-datepicker-toggle>
            <mat-datepicker #picker1></mat-datepicker>
          </mat-form-field>
        </div>
        <div class="p-2 col-lg-2 col-md-2 col-sm-4 align-items-end">
          <mat-label>{{ 'accountStatReport.dateTo' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input [matDatepicker]="picker2" matInput formControlName="endingDate" />
            <mat-datepicker-toggle [for]="picker2" matSuffix> </mat-datepicker-toggle>
            <mat-datepicker #picker2></mat-datepicker>
          </mat-form-field>
        </div>
        <div class="p-2 col-lg-2 col-md-2 col-sm-4 align-items-end">
          <mat-label>{{ 'accountStatReport.accountGroup' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <mat-select formControlName="accountGroup">
              <mat-option *ngFor="let group of accountGroups" [value]="group.value">
                {{ group.display | translate }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div class="p-2 col-lg-2 col-md-2 col-sm-4 align-items-end">
          <mat-label>{{ 'accountStatReport.businessGroup' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <mat-select formControlName="businessGroup">
              <mat-option *ngFor="let bGroup of businessGroups" [value]="bGroup.value">
                {{ bGroup.display | translate }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div class="p-2 col-lg-2 col-md-2 col-sm-4 align-items-end">
          <mat-label>{{ 'accountStatReport.accountType' | translate }}e</mat-label>

          <mat-form-field class="w-100">
            <mat-select formControlName="accountType">
              <mat-option *ngFor="let actType of accountTypes" [value]="actType.value">
                {{ actType.display | translate }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div class="p-2 col-lg-3 col-md-3 col-sm-4 align-items-end"></div>
      </div>
      <div class="row no-gutters">
        <div class="p-2 col-lg-3 col-md-3 col-sm-12">
          <mat-radio-group aria-label="Select an option" formControlName="reportType">
            <mat-radio-button color="primary" value="PDF">PDF</mat-radio-button>
            <mat-radio-button color="primary" value="XLS">XLS</mat-radio-button>
            <mat-radio-button color="primary" value="CSV">CSV</mat-radio-button>
          </mat-radio-group>
        </div>
      </div>
      <ng-container>
        <button (click)="getReport($event); (false)" mat-stroked-button color="primary">
          {{ 'searchPanel.searchString' | translate }}
        </button>
        <button class="m-l-10" (click)="clearFilters($event); (false)" mat-stroked-button>
          {{ 'searchPanel.clear' | translate }}
        </button>
      </ng-container>
    </form>
  </mat-card>
</ng-container>
