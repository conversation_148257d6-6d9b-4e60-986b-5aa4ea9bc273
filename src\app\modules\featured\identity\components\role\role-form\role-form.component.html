<ng-container *ngIf="!loading">
  <mat-card [ngClass]="{ readOnly: isViewMode }" appearance="outlined">
    <mat-card-title>{{ formTitle | translate }}</mat-card-title>
    <mat-tab-group #tabs mat-stretch-tabs="false" animationDuration="0ms">
      <mat-tab [label]="'Roles'">
        <ng-template mat-tab-label>
          {{ 'roles.roleTab' | translate }}
        </ng-template>
        <form [formGroup]="roleForm">
          <div class="row no-gutters">
            <div class="col-md-6 col-lg-6 col-sm-12 p-2">
              <mat-label>{{ 'roles.roleNameField' | translate }}</mat-label>
              <mat-form-field class="w-100" [subscriptSizing]="'fixed'">
                <input matInput formControlName="name" />
                <mat-error
                  *ngIf="
                    roleForm.controls['name'].hasError('required') &&
                    roleForm.controls['name'].touched
                  "
                  >{{ 'common.required' | translate }}</mat-error
                >
              </mat-form-field>
            </div>
            <div class="col-md-6 col-lg-6 col-sm-12 p-2">
              <mat-label>{{ 'roles.roleDesc' | translate }}</mat-label>
              <mat-form-field class="w-100" [subscriptSizing]="'fixed'">
                <textarea matInput formControlName="description"></textarea>
                <mat-error
                  *ngIf="
                    roleForm.controls['description'].hasError('required') &&
                    roleForm.controls['description'].touched
                  "
                  >{{ 'common.required' | translate }}</mat-error
                >
              </mat-form-field>
            </div>

            <!-- </mat-card-content> -->
          </div>
        </form>
      </mat-tab>
      <mat-tab [label]="'Permissions'">
        <ng-template mat-tab-label>
          {{ 'roles.permissionTab' | translate }}
        </ng-template>
        <app-permission-tabs
          #apppermissions
          [roleName]="roles?.name"
          [mode]="mode"
          [disabled]="roleForm.disabled"
          [adminPermissions]="adminPermissions"
          [userPermissions]="roles?.permissions"></app-permission-tabs>
        <!-- <app-permission
          #apppermissions
          [roleName]="roles?.name"
          [mode]="mode"
          [allowedPermissions]="roles?.permissions"></app-permission> -->
      </mat-tab>
    </mat-tab-group>
  </mat-card>

  <div class="text-center">
    <ng-container *ngIf="isViewMode">
      <button class="m-l-10" [routerLink]="['../../']" mat-stroked-button color="primary">
        {{ 'common.cancel' | translate }}
      </button>
    </ng-container>
    <ng-container *ngIf="!isViewMode">
      <button class="m-l-10" (click)="onSubmit($event)" mat-stroked-button color="primary">
        {{ 'common.submit' | translate }}
      </button>
      <button class="m-l-10" [routerLink]="['../../']" mat-stroked-button color="warn">
        {{ 'common.cancel' | translate }}
      </button>
    </ng-container>
  </div>
</ng-container>
