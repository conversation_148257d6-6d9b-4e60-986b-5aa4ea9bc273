import { Injectable } from '@angular/core';
import { MatDialogConfig } from '@angular/material/dialog';
import { MultilingualService } from './multilingual.service';

@Injectable({
  providedIn: 'root',
})
export class DialogConfigService {
  private defaultConfig: MatDialogConfig = {
    minWidth: '600px',
    panelClass: 'green_theme',
    hasBackdrop: true,
    disableClose: false,
    autoFocus: false,
  };

  constructor(private multilingualService: MultilingualService) {}

  getDefaultConfig(): MatDialogConfig {
    const config = { ...this.defaultConfig };
    // Always set the direction based on current language
    config.direction = this.multilingualService.getOptions().dir;
    return config;
  }

  // Optional: Method to set or update the default configuration
  setDefaultConfig(config: MatDialogConfig): void {
    this.defaultConfig = { ...this.defaultConfig, ...config };
  }
}
