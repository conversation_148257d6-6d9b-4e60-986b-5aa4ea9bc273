import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { createParamsFromObject } from 'src/app/core/utils/date-utils';
import { environment } from 'src/environments/environment';
import { Category } from '../../modules/featured/catalog/models/category';
import { CategoryParams } from '../../modules/featured/catalog/models/categoryParams';

@Injectable({
  providedIn: 'root',
})
export class CategoryService {
  baseUrl = environment.apiUrl + 'catalog/categories';
  constructor(private http: HttpClient) {}

  getAllCategories(categoryParams: CategoryParams): Observable<Category[]> {
    const params = createParamsFromObject(categoryParams);
    return this.http.get<Category[]>(this.baseUrl, { params: params });
  }

  getCategoryById(categoryId: string): Observable<Category> {
    const params = createParamsFromObject(new CategoryParams());
    return this.http.get<Category>(this.baseUrl + '/' + categoryId, { params: params });
  }

  createCategory(category: Category): Observable<any> {
    const params = createParamsFromObject(new CategoryParams());
    return this.http.post(this.baseUrl, category, { params: params });
  }

  updateCategory(category: Category, categoryId: string): Observable<any> {
    const params = createParamsFromObject(new CategoryParams());
    return this.http.put(this.baseUrl + '/' + categoryId, category, { params: params });
  }

  deleteCategory(categoryId: string): Observable<any> {
    const params = createParamsFromObject(new CategoryParams());
    return this.http.delete(this.baseUrl + '/' + categoryId, { params: params });
  }

  importData(formData: FormData) {
    const params = new HttpParams();
    return this.http
      .post(this.baseUrl + '/import', formData, {
        withCredentials: true,
        responseType: 'arraybuffer',
        params: params,
      })
      .pipe(
        tap((result: ArrayBuffer) => {
          const fileURL = URL.createObjectURL(new Blob([result]));
          const link = document.createElement('a');
          link.href = fileURL;
          link.setAttribute('download', 'Category_List.xls');
          document.body.appendChild(link);
          link.click();
        })
      );
  }
}
