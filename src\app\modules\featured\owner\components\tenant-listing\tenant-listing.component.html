<mat-card appearance="outlined">
  <mat-card-content>
    <!-- action bar -->
    <app-create-action [label]="'tenants.tenantCreate' | translate"></app-create-action>
    <!-- action bar -->

    <ng-template mat-tab-label>{{ 'tenants.tenantLists' | translate }}</ng-template>

    <!-- search field -->
    <div class="row no-gutters">
      <div class="col-md-6 col-lg-6 col-sm-12 d-flex">
        <mat-form-field class="w-100">
          <input
            #searchInput
            (keyup)="applyFilter($event.target.value)"
            matInput
            autocomplete="off" />
          <i-tabler class="icon-16" matPrefix name="search"></i-tabler>
          <a
            class="cursor-pointer"
            *ngIf="searchInput?.value"
            (click)="clearSearchInput()"
            matSuffix>
            <i-tabler class="icon-16 error" name="X"></i-tabler>
          </a>
        </mat-form-field>
      </div>
    </div>
    <!-- search field -->
    <mat-card-title class="m-t-10">{{ 'tenants.tenantLists' | translate }}</mat-card-title>
    <div class="table-responsive">
      <table class="w-100" [dataSource]="dataSource" mat-table matSort>
        <ng-container matColumnDef="tenantId">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'tenants.id' | translate }}
          </th>
          <td class="f-s-14" *matCellDef="let element" [data]="element.tenantId" mat-cell appHyphen>
            {{ element.tenantId }}
          </td>
        </ng-container>
        <ng-container matColumnDef="tenantName">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'tenants.name' | translate }}
          </th>
          <td
            class="f-s-14"
            *matCellDef="let element"
            [data]="element.tenantName"
            mat-cell
            appHyphen>
            {{ element.tenantName }}
          </td>
        </ng-container>
        <ng-container matColumnDef="commercialRegistrationNo">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'tenants.regNo' | translate }}
          </th>
          <td
            class="f-s-14"
            *matCellDef="let element"
            [data]="element.commercialRegistrationNo"
            mat-cell
            appHyphen>
            {{ element.commercialRegistrationNo }}
          </td>
        </ng-container>
        <ng-container matColumnDef="vatNumber">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'tenants.vatNo' | translate }}
          </th>
          <td
            class="f-s-14"
            *matCellDef="let element"
            [data]="element.vatNumber"
            mat-cell
            appHyphen>
            {{ element.vatNumber }}
          </td>
        </ng-container>
        <ng-container matColumnDef="emailId">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'tenants.email' | translate }}
          </th>
          <td class="f-s-14" *matCellDef="let element" [data]="element.emailId" mat-cell appHyphen>
            {{ element.emailId }}
          </td>
        </ng-container>
        <ng-container matColumnDef="phoneNumber">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'common.field.phone' | translate }}
          </th>
          <td
            class="f-s-14"
            *matCellDef="let element"
            [data]="element.phoneNumber"
            mat-cell
            appHyphen>
            {{ element.phoneNumber }}
          </td>
        </ng-container>
        <ng-container matColumnDef="action">
          <th *matHeaderCellDef mat-header-cell>
            {{ 'common.action' | translate }}
          </th>
          <td class="f-s-14" *matCellDef="let element" mat-cell>
            <button
              class="d-flex justify-content-center"
              [matMenuTriggerFor]="menu1"
              mat-icon-button>
              <i-tabler class="icon-20 d-flex" name="dots-vertical"></i-tabler>
            </button>
            <mat-menu class="cardWithShadow" #menu1="matMenu">
              <button [routerLink]="['view', element.id]" mat-menu-item>
                <div class="d-flex align-items-center">
                  <i-tabler class="icon-16 m-r-4" name="eye"></i-tabler>
                  <span>{{ 'common.viewAction' | translate }}</span>
                </div>
              </button>
              <button [routerLink]="['edit', element.id]" mat-menu-item>
                <div class="d-flex align-items-center">
                  <i-tabler class="icon-16 m-r-4" name="edit"></i-tabler>
                  <span>{{ 'common.editAction' | translate }}</span>
                </div>
              </button>
              <button [routerLink]="['zatca', element.id]" mat-menu-item>
                <div class="d-flex align-items-center">
                  <i-tabler class="icon-16 m-r-4" name="edit"></i-tabler>
                  <span>Zatca Registration</span>
                </div>
              </button>
            </mat-menu>
          </td>
        </ng-container>
        <tr class="mat-row" *matNoDataRow>
          <td class="f-s-14" class="text-center" [attr.colspan]="displayedColumns.length">
            {{ 'common.noDataFound' | translate }}
          </td>
        </tr>
        <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
        <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
      </table>
    </div>
    <mat-paginator
      *ngIf="dataSource?.filteredData?.length > 0"
      [length]="dataSource.filteredData.length"
      [pageIndex]="0"
      [pageSize]="10"
      [pageSizeOptions]="[5, 10, 20]"></mat-paginator> </mat-card-content
></mat-card>
