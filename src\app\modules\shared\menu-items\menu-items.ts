import { Injectable } from '@angular/core';

export interface BadgeItem {
  type: string;
  value: string;
}
export interface Saperator {
  name: string;
  type?: string;
}
export interface SubChildren {
  state: string;
  name: string;
  type?: string;
}
export interface ChildrenItems {
  state: string;
  name: string;
  type?: string;
  child?: SubChildren[];
  permissions?: string[];
}

export interface Menu {
  state: string;
  name: string;
  type: string;
  icon: string;
  badge?: BadgeItem[];
  saperator?: Saperator[];
  children?: ChildrenItems[];
  permissions?: string[];
  parentQualifier?: string[];
}

const MENUITEMS = [
  {
    state: '',
    name: 'Inventory Management',
    type: 'saperator',
    icon: 'av_timer',
    permissions: ['InventoryManagement', 'AllPermissions'],
  },
  {
    state: 'inventory',
    name: 'Inventory',
    type: 'sub',
    icon: 'local_mall',
    permissions: ['InventoryManagement', 'AllPermissions'],
    children: [
      {
        state: 'products',
        name: 'Products',
        type: 'link',
        icon: 'price_change',
        permissions: ['Product', 'AllPermissions'],
      },
      {
        state: 'units',
        name: 'Units',
        type: 'link',
        icon: 'price_change',
        permissions: ['Units', 'AllPermissions'],
      },
      {
        state: 'category',
        name: 'Categories',
        type: 'link',
        icon: 'price_change',
        permissions: ['Categories', 'AllPermissions'],
      },
      {
        state: 'adjustments',
        name: 'Open Quantity Adjustments',
        type: 'link',
        icon: 'price_change',
        permissions: ['OpenQtyAdjustments.Create', 'AllPermissions'],
      },
      {
        state: 'stockadjustment',
        name: 'Stock Taking',
        type: 'link',
        icon: 'price_change',
        permissions: ['Adjustments.View', 'AllPermissions'],
      },
      {
        state: 'priceupdate',
        name: 'Price Adjustments',
        type: 'link',
        icon: 'price_change',
        permissions: ['Adjustments.PriceUpdate', 'AllPermissions'],
      },
      // {
      //   state: 'stocks',
      //   name: 'Stock Management',
      //   type: 'subchild',
      //   permissions: ['StockManagement', 'AllPermissions'],
      //   subchildren: [
      //     {
      //       state: 'adjustments',
      //       name: 'Open Quantity Adjustments',
      //       type: 'link',
      //       icon: 'price_change',
      //       permissions: ['OpenQtyAdjustments.Create', 'AllPermissions'],
      //     },
      //     {
      //       state: 'stockadjustment',
      //       name: 'Stock Taking',
      //       type: 'link',
      //       icon: 'price_change',
      //       permissions: ['Adjustments.View', 'AllPermissions'],
      //     },
      //     {
      //       state: 'priceupdate',
      //       name: 'Price Adjustments',
      //       type: 'link',
      //       icon: 'price_change',
      //       permissions: ['Adjustments.PriceUpdate', 'AllPermissions'],
      //     },
      //   ],
      // },
    ],
  },
  {
    state: '',
    name: 'Accounts Management',
    type: 'saperator',
    icon: 'av_timer',
    permissions: ['AccountsManagement', 'AllPermissions'],
  },
  {
    state: 'accounts',
    name: 'Accounts',
    type: 'sub',
    icon: 'price_change',
    permissions: ['AccountsManagement', 'AllPermissions'],
    children: [
      {
        state: 'chartOfAccounts',
        name: 'Chart of Accounts',
        type: 'link',
        icon: 'price_change',
        permissions: ['ChartOfAccounts', 'AllPermissions'],
      },
      {
        state: 'accountsReports',
        name: 'Accounts Reports',
        type: 'subchild',
        icon: 'price_change',
        permissions: ['ChartOfAccounts', 'AllPermissions'],
        subchildren: [
          {
            state: 'accountsList',
            name: 'List Of Accounts',
            type: 'link',
            icon: 'price_change',
            permissions: ['ChartOfAccounts', 'AllPermissions'],
          },
          {
            state: 'accountsStatement',
            name: 'Statement Of Accounts',
            type: 'link',
            icon: 'price_change',
            permissions: ['ChartOfAccounts', 'AllPermissions'],
          },
        ],
      },
      {
        state: 'accountSetup',
        name: 'Account Setup',
        type: 'link',
        icon: 'price_change',
        permissions: ['ChartOfAccounts', 'AllPermissions'],
      },
    ],
  },
  {
    state: '',
    name: 'Reports Management',
    type: 'saperator',
    icon: 'av_timer',
    permissions: ['ReportsManagement', 'AllPermissions'],
  },
  {
    state: 'reports',
    name: 'Reports',
    type: 'sub',
    icon: 'price_change',
    permissions: ['ReportsManagement', 'AllPermissions'],
    children: [
      {
        state: 'priceReports',
        name: 'Item Price Reports',
        type: 'link',
        icon: 'price_change',
        permissions: ['PriceReports', 'AllPermissions'],
      },
      {
        state: 'stockReports',
        name: 'Stock-value Reports',
        type: 'link',
        icon: 'price_change',
        permissions: ['StockValueReports', 'AllPermissions'],
      },
    ],
  },
  {
    state: '',
    name: 'Enterprise Management',
    type: 'saperator',
    icon: 'av_timer',
    permissions: ['EnterpriseManagement', 'AllPermissions'],
  },
  {
    state: 'enterprise',
    name: 'Enterprise',
    type: 'sub',
    icon: 'settings',
    permissions: ['EnterpriseManagement', 'AllPermissions'],
    children: [
      {
        state: 'company',
        name: 'Enterprise Profile',
        type: 'link',
        icon: 'price_change',
        permissions: ['Company.Update', 'AllPermissions'],
      },
      {
        state: 'branches',
        name: 'Branches',
        type: 'link',
        icon: 'price_change',
        permissions: ['Branch', 'AllPermissions'],
      },
      {
        state: 'warehouses',
        name: 'Warehouses',
        type: 'link',
        icon: 'price_change',
        permissions: ['WareHouse', 'AllPermissions'],
      },
    ],
  },
  {
    state: '',
    name: 'Utility Management',
    type: 'saperator',
    icon: 'av_timer',
    permissions: ['ReportsManagement', 'AllPermissions'],
  },
  {
    state: 'utility',
    name: 'Utility',
    type: 'sub',
    icon: 'price_change',
    permissions: ['ReportsManagement', 'AllPermissions'],
    children: [
      {
        state: 'dataImport',
        name: 'Data Import',
        type: 'link',
        icon: 'price_change',
        permissions: ['PriceReports', 'AllPermissions'],
      },
      {
        state: 'branchSetup',
        name: 'Branch Setup',
        type: 'link',
        icon: 'price_change',
        permissions: ['StockValueReports', 'AllPermissions'],
      },
    ],
  },
  {
    state: '',
    name: 'Trade Management',
    type: 'saperator',
    icon: 'av_timer',
    permissions: ['TradeManagement', 'AllPermissions'],
  },
  {
    state: 'trading',
    name: 'Tradings',
    type: 'sub',
    icon: 'price_change',
    permissions: ['TradeManagement', 'AllPermissions'],
    children: [
      {
        state: 'sales',
        name: 'Sales',
        type: 'link',
        icon: 'price_change',
        permissions: ['Sales', 'AllPermissions'],
      },
      {
        state: 'customers',
        name: 'Customer',
        type: 'link',
        icon: 'price_change',
        permissions: ['Customer', 'AllPermissions'],
      },
      {
        state: 'suppliers',
        name: 'Supplier',
        type: 'link',
        icon: 'price_change',
        permissions: ['Supplier', 'AllPermissions'],
      },
      {
        state: 'distributors',
        name: 'Distributors',
        type: 'link',
        icon: 'price_change',
        permissions: ['Distributor', 'AllPermissions'],
      },
    ],
  },
];

@Injectable()
export class MenuItems {
  getMenuitem(): Menu[] {
    return MENUITEMS;
  }
}
