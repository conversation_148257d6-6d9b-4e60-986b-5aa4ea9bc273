import { SelectionModel } from '@angular/cdk/collections';
import { Component, ElementRef, ViewChild } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { Subject } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { CustomerService } from 'src/app/core/api/trading/customer.service';
import { ICustomer, ICustomerSearch } from 'src/app/core/interfaces/customer';
import { CustomerParams } from 'src/app/core/models/params/customerParams';

@Component({
  selector: 'app-customer-get-all',
  templateUrl: './customer-get-all.component.html',
  styleUrls: ['./customer-get-all.component.scss'],
})
export class CustomerGetAllComponent {
  @ViewChild(MatSort) sort: MatSort;
  @ViewChild('filter') filter: ElementRef;
  @ViewChild('searchInput') searchInput: ElementRef;
  constructor(
    private customerService: CustomerService,
    public dialogRef: MatDialogRef<CustomerGetAllComponent>
  ) {
    this.searchSubject.pipe(debounceTime(300)).subscribe(searchTerm => {
      this.applyFilter(searchTerm);
    });
  }
  displayedSearchColumns: string[] = [
    'nameArabic',
    'nameEnglish',
    'vatNumber',
    'accountNumber',
    'phoneNumber',
    'emailId',
  ];
  public isLoading = false;
  public customers: ICustomer[] = [];
  public resultNotFound = false;
  dataSource: MatTableDataSource<ICustomer>;
  selection = new SelectionModel<ICustomer>();
  private searchSubject = new Subject<string>();

  ngOnInit(): void {
    this.getAllProducts();
  }

  getAllProducts() {
    this.isLoading = true;
    const params = new CustomerParams();
    params.pageSize = *********;
    this.customerService
      .getCustomers(params)
      .subscribe(
        (searchResult: ICustomerSearch) => (
          (this.isLoading = false),
          (this.customers = searchResult.customers),
          (this.resultNotFound = this.customers?.length > 0 ? false : true),
          (this.dataSource = new MatTableDataSource<ICustomer>(this.customers)),
          (this.selection = new SelectionModel<ICustomer>(true))
        )
      );
  }

  applyFilter(filterValue: string): void {
    filterValue = filterValue.trim();
    filterValue = filterValue.toLowerCase();
    this.dataSource.filter = filterValue;
    if (this.dataSource.filteredData.length > 0) {
      this.selection.clear(); // Clear previous selection
      this.selection.select(this.dataSource.filteredData[0]); // Select the first record
    }
  }

  clearSearchInput() {
    this.searchInput.nativeElement.value = '';
    this.applyFilter('');
  }

  onSearchInput(value: string): void {
    this.searchSubject.next(value);
  }

  selectRow(customer: ICustomer) {
    console.log('selected customer', customer);
    this.dialogRef.close(customer);
  }
}
