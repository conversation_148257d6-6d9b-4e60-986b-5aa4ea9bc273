<mat-card appearance="outlined">
  <mat-tab-group #tabs [dynamicHeight]="true" mat-stretch-tabs="false" animationDuration="0ms">
    <form [formGroup]="profileForm">
      <mat-tab>
        <ng-template mat-tab-label>{{ 'profile.personalDetails' | translate }}</ng-template>
        <div class="row no-gutters">
          <div class="col-md-6 col-sm-6 p-2">
            <mat-label>{{ 'profile.firstName' | translate }}</mat-label>

            <mat-form-field class="w-100">
              <input [formControl]="profileForm.controls['fname']" matInput />
            </mat-form-field>
            <mat-error
              *ngIf="
                profileForm.controls['fname'].hasError('required') &&
                profileForm.controls['fname'].touched
              ">
              {{ 'common.required' | translate }}</mat-error
            >
            <mat-error
              *ngIf="
                profileForm.controls['fname'].hasError('minlength') &&
                profileForm.controls['fname'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
            <mat-error
              *ngIf="
                profileForm.controls['fname'].hasError('maxlength') &&
                profileForm.controls['fname'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </div>

          <div class="col-md-6 col-sm-6 p-2">
            <mat-label>{{ 'profile.lastName' | translate }}</mat-label>

            <mat-form-field class="w-100">
              <input [formControl]="profileForm.controls['lname']" matInput />
            </mat-form-field>
            <mat-error
              *ngIf="
                profileForm.controls['lname'].hasError('required') &&
                profileForm.controls['lname'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
            <mat-error
              *ngIf="
                profileForm.controls['lname'].hasError('minlength') &&
                profileForm.controls['lname'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
            <mat-error
              *ngIf="
                profileForm.controls['lname'].hasError('maxlength') &&
                profileForm.controls['lname'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </div>

          <div class="col-md-6 col-sm-6 p-2">
            <mat-label>{{ 'profile.email' | translate }}</mat-label>

            <mat-form-field class="w-100">
              <input [formControl]="profileForm.controls['email']" matInput type="email" />
            </mat-form-field>
            <mat-error
              *ngIf="
                profileForm.controls['email'].hasError('required') &&
                profileForm.controls['email'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
            <mat-error
              *ngIf="
                profileForm.controls['email'].errors?.email && profileForm.controls['email'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </div>

          <div class="col-md-6 col-sm-6 p-2">
            <mat-label>{{ 'profile.birthdate' | translate }}</mat-label>

            <mat-form-field class="w-100">
              <input
                [matDatepicker]="picker"
                [formControl]="profileForm.controls['date']"
                matInput />
              <mat-datepicker-toggle [for]="picker" matSuffix></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
            <mat-error
              *ngIf="
                profileForm.controls['date'].hasError('required') &&
                profileForm.controls['date'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
            <mat-error
              *ngIf="
                profileForm.controls['date'].errors?.date && profileForm.controls['date'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </div>

          <div class="col-md-6 col-sm-6 p-2">
            <mat-label>{{ 'profile.phone' | translate }}</mat-label>

            <mat-form-field class="w-100">
              <input [formControl]="profileForm.controls['phone']" matInput type="text" />
            </mat-form-field>
            <mat-error
              *ngIf="
                profileForm.controls['phone'].hasError('required') &&
                profileForm.controls['phone'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
            <mat-error
              *ngIf="
                profileForm.controls['phone'].errors?.phone && profileForm.controls['phone'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </div>

          <div class="col-md-6 col-sm-6 p-2">
            <mat-label>{{ 'profile.language' | translate }}</mat-label>

            <mat-form-field class="w-100">
              <mat-select [formControl]="profileForm.controls['language']">
                <mat-option *ngFor="let language of languages" [value]="language.value">
                  {{ language?.viewValue }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
        <div class="text-center">
          <button
            (click)="profileFormSubmitted($event)"
            mat-stroked-button
            color="primary"
            type="submit">
            {{ 'common.submit' | translate }}
          </button>
        </div>
      </mat-tab>
    </form>
    <form [formGroup]="securityForm">
      <mat-tab>
        <ng-template mat-tab-label>{{ 'profile.security' | translate }}</ng-template>
        <div class="row no-gutters">
          <div class="col-md-6 col-sm-6 p-2">
            <mat-label>{{ 'profile.password' | translate }}</mat-label>

            <mat-form-field class="w-100">
              <input
                [type]="passwordhide ? 'password' : 'text'"
                [formControl]="securityForm.controls['password']"
                matInput
                type="password" />
              <i-tabler
                class="icon-16 m-r-4 op-4"
                (click)="passwordhide = !passwordhide; (false)"
                matSuffix
                name="{{ passwordhide ? 'eye-off' : 'eye' }}"></i-tabler>
            </mat-form-field>
            <mat-error
              *ngIf="
                securityForm.controls['password'].hasError('required') &&
                securityForm.controls['password'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </div>
          <div class="col-md-6 col-sm-6 p-2">
            <mat-label>{{ 'profile.confirmPassword' | translate }}</mat-label>

            <mat-form-field class="w-100">
              <input
                [type]="confirmPasswordhide ? 'password' : 'text'"
                [formControl]="securityForm.controls['confirmPassword']"
                matInput
                type="password" />
              <i-tabler
                class="icon-16 m-r-4 op-4"
                (click)="confirmPasswordhide = !confirmPasswordhide; (false)"
                matSuffix
                name="{{ confirmPasswordhide ? 'eye-off' : 'eye' }}"></i-tabler>
            </mat-form-field>
            <mat-error
              *ngIf="
                securityForm.controls['confirmPassword'].hasError('required') &&
                securityForm.controls['confirmPassword'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
            <mat-error *ngIf="securityForm.controls['confirmPassword'].errors?.equalTo">{{
              'profile.passwordsDoNotMatch' | translate
            }}</mat-error>
          </div>
        </div>
        <div class="text-center">
          <button
            [disabled]="!securityForm.valid"
            (click)="securityFormSubmitted($event)"
            mat-stroked-button
            color="primary"
            type="submit">
            {{ 'common.submit' | translate }}
          </button>
        </div>
      </mat-tab>
    </form>
  </mat-tab-group>
</mat-card>
