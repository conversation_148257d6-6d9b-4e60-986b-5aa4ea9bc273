import { IInventory } from 'src/app/modules/featured/catalog/models/product';
import { ICustomer } from 'src/app/core/interfaces/customer';
import { IPaymentDetails } from './payment';

export interface ISaleDetails {
  documentUuid?: string;
  issueDate?: string;
  orderNumber?: string | null;
  orderDate?: string | null;
  invoiceStatus?: string | null;
  costCentreId?: number | null;
  distributorAccountId?: number | null;
  invoiceDiscount?: number | null;
  notes?: string;
  items: ISalesItem[];
  existingClient?: boolean;
  customer: ICustomer | null;
  payments: IPaymentDetails;
  referenceDocumentId?: number | null;
  referenceDocumentDate?: string | null;
  transactionType?: SaletransactionTypes;
  provisionList?: Provision[] | null;
  id?: number;
  documentNumber?: string;
  posTerminalId?: null;
  createdAt?: Date;
  referenceDocumentNumber?: string;
  isPosted?: null;
  creationType?: number;
  sequenceNumber?: number;
  taxType?: number;
  stage?: string;
  certificateId?: number;
  taxableAmountsByCategories?: null;
  originatingSystemId?: null;
  userId?: null;
  issuedBy?: null;
  issuedAt?: Date;
}

export interface SalesCreateResponse {
  documentId?: number;
  documentUuid?: string;
  documentNumber?: string;
  createdAt?: string;
  sequenceNumber?: number;
}

export interface SalesIntegratedCreateResponse {
  documentNumber?: string;
  documentUuid?: string;
  documentId?: number;
  documentType?: string;
  status?: string;
  zatcaExceptionResponse?: ZatcaExceptionResponse;
}

export interface ZatcaExceptionResponse {
  validationResults: ValidationResults;
  reportingStatus: string;
  clearanceStatus: string;
}

export interface ZatcaModalData {
  zatcaReportingStatus: ZatcaExceptionResponse;
  viewOnly: boolean;
}

export interface ValidationResults {
  infoMessages: unknown[];
  warningMessages: unknown[];
  errorMessages: unknown[];
  status: string;
}

export interface Provision {
  name: string;
  clause: string;
}

export enum SaletransactionTypes {
  quotation = 'quotation',
  sales = 'sales',
  partialCreditNote = 'partialCreditNote',
  fullCreditNote = 'fullCreditNote',
  debitNote = 'debitNote',
  allNotes = 'allNotes',
  purchase = 'Purchase',
}

export enum salesNotesType {
  fullCreditNote = 'fullCreditNote',
  partialCreditNote = 'partialCreditNote',
  debitNote = 'debitNote',
}

export interface ISalesItem {
  id?: number;
  itemId?: number;
  itemCode?: string;
  quantity?: number;
  itemUnitId?: number;
  price?: number;
  subtotal?: number;
  vat?: number;
  product?: IInventory;
  discount?: number;
  isGeneralDscntMethod?: boolean;
  priceType?: number;
  warehouseName?: string;
  itemName?: string;
  unitName?: string;
  notes?: null;
  subTotalVat?: number;
  warehouseId?: number;
  vatAmount?: number;
  // for notes
  returnableQty?: number;
  transactionItemId?: number;
  invoiceLineNumber?: number;
  qtyToAdd?: null;
  cogs?: number;
  isFree?: boolean;
  generalDscntMethod?: boolean;
}

export interface ISalesResponse {
  searchTimeStamp: Date;
  totalRecordsCount: number;
  totalPages: number;
  morePages: boolean;
  transactions: IInvoice[];
}

export interface IInvoice {
  id: number;
  branchId: number;
  creationType: string | number;
  issueDate: Date | string;
  orderDate: Date | string;
  orderId: number;
  referenceDocumentId: number;
  paymentDueDate: Date | string;
  status: string;
  invoiceTotal: number;
  totalVat: number;
  phoneNumber: number;
  vatNumber: number;
  returnStatus?: string;
  costCentreId: number;
  documentNumber: string;
  unpaidAmount: null;
  isPosted: boolean;
  distributorAccountId: number;
  referenceDocumentNumber: null;
  referenceDocumentDate: Date;
  settlementStatus: null;
  paymentMethod: null;
  transactionType: salesNotesType;
  account?: IAccountDetails;
  documentUuid?: string;
  stage?: string;
  documentId?: number;
}

export interface IAccountDetails {
  accountId: number;
  accountNumber: number;
  nameArabic: string;
  nameEnglish: string;
}

// for full credit note logic START
export interface IPostSalesNotesPayments {
  notesCommentsDate: IPostSalesNotesEntry;
  notesPaymentDetails: IPaymentDetails;
}

export interface IPostSalesNotesEntry {
  creditNoteDate: string;
  creditNoteText: null;
}

// for full credit note logic END

export interface customePayment {
  bankType?: boolean;
  cashType?: boolean;
  cardType?: boolean;
  creditType?: boolean;
}

// vouchers START
export interface PaymentVoucher {
  voucherDate: string;
  paymentAccountNumber: number | null;
  voucherType: voucherType;
  items: VoucherItems[];
  payments: IPaymentDetails;
}

export interface VoucherItems {
  id: string | null;
  invoiceId: string;
  invoiceDate: string;
  invoiceNumber: string;
  paidAmount: number;
  account?: IAccountDetails;
  distributorAccountId?: number | null;
  costCentreId?: number | null;
  notes?: string | null;
  issueType: issueType;
}

export enum voucherType {
  SalesReceivable = 'SalesReceivable',
  PurchasePayable = 'PurchasePayable',
  GeneralReceivable = 'GeneralReceivable',
  GeneralPayable = 'GeneralPayable',
}

export enum issueType {
  General = 'General',
  VatExpenses = 'VatExpenses',
  VatReconciliation = 'VatReconciliation',
}

export interface voucherPayments {
  searchTimestamp: Date;
  totalRecordsCount: number;
  totalPages: number;
  morePages: boolean;
  vouchers: Voucher[];
}

export interface Voucher {
  paymentId: number;
  voucherNumber: string;
  voucherDate: Date;
  voucherAmount: number;
  paymentMethod: number;
  notes: string | null;
  voucherType: string;
}

export interface DeleteItemRows {
  itemCode: number;
  warehouseId: number;
  unitName: string;
}

// vouchers END
