<ng-container>
  <!-- action bar -->
  <app-create-action
    *appHasPermission="['Categories.Create', 'AllPermissions']"
    [label]="'productCategory.createCatgory' | translate"></app-create-action>
  <!-- action bar -->
  <!----------------------------------- mat table content --------------------------------------->

  <mat-card appearance="outlined">
    <mat-card-content>
      <!-- search field -->
      <div class="row no-gutters">
        <div class="col-md-6 col-lg-6 col-sm-12 d-flex">
          <mat-form-field class="w-100">
            <input
              #searchInput
              (keyup)="applyFilter($event.target.value)"
              matInput
              autocomplete="off" />
            <i-tabler class="icon-16" matPrefix name="search"></i-tabler>
            <a
              class="cursor-pointer"
              *ngIf="searchInput?.value"
              (click)="clearSearchInput()"
              matSuffix>
              <i-tabler class="icon-16 error" name="X"></i-tabler>
            </a>
          </mat-form-field>
        </div>
      </div>
      <!-- search field -->
      <mat-card-title class="m-t-10">{{ 'productCategory.listings' | translate }}</mat-card-title>
      <div class="table-responsive">
        <table [dataSource]="tableData" mat-table matSort>
          <!-- Name Column -->
          <ng-container matColumnDef="nameArabic">
            <th *matHeaderCellDef mat-header-cell mat-sort-header>
              {{ 'productCategory.nameArabic' | translate }}
            </th>
            <td *matCellDef="let element" mat-cell>
              {{ element.nameArabic }}
            </td>
          </ng-container>
          <!-- Name Column -->
          <ng-container matColumnDef="nameEnglish">
            <th *matHeaderCellDef mat-header-cell mat-sort-header>
              {{ 'productCategory.nameEnglish' | translate }}
            </th>
            <td *matCellDef="let element" mat-cell>
              {{ element.nameEnglish }}
            </td>
          </ng-container>
          <!-- User Actions Column -->
          <ng-container matColumnDef="action">
            <th *matHeaderCellDef mat-header-cell>
              {{ 'productCategory.action' | translate }}
            </th>
            <td *matCellDef="let element" mat-cell>
              <a
                class="cursor-pointer"
                *appHasPermission="['Categories.Update', 'AllPermissions']"
                [routerLink]="['edit', element.categoryId]"
                ><i-tabler class="icon-16" name="edit"></i-tabler>
              </a>
              <a
                class="cursor-pointer"
                *appHasPermission="['Categories.Delete', 'AllPermissions']"
                (click)="deleteCategory(element.categoryId); (false)"
                ><i-tabler class="icon-16" name="trash"></i-tabler>
              </a>
            </td>
          </ng-container>
          <!----------------------------------- not data found  ------------------------------------------>
          <tr class="mat-row" *matNoDataRow>
            <td class="text-center text-info" [attr.colspan]="displayedColumns.length">
              {{ 'common.noDataFound' | translate }}
            </td>
          </tr>
          <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
          <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
        </table>
      </div>
      <mat-paginator
        *ngIf="tableData?.filteredData?.length > 0"
        [length]="tableData.filteredData.length"
        [pageIndex]="0"
        [pageSize]="25"
        [pageSizeOptions]="[25, 50]"></mat-paginator>
    </mat-card-content>
  </mat-card>
</ng-container>
