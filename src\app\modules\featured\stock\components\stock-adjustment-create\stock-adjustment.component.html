<!-- Filter Place Holders -->
<ng-container *ngIf="!isViewMode && !isEditMode">
  <form [formGroup]="filterForm" autocomplete="off">
    <mat-card>
      <div class="row">
        <div class="col-12">
          <div class="row no-gutters">
            <div class="p-2 col-md-5">
              <app-searchbox #searchBoxForm formControlName="searchBoxForm"></app-searchbox>
            </div>
            <div class="p-2 col-lg-2 col-md-2 col-sm-6 d-flex align-items-end">
              <div class="form-group">
                <mat-label>{{ 'openqty.category' | translate }}</mat-label>
                <mat-form-field class="w-100">
                  <mat-select #select multiple formControlName="categoryId">
                    <app-select-check-all [model]="filterForm.get('categoryId')" [values]="select">
                    </app-select-check-all>
                    <mat-option
                      *ngFor="let parentCategory of categoryList"
                      [value]="parentCategory.categoryId">
                      {{
                        parentCategory.parentCategoryId
                          ? getParentName(parentCategory.parentCategoryId) +
                            '/' +
                            (parentCategory | localized)
                          : (parentCategory | localized)
                      }}</mat-option
                    >
                  </mat-select>
                </mat-form-field>
              </div>
            </div>
            <div class="p-2 col-lg-2 col-md-2 col-sm-6 d-flex align-items-end">
              <div class="form-group">
                <mat-label>{{ 'openqty.warehouse' | translate }}</mat-label>
                <mat-form-field class="w-100">
                  <mat-select #wareHouse multiple formControlName="warehouseIds">
                    <app-select-check-all
                      [model]="filterForm.get('warehouseIds')"
                      [values]="wareHouse">
                    </app-select-check-all>
                    <mat-option
                      *ngFor="let wareHouse of warehouseList"
                      [value]="wareHouse.warehouseId">
                      {{ wareHouse | localized }}
                    </mat-option>
                  </mat-select>
                  <mat-error
                    *ngIf="
                      filterForm?.controls['warehouseIds']?.hasError('required') &&
                      filterForm?.controls['warehouseIds']?.touched
                    "
                    >{{ 'common.required' | translate }}</mat-error
                  >
                </mat-form-field>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <button
          (click)="getFilterData($event); searchBoxForm.markAllAsTouched(); (false)"
          mat-raised-button
          color="primary">
          {{ 'searchPanel.searchString' | translate }}
        </button>
        <button class="m-l-10" (click)="clearFilters($event); (false)" mat-raised-button>
          {{ 'searchPanel.clear' | translate }}
        </button>
      </div>
    </mat-card>
  </form>
</ng-container>
<!-- Filter Place Holders -->

<!-- Main Table Contents -->
<mat-card appearance="outlined">
  <mat-card-title>{{ 'common.field.accountDetails' | translate }}</mat-card-title>
  <form [formGroup]="adjustmentForm" autocomplete="off">
    <div class="table-responsive">
      <table
        class="w-100"
        [dataSource]="tableData"
        mat-table
        matSort
        formArrayName="InventoryOpeningRequest">
        <ng-container matColumnDef="action">
          <th class="font-medium" *matHeaderCellDef mat-header-cell>
            {{ 'createAdjsutments.action' | translate }}
          </th>
          <td *matCellDef="let element; let i = index; let last = last" mat-cell>
            <a class="cursor-pointer" (click)="deleteEntry(getActualIndex(i)); (false)"
              ><i-tabler class="icon-16" name="trash"></i-tabler>
            </a>
          </td>
        </ng-container>
        <ng-container matColumnDef="itemCode">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'createAdjsutments.itemCode' | translate }}
          </th>
          <td *matCellDef="let element; let i = index" [formGroupName]="getActualIndex(i)" mat-cell>
            {{ element.itemCode }}
          </td>
        </ng-container>
        <ng-container matColumnDef="actualQty">
          <th *matHeaderCellDef mat-header-cell>{{ 'createAdjsutments.actualqty' | translate }}</th>
          <ng-container *ngIf="isEditMode">
            <td
              *matCellDef="let element; let i = index"
              [formGroupName]="getActualIndex(i)"
              mat-cell>
              <mat-form-field class="w-100">
                <input
                  (change)="actualQuantityValueChange(getActualIndex(i))"
                  min="0"
                  type="number"
                  matInput
                  formControlName="actualQty"
                  placeholder="Actual Quantity" />
              </mat-form-field>
            </td>
          </ng-container>
          <ng-container *ngIf="isViewMode">
            <td
              *matCellDef="let element; let i = index"
              [formGroupName]="getActualIndex(i)"
              mat-cell>
              {{ element.actualQty }}
            </td>
          </ng-container>
        </ng-container>
        <!-- Cost Price -->
        <ng-container matColumnDef="itemNameArabic">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'createAdjsutments.nameArabic' | translate }}
          </th>
          <td *matCellDef="let element; let i = index" [formGroupName]="getActualIndex(i)" mat-cell>
            {{ element.itemNameArabic }}
          </td>
        </ng-container>
        <ng-container matColumnDef="itemNameEnglish">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'createAdjsutments.nameEnglish' | translate }}
          </th>
          <td *matCellDef="let element; let i = index" [formGroupName]="getActualIndex(i)" mat-cell>
            {{ element.itemNameEnglish }}
          </td>
        </ng-container>
        <!-- Purchase Price  -->
        <ng-container matColumnDef="unitName">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'createAdjsutments.unit' | translate }}
          </th>
          <td *matCellDef="let element; let i = index" [formGroupName]="getActualIndex(i)" mat-cell>
            {{ element.unitName }}
          </td>
        </ng-container>
        <!-- open Purchase Price -->
        <ng-container matColumnDef="currentQty">
          <th *matHeaderCellDef mat-header-cell>
            {{ 'createAdjsutments.OpenQty' | translate }}
          </th>
          <td *matCellDef="let element; let i = index" [formGroupName]="getActualIndex(i)" mat-cell>
            {{ element.currentQty }}
          </td>
        </ng-container>
        <ng-container matColumnDef="status">
          <th *matHeaderCellDef mat-header-cell>{{ 'createAdjsutments.status' | translate }}</th>
          <td *matCellDef="let element; let i = index" [formGroupName]="getActualIndex(i)" mat-cell>
            <a
              class="cursor-pointer"
              *ngIf="
                adjustmentForm.get('InventoryOpeningRequest')?.controls[getActualIndex(i)][
                  'controls'
                ]['status']?.value === 1
              "
              ><i-tabler class="text-primary icon-16" name="circle-arrow-up"></i-tabler>
            </a>
            <a
              class="cursor-pointer"
              *ngIf="
                adjustmentForm.get('InventoryOpeningRequest')?.controls[getActualIndex(i)][
                  'controls'
                ]['status']?.value === 2
              "
              ><i-tabler class="text-success icon-16" name="equal-double"></i-tabler>
            </a>
            <a
              class="cursor-pointer"
              *ngIf="
                adjustmentForm.get('InventoryOpeningRequest')?.controls[getActualIndex(i)][
                  'controls'
                ]['status']?.value === -1
              "
              ><i-tabler class="text-warning icon-16" name="circle-arrow-down"></i-tabler>
            </a>
          </td>
        </ng-container>
        <ng-container matColumnDef="remark">
          <th *matHeaderCellDef mat-header-cell>{{ 'createAdjsutments.remark' | translate }}</th>
          <ng-container *ngIf="isEditMode">
            <td
              *matCellDef="let element; let i = index"
              [formGroupName]="getActualIndex(i)"
              mat-cell>
              <mat-form-field class="w-100">
                <input type="text" matInput formControlName="remark" />
              </mat-form-field>
            </td>
          </ng-container>
          <ng-container *ngIf="isViewMode">
            <td
              *matCellDef="let element; let i = index"
              [formGroupName]="getActualIndex(i)"
              mat-cell>
              {{ element.remark }}
            </td>
          </ng-container>
        </ng-container>
        <ng-container matColumnDef="qtyDifference">
          <th *matHeaderCellDef mat-header-cell>
            {{ 'createAdjsutments.difference' | translate }}
          </th>
          <td *matCellDef="let element; let i = index" [formGroupName]="getActualIndex(i)" mat-cell>
            {{
              adjustmentForm.get('InventoryOpeningRequest')?.controls[getActualIndex(i)][
                'controls'
              ]['qtyDifference']?.value
            }}
          </td>
        </ng-container>
        <tr class="mat-row" *matNoDataRow>
          <td class="text-center" [attr.colspan]="displayedColumns.length">
            {{ 'common.noDataFound' | translate }}
          </td>
        </tr>
        <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
        <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
      </table>
    </div>
    <mat-paginator *ngIf="tableData?.filteredData?.length > 0" [pageSizeOptions]="[100, 125]">
    </mat-paginator>
  </form>
</mat-card>
<!-- Main Table Contents -->
<!------------------------------------ Action Buttons ------------------------------------------------->
<div class="text-center">
  <ng-container *ngIf="isEditMode">
    <ng-container class="m-b-10" *appHasPermission="['Adjustments.Update', 'AllPermissions']">
      <button
        class="m-l-10"
        *ngIf="!postSaved"
        (click)="onSubmit($event)"
        mat-stroked-button
        color="primary">
        {{ 'common.submit' | translate }}
      </button>
    </ng-container>
    <ng-container class="m-b-10" *appHasPermission="['Adjustments.Post', 'AllPermissions']">
      <button
        class="m-l-10"
        *ngIf="!postSaved"
        (click)="onPostClick($event); (false)"
        mat-stroked-button
        color="primary">
        {{ 'createAdjsutments.post' | translate }}
      </button>
    </ng-container>
    <button
      class="m-l-10"
      *ngIf="!postSaved"
      [routerLink]="['../../']"
      mat-stroked-button
      color="warn">
      {{ 'common.cancel' | translate }}
    </button>
    <button
      class="m-l-10"
      *ngIf="postSaved"
      [routerLink]="['../']"
      mat-stroked-button
      color="primary"
      color="primary">
      {{ 'common.cancel' | translate }}
    </button>
  </ng-container>
  <ng-container *ngIf="isViewMode">
    <button class="m-l-10" [routerLink]="['../../']" mat-button mat-stroked-button color="primary">
      {{ 'common.cancel' | translate }}
    </button>
  </ng-container>

  <ng-container *ngIf="!isEditMode && !isViewMode">
    <ng-container class="m-b-10" *appHasPermission="['Adjustments.Create', 'AllPermissions']">
      <button
        class="m-l-10"
        *ngIf="tableData?.filteredData?.length > 0"
        (click)="onSubmit($event)"
        mat-stroked-button
        color="primary">
        {{ 'common.submit' | translate }}
      </button>
    </ng-container>
    <button class="m-l-10" [routerLink]="['../']" mat-stroked-button color="warn">
      {{ 'common.cancel' | translate }}
    </button>
  </ng-container>
</div>
