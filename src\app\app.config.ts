export interface AppSettings {
  dir: 'ltr' | 'rtl';
  theme: string;
  sidenavOpened: boolean;
  sidenavCollapsed: boolean;
  boxed: boolean;
  horizontal: boolean;
  activeTheme: string;
  language: string;
  cardBorder: boolean;
  navPos: 'side' | 'top';
}

export const language_mapping = {
  default_language: 'en', // Use lowercase for consistency
  secondary_language: 'ar',
  en: {
    dir: 'ltr',
  },
  ar: {
    dir: 'rtl',
  },
} as const;

export const defaults: AppSettings = {
  dir: language_mapping[language_mapping.default_language].dir,
  theme: 'light',
  sidenavOpened: false,
  sidenavCollapsed: false,
  boxed: false,
  horizontal: true,
  cardBorder: true,
  activeTheme: 'green_theme',
  language: language_mapping.default_language,
  navPos: 'top',
};
