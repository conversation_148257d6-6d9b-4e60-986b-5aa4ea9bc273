<ng-container *ngIf="!loading">
  <form [formGroup]="productForm" [ngClass]="{ readOnly: isViewMode }" autocomplete="off">
    <mat-card appearance="outlined">
      <mat-card-title>{{ 'productBaicTab.productConfiguration' | translate }}</mat-card-title>
      <div class="row no-gutters">
        <!-- Item Code  -->
        <!-- Adding slash and hyphen -->
        <div class="p-2 col-md-2 col-lg-2 col-sm-12">
          <mat-label>{{ 'productBaicTab.itemCode' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input type="text" matInput formControlName="itemCode" />
            <mat-error
              *ngIf="
                productForm?.controls['itemCode'].hasError('required') &&
                productForm?.controls['itemCode'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </mat-form-field>
        </div>
        <!-- Product Name Arabic  -->
        <div class="p-2 col-md-5 col-lg-5 col-sm-12">
          <mat-label>{{ 'productBaicTab.nameArabic' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input type="text" maxlength="40" matInput formControlName="nameArabic" />
            <mat-error
              *ngIf="
                productForm?.controls['nameArabic'].hasError('required') &&
                productForm?.controls['nameArabic'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </mat-form-field>
        </div>
        <!-- Product Name English -->
        <div class="p-2 col-md-5 col-lg-5 col-sm-12">
          <mat-label>{{ 'productBaicTab.nameEnglish' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input type="text" maxlength="40" matInput formControlName="nameEnglish" />
            <mat-error
              *ngIf="
                productForm?.controls['nameEnglish'].hasError('required') &&
                productForm?.controls['nameEnglish'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </mat-form-field>
        </div>
      </div>
      <div class="row no-gutters">
        <!-- Business Locations -->
        <div class="p-2 col-md-3 col-lg-3 col-sm-12">
          <mat-label>{{ 'productBaicTab.warehouse' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <mat-select #warehouses formControlName="warehouseIds" multiple>
              <app-select-check-all [model]="productForm.get('warehouseIds')" [values]="warehouses">
              </app-select-check-all>
              <mat-option *ngFor="let warehouse of wareHouseList" [value]="warehouse.warehouseId"
                >{{ warehouse | localized }}
              </mat-option>
            </mat-select>
            <mat-error
              *ngIf="
                productForm?.controls['warehouseIds'].hasError('required') &&
                productForm?.controls['warehouseIds'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </mat-form-field>
        </div>
        <!-- Product Partnumber -->
        <div class="p-2 col-md-2 col-lg-2 col-sm-12">
          <mat-label>{{ 'productBaicTab.partNumber' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input min="0" type="number" matInput formControlName="partNumber" />
            <mat-error
              *ngIf="
                productForm?.controls['partNumber'].hasError('required') &&
                productForm?.controls['partNumber'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </mat-form-field>
        </div>
        <!-- Product Tax  -->
        <div class="p-2 col-md-2 col-lg-2 col-sm-12">
          <mat-label>{{ 'productBaicTab.vat' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input min="0" type="number" matInput formControlName="vat" />
          </mat-form-field>

          <mat-error
            *ngIf="
              productForm?.controls['vat'].hasError('required') &&
              productForm?.controls['vat'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </div>
        <!-- Item Type  -->
        <div class="p-2 col-md-2 col-lg-2 col-sm-12">
          <mat-label>{{ 'productBaicTab.itemTypes' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <mat-select #itemType formControlName="itemType">
              <mat-option *ngFor="let itemType of itemTypes" [value]="itemType.value"
                >{{ itemType.display }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <!-- Product Category -->
        <div class="p-2 col-md-2 col-lg-2 col-sm-12">
          <mat-label>{{ 'productBaicTab.category' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <mat-select formControlName="categoryId">
              <mat-option
                *ngFor="let parentCategory of categoryList"
                [value]="parentCategory.categoryId">
                {{ parentCategory | localized }}</mat-option
              >
            </mat-select>
            <mat-error
              *ngIf="
                productForm?.controls['parentCategory']?.hasError('required') &&
                productForm?.controls['parentCategory']?.touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </mat-form-field>
        </div>
        <!-- Product Maximum Quantity -->
        <div class="p-2 col-md-2 col-lg-2 col-sm-12">
          <mat-label>{{ 'productBaicTab.maxQty' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input min="0" type="number" matInput formControlName="maximumQuantity" />
            <mat-error
              *ngIf="
                productForm?.controls['maximumQuantity'].hasError('required') &&
                productForm?.controls['maximumQuantity'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </mat-form-field>
        </div>
        <!-- Product Minimum Quantity -->
        <div class="p-2 col-md-2 col-lg-2 col-sm-12">
          <mat-label>{{ 'productBaicTab.minQty' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input min="0" type="number" matInput formControlName="minimumQuantity" />
            <mat-error
              *ngIf="
                productForm?.controls['minimumQuantity'].hasError('required') &&
                productForm?.controls['minimumQuantity'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </mat-form-field>
        </div>
        <!-- Color  -->
        <div class="p-2 col-md-2 col-lg-2 col-sm-12">
          <mat-label>{{ 'productBaicTab.color' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input [appInputFormat]="'capital-case'" type="text" matInput formControlName="color" />
          </mat-form-field>
        </div>
        <!-- Size  -->
        <div class="p-2 col-md-2 col-lg-2 col-sm-12">
          <mat-label>{{ 'productBaicTab.size' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input type="text" matInput formControlName="size" />
          </mat-form-field>
        </div>
        <!-- Product Days to alert  -->
        <!-- <div class="p-2 col-md-2 col-lg-2 col-sm-12">
          <mat-label>{{ 'productBaicTab.daysToAlert' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input min="0" type="number" matInput formControlName="daysToAlert" />
          </mat-form-field>

          <mat-error
            *ngIf="
              productForm?.controls['daysToAlert'].hasError('required') &&
              productForm?.controls['daysToAlert'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </div> -->
        <div class="p-2 col-md-4 col-lg-4 col-sm-12">
          <mat-label>{{ 'productBaicTab.description' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <textarea
              #description
              #autosize="cdkTextareaAutosize"
              maxlength="200"
              type="text"
              matInput
              cdkTextareaAutosize
              cdkAutosizeMinRows="1"
              cdkAutosizeMaxRows="5"
              formControlName="notes"></textarea>
            <mat-error
              *ngIf="
                productForm?.controls['notes'].hasError('required') &&
                productForm?.controls['notes'].touched
              "
              >{{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="row no-gutters" class="displaynone">
        <!-- Product Active -->
        <div class="p-2 col-md-2 col-lg-2 col-sm-12">
          <mat-slide-toggle
            [labelPosition]="'after'"
            [formControl]="productForm?.controls['isActive']"
            >{{ 'productBaicTab.active' | translate }}</mat-slide-toggle
          >
        </div>
        <!-- Product Expiry -->
        <div class="p-2 col-md-2 col-lg-2 col-sm-12">
          <mat-slide-toggle
            [labelPosition]="'after'"
            [formControl]="productForm?.controls['hasExpirationDate']"
            >{{ 'productBaicTab.expiryDate' | translate }}
          </mat-slide-toggle>
        </div>
        <!-- Product Weighted -->
        <div class="p-2 col-md-2 col-lg-2 col-sm-12">
          <mat-slide-toggle
            [labelPosition]="'after'"
            [formControl]="productForm?.controls['isItemWeighed']"
            >{{ 'productBaicTab.weighted' | translate }}
          </mat-slide-toggle>
        </div>
        <section class="line-height">
          <mat-radio-group [formControl]="productForm?.controls['isGeneralSalesPolicy']">
            <mat-radio-button class="p-r-10" [value]="true" color="primary">
              {{ 'productBaicTab.general' | translate }}
            </mat-radio-button>
            <mat-radio-button class="p-r-10" [value]="false" color="accent">
              {{ 'productBaicTab.private' | translate }}
            </mat-radio-button>
          </mat-radio-group>
        </section>
      </div>

      <!-- Unit prices and discounts -->
      <form [formGroup]="uomform" autocomplete="off">
        <div class="row no-gutters">
          <div class="table-responsive w-100">
            <table [dataSource]="dataSource" mat-table formArrayName="itemUnitRequests">
              <!-- Unit Barcode -->
              <ng-container matColumnDef="unitBarcode">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'productUnitPricesTab.unitBarcode' | translate }}
                </th>
                <td *matCellDef="let element; let i = index" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100">
                    <input
                      #barcodeInput
                      [matTooltip]="barcodeInput.value"
                      matInput
                      type="text"
                      formControlName="unitBarcode"
                      alphanumeric />
                    <mat-error
                      *ngIf="
                        uomform
                          .get('itemUnitRequests')
                          .controls[i].get('unitBarcode')
                          .hasError('required')
                      ">
                      {{ 'common.required' | translate }}
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>

              <!-- Unit Of Measure -->
              <ng-container matColumnDef="unitofmeasure">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'productUnitPricesTab.unitOfMeasureCost' | translate }}
                </th>
                <td *matCellDef="let element; let i = index" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100">
                    <mat-select formControlName="unitId">
                      <mat-option
                        *ngFor="let unit of units"
                        [value]="unit.unitId"
                        [disabled]="isDisabled(unit.unitId)">
                        {{ unit.name }}
                      </mat-option>
                    </mat-select>
                    <mat-error
                      *ngIf="
                        uomform
                          .get('itemUnitRequests')
                          .controls[i].get('unitId')
                          .hasError('required')
                      ">
                      {{ 'common.required' | translate }}
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>

              <!-- Cost Price -->
              <ng-container matColumnDef="costPrice">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'productUnitPricesTab.costPrice' | translate }}
                </th>
                <td *matCellDef="let element; let i = index" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100">
                    <input
                      [matTooltip]="
                        uomform.get('itemUnitRequests').controls[i].get('costPrice').value
                      "
                      matInput
                      type="number"
                      formControlName="costPrice" />
                    <mat-error
                      *ngIf="
                        uomform
                          .get('itemUnitRequests')
                          .controls[i].get('costPrice')
                          .hasError('required')
                      ">
                      {{ 'common.required' | translate }}
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>

              <!-- Purchase Price -->
              <ng-container matColumnDef="purchasePrice">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'productUnitPricesTab.purchasePrice' | translate }}
                </th>
                <td *matCellDef="let element; let i = index" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100">
                    <input
                      [matTooltip]="
                        uomform.get('itemUnitRequests').controls[i].get('purchasePrice').value
                      "
                      matInput
                      type="number"
                      formControlName="purchasePrice" />
                    <mat-error
                      *ngIf="
                        uomform
                          .get('itemUnitRequests')
                          .controls[i].get('purchasePrice')
                          .hasError('required')
                      ">
                      {{ 'common.required' | translate }}
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>

              <!-- Open Purchase Price -->
              <ng-container matColumnDef="openPurchasePrice">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'productUnitPricesTab.openPurRice' | translate }}
                </th>
                <td *matCellDef="let element; let i = index" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100">
                    <input
                      [matTooltip]="
                        uomform.get('itemUnitRequests').controls[i].get('openPurchasePrice').value
                      "
                      matInput
                      type="number"
                      formControlName="openPurchasePrice" />
                    <mat-error
                      *ngIf="
                        uomform
                          .get('itemUnitRequests')
                          .controls[i].get('openPurchasePrice')
                          .hasError('required')
                      ">
                      {{ 'common.required' | translate }}
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>

              <!-- Wholesale Price -->
              <ng-container matColumnDef="wholesalePrice">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'productUnitPricesTab.wholesalePrice' | translate }}
                </th>
                <td *matCellDef="let element; let i = index" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100">
                    <input
                      [matTooltip]="
                        uomform.get('itemUnitRequests').controls[i].get('wholesalePrice').value
                      "
                      matInput
                      type="number"
                      formControlName="wholesalePrice" />
                    <mat-error
                      *ngIf="
                        uomform
                          .get('itemUnitRequests')
                          .controls[i].get('wholesalePrice')
                          .hasError('required')
                      ">
                      {{ 'common.required' | translate }}
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>

              <!-- Distribution Price -->
              <ng-container matColumnDef="distributorPrice">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'productUnitPricesTab.distributorPrice' | translate }}
                </th>
                <td *matCellDef="let element; let i = index" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100">
                    <input
                      [matTooltip]="
                        uomform.get('itemUnitRequests').controls[i].get('distributorPrice').value
                      "
                      matInput
                      type="number"
                      formControlName="distributorPrice" />
                    <mat-error
                      *ngIf="
                        uomform
                          .get('itemUnitRequests')
                          .controls[i].get('distributorPrice')
                          .hasError('required')
                      ">
                      {{ 'common.required' | translate }}
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>

              <!-- Retail Price -->
              <ng-container matColumnDef="retailPrice">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'productUnitPricesTab.retailPrice' | translate }}
                </th>
                <td *matCellDef="let element; let i = index" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100">
                    <input
                      [matTooltip]="
                        uomform.get('itemUnitRequests').controls[i].get('retailPrice').value
                      "
                      matInput
                      type="number"
                      formControlName="retailPrice" />
                    <mat-error
                      *ngIf="
                        uomform
                          .get('itemUnitRequests')
                          .controls[i].get('retailPrice')
                          .hasError('required')
                      ">
                      {{ 'common.required' | translate }}
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>

              <!-- Transport Cost -->
              <ng-container matColumnDef="transportCost">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'productUnitPricesTab.transportCost' | translate }}
                </th>
                <td *matCellDef="let element; let i = index" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100">
                    <input
                      [matTooltip]="
                        uomform.get('itemUnitRequests').controls[i].get('transportCost').value
                      "
                      matInput
                      type="number"
                      formControlName="transportCost" />
                    <mat-error
                      *ngIf="
                        uomform
                          .get('itemUnitRequests')
                          .controls[i].get('transportCost')
                          .hasError('required')
                      ">
                      {{ 'common.required' | translate }}
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>

              <!-- Discount Method -->
              <ng-container matColumnDef="isGeneralDscntMethod">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'productDiscountsTab.discountMethod' | translate }}
                </th>
                <td *matCellDef="let element; let i = index" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100">
                    <mat-select formControlName="isGeneralDscntMethod">
                      <mat-option
                        *ngFor="let value of fixedPercentageDropDown"
                        [value]="value.value">
                        {{ value.display }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </td>
              </ng-container>

              <!-- Discount -->
              <ng-container matColumnDef="discount">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'productDiscountsTab.discount' | translate }}
                </th>
                <td *matCellDef="let element; let i = index" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100">
                    <input
                      [matTooltip]="
                        uomform.get('itemUnitRequests').controls[i].get('discount').value
                      "
                      matInput
                      type="number"
                      formControlName="discount" />
                  </mat-form-field>
                </td>
              </ng-container>

              <!-- Profit Method -->
              <ng-container matColumnDef="isGeneralProfitMethod">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'productDiscountsTab.profitMethod' | translate }}
                </th>
                <td *matCellDef="let element; let i = index" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100">
                    <mat-select formControlName="isGeneralProfitMethod">
                      <mat-option
                        *ngFor="let value of fixedPercentageDropDown"
                        [value]="value.value">
                        {{ value.display }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </td>
              </ng-container>

              <!-- Profit -->
              <ng-container matColumnDef="profit">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'productDiscountsTab.profit' | translate }}
                </th>
                <td *matCellDef="let element; let i = index" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100">
                    <input
                      [matTooltip]="uomform.get('itemUnitRequests').controls[i].get('profit').value"
                      matInput
                      type="number"
                      formControlName="profit" />
                  </mat-form-field>
                </td>
              </ng-container>

              <!-- Action -->
              <ng-container matColumnDef="action">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'productUnitPricesTab.action' | translate }}
                </th>
                <ng-container *ngIf="!isViewMode">
                  <td *matCellDef="let element; let i = index; let last = last" mat-cell>
                    <a class="m-r-10 cursor-pointer" *ngIf="last && i < units.length - 1">
                      <i-tabler
                        class="icon-16"
                        (click)="addUnitsPriceRow(); (false)"
                        name="plus"></i-tabler>
                    </a>
                    <a class="cursor-pointer" *ngIf="displayRemoveIcon">
                      <i-tabler
                        class="icon-16"
                        (click)="deleteUoMEntry(i); (false)"
                        name="trash"></i-tabler>
                    </a>
                  </td>
                </ng-container>
                <ng-container *ngIf="isViewMode">
                  <td *matCellDef="let element; let i = index; let last = last" mat-cell></td>
                </ng-container>
              </ng-container>

              <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
              <tr
                *matRowDef="let row; columns: displayedColumns; let i = index"
                [formGroupName]="i"
                mat-row></tr>
            </table>
          </div>
        </div>
      </form>
      <!-- Unit prices and discounts -->
      <!-- Product Description -->
      <div class="row no-gutters">
        <div class="col-md-6 col-lg-6 col-sm-12" class="displaynone">
          <!-- special instructions -->
          <mat-accordion>
            <mat-expansion-panel class="no-padding" [expanded]="isViewMode">
              <mat-expansion-panel-header class="bg-light-primary">
                <mat-panel-title
                  >{{ 'productBaicTab.specialInstructions' | translate
                  }}<i-tabler
                    class="icon-16 text-error m-l-10"
                    *ngIf="!productForm.valid && submitted && isSpecialTabError()"
                    name="exclamation-circle"></i-tabler
                ></mat-panel-title>
              </mat-expansion-panel-header>
              <!-- Product Item Free  -->
              <div class="row no-gutters">
                <div class="col-md-4 col-lg-4 col-sm-12 p-2">
                  <mat-slide-toggle [formControl]="productForm?.controls['isItemFree']">{{
                    'productSpecialInstructionsTab.itemFree' | translate
                  }}</mat-slide-toggle>
                </div>
                <div class="col-md-4 col-lg-4 col-sm-12 p-2">
                  <mat-label>{{
                    'productSpecialInstructionsTab.freeStartDate' | translate
                  }}</mat-label>
                  <mat-form-field class="w-100">
                    <input
                      [matDatepicker]="startDateDP"
                      matInput
                      name="freeStartDate"
                      formControlName="freeStartDate" />
                    <mat-datepicker-toggle [for]="startDateDP" matSuffix></mat-datepicker-toggle>
                    <mat-datepicker #startDateDP></mat-datepicker>
                    <mat-error
                      *ngIf="
                        productForm?.controls['freeStartDate'].hasError('required') &&
                        productForm?.controls['freeStartDate'].touched
                      "
                      >{{ 'common.required' | translate }}</mat-error
                    >
                  </mat-form-field>
                </div>
                <div class="col-md-4 col-lg-4 col-sm-12 p-2">
                  <mat-label>{{
                    'productSpecialInstructionsTab.freeEndDate' | translate
                  }}</mat-label>
                  <mat-form-field class="w-100">
                    <input
                      [matDatepicker]="endDateDP"
                      matInput
                      name="freeEndDate"
                      formControlName="freeEndDate" />
                    <mat-datepicker-toggle [for]="endDateDP" matSuffix></mat-datepicker-toggle>
                    <mat-datepicker #endDateDP></mat-datepicker>
                    <mat-error
                      *ngIf="
                        productForm?.controls['freeEndDate'].hasError('required') &&
                        productForm?.controls['freeEndDate'].touched
                      "
                      >{{ 'common.required' | translate }}</mat-error
                    >
                  </mat-form-field>
                </div>
              </div>
              <!-- Product Print -->

              <div class="row no-gutters">
                <div class="col-md-4 col-lg-4 col-sm-12 p-2">
                  <mat-slide-toggle [formControl]="productForm?.controls['isPrintFrozen']"
                    >{{ 'productSpecialInstructionsTab.itemPrintFrozen' | translate }}
                  </mat-slide-toggle>
                </div>
                <div class="col-md-8 col-lg-8 col-sm-12 p-2">
                  <mat-label>{{ 'productSpecialInstructionsTab.reason' | translate }}</mat-label>
                  <mat-form-field class="w-100">
                    <input type="text" matInput formControlName="reasonToFreezePrint" />
                    <mat-error
                      *ngIf="
                        productForm?.controls['reasonToFreezePrint'].hasError('required') &&
                        productForm?.controls['reasonToFreezePrint'].touched
                      "
                      >{{ 'common.required' | translate }}</mat-error
                    >
                  </mat-form-field>
                </div>
              </div>

              <div class="row no-gutters">
                <!-- Product Sale Frozen -->
                <div class="col-md-4 col-lg-4 col-sm-12 p-2">
                  <mat-slide-toggle [formControl]="productForm?.controls['isSaleFrozen']"
                    >{{ 'productSpecialInstructionsTab.itemSalesFrozen' | translate }}
                  </mat-slide-toggle>
                </div>
                <div class="col-md-8 col-lg-8 col-sm-12 p-2">
                  <mat-label>{{ 'productSpecialInstructionsTab.reason' | translate }}</mat-label>
                  <mat-form-field class="w-100">
                    <input type="text" matInput formControlName="reasonToFreezeSale" />
                    <mat-error
                      *ngIf="
                        productForm?.controls['reasonToFreezeSale'].hasError('required') &&
                        productForm?.controls['reasonToFreezeSale'].touched
                      "
                      >{{ 'common.required' | translate }}</mat-error
                    >
                  </mat-form-field>
                </div>
              </div>

              <!-- Product isTransferFrozen -->
              <div class="col-md-12 col-lg-12 col-sm-12 p-2">
                <mat-slide-toggle [formControl]="productForm?.controls['isTransferFrozen']"
                  >{{ 'productSpecialInstructionsTab.itemTransferFrozen' | translate }}
                </mat-slide-toggle>
              </div>
              <!-- </div> -->
            </mat-expansion-panel>
          </mat-accordion>
        </div>
        <div class="col-md-6 col-lg-6 col-sm-12">
          <ng-contianer>
            <div class="image-preview">
              <!-- <svg
                class="icon icon-tabler icons-tabler-outline icon-tabler-upload"
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="1"
                stroke-linecap="round"
                stroke-linejoin="round">
                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                <path d="M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2" />
                <path d="M7 9l5 -5l5 5" />
                <path d="M12 4l0 12" />
              </svg> -->
              <img
                class="cropped-image"
                [src]="croppedImage ? croppedImage : defaultImage"
                alt="Cropped Image" />
              <button
                (click)="fileInput.click(); $event.stopPropogation()"
                mat-stroked-button
                color="primary">
                {{ 'common.chooseFile' | translate }}
              </button>
              <input
                #fileInput
                (change)="fileChangeEvent($event)"
                style="display: none"
                type="file"
                accept="image/*" />
            </div>

            <div class="image-cropper-container">
              <image-cropper
                *ngIf="imageChangedEvent"
                [imageChangedEvent]="imageChangedEvent"
                [maintainAspectRatio]="true"
                [aspectRatio]="1"
                [canvasWidth]="200"
                [canvasHeight]="200"
                [minWidth]="200"
                [minHeight]="150"
                [maxWidth]="200"
                [maxHeight]="200"
                [centerTouchRadius]="0"
                [cropperMinHeight]="100"
                [cropperMinWidth]="100"
                (imageCropped)="imageCropped($event)"
                (imageLoaded)="imageLoaded()"
                (cropperReady)="cropperReady()"
                (loadImageFailed)="loadImageFailed()"
                format="png">
              </image-cropper>
            </div>
          </ng-contianer>
        </div>
        <!-- special instructions -->
      </div>
    </mat-card>
    <!-- </div> -->
  </form>

  <!------------------------------------ Action Buttons ------------------------------------------------->
  <div class="text-center">
    <ng-container *ngIf="isCreateMode">
      <button class="m-l-10" (click)="onSubmit($event)" mat-flat-button color="primary">
        {{ 'common.submit' | translate }}
      </button>
      <button class="m-l-10" [routerLink]="['../']" mat-stroked-button color="warn">
        {{ 'common.cancel' | translate }}
      </button>
    </ng-container>
    <ng-container *ngIf="isEditMode">
      <button class="m-l-10" (click)="onSubmit($event)" mat-flat-button color="primary">
        {{ 'common.submit' | translate }}
      </button>
      <button class="m-l-10" [routerLink]="['../../']" mat-stroked-button color="warn">
        {{ 'common.cancel' | translate }}
      </button>
    </ng-container>
    <ng-container *ngIf="isViewMode">
      <button
        class="m-l-10"
        [routerLink]="['../../']"
        type="button"
        mat-stroked-button
        color="primary">
        {{ 'common.buttons.back' | translate }}
      </button>
    </ng-container>
  </div>
</ng-container>
