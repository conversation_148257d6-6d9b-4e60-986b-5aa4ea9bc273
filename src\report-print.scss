/* Report Print Styles */
$print-border-color: #cccccc;
$print-bg-light: #f9f9f9;
$print-bg-accent: #e6f2ff;
$print-text-accent: #0056b3;
$print-border-width: 1px;

@media print {
  /* Page setup */
  @page { size: A4; margin: 0; }
  
  html, body {
    margin: 0 !important;
    padding: 0 !important;
    width: 210mm !important;
    height: fit-content !important;
    background-color: white !important;
    print-color-adjust: exact !important;
  }
  
  /* Hide UI elements */
  app-header, app-footer, .mat-toolbar, .mat-sidenav, .mat-drawer,
  .mat-drawer-container, .mat-drawer-content, button,
  .action-buttons, .no-print { display: none !important; }
  
  /* Utility classes */
  .page-break-before { page-break-before: always !important; }
  .page-break-after { page-break-after: always !important; }
  .avoid-break { page-break-inside: avoid !important; }
  
  /* Invoice structure */
  .invoice-container {
    width: 100% !important;
    margin: 0 auto !important;
    padding: 0 !important;
    background-color: white !important;
    box-shadow: none !important;
  }
  
  .header-layout, .invoice-client-info, .invoice-footer, .invoice-footer-info {
    page-break-inside: avoid !important;
  }
  
  /* Tables */
  .items-table {
    width: 100% !important;
    border-collapse: collapse !important;
    border: $print-border-width solid $print-border-color !important;
    
    th, td {
      border: $print-border-width solid $print-border-color !important;
      padding: 1px 2px !important;
      line-height: 1.1 !important;
      word-break: break-word !important;
    }
    
    /* Prevent row truncation at page breaks */
    tr {
      page-break-inside: avoid !important;
    }
    
    /* Keep header with at least one row if possible */
    thead {
      display: table-header-group !important;
    }
    
    th { background-color: $print-bg-accent !important; }
    .right-align { text-align: right !important; padding-right: 4px !important; }
    .left-align { text-align: left !important; padding-left: 4px !important; }
  }
  
  /* Colors */
  .title-table .title-cell, .totals-table tr.total-row, .totals-table tr.total-row td {
    background-color: $print-bg-accent !important;
  }
  
  .client-info-table, .client-details-table, .meta-details-table,
  .footer-info-table, .client-details-table td.label,
  .client-details-table td.arabic, .meta-details-table td.label,
  .footer-info-table td.signature-section,
  .invoice-notes .notes-content, .qr-code-container {
    background-color: $print-bg-light !important;
  }
  
  /* Borders */
  .totals-table, .invoice-notes .notes-content {
    border: $print-border-width solid $print-border-color !important;
  }
  
  .qr-code-container {
    border: $print-border-width solid #eee !important;
  }
  
  /* Table properties */
  .client-info-table, .client-details-table,
  .meta-details-table, .footer-info-table {
    border-collapse: collapse;
  }
}