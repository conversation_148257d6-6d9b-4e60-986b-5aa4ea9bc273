// autocomplete selection - clear - db scan and view icon - START
.icon-container {
  display: flex;
  align-items: center; /* Vertically center icons */
  gap: 16px; /* Adjust space between icons */
}

.icon-container a {
  display: flex;
  align-items: center;
  justify-content: center;
}

.input-with-icons {
  display: flex;
  align-items: center; /* Aligns input and icons vertically center */
  position: relative; /* Allows positioning of the icons inside */
}
// autocomplete selection - clear - db scan and view icon - END

// zatca xml icon and error icon css- START
.status-text {
  margin-left: 8px; /* Adjust spacing between icons and text */
}

.line-item-content-container td.mat-column-itemName {
  white-space: normal;
  font-size: 14px;
}

.line-item-content-container td.mat-column-warehouseName {
  white-space: normal;
  font-size: 14px;
}

.line-item-content-container td.mat-column-unitName {
  white-space: normal;
  font-size: 14px;
}

.line-item-content-container td.mat-column-itemCode {
  white-space: normal;
  font-size: 14px;
}

.status-container {
  display: flex;
  justify-content: space-between; /* Align text to the start and buttons to the end */
  align-items: center;
  width: 100%; /* Ensure the container takes the full width */
}

.button-container {
  display: flex;
  gap: 2px; /* Space between the buttons */
}
// zatca xml icon and error icon css- END

.custom-dialog-class .mat-dialog-content {
  max-height: calc(100% - 64px); /* Adjust based on your header height */
  max-width: calc(100% - 64px); /* Adjust based on your header height */
  overflow-y: auto;
  overflow-x: auto;
}

mat-error {
  overflow-wrap: break-word !important;
  white-space: normal !important;
}

/* Generic styles for autocomplete with sticky header and scrollable table */
.autocomplete-table {
  max-height: inherit; /* Set a default max height */
  overflow: hidden; /* Prevent inner scrolling */
}

.autocomplete-table .autocomplete-panel {
  overflow-y: auto !important; /* Enable vertical scrolling */
  padding: 0px; /* Reset padding */
}

.autocomplete-table .table-responsive {
  max-height: inherit; /* Allow height to be inherited */
  overflow: visible; /* Prevent inner scrolling */
}

.autocomplete-table .table-responsive mat-table {
  width: 100%; /* Ensure full width for sticky headers */
}

.autocomplete-table .table-responsive mat-header-row {
  position: sticky; /* Make header sticky */
  top: 0; /* Stick to the top */
  z-index: 1; /* Ensure it stays above other content */
}

.autocomplete-table .table-responsive mat-row {
  padding: 0; /* Reset padding for rows */
}

/* Add padding to the first row to prevent overlap with the sticky header */
.autocomplete-table .table-responsive mat-row.first-row {
  padding-top: 48px; /* Adjust this value based on your header height */
}

/* Additional styles for mat-option if needed */
.autocomplete-table .table-responsive mat-table mat-option {
  padding: 0px;
  overflow: visible;
  min-width: min-content;
}

.autocomplete-table .table-responsive mat-table mat-option .mdc-list-item__primary-text {
  width: 100%;
}

.autocomplete-table .table-responsive mat-table mat-option mat-pseudo-checkbox {
  display: none;
}

.new .table-responsive {
  max-height: 300px;
}

.new mat-option {
  display: contents;
  mat-pseudo-checkbox {
    display: none;
  }
}

.new.mat-mdc-autocomplete-panel {
  overflow: visible !important; /* Enable vertical scrolling */
}

// .new .table-responsive tr {
//   height: 58px; /* Set the height for the first row */
// }

.new .table-responsive tr.mat-mdc-row:hover {
  background-color: none; /* Light background color on hover */
}

.hide {
  visibility: hidden;
}
.displaynone {
  display: none;
}
