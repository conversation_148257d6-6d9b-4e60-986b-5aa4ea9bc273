import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-confirm-dialog',
  templateUrl: './confirm-dialog.component.html',
  styleUrls: ['./confirm-dialog.component.scss'],
})
export class ConfirmDialogComponent {
  constructor(
    public dialogRef: MatDialogRef<ConfirmDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public message: string
  ) {}

  onNoClick(event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    this.dialogRef.close(false);
  }

  onClick(event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    this.dialogRef.close(true);
  }
}
