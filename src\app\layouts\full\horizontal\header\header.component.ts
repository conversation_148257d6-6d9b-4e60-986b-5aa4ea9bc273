import { Directionality } from '@angular/cdk/bidi';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { Observable, Subscription, fromEvent, merge, of } from 'rxjs';
import { mapTo } from 'rxjs/operators';
import { ILoginBranches } from 'src/app/modules/core/core/models/login';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { BranchService } from 'src/app/modules/core/core/services/branch.service';
import { LocalStorageService } from 'src/app/modules/core/core/services/local-storage.service';
import { MultilingualService } from 'src/app/modules/core/core/services/multilingual.service';
import { BranchSwitcherDialogComponent } from 'src/app/modules/shared/components/branch-switcher-dialog/branch-switcher-dialog.component';
import { LogoutDialogComponent } from 'src/app/modules/shared/components/logout-dialog/logout-dialog.component';

interface profile {
  id: number;
  title: string;
  subtitle: string;
  link: string;
}

@Component({
  selector: 'app-horizontal-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
})
export class AppHorizontalHeaderComponent implements OnInit, OnDestroy {
  @Input() showToggle = true;
  @Input() toggleChecked = false;
  @Output() toggleMobileNav = new EventEmitter<void>();
  @Output() toggleMobileFilterNav = new EventEmitter<void>();
  @Output() toggleCollapsed = new EventEmitter<void>();
  offlineEvent: Observable<Event>;
  onlineEvent: Observable<Event>;
  subscriptions: Subscription[] = [];
  appOnline = false;

  // Branch and year data
  branchesAndYears: ILoginBranches[] = [];
  currentBranch = '';
  currentYear = '';
  isLoading = false;

  constructor(
    public dialog: MatDialog,
    public localStorage: LocalStorageService,
    public authService: AuthService,
    private direction: Directionality,
    private multilingualService: MultilingualService,
    private branchService: BranchService,
    private toastr: ToastrService
  ) {
    // Subscribe to branches
    this.subscriptions.push(
      this.branchService.branches$.subscribe(branches => {
        this.branchesAndYears = branches;
      })
    );

    // Get current branch/year
    const current = this.branchService.getCurrentBranchAndYear();
    this.currentBranch = current.branch;
    this.currentYear = current.year;
  }

  ngOnInit(): void {
    this.handleAppConnectivityChanges();
  }

  private handleAppConnectivityChanges(): void {
    const event = merge(
      fromEvent(window, 'offline').pipe(mapTo(false)),
      fromEvent(window, 'online').pipe(mapTo(true)),
      of(navigator.onLine)
    );
    this.subscriptions.push(event.subscribe(e => (this.appOnline = e)));
  }

  onClickLogout() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.panelClass = 'green_theme';
    dialogConfig.minWidth = '600px';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = true;
    // Get current direction from multilingual service instead of using Directionality
    dialogConfig.direction = this.multilingualService.getOptions().dir;
    dialogConfig.position = {
      top: '10vh',
    };
    const dialogRef = this.dialog.open(LogoutDialogComponent, dialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result) this.authService.logout();
    });
  }

  openBranchSwitcher(): void {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.panelClass = 'green_theme';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    // Get current direction from multilingual service instead of using Directionality
    dialogConfig.direction = this.multilingualService.getOptions().dir;
    dialogConfig.data = {
      branches: this.branchesAndYears,
      currentBranch: this.currentBranch,
      currentYear: this.currentYear,
    };
    dialogConfig.width = '500px';
    dialogConfig.position = {
      top: '10vh',
    };

    const dialogRef = this.dialog.open(BranchSwitcherDialogComponent, dialogConfig);

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.switchBranch(result.branch, result.year);
      }
    });
  }

  switchBranch(branch: ILoginBranches, year: any): void {
    this.isLoading = true;
    this.branchService
      .switchBranch(branch.branchId.toString(), year.id, branch.branchName, year.year)
      .subscribe(
        () => {
          this.currentBranch = branch.branchName;
          this.currentYear = year.year;
          this.toastr.success(`Switched to ${branch.branchName} / ${year.year}`);
          this.isLoading = false;
        },
        error => {
          this.isLoading = false;
          this.toastr.error('Failed to switch branch');
          console.error('Error switching branch:', error);
        }
      );
  }

  profiledd: profile[] = [
    {
      id: 1,
      title: 'header.profile',
      subtitle: 'header.accountSettings',
      link: 'profile',
    },
  ];

  ngOnDestroy(): void {
    this.subscriptions.forEach(subscription => subscription.unsubscribe());
  }

  toggleLanguage(): void {
    const currentLang = this.multilingualService.getCurrentLanguage();
    const newLang = currentLang === 'en' ? 'ar' : 'en';

    // Update language through MultilingualService with userToggled=true
    this.multilingualService.setLanguage(newLang, true);

    // Force close any open overlays to prevent direction mismatch
    this.dialog.closeAll();
  }
}
