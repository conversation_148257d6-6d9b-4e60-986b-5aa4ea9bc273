import { AriaDescriber, FocusMonitor } from '@angular/cdk/a11y';
import { Directionality } from '@angular/cdk/bidi';
import { Overlay } from '@angular/cdk/overlay';
import { Platform } from '@angular/cdk/platform';
import { ScrollDispatcher } from '@angular/cdk/scrolling';
import { DOCUMENT } from '@angular/common';
import {
  Directive,
  ElementRef,
  Input,
  HostListener,
  Inject,
  NgZone,
  Optional,
  ViewContainerRef,
} from '@angular/core';
import { NgControl } from '@angular/forms';
import {
  MAT_TOOLTIP_DEFAULT_OPTIONS,
  MAT_TOOLTIP_SCROLL_STRATEGY,
  MatTooltip,
  MatTooltipDefaultOptions,
} from '@angular/material/tooltip';

@Directive({
  selector: '[appTooltip]',
})
export class TooltipDirective extends MatTooltip {
  constructor(
    private control: NgControl,
    overlay: Overlay,
    elementRef: ElementRef<HTMLElement>,
    scrollDispatcher: ScrollDispatcher,
    viewContainerRef: ViewContainerRef,
    ngZone: NgZone,
    platform: Platform,
    ariaDescriber: AriaDescriber,
    focusMonitor: FocusMonitor,
    @Inject(MAT_TOOLTIP_SCROLL_STRATEGY) scrollStrategy: any,
    @Optional() dir: Directionality,
    @Inject(MAT_TOOLTIP_DEFAULT_OPTIONS) defaultOptions: MatTooltipDefaultOptions,
    @Inject(DOCUMENT) _document: any
  ) {
    super(
      overlay,
      elementRef,
      scrollDispatcher,
      viewContainerRef,
      ngZone,
      platform,
      ariaDescriber,
      focusMonitor,
      scrollStrategy,
      dir,
      defaultOptions,
      _document
    );
  }

  @HostListener('mouseenter') onMouseEnter() {
    this.updateTooltip();
    this.show();
  }

  @HostListener('mouseleave') onMouseLeave() {
    this.message = ''; // Clear the tooltip content on mouse leave
    this.hide();
  }

  private updateTooltip() {
    if (this.control && this.control.value) {
      this.message = String(this.control.value); // Set the tooltip content to the form control's value
      if (this.tooltipClass) {
        this.tooltipClass = 'max-tooltip-content'; // Apply custom class if provided
      }
    }
  }
}
