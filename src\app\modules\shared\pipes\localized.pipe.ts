import { Pipe, PipeTransform } from '@angular/core';
import { MultilingualService } from '../../../modules/core/core/services/multilingual.service';

@Pipe({
  name: 'localized',
  pure: false,
})
export class LocalizedPipe implements PipeTransform {
  constructor(private multilingualService: MultilingualService) {}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  transform(options: any, defaultIndex = 0): string {
    // Use the MultilingualService to get the localized property
    return this.multilingualService.getLocalizedProperty(options);
  }
}
