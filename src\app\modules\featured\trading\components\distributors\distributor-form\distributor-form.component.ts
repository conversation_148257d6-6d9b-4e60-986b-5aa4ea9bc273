import { Component, Inject, OnInit, Optional, SkipSelf, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { CustomValidators } from 'ngx-custom-validators';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/core/api/common.service';
import { DistributorService } from 'src/app/core/api/trading/distributor.service';
import { IDistributor } from 'src/app/core/interfaces/distributor';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { Account } from 'src/app/modules/featured/accounts/models/account';
import { ChartOfAccountsService } from 'src/app/modules/featured/accounts/services/chart-of-accounts.service';
import { AddressComponent } from 'src/app/modules/shared/components/address/address.component';

@Component({
  selector: 'app-distributor-form',
  templateUrl: './distributor-form.component.html',
  styleUrls: ['./distributor-form.component.scss'],
})
export class DistributorFormComponent implements OnInit {
  @ViewChild('addressForm', { static: false }) addressForm: AddressComponent;
  distributorForm: UntypedFormGroup;
  imageChangedEvent: any = '';
  croppedImage: any = '';
  showCropper = false;
  defaultImage = 'assets/images/users/default.png';
  file: File;
  formTitle: string;
  isEditMode = false;
  isViewMode = false;
  isCreateMode = false;
  distributorId: string;
  parentAccounts: Account[] = [];
  constructor(
    @Optional() @SkipSelf() @Inject(MAT_DIALOG_DATA) public modalData: any,
    private authService: AuthService,
    private toastr: ToastrService,
    private distributorService: DistributorService,
    private fb: UntypedFormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private chartOfAccountsService: ChartOfAccountsService,
    private commonService: CommonService
  ) {}

  ngOnInit(): void {
    this.initializeForm(this.modalData?.data);
    this.getAccountIds();
    this.router.url.includes('view')
      ? ((this.isViewMode = true), (this.formTitle = 'Distributor Details'))
      : null;
    this.router.url.includes('edit')
      ? ((this.isEditMode = true), (this.formTitle = 'Edit Distributor Details'))
      : null;
    this.router.url.includes('create')
      ? ((this.isCreateMode = true), (this.formTitle = 'Register Distributor'))
      : null;
    this.route.params.subscribe(params => {
      this.distributorId = params['id'];
      if (this.distributorId) {
        this.getDistributor(this.distributorId);
      } else {
        this.isEditMode = false;
        this.formTitle = 'Register Distributor';
      }
    });
  }

  ngAfterViewInit(): void {
    if (this.modalData?.data) {
      this.disbaleAllForms();
    }
  }

  getAccountIds() {
    this.chartOfAccountsService
      .getChartOfAccountsByAccountGroupAndBusinessGroup('ASSETS', 'DISTRIBUTOR')
      .subscribe((response: Account[]) => {
        this.parentAccounts = response;
      });
  }

  getDistributor(branchId: string): void {
    this.distributorService.getDistributorById(branchId).subscribe(
      response => {
        this.patchData(response);
      },
      error => console.log(error)
    );
  }

  initializeForm(data?: IDistributor) {
    this.distributorForm = this.fb.group({
      parentAccountId: [data?.parentAccountId ?? null, Validators.compose([Validators.required])],
      nameArabic: [data?.nameArabic ?? null, Validators.compose([Validators.required])],
      nameEnglish: [data?.nameEnglish ?? null, Validators.compose([Validators.required])],
      hasTransferPrivilege: [
        data?.hasTransferPrivilege ?? false,
        Validators.compose([Validators.required]),
      ],
      hasCardPrivilege: [
        data?.hasCardPrivilege ?? false,
        Validators.compose([Validators.required]),
      ],
      hasCashPrivilege: [
        data?.hasCashPrivilege ?? false,
        Validators.compose([Validators.required]),
      ],
      hasCreditPrivilege: [
        data?.hasCreditPrivilege ?? false,
        Validators.compose([Validators.required]),
      ],
      isFreezed: [data?.isFreezed ?? false, Validators.compose([Validators.required])],
      discountPercent: [data?.discountPercent ?? 0, Validators.compose([Validators.required])],
      paymentToleranceDays: [
        data?.paymentToleranceDays ?? 0,
        Validators.compose([Validators.required]),
      ],
      allowedBalance: [data?.allowedBalance ?? 0, Validators.compose([Validators.required])],
      phoneNumber: [data?.phoneNumber ?? null, Validators.compose([])],
      vatNumber: [data?.vatNumber ?? 0, Validators.compose([Validators.required])],
      emailId: [data?.emailId ?? null, Validators.compose([CustomValidators.email])],
      accountNumber: [data?.accountNumber ?? null],
      commission: [0],
      yearlyTarget: [0],
      address: [data?.address ?? null],
    });
    this.disableForm();
  }

  patchData(data: IDistributor): void {
    this.initializeForm(data);
  }

  private disableForm(): void {
    if (this.isViewMode) {
      this.disbaleAllForms();
    }
  }

  private disbaleAllForms(): void {
    this.distributorForm.disable();
    this.addressForm.disableForm();
  }

  onSubmit(event: Event) {
    event.preventDefault();
    this.addressForm.markFormAsTouched();
    this.distributorForm.markAllAsTouched();
    if (this.distributorForm && this.distributorForm?.valid && this.addressForm.isValid()) {
      if (!this.isEditMode) {
        console.log('this.customerForm.value', this.distributorForm.value);
        this.distributorService.createDistributor(this.distributorForm.value).subscribe(() => {
          //this.toastr.success('Distributor Created Successfully');
          this.commonService.playSuccessSound();
          this.router.navigate(['../'], { relativeTo: this.route });
        });
      } else {
        this.distributorService
          .updateDistributor(this.distributorForm.value, this.distributorId)
          .subscribe(() => {
            //this.toastr.success('Distributor Updated Successfully');
            this.commonService.playSuccessSound();
            this.router.navigate(['../../'], { relativeTo: this.route });
          });
      }
    } else {
      this.commonService.scrollToError();
    }
  }
}
