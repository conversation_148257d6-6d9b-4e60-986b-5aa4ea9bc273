import {
  Component,
  Input,
  ElementRef,
  EventEmitter,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import {
  ControlValueAccessor,
  UntypedFormBuilder,
  UntypedFormGroup,
  NG_VALUE_ACCESSOR,
} from '@angular/forms';
import {
  MatAutocomplete,
  MatAutocompleteSelectedEvent,
  MatAutocompleteTrigger,
} from '@angular/material/autocomplete';
import { MatSort } from '@angular/material/sort';
import { MatTable, MatTableDataSource } from '@angular/material/table';
import { debounceTime, filter, finalize, switchMap, tap } from 'rxjs/operators';
import { Account } from '../../models/account';
import { ChartOfAccountsService } from '../../services/chart-of-accounts.service';
import { AccountParams } from '../../models/accountParams';
import { AccountsListWindowComponent } from '../accounts-list-window/accounts-list-window.component';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { Directionality } from '@angular/cdk/bidi';
import { LocalStorageService } from 'src/app/modules/core/core/services/local-storage.service';

@Component({
  selector: 'app-accounts-prosearch-box',
  templateUrl: './accounts-prosearch-box.component.html',
  styleUrls: ['./accounts-prosearch-box.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: AccountsProsearchBoxComponent,
    },
  ],
})
export class AccountsProsearchBoxComponent implements OnInit {
  @Input() label: string;
  @Input() filterTypes: string[];
  @Input() placeHolderText: string[];
  @Input() reportLabel: string;
  @ViewChild('userInput') userInput: ElementRef;
  @ViewChild(MatTable) table: MatTable<Account>;
  @ViewChild(MatAutocompleteTrigger) autoCompleteTrigger: MatAutocompleteTrigger;
  @ViewChild(MatAutocomplete) autoComplete: MatAutocomplete;
  @ViewChild(MatSort) sort: MatSort;
  @Output() accountSelected: EventEmitter<Account> = new EventEmitter<Account>();
  searchBoxForm: UntypedFormGroup;
  searchPlaceholder: string;
  touched = false;
  public accountValueSet = false;
  public isAccountLoading = false;
  public accounts: Account[] = [];
  public resultNotFound = false;
  accountSearchSource: MatTableDataSource<Account>;
  displayedSearchColumns: string[] = ['accountNumber-nameArabic'];
  constructor(
    private formBuilder: UntypedFormBuilder,
    private accountService: ChartOfAccountsService,
    private dialog: MatDialog,
    private direction: Directionality,
    private localStorage: LocalStorageService
  ) {}

  ngOnInit(): void {
    this.searchBoxForm = this.formBuilder.group({
      searchString: [],
    });
    this.searchBoxForm
      .get('searchString')
      .valueChanges.pipe(
        filter(value => {
          this.accountValueSet = false;
          if (!!value && typeof value !== 'object') {
            console.log('search Value', value);
            return value !== null && value.trim().length >= 3;
          } else {
            return false;
          }
        })
      )
      .pipe(
        debounceTime(500),
        tap(
          () => (
            (this.accounts = []),
            (this.accountSearchSource = null),
            (this.resultNotFound = false),
            (this.isAccountLoading = true)
          )
        ),
        switchMap(value => {
          const params = new AccountParams();
          params.searchString = value;
          return this.accountService
            .getAllChartOfAccounts(params)
            .pipe(finalize(() => (this.isAccountLoading = false)));
        })
      )
      .subscribe(
        searchResult => (
          (this.accounts = searchResult.accounts),
          (this.resultNotFound = this.accounts?.length > 0 ? false : true),
          (this.accountSearchSource = new MatTableDataSource<Account>(this.accounts)),
          console.log('set', this.accountSearchSource),
          setTimeout(() => {
            this.accountSearchSource.sort = this.sort;
          })
        )
      );
  }

  ngAfterViewInit() {
    // Clear the input and emit when a selection is made
    this.autoCompleteTrigger.autocomplete.optionSelected.subscribe(option => {
      console.log('select event triggered..');
      option.option.deselect;
      this.accountSearchSource = null;
      this.onSelection(option.option.value);
    });
  }

  onSelection(selectedAccount: Account) {
    console.log('onSelection we need to pass event', selectedAccount, this.autoComplete);
    //this.searchBoxForm.get('searchString').patchValue(null, { emitEvent: false });
    this.searchBoxForm.controls['searchString'].setValue(
      selectedAccount.accountNumber + '-' + this.getAccountName(selectedAccount)
    );
    this.accountSelected.emit(selectedAccount);
  }

  resetSearchBox(): void {
    this.searchBoxForm.reset();
    this.searchBoxForm.updateValueAndValidity();
  }

  onViewAccountsList(event: Event) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '90vw';
    dialogConfig.position = {
      top: '50px',
    };
    dialogConfig.maxHeight = '90vh';
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.data = {
      header: 'Accounts List',
      data: this.searchBoxForm.controls['searchString'].value,
    };
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(AccountsListWindowComponent, dialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (!dialogConfig.data.data) {
        this.searchBoxForm.controls['searchString'].setValue('');
      } else if (typeof dialogConfig.data.data === 'string') {
        this.searchBoxForm.controls['searchString'].setValue(dialogConfig.data.data);
      } else {
        this.searchBoxForm.controls['searchString'].setValue(
          dialogConfig.data.data?.accountNumber + '-' + this.getAccountName(dialogConfig.data.data)
        );
      }
      this.accountValueSet = true;
    });
  }

  writeValue(obj: any): void {
    obj && this.searchBoxForm.setValue(obj, { emitEvent: false });
  }
  registerOnChange(fn: any): void {
    this.searchBoxForm.valueChanges.subscribe(fn);
  }
  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }
  setDisabledState?(isDisabled: boolean): void {}

  onTouched() {}

  optionSelected(event: MatAutocompleteSelectedEvent): void {
    console.log('see the selected data', event);
    // if (event.option.select) {
    //   this.customerSearchSource = null;
    //   this.customerSearchSource = new MatTableDataSource([event.option.value]);
    //   this.onSelection(event.option.value);
    // }
  }

  clearSelection() {
    this.accounts = [];
    this.accountSearchSource = null;
    this.searchBoxForm.get('searchString').patchValue(null, { emitEvent: false });
    this.resultNotFound = false;
    this.autoCompleteTrigger.closePanel();
  }

  public setFocus() {
    this.userInput.nativeElement.focus();
  }

  getAccountName(account: Account) {
    if (this.localStorage.getItem('locale') === 'AR' && account.nameArabic !== null) {
      return account.nameArabic;
    } else if (this.localStorage.getItem('locale') === 'EN' && account.nameEnglish !== null) {
      return account.nameEnglish;
    } else if (account.nameEnglish !== null) {
      return account.nameEnglish;
    } else if (account.nameArabic !== null) {
      return account.nameArabic;
    } else {
      return '';
    }
  }
}
