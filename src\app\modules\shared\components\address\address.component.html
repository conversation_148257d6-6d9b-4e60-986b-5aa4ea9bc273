<mat-card-title>{{ 'address.address' | translate }}</mat-card-title>
<form [formGroup]="addressForm" autocomplete="off">
  <div class="row no-gutters">
    <div class="col-md-3 col-lg-3 col-sm-6 p-2">
      <mat-label>{{ 'address.bldgnNo' | translate }}</mat-label>

      <mat-form-field class="w-100">
        <input
          #buildingNumber
          [appMaxlength]="4"
          type="number"
          matInput
          formControlName="buildingNumber" />
        <mat-error
          *ngIf="
            addressForm.controls['buildingNumber'].hasError('required') &&
            addressForm.controls['buildingNumber'].touched
          "
          >{{ 'common.required' | translate }}</mat-error
        >
        <mat-error
          *ngIf="
            addressForm.controls['buildingNumber'].hasError('minDigits') &&
            addressForm.controls['buildingNumber'].touched
          "
          >{{ 'common.minLength' | translate : { length: 4 } }}</mat-error
        >
      </mat-form-field>
    </div>

    <div class="col-md-3 col-lg-3 col-sm-6 p-2">
      <mat-label>{{ 'address.addnNo' | translate }}</mat-label>

      <mat-form-field class="w-100">
        <input
          #additionalNumber
          maxlength="4"
          type="text"
          matInput
          formControlName="additionalNumber" />
        <mat-error
          *ngIf="
            addressForm.controls['additionalNumber'].hasError('required') &&
            addressForm.controls['additionalNumber'].touched
          "
          >{{ 'common.required' | translate }}</mat-error
        >
      </mat-form-field>
    </div>

    <div class="col-md-3 col-lg-3 col-sm-6 p-2">
      <mat-label>{{ 'address.streetName' | translate }}</mat-label>

      <mat-form-field class="w-100">
        <textarea
          #streetName
          #autosize="cdkTextareaAutosize"
          maxlength="90"
          type="text"
          matInput
          formControlName="streetName"
          cdkTextareaAutosize
          cdkAutosizeMaxRows="5"></textarea>
      </mat-form-field>
    </div>

    <div class="col-md-3 col-lg-3 col-sm-6 p-2">
      <mat-label>{{ 'address.city' | translate }}</mat-label>

      <mat-form-field class="w-100">
        <textarea
          #city
          #autosize="cdkTextareaAutosize"
          maxlength="30"
          type="text"
          matInput
          formControlName="city"
          cdkTextareaAutosize
          cdkAutosizeMaxRows="5"></textarea>
        <mat-error
          *ngIf="
            addressForm.controls['city'].hasError('required') &&
            addressForm.controls['city'].touched
          "
          >{{ 'common.required' | translate }}</mat-error
        >
      </mat-form-field>
    </div>

    <div class="col-md-3 col-lg-3 col-sm-6 p-2">
      <mat-label>{{ 'address.district' | translate }}</mat-label>

      <mat-form-field class="w-100">
        <textarea
          #district
          #autosize="cdkTextareaAutosize"
          maxlength="30"
          type="text"
          matInput
          formControlName="district"
          cdkTextareaAutosize
          cdkAutosizeMaxRows="5"></textarea>
      </mat-form-field>
    </div>

    <div class="col-md-3 col-lg-3 col-sm-6 p-2">
      <mat-label>{{ 'address.country' | translate }}</mat-label>

      <mat-form-field class="w-100">
        <textarea
          #country
          #autosize="cdkTextareaAutosize"
          maxlength="30"
          type="text"
          matInput
          formControlName="country"
          cdkTextareaAutosize
          cdkAutosizeMaxRows="5"></textarea>
        <mat-error
          *ngIf="
            addressForm.controls['country'].hasError('required') &&
            addressForm.controls['country'].touched
          "
          >{{ 'common.required' | translate }}</mat-error
        >
      </mat-form-field>
    </div>

    <div class="col-md-3 col-lg-3 col-sm-6 p-2">
      <mat-label>{{ 'address.shortAddress' | translate }}</mat-label>

      <mat-form-field class="w-100">
        <input #shortAddress maxlength="8" type="text" matInput formControlName="shortAddress" />
        <mat-error
          *ngIf="
            addressForm.controls['shortAddress'].hasError('required') &&
            addressForm.controls['shortAddress'].touched
          "
          >{{ 'common.required' | translate }}</mat-error
        >
      </mat-form-field>
    </div>

    <div class="col-md-3 col-lg-3 col-sm-6 p-2">
      <mat-label>{{ 'address.otherAddress' | translate }}</mat-label>

      <mat-form-field class="w-100">
        <textarea
          #address2
          #autosize="cdkTextareaAutosize"
          maxlength="90"
          type="text"
          matInput
          formControlName="address2"
          cdkTextareaAutosize
          cdkAutosizeMaxRows="5"></textarea>
      </mat-form-field>
    </div>

    <div class="col-md-3 col-lg-3 col-sm-6 p-2">
      <mat-label>{{ 'address.postalCode' | translate }}</mat-label>

      <mat-form-field class="w-100">
        <input #postalCode [appMaxlength]="5" type="number" matInput formControlName="postalCode" />
        <mat-error
          *ngIf="
            addressForm.controls['postalCode'].hasError('required') &&
            addressForm.controls['postalCode'].touched
          "
          >{{ 'common.required' | translate }}</mat-error
        >
        <mat-error
          *ngIf="
            addressForm.controls['postalCode'].hasError('minDigits') &&
            addressForm.controls['postalCode'].touched
          "
          >{{ 'common.minLength' | translate : { length: 5 } }}</mat-error
        >
      </mat-form-field>
    </div>
  </div>
</form>
