import { Directionality } from '@angular/cdk/bidi';
import { Component, ElementRef, ViewChild } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ToastrService } from 'ngx-toastr';
import { PartnerService } from 'src/app/core/api/partners/partner.service';
import { CustomerService } from 'src/app/core/api/trading/customer.service';
import { ICustomer } from 'src/app/core/interfaces/customer';
import { CustomerParams } from 'src/app/core/models/params/customerParams';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { BranchPartnerResponse } from '../../models/partner';
import { DeleteConfirmationComponent } from 'src/app/modules/shared/components/delete-confirmation/delete-confirmation.component';
import { CommonService } from 'src/app/core/api/common.service';

@Component({
  selector: 'app-branch-partner-listing',
  templateUrl: './branch-partner-listing.component.html',
  styleUrls: ['./branch-partner-listing.component.scss'],
})
export class BranchPartnerListingComponent {
  @ViewChild('filter') filter: ElementRef;
  @ViewChild('searchInput') searchInput: ElementRef;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  units: BranchPartnerResponse[];
  displayedColumns: string[];
  dataSource: MatTableDataSource<BranchPartnerResponse>;
  isLoading = true;
  constructor(
    public partnerService: PartnerService,
    private authService: AuthService,
    public dialog: MatDialog,
    private toastr: ToastrService,
    private direction: Directionality,
    private commonService: CommonService
  ) {}

  ngOnInit(): void {
    this.getUnits();
    this.initColumns();
  }

  ngAfterViewInit(): void {
    if (this.dataSource) {
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
    }
  }

  getUnits(): void {
    this.partnerService.getAll().subscribe(result => {
      console.log(result);
      this.units = result;
      this.isLoading = false;
      this.dataSource = new MatTableDataSource<BranchPartnerResponse>(this.units);
      this.dataSource.paginator = this.paginator;
    });
  }

  initColumns(): void {
    this.displayedColumns = [
      'action',
      'accountNumber',
      'nameArabic',
      'nameEnglish',
      'partnerBranchNameArabic',
      'partnerBranchNameEnglish',
    ];
  }

  applyFilter(filterValue: string): void {
    filterValue = filterValue.trim();
    filterValue = filterValue.toLowerCase();
    this.dataSource.filter = filterValue;
  }

  clearSearchInput() {
    this.searchInput.nativeElement.value = '';
    this.getUnits();
  }

  deleteUnit(customerId?: string) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '600px';
    dialogConfig.panelClass = 'green_theme';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(DeleteConfirmationComponent, dialogConfig);
    dialogRef.componentInstance.accepted.subscribe(result => {
      this.partnerService.delete(customerId).subscribe(result => {
        this.commonService.playSuccessSound();
        this.getUnits();
        this.isLoading = false;
      });
    });
  }
}
