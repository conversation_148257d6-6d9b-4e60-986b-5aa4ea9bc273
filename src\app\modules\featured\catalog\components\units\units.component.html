<!----------------------------------- mat table content --------------------------------------->
<ng-container>
  <!-- action bar -->
  <app-create-action
    *appHasPermission="['Units.Create', 'AllPermissions']"
    [label]="'productUnits.createUnit' | translate"></app-create-action>
  <!-- action bar -->

  <mat-card appearance="outlined">
    <mat-card-content>
      <!-- search field -->
      <div class="row no-gutters">
        <div class="col-md-6 col-lg-6 col-sm-12 d-flex">
          <mat-form-field class="w-100">
            <input
              #searchInput
              (keyup)="applyFilter($event.target.value)"
              matInput
              autocomplete="off" />
            <i-tabler class="icon-16" matPrefix name="search"></i-tabler>
            <a
              class="cursor-pointer"
              *ngIf="searchInput?.value"
              (click)="clearSearchInput()"
              matSuffix>
              <i-tabler class="icon-16 error" name="X"></i-tabler>
            </a>
          </mat-form-field>
        </div>
      </div>
      <mat-card-title class="m-t-10">{{ 'productUnits.listing' | translate }}</mat-card-title>
      <!-- search field -->
      <div class="table-responsive">
        <table [dataSource]="dataSource" mat-table matSort>
          <!-- Unit Name Column -->
          <ng-container matColumnDef="name">
            <th *matHeaderCellDef mat-header-cell mat-sort-header>
              {{ 'productUnits.name' | translate }}
            </th>
            <td class="f-s-14" *matCellDef="let element" mat-cell>
              {{ element.name }}
            </td>
          </ng-container>
          <!-- Unit Type Column -->
          <ng-container matColumnDef="type">
            <th *matHeaderCellDef mat-header-cell mat-sort-header>
              {{ 'productUnits.type' | translate }}
            </th>
            <td class="f-s-14" *matCellDef="let element" mat-cell>
              {{ element.type }}
            </td>
          </ng-container>
          <!-- Unit Factor Column -->
          <ng-container matColumnDef="factorRefUom">
            <th *matHeaderCellDef mat-header-cell mat-sort-header>
              {{ 'productUnits.factor' | translate }}
            </th>
            <td class="f-s-14" *matCellDef="let element" mat-cell>
              {{ element.factorRefUom }}
            </td>
          </ng-container>
          <!-- User Actions Column -->
          <ng-container matColumnDef="action">
            <th *matHeaderCellDef mat-header-cell>{{ 'productUnits.action' | translate }}</th>
            <td class="f-s-14" *matCellDef="let element" mat-cell>
              <a
                class="cursor-pointer"
                *appHasPermission="['Units.Update', 'AllPermissions']"
                [routerLink]="['edit', element.unitId]"
                ><i-tabler class="icon-16" name="edit"></i-tabler>
              </a>
              <a
                class="cursor-pointer"
                *appHasPermission="['Units.Delete', 'AllPermissions']"
                (click)="deleteUnit(element.unitId)"
                ><i-tabler class="icon-16" name="trash"></i-tabler>
              </a>
            </td>
          </ng-container>
          <tr class="mat-row" *matNoDataRow>
            <td class="f-s-14" class="text-center" [attr.colspan]="displayedColumns.length">
              {{ 'common.noDataFound' | translate }}
            </td>
          </tr>
          <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
          <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
        </table>
      </div>
      <mat-paginator
        *ngIf="dataSource?.filteredData?.length > 0"
        [length]="dataSource.filteredData.length"
        [pageIndex]="0"
        [pageSize]="25"
        [pageSizeOptions]="[25, 50]"></mat-paginator>
    </mat-card-content>
  </mat-card>
</ng-container>

<!------------------------------------------- not units message --------------------------------->
