<app-dialog-header></app-dialog-header>
<h2 mat-dialog-title>Add New Term and Condition</h2>
<mat-dialog-content>
  <mat-form-field class="w-100 p-2">
    <input [(ngModel)]="newSectionName" matInput placeholder="Section Name" />
  </mat-form-field>
  <mat-form-field class="w-100 p-2">
    <textarea [(ngModel)]="newSectionTerms" matInput placeholder="Section Terms"></textarea>
  </mat-form-field>
</mat-dialog-content>
<mat-dialog-actions align="center">
  <button (click)="onAddNewSection()" mat-stroked-button color="primary">
    {{ 'common.buttons.submit' | translate }}
  </button>
  <button (click)="onCancel()" mat-button color="warn" mat-stroked-button>
    {{ 'common.buttons.cancel' | translate }}
  </button>
</mat-dialog-actions>
