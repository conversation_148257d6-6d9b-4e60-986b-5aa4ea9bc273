import { Injectable } from '@angular/core';
import { GetPermissionListResultDto, PermissionGroupDto } from 'src/app/core/interfaces/permission';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';

@Injectable()
export class PermissionService {
  constructor(private authService: AuthService) {}
  public getPermissions(): GetPermissionListResultDto {
    console.log(this.transformPermissions(this.authService.getJwtPermissions));
    return this.transformPermissions(this.authService.getJwtPermissions);
  }

  public getPermissionsFromUser(permissions: string[]): GetPermissionListResultDto {
    console.log(this.authService.sortedPermissions(permissions));
    return this.transformPermissions(this.authService.sortedPermissions(permissions));
  }

  transformPermissions(permissions: string[]): GetPermissionListResultDto {
    const groupsMap: { [key: string]: PermissionGroupDto } = {};

    permissions.forEach(permission => {
      const [parentName, feature, access] = permission.split('.');
      const groupName = parentName;

      if (!groupsMap[groupName]) {
        groupsMap[groupName] = {
          name: groupName,
          displayName: groupName,
          permissions: [],
        };
      }

      if (parentName === feature) {
        // Add the main parent permission
        groupsMap[groupName].permissions.push({
          name: parentName,
          displayName: parentName,
          parentName: null,
          isGranted: true,
          permissionGroupName: groupName,
        });
      }

      // Add parent permission for each distinct second qualifier
      if (
        !groupsMap[groupName].permissions.some(p => p.name === feature && p.parentName === null)
      ) {
        groupsMap[groupName].permissions.push({
          name: feature,
          displayName: feature,
          parentName: null,
          isGranted: true,
          permissionGroupName: groupName,
        });
      }

      groupsMap[groupName].permissions.push({
        name: permission,
        displayName: permission,
        parentName: feature,
        isGranted: true,
        permissionGroupName: groupName,
      });
    });

    return {
      entityDisplayName: 'Permissions',
      groups: Object.values(groupsMap),
    };
  }
}
