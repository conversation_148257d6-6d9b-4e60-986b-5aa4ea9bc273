<!-- Filter Place Holders -->
<mat-card appearance="outlined">
  <form [formGroup]="filterForm" autocomplete="off">
    <div class="row no-gutters">
      <div class="p-2 col-md-5">
        <app-searchbox #searchBoxForm formControlName="searchBoxForm"></app-searchbox>
      </div>
      <div class="p-2 col-lg-2 col-md-2 col-sm-3 d-flex align-items-end">
        <div class="form-group">
          <mat-label>{{ 'stockReports.branch' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <mat-select #branch multiple formControlName="branchId">
              <app-select-check-all [model]="filterForm.get('branchId')" [values]="branch">
              </app-select-check-all>
              <mat-option *ngFor="let branch of branchList" [value]="branch.branchId">
                {{ branch | localized }}</mat-option
              >
            </mat-select>
          </mat-form-field>
        </div>
      </div>
      <div class="p-2 col-lg-2 col-md-2 col-sm-3 d-flex align-items-end">
        <div class="form-group">
          <mat-label>{{ 'stockReports.category' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <mat-select #select multiple formControlName="categoryId">
              <app-select-check-all [model]="filterForm.get('categoryId')" [values]="select">
              </app-select-check-all>
              <mat-option
                *ngFor="let parentCategory of categoryList"
                [value]="parentCategory.categoryId">
                {{ parentCategory | localized }}</mat-option
              >
            </mat-select>
          </mat-form-field>
        </div>
      </div>
      <div class="p-2 col-lg-2 col-md-2 col-sm-3 d-flex align-items-end">
        <div class="form-group">
          <mat-label>{{ 'stockReports.warehouse' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <mat-select #wareHouse multiple formControlName="warehouseId">
              <app-select-check-all [model]="filterForm.get('warehouseId')" [values]="wareHouse">
              </app-select-check-all>
              <mat-option *ngFor="let warehouse of warehouseList" [value]="warehouse.warehouseId">
                {{ warehouse | localized }}</mat-option
              >
            </mat-select>
          </mat-form-field>
        </div>
      </div>
      <div class="p-2 col-lg-2 col-md-2 col-sm-6 d-flex">
        <div class="form-group justify-content-end">
          <mat-checkbox color="primary" formControlName="showDetailed">Show Detailed</mat-checkbox>
        </div>
      </div>
      <div class="p-2 col-lg-4 col-md-4 col-sm-6 d-flex">
        <div class="form-group justify-content-end">
          <mat-radio-group aria-label="Select an option" formControlName="options">
            <mat-radio-button color="primary" value="PDF">PDF</mat-radio-button>
            <mat-radio-button color="primary" value="XLS">XLS</mat-radio-button>
            <mat-radio-button color="primary" value="CSV">CSV</mat-radio-button>
          </mat-radio-group>
        </div>
      </div>
    </div>
    <button
      (click)="getReportData($event); searchBoxForm.markAllAsTouched(); (false)"
      mat-stroked-button
      color="primary">
      {{ 'common.submit' | translate }}
    </button>
    <button class="m-l-10" (click)="clearFilters($event); (false)" mat-stroked-button>
      {{ 'searchPanel.clear' | translate }}
    </button>
  </form>
</mat-card>
<!-- Filter Place Holders -->
