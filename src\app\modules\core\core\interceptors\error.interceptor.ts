import {
  HttpErrorResponse,
  HttpEvent,
  HttpHandler,
  HttpInterceptor,
  HttpRequest,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';
import { BusyService } from '../services/busy.service';

@Injectable()
export class ErrorInterceptor implements HttpInterceptor {
  constructor(
    private busyService: BusyService,
    private toastr: ToastrService,
    private authService: AuthService
  ) {}

  intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    return next.handle(request).pipe(
      catchError((response: HttpErrorResponse) => {
        switch (response.status) {
          case 400:
            if (response.error.message) {
              this.toastr.error(response?.error?.message);
            } else {
              this.toastr.error('Login Failure');
            }
            break;
          case 401:
            this.toastr.error('Authentication Failure', response?.error?.message);
            this.authService.logout(true);
            break;
          case 403:
            this.toastr.error(response?.error?.message);
            break;
          case 404:
            this.toastr.error(response?.error?.message);
            //this.router.navigateByUrl('/not-found');
            break;
          case 422:
            if (response.error.message) {
              this.toastr.error(response?.error?.message);
            }
            break;
          case 500:
            this.toastr.error('Something Went Wrong', response?.error?.message);
            break;
          default:
            this.toastr.error('Something Went Wrong', response?.error?.message);
            break;
        }
        this.busyService.isLoading.next(false);
        return throwError(response.error);
      })
    );
  }
}
