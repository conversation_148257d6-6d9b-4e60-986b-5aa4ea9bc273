import { Directionality } from '@angular/cdk/bidi';
import { SelectionModel } from '@angular/cdk/collections';
import {
  Component,
  ElementRef,
  EventEmitter,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { MatAutocomplete, MatAutocompleteTrigger } from '@angular/material/autocomplete';
import { MatSort } from '@angular/material/sort';
import { MatTable, MatTableDataSource } from '@angular/material/table';
import { Subscription } from 'rxjs';
import { debounceTime, filter, switchMap, tap } from 'rxjs/operators';
import { MatTableKeyboardNavigationDirective } from '../../directives/tablekeynavigator.directive';

@Component({
  template: '',
})
export abstract class BaseSearchSelectionComponent<T> implements OnInit, OnDestroy {
  @ViewChild('userInput') userInput: ElementRef;
  @ViewChild(MatTable) table: MatTable<T>;
  @ViewChild(MatAutocompleteTrigger) autoCompleteTrigger: MatAutocompleteTrigger;
  @ViewChild(MatAutocomplete) autoComplete: MatAutocomplete;
  @ViewChild(MatSort) sort: MatSort;
  @ViewChild(MatTableKeyboardNavigationDirective)
  keyboardNavDirective: MatTableKeyboardNavigationDirective;

  @Output() itemSelected: EventEmitter<T> = new EventEmitter<T>();
  public selection = new SelectionModel<T>();
  public autoCompleteInput: UntypedFormGroup;
  public isLoading = false;
  public resultNotFound = false;
  public itemSource: MatTableDataSource<T>;
  private subscription: Subscription;

  protected abstract fetchItems(value: string): any; // Define the method to fetch items

  protected abstract extractData(searchResult: any): T[];

  protected abstract clearForms(): void;

  constructor(protected formBuilder: UntypedFormBuilder, protected direction: Directionality) {}

  ngOnInit(): void {
    this.autoCompleteInput = this.formBuilder.group({
      userInput: new UntypedFormControl(null),
    });
    this.subscription = this.autoCompleteInput
      .get('userInput')
      .valueChanges.pipe(
        tap(value => {
          if (value && typeof value !== 'object') {
            this.clearForms();
          }
        }),
        debounceTime(500),
        filter(value => this.shouldTriggerSearch(value)),
        tap(() => this.resetSearchResults()),
        switchMap(value => this.fetchItems(value))
      )
      .subscribe(
        (searchResult: T[]) => this.handleSearchResult(searchResult),
        error => this.handleSearchError()
      );
  }

  disbaleInputSearch(): void {
    this.autoCompleteInput.get('userInput').disable();
  }

  setFocusInputSearch(): void {
    this.userInput?.nativeElement.focus();
  }

  private shouldTriggerSearch(value: unknown): boolean {
    if (typeof value === 'string') {
      const trimmedValue = value.trim();

      if (trimmedValue.length >= 3) {
        console.log('search Value', value);
        return true; // Trigger search if length is 3 or more
      } else if (trimmedValue.length === 0) {
        this.clearSelection(); // Clear selection if input is empty
      } else {
        // If length is less than 3 but not empty
        this.itemSource = null; // Reset item source
        this.resultNotFound = false; // Reset result not found flag
        this.autoCompleteTrigger.closePanel(); // Close the autocomplete panel
      }
    }

    return false; // Do not trigger search for non-string values or invalid lengths
  }

  private handleSearchResult(searchResult: T[]): void {
    if (searchResult) {
      const data = this.extractData(searchResult);
      this.itemSource = new MatTableDataSource<T>(data);
      this.resultNotFound = this.itemSource.data.length === 0;
      this.selection = new SelectionModel<T>(true);
    } else {
      this.resetSearchResults();
    }
  }

  private handleSearchError(): void {
    this.itemSource = null;
    this.resultNotFound = true;
    this.isLoading = false;
  }

  private resetSearchResults(): void {
    this.itemSource = null;
    this.resultNotFound = false;
    this.isLoading = false;
  }

  public setFocus() {
    this.userInput.nativeElement.focus();
  }

  onSelection(selectedItem: T) {
    this.autoCompleteInput.get('userInput').patchValue(null, { emitEvent: false });
    this.itemSelected.emit(selectedItem);
    this.clearSelection();
  }

  clearSelection() {
    this.itemSource = null;
    this.autoCompleteInput.get('userInput').patchValue(null, { emitEvent: false });
    this.resultNotFound = false;
    this.autoCompleteTrigger.closePanel();
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
