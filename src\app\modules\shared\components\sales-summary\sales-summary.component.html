<ng-container *ngIf="!showOnlyGrandTotal">
  <table class="summary-table">
    <tr>
      <td class="summary-item">
        <span>{{ 'salesSummary.totalExVatDisc' | translate }}</span>
      </td>
      <td class="summary-item value-column">
        {{ totalExcVatDisc }}
      </td>
      <td class="summary-item">
        <span>{{ 'salesSummary.totalVat' | translate }}</span>
      </td>
      <td class="summary-item value-column">
        {{ totalVat }}
      </td>
    </tr>
    <tr>
      <td class="summary-item">
        <span>{{ 'salesSummary.totalDiscount' | translate }}</span>
      </td>
      <td class="summary-item value-column">
        {{ discount }}
      </td>
      <td class="summary-item">
        <span>{{ 'salesSummary.balanceAmt' | translate }}</span>
      </td>
      <td class="summary-item value-column">
        {{ balanceAmount }}
      </td>
    </tr>
    <tr>
      <ng-container *ngIf="isFractionShown">
        <td class="summary-item">
          <span>{{ 'salesSummary.fractionAmt' | translate }}</span>
        </td>
        <td class="summary-item value-column">
          {{ fractionTotals }}
        </td>
      </ng-container>
      <ng-container *ngIf="ischangeAmountShown">
        <td class="summary-item">
          <span>{{ 'salesSummary.changeAmt' | translate }}</span>
        </td>
        <td class="summary-item value-column">
          {{ changeAmount }}
        </td>
      </ng-container>
    </tr>
    <tr>
      <td class="summary-item">
        <span class="f-s-20 text-primary">{{ 'salesSummary.grandTotal' | translate }}</span>
      </td>
      <td class="summary-item value-column f-s-20 text-primary" colspan="4">
        {{ grandTotal }}
      </td>
    </tr>
  </table>
</ng-container>

<ng-container *ngIf="showOnlyGrandTotal">
  <table class="summary-table">
    <tr>
      <td class="summary-item">
        <span class="f-s-20 text-primary">{{ 'salesSummary.grandTotal' | translate }}</span>
      </td>
      <td class="summary-item value-column f-s-20 text-primary" colspan="4">
        {{ grandTotal | currency : 'USD' : 'symbol' : '1.2-2' : 'en-US' }}
      </td>
    </tr>
  </table>
</ng-container>
