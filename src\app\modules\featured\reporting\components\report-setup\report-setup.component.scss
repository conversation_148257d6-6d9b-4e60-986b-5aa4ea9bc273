// Variables inspired by invoice template
$invoice-border-color: #cccccc;
$invoice-bg-color: #e6f2ff;
$invoice-blue-font: #0056b3;
$border-thickness: 1px;
$margin-sections: 10px;

.main-container {
  padding: 20px;

  .row {
    margin: 0;
  }

  .col-md-6 {
    padding: 0 10px;
  }
}

// Form and Preview Sections Layout
.form-section {
  padding-right: 15px;

  @media (max-width: 767px) {
    padding-right: 10px;
    margin-bottom: 20px;
  }
}

.preview-section {
  padding-left: 15px;
  border-left: 2px solid #e9ecef;

  @media (max-width: 767px) {
    padding-left: 10px;
    border-left: none;
    border-top: 2px solid #e9ecef;
    padding-top: 20px;
  }
}

// Form Section Styling
.form-card {
  margin-bottom: 20px;

  .mat-card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;

    .mat-card-title {
      color: $invoice-blue-font;
    }
  }

  .section-header {
    margin: 20px 0 10px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid $invoice-bg-color;

    h4 {
      color: $invoice-blue-font;
      margin: 0;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }

  mat-label {
    color: #495057;
  }

  .mat-form-field {
    margin-bottom: 5px;

    textarea {
      resize: vertical;
      min-height: 60px;

      &:focus {
        outline: none;
      }
    }
  }
}

// Action Buttons
.action-buttons {
  text-align: center;
  margin-top: 20px;
  padding: 15px;

  button {
    margin: 0 5px;
    min-width: 120px;
  }
}

// Preview Section Styling
.preview-card {
  position: sticky;
  top: 20px;

  @media (max-width: 767px) {
    position: static;
  }

  .mat-card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;

    .mat-card-title {
      color: $invoice-blue-font;
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .header-actions {
    margin-left: auto;

    .a4-preview-button {
      display: flex;
      align-items: center;
      gap: 6px;

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }
}

.preview-container {
  font-family: Arial, sans-serif; // Match actual report font
  font-size: 12px; // Default font size for all content
  color: #333;
  background-color: white;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
  margin-top: 10px;
  min-height: 400px;
  transition: all 0.3s ease;

  // Force LTR direction for consistent preview layout regardless of app direction
  direction: ltr !important;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  @media (max-width: 767px) {
    min-height: auto;
  }
}

// Use exact styling from invoice template
.preview-header {
  margin-bottom: $margin-sections;
}

.header-layout {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border-bottom: $border-thickness solid $invoice-border-color;
  padding-bottom: 10px;
  margin-bottom: $margin-sections;

  // Force LTR layout regardless of app direction
  direction: ltr !important;
}

.address {
  flex: 1;

  h3 {
    color: $invoice-blue-font !important;
    font-size: 16px !important; // Company name font size
  }

  &.english-address {
    padding-right: 15px;
    text-align: left !important;
    direction: ltr !important;
    order: 1; // Always first (left)

    h3 {
      color: $invoice-blue-font;
      margin: 0 0 5px 0;
    }

    p {
      margin: 0 0 3px 0;
      word-break: break-word;
      white-space: normal;
    }
  }

  &.arabic-address {
    padding-left: 15px;
    text-align: right !important;
    direction: rtl !important;
    order: 3; // Always last (right)

    h3 {
      color: $invoice-blue-font;
      margin: 0 0 5px 0;
    }

    p {
      margin: 0 0 3px 0;
      word-break: break-word;
      white-space: normal;
    }
  }

  .company-info {
    font-weight: bold !important;
    color: #333 !important;
  }

  .contact-detail {
    font-weight: normal !important;
    color: #555 !important;
  }
}

.logo {
  width: 100px;
  flex: 0 0 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  order: 2; // Always in the middle

  img {
    width: 80px;
    height: auto;
    max-height: 80px;
    object-fit: contain;
  }
}

.preview-qr-section {
  margin-top: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;

  .qr-text-preview {
    strong {
      color: $invoice-blue-font;
      margin-right: 8px;
    }

    span {
      color: #333;
      white-space: pre-wrap;
      word-break: break-word;
    }
  }
}

.preview-note {
  margin-top: 15px;
  padding: 8px 12px;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;

  p {
    margin: 0;
    color: #856404;
    text-align: center;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .main-container {
    padding: 10px;

    .col-md-6 {
      padding: 0 5px;
      margin-bottom: 20px;
    }
  }

  .header-layout {
    flex-direction: column;
    align-items: center;
    text-align: center;

    // Force LTR direction even on mobile
    direction: ltr !important;

    .address {
      margin-bottom: 10px;
      padding: 0 !important;
      width: 100%;

      &.english-address {
        order: 1; // English first on mobile
        text-align: left !important;
        direction: ltr !important;
      }

      &.arabic-address {
        order: 3; // Arabic last on mobile
        text-align: right !important;
        direction: rtl !important;
      }
    }

    .logo {
      order: 2; // Logo in middle on mobile
      margin-bottom: 10px;
    }
  }
}

// Print Styles
@media print {
  .form-card,
  .action-buttons {
    display: none !important;
  }

  .preview-card {
    box-shadow: none !important;
    border: none !important;
  }

  .preview-container {
    border: none !important;
    padding: 0 !important;
  }
}

// A4 Modal Styles
::ng-deep .a4-preview-modal {
  .mat-mdc-dialog-container {
    font-family: Arial, sans-serif !important;
    padding: 0 !important;
    max-width: none !important;
    max-height: none !important;
    width: 100vw !important;
    height: 100vh !important;
    background-color: #f5f5f5;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .preview-header {
    width: 794px; // A4 width
    height: 1123px; // A4 height
    background-color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 20px;
    box-sizing: border-box;

    // Ensure A4 preview uses same font sizing as regular preview
    font-size: 12px !important;
  }

  // Close button styling
  .a4-close-button {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    background-color: #0056b3 !important;

    mat-icon {
      color: white;
    }
  }
}
