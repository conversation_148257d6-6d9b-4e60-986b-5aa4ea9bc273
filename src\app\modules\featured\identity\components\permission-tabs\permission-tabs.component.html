<mat-tab-group disableRipple="true" mat-stretch-tabs="false" animationDuration="0ms">
  <mat-tab *ngFor="let parent of groupedPermissions | keyvalue">
    <!-- <ng-template mat-tab-label> {{ parent.key }} ({{ getCheckedCount(parent.key) }}) </ng-template> -->
    <ng-template mat-tab-label>
      <span [attr.id]="parent.key"
        >{{ 'identitymodes.' + parent.key | translate }} ({{ getCheckedCount(parent.key) }})</span
      >
    </ng-template>

    <div *ngFor="let child of parent.value | keyvalue">
      <mat-divider class="m-t-10"></mat-divider>
      <mat-card-title class="text-accent">{{ 'feature.' + child.key | translate }}</mat-card-title>
      <mat-checkbox
        *ngIf="!isViewMode"
        [disabled]="disabled"
        [checked]="isAllSelected(parent.key, child.key)"
        [indeterminate]="isIndeterminate(parent.key, child.key)"
        [disableRipple]="true"
        (change)="toggleSelectAll(parent.key, child.key, $event.checked)"
        color="warn">
        {{ 'identitymodes.SelectAll' | translate }}
      </mat-checkbox>
      <div class="flex">
        <div *ngFor="let permission of child.value">
          <mat-checkbox
            [disabled]="isViewMode || disabled"
            [checked]="permission.selected"
            (change)="toggleSelection(permission)"
            color="primary">
            {{ 'identitymodes.' + (permission.permission | lastWord) | translate }}
          </mat-checkbox>
        </div>
      </div>
    </div>
  </mat-tab>
</mat-tab-group>
