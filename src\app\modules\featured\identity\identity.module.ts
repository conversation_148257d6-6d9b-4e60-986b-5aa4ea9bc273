import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { TranslateModule } from '@ngx-translate/core';
import { MaterialModule } from 'src/app/modules/material/material.module';
import { SharedModule } from 'src/app/modules/shared/shared.module';
import { IdentityDashboardComponent } from './components/dashboard/identity-dashboard.component';
import { PermissionComponent } from './components/permission/permission.component';
import { PermissionService } from './services/permission.service';
import { RoleFormComponent } from './components/role/role-form/role-form.component';
import { RoleListComponent } from './components/role/role-list/role-list.component';
import { UserFormComponent } from './components/users/user-form/user-form.component';
import { UserListComponent } from './components/users/user-list/user-list.component';
import { IdentityRoutingModule } from './identity-routing.module';
import { IdentityComponent } from './identity.component';
import { RoleService } from './services/role.service';
import { PermissionTabsComponent } from './components/permission-tabs/permission-tabs.component';

@NgModule({
  declarations: [
    IdentityComponent,
    UserListComponent,
    RoleListComponent,
    UserFormComponent,
    RoleFormComponent,
    IdentityDashboardComponent,
    PermissionComponent,
    UserListComponent,
    UserFormComponent,
    PermissionTabsComponent,
  ],
  imports: [
    CommonModule,
    IdentityRoutingModule,
    SharedModule,
    MaterialModule,
    TranslateModule,
    FlexLayoutModule,
  ],
  providers: [RoleService, PermissionService],
})
export class IdentityModule {}
