import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { forkJoin } from 'rxjs';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { CommonService } from 'src/app/core/api/common.service';
import { StoreParams } from 'src/app/modules/featured/settings/models/storeParams';
import { ProductAdjustmentsParams } from '../../../catalog/models/productAdjustmentParams';
import { ProductService } from '../../../../../core/api/product.service';
import { Document } from '../../models/adjustment-list';
import { StoreService } from 'src/app/core/api/store.service';

@Component({
  selector: 'app-adjustment-list',
  templateUrl: './adjustment-list.component.html',
  styleUrls: ['./adjustment-list.component.scss'],
})
export class AdjustmentListComponent implements OnInit {
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  title: string;
  companyId: string;
  tableData = new MatTableDataSource<Document>();
  products: Document[];
  warehouseList: any;
  submitted = false;
  displayedColumns: string[] = [
    'action',
    'docId',
    'inventoryChkDate',
    'warehouseName',
    'createdBy',
    'isPosted',
  ];
  formTitle: string;
  filterForm: UntypedFormGroup;
  constructor(
    private storeService: StoreService,
    private authService: AuthService,
    private commonService: CommonService,
    private fb: UntypedFormBuilder,
    private productService: ProductService
  ) {}

  ngOnInit(): void {
    this.initializeFilterForm();
    this.formTitle = 'Add Adjustments';
    this.getAllDropDownData();
  }

  ngAfterViewInit(): void {
    if (this.tableData) {
      this.tableData.paginator = this.paginator;
      this.tableData.sort = this.sort;
    }
  }

  initializeFilterForm() {
    this.filterForm = this.fb.group({
      warehouseIds: [[], Validators.required],
    });
  }

  getAllDropDownData() {
    const warehouses = this.storeService.getStores(
      new StoreParams(),
      this.authService.getCompanyID
    );
    forkJoin([warehouses]).subscribe(results => {
      this.warehouseList = results[0];
      if (this.warehouseList.length > 0) {
        this.filterForm.controls['warehouseIds'].setValue(
          this.warehouseList.map(data => data.warehouseId)
        );
      }
      this.getFilterData();
    });
  }

  getFilterData(event?: Event) {
    event?.preventDefault();
    this.filterForm.markAllAsTouched();
    if (this.filterForm.valid) {
      const params: ProductAdjustmentsParams = <ProductAdjustmentsParams>{};
      console.log(this.filterForm.controls);
      params.warehouseIds = this.filterForm.controls['warehouseIds'].value;
      this.productService.getDocumentsAdjustmentsByFilter(params).subscribe(result => {
        setTimeout(() => {
          this.tableData = new MatTableDataSource(result.documents);
          this.tableData.paginator = this.paginator;
          this.tableData.sort = this.sort;
        });
      });
    } else {
      this.commonService.scrollToError();
    }
  }
}
