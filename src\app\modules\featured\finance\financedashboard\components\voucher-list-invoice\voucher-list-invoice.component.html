<app-dialog-header></app-dialog-header>

<div *ngIf="isLoading">
  <mat-spinner class="spinner-wrapper" diameter="30"></mat-spinner>
</div>

<ng-container *ngIf="!isLoading">
  <mat-card appearance="outlined">
    <mat-card-content>
      <mat-card-title>{{ 'voucher.invoiceVouchersListings' | translate }}</mat-card-title>
      <form [formGroup]="voucherForm">
        <div class="row no-gutters">
          <div class="col-md-2 col-lg-2 p-2">
            <mat-label>{{ 'voucher.invoiceNo' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <input type="text" readonly matInput formControlName="invoiceNumber" />
            </mat-form-field>
          </div>
          <div class="col-md-2 col-lg-2 p-2">
            <mat-label>{{ 'voucher.invoiceDate' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <input type="text" matInput readonly formControlName="invoiceDate" />
            </mat-form-field>
          </div>
          <div class="col-md-4 col-lg-4 p-2">
            <mat-label>{{ 'voucher.name' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <input type="text" matInput readonly formControlName="name" />
            </mat-form-field>
          </div>
          <div class="col-md-4 col-lg-4 p-2">
            <mat-label>{{ 'voucher.accountNo' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <input type="text" matInput readonly formControlName="accountNumber" />
            </mat-form-field>
          </div>
        </div>
        <div class="table-responsive">
          <table [dataSource]="dataSource" mat-table matSort>
            <!-- Define columns -->
            <ng-container *ngFor="let col of displayedColumns" [matColumnDef]="col">
              <th *matHeaderCellDef mat-header-cell mat-sort-header>
                {{ 'voucher.' + col | translate }}
              </th>
              <td *matCellDef="let element" mat-cell>
                <ng-container *ngIf="col === 'paymentMethod'; else defaultDisplay">
                  <div class="text-center cursor-pointer rounded bg-light-primary">
                    {{ getPaymentType(element.paymentMethod) | translate }}
                  </div>
                </ng-container>
                <ng-template #defaultDisplay>
                  <!-- Default display for other columns -->
                  {{ element[col] }}
                </ng-template>
              </td>
              <td class="bg-accent" *matFooterCellDef mat-footer-cell>
                {{
                  col === 'voucherDate'
                    ? ('voucher.voucherTotal' | translate)
                    : col !== 'paidAmount'
                    ? ''
                    : total
                }}
              </td>
            </ng-container>

            <!-- Set up table rows -->
            <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
            <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
            <tr *matFooterRowDef="displayedColumns" mat-footer-row></tr>
          </table>
        </div>
      </form>
    </mat-card-content>
  </mat-card>
</ng-container>
