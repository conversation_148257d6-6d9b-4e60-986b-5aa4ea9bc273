import { AriaDescriber, FocusMonitor } from '@angular/cdk/a11y';
import { Directionality } from '@angular/cdk/bidi';
import { Overlay, ScrollDispatcher } from '@angular/cdk/overlay';
import { Platform } from '@angular/cdk/platform';
import { DOCUMENT } from '@angular/common';
import {
  AfterContentChecked,
  Directive,
  ElementRef,
  Inject,
  Input,
  NgZone,
  Optional,
  ViewContainerRef,
} from '@angular/core';
import {
  MAT_TOOLTIP_DEFAULT_OPTIONS,
  MAT_TOOLTIP_SCROLL_STRATEGY,
  MatTooltip,
  MatTooltipDefaultOptions,
} from '@angular/material/tooltip';

@Directive({
  selector: '[appDropDownTooltip]',
})
export class DropDownTooltipDirective extends MatTooltip implements AfterContentChecked {
  private _elementRefDirective: ElementRef;
  @Input() get tooltip(): string {
    return this.message;
  }
  @Input() get class(): string {
    return this.tooltipClass as string;
  }

  set tooltip(value) {
    this.message = value;
  }

  set class(value) {
    this.tooltipClass = value;
  }

  constructor(
    overlay: Overlay,
    elementRef: ElementRef<HTMLElement>,
    scrollDispatcher: ScrollDispatcher,
    viewContainerRef: ViewContainerRef,
    ngZone: NgZone,
    platform: Platform,
    ariaDescriber: AriaDescriber,
    focusMonitor: FocusMonitor,
    @Inject(MAT_TOOLTIP_SCROLL_STRATEGY) scrollStrategy: any,
    @Optional() dir: Directionality,
    @Inject(MAT_TOOLTIP_DEFAULT_OPTIONS) defaultOptions: MatTooltipDefaultOptions,
    @Inject(DOCUMENT) _document: any
  ) {
    super(
      overlay,
      elementRef,
      scrollDispatcher,
      viewContainerRef,
      ngZone,
      platform,
      ariaDescriber,
      focusMonitor,
      scrollStrategy,
      dir,
      defaultOptions,
      _document
    );
    this._elementRefDirective = elementRef;
  }

  ngAfterContentChecked() {
    console.log('drop down tooltip', this._elementRefDirective.nativeElement.innerText);
    const value = this._elementRefDirective.nativeElement.innerText;
    this.tooltip = value;
    this.class = 'max-tooltip-content';
  }
}
