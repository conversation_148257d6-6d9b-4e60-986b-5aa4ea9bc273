import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON>, ViewChild, ViewChildren } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort, Sort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { Branch } from '../../../catalog/models/branch';
import { IReportType } from '../../../catalog/models/reports';
import { forkJoin } from 'rxjs';
import { MatSelect } from '@angular/material/select';
import { ReportService } from '../../../settings/services/report.service';
import { BranchService } from '../../../settings/services/branch.service';
import { CookieService } from 'ngx-cookie-service';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { MultilingualService } from 'src/app/modules/core/core/services/multilingual.service';
import { StoreService } from 'src/app/core/api/store.service';
import { CategoryService } from 'src/app/core/api/category.service';
import { Observable } from 'rxjs';
import { CategoryParams } from '../../../catalog/models/categoryParams';
import { StoreParams } from '../../../settings/models/storeParams';
import { BranchParams } from '../../../settings/models/branchParams';
import * as moment from 'moment';
import { PaginatedFilter } from 'src/app/core/interfaces/PaginatedFilter';
import { PurchaseReportParams } from '../../../settings/models/reportParams';
import {
  invoiceStatus,
  purchaseTransactionTypes,
  settlementStatus,
} from 'src/app/core/configs/dropDownConfig';

@Component({
  selector: 'app-purchase-reports',
  templateUrl: './purchase-reports.component.html',
  styleUrls: ['./purchase-reports.component.scss'],
})
export class PurchaseReportsComponent implements OnInit {
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  dataSource: MatTableDataSource<any>;
  isLoading = true;

  filtersForm: UntypedFormGroup;
  isAdvancedSearchEnabled = false;
  pdfUrl = '';
  //
  purchaseTransactionTypes = purchaseTransactionTypes;
  invoiceStatus = invoiceStatus;
  settlementStatus = settlementStatus;
  templateId: number;
  reportTypeList: IReportType[];
  reportData: unknown[] = [];
  selectedReportType: string;
  selectedReportEndPoint: string;
  selectedJasperReportEndPoint: string;
  displayedColumns: string[] = [];
  allColumns: string[] = [];
  branchList: Branch[];
  totalItems = 0;
  pageSize = 100;
  currentPage = 0;
  totalPages = 0;
  morePages = false;
  isAdvancedSearchVisible = false;
  yearIdList: any;
  allWarehouses: any;
  warehouseList: any;
  categoryList: any;
  filteredYearIdList: any;
  // columnNames: { [key: string]: string } = {
  // accountNumber: 'Account Number',
  // accountNature: 'Account Nature',
  // itemId: 'Item ID',
  // itemName: 'Item Name',
  // itemCode: 'Item Code',
  // nameEnglish: 'Name (English)',
  // nameArabic: 'Name (Arabic)',
  // branchId: 'Branch Id',
  // accountType: 'Account Type',
  // accountGroup: 'Account Group',
  // businessGroup: 'Business Group',
  //};
  loading = true;
  request: any;
  @ViewChildren(MatSelect) matSelects!: QueryList<MatSelect>;
  searchConfigs: any;
  constructor(
    private formBuilder: UntypedFormBuilder,
    private reportService: ReportService,
    private branchService: BranchService,
    private cookieService: CookieService,
    private authService: AuthService,
    private translateService: MultilingualService,
    private storeService: StoreService,
    private categoryService: CategoryService
  ) {}

  ngOnInit(): void {
    this.dataSource = new MatTableDataSource(this.reportData);
    this.dataSource.paginator = this.paginator;
    this.getAllDropDownData();
  }

  filterYearIdForSelectedBranch() {
    this.filteredYearIdList = this.yearIdList
      .filter(year => this.filtersForm.controls['branchId'].value === year.branchId)
      .flatMap(filteredYear => filteredYear.years);
  }

  // Get all dropdowns
  getAllDropDownData(): void {
    const category = this.categoryService.getAllCategories(new CategoryParams());
    const warehouses = this.storeService.getAllStores(new StoreParams());
    const reportTypes = this.reportService.getAllReportType(4);
    const branches = this.branchService.getAllBranchesWithYear(new BranchParams());
    const yearIds = this.authService.getUserBranchesYearId(this.authService.getCompanyID);
    forkJoin([reportTypes, branches, yearIds, warehouses, category]).subscribe(results => {
      this.reportTypeList = results[0];
      this.branchList = results[1];
      this.yearIdList = results[2];
      this.allWarehouses = results[3];
      this.categoryList = results[4];
      this.loading = false;
      this.searchConfigs = this.reportTypeList[0].searchConfigs;
    });
  }
  getReportData(event?: Event, pageEvent?: PaginatedFilter) {
    event?.preventDefault();
    this.filtersForm.markAllAsTouched();
    this.isLoading = true;
    const params: PurchaseReportParams = <PurchaseReportParams>{};
    params.templateId = this.templateId;
    params.reportType = this.selectedReportType;
    params.transactionType = this.filtersForm?.controls['transactionType']?.value;

    params.invoiceStatus = this.filtersForm?.controls['invoiceStatus']?.value;
    params.settlementStatus = this.filtersForm?.controls['settlementStatus']?.value;
    params.branchId = this.filtersForm?.controls['branchId']?.value;
    params.yearId = this.filtersForm?.controls['yearId']?.value;
    params.documentNumber = this.filtersForm.controls['documentNumber']?.value;
    params.searchString = this.filtersForm.controls['searchString']?.value;
    params.warehouseId = this.filtersForm.controls['warehouseIds']?.value;
    params.categoryId = this.filtersForm.controls['categoryIds']?.value;

    if (this.filtersForm?.controls['issueDateFrom']?.value)
      params.issueDateFrom = moment(this.filtersForm.controls['issueDateFrom'].value).format(
        'YYYY-MM-DD'
      );
    if (this.filtersForm?.controls['issueDateTo']?.value)
      params.issueDateTo = moment(this.filtersForm.controls['issueDateTo'].value).format(
        'YYYY-MM-DD'
      );

    // Pagination parameters
    params.page = this.currentPage + 1;
    params.pageSize = this.pageSize;
    params.endPoint = this.selectedReportEndPoint;
    params.jasperEndPoint = this.selectedJasperReportEndPoint;
    const requestPayload = {
      ...(this.request || {}),
      ...params,
    };
    this.request = requestPayload;
    console.log(requestPayload);
    this.reportService
      .getPurchaseReports(requestPayload)
      .subscribe(
        (result: any) => {
          console.log(result);
          this.reportData = result.reportData;
          if (this.displayedColumns.length === 0) {
            this.displayedColumns = this.translateService.updateDisplayedColumns(
              result?.columnsToDisplay
            );
            this.allColumns = this.displayedColumns;
          }
          this.dataSource = new MatTableDataSource(this.reportData);
          // Set page properties
          this.totalItems = result.totalRecordsCount;
          this.totalPages = result.totalPages;
          this.morePages = result.morePages;
          this.selectedReportType = this.request.reportType;
        },
        error => {
          console.log(error);
        }
      )
      .add(() => (this.isLoading = false));
  }

  clearFilters(event: Event) {
    event.preventDefault();
    this.filtersForm.markAsUntouched();
    this.filtersForm.markAsPristine();
    this.filtersForm.reset();
    this.getReportData();
  }

  // getColumnDisplayName(key: string): string {
  //   return this.columnNames[key] || key;
  // }
  downloadPdf() {
    this.request.type = 'PDF';

    this.reportService.getAccountingJasperReports(this.request).subscribe(
      (result: any) => {
        console.log('Success...');
      },
      error => {
        console.log(error);
      }
    );
  }

  toggleAdvancedSearch() {
    this.isAdvancedSearchVisible = !this.isAdvancedSearchVisible;
  }
  onSortChange(sortState: Sort) {
    this.request.sortBy = sortState.active;
    this.request.sortDir = sortState.direction;
    this.request.sortFields = `${sortState.active}:${sortState.direction}`;

    this.getReportData(); // Re-fetch the report data with sorting
  }

  onPageChange(event: PageEvent) {
    this.pageSize = event.pageSize;
    this.currentPage = event.pageIndex;
    this.getReportData();
  }
  getSelectForField(field: string): MatSelect | undefined {
    return this.matSelects?.toArray().find(matSelect => matSelect.ngControl?.name === field);
  }

  subscribeToFormChanges(): void {
    this.filtersForm.get('branchId')!.valueChanges.subscribe(value => {
      this.filterYearIdForSelectedBranch();
    });

    this.filtersForm.valueChanges.subscribe(data => {
      if (data) {
        this.warehouseList = this.allWarehouses.filter(item => {
          return this.filtersForm.get('branchId').value === item.branchId;
        });
      }
    });
  }

  createForm() {
    this.filtersForm = this.formBuilder.group({});
    this.searchConfigs.forEach(config => {
      const validators = config.mandatory ? [Validators.required] : [];
      this.filtersForm.addControl(config.backendParam, this.formBuilder.control(null, validators));
      if (config.type === 'dateRange') {
        this.filtersForm.addControl(
          config.backendParam + 'From',
          this.formBuilder.control(null, validators)
        );
        this.filtersForm.addControl(
          config.backendParam + 'To',
          this.formBuilder.control(null, validators)
        );
      }
    });
    this.subscribeToFormChanges();
    const branchIdFromCookie = +this.cookieService.get('branchId');
    const yearIdFromCookie = +this.cookieService.get('yearId');
    this.filtersForm.patchValue({ branchId: branchIdFromCookie, yearId: yearIdFromCookie });
    this.filterYearIdForSelectedBranch();
  }

  getListForField(field: string) {
    switch (field) {
      case 'branchId':
        return this.branchList;
      case 'yearId':
        return this.filteredYearIdList;
      case 'transactionType':
        return this.purchaseTransactionTypes;
      case 'invoiceStatus':
        return this.invoiceStatus;
      case 'settlementStatus':
        return this.settlementStatus;
      case 'reportType':
        return this.reportTypeList;
      case 'warehouseIds':
        return this.allWarehouses;
      case 'categoryIds':
        return this.categoryList;
      default:
        return [];
    }
  }

  selectReportType(report: any) {
    this.selectedReportType = report.name;
    this.selectedReportEndPoint = report.endPoint;
    this.selectedJasperReportEndPoint = report.endPoint;
    const reportType = this.reportTypeList.find(report => report.name === this.selectedReportType);
    this.templateId = reportType.id;
    this.searchConfigs = reportType.searchConfigs;
    this.createForm();
  }

  resetReportType() {
    this.selectedReportType = null;
    this.reportData = [];
    this.request = {};
  }

  transformReportName(name: string): string {
    return name ? name.replace(/ /g, '') : '';
  }
  updateDisplayedColumns(selectedColumns: string[]) {
    this.displayedColumns = selectedColumns.length ? selectedColumns : this.allColumns;
  }
}
