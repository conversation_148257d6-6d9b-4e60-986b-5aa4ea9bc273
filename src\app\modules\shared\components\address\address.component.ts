import { Component, Input, OnInit } from '@angular/core';
import {
  ControlValueAccessor,
  UntypedFormBuilder,
  UntypedFormGroup,
  NG_VALUE_ACCESSOR,
  Validators,
} from '@angular/forms';
import { CommonService } from 'src/app/core/api/common.service';

@Component({
  selector: 'app-address',
  templateUrl: './address.component.html',
  styleUrls: ['./address.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: AddressComponent,
    },
  ],
})
export class AddressComponent implements OnInit, ControlValueAccessor {
  @Input() noValidators = false;
  addressForm: UntypedFormGroup;
  touched = false;
  constructor(private formBuilder: UntypedFormBuilder, private commonService: CommonService) {}

  ngOnInit() {
    this.addressForm = this.formBuilder.group({
      buildingNumber: [
        null,
        this.noValidators ? [] : [Validators.required, this.commonService.minDigitsValidator(4)],
      ],
      streetName: [null, this.noValidators ? [] : [Validators.required]],
      district: [null, this.noValidators ? [] : [Validators.required]],
      additionalNumber: [null, this.noValidators ? [] : [Validators.required]],
      postalCode: [
        null,
        this.noValidators ? [] : [Validators.required, this.commonService.minDigitsValidator(5)],
      ],
      city: [null, this.noValidators ? [] : [Validators.required]],
      country: [null, this.noValidators ? [] : [Validators.required]],
      address2: [null, this.noValidators ? [] : [Validators.required]],
      shortAddress: [null, this.noValidators ? [] : [Validators.required]],
      addressId: [null],
    });
  }

  markAsTouched() {
    this.onTouched();
    this.touched = true;
  }

  markFormAsTouched() {
    this.addressForm.markAllAsTouched();
  }

  isValid() {
    return this.addressForm.valid;
  }

  onTouched() {}

  writeValue(obj: any): void {
    obj && this.addressForm.setValue(obj, { emitEvent: false });
  }
  registerOnChange(fn: any): void {
    this.addressForm.valueChanges.subscribe(fn);
  }
  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState?(isDisabled: boolean): void {
    isDisabled ? this.addressForm.disabled : this.addressForm.enabled;
  }

  disableForm(): void {
    this.addressForm.disable();
  }
}
