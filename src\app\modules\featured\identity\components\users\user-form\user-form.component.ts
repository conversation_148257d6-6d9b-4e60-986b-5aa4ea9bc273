import { Component, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { CustomValidators } from 'ngx-custom-validators';
import { ToastrService } from 'ngx-toastr';
import { forkJoin } from 'rxjs';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { IRole } from 'src/app/core/interfaces/role';
import { RoleParams } from 'src/app/core/models/params/roleParams';
import { RoleService } from 'src/app/modules/featured/identity/services/role.service';
import { BranchParams } from 'src/app/modules/featured/settings/models/branchParams';
import { StoreParams } from 'src/app/modules/featured/settings/models/storeParams';
import { BranchService } from 'src/app/modules/featured/settings/services/branch.service';
import { UserService } from '../../../../../../core/api/identity/user.service';
import { ActionType } from 'src/app/core/enums/actionType';
import { StoreService } from 'src/app/core/api/store.service';
import { IUser } from 'src/app/core/interfaces/user';

@Component({
  selector: 'app-user-form',
  templateUrl: './user-form.component.html',
  styleUrls: ['./user-form.component.scss'],
})
export class UserFormComponent implements OnInit {
  userForm: UntypedFormGroup;
  formTitle: string;
  branchesList: any;
  defaultBranchesList: any;
  wareHouseList: any;
  wareHouseAll: any;
  rolesList: IRole[];
  loading = true;
  editUserId = '';
  mode: ActionType;
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private fb: UntypedFormBuilder,
    private userService: UserService,
    private branchService: BranchService,
    private authService: AuthService,
    private storeService: StoreService,
    public roleService: RoleService,
    private toastr: ToastrService
  ) {}

  get isEditMode(): boolean {
    return this.mode === ActionType.edit;
  }
  get isViewMode(): boolean {
    return this.mode === ActionType.view;
  }

  get isCreateMode(): boolean {
    return this.mode === ActionType.create;
  }

  ngOnInit(): void {
    this.formTitle = this.route.snapshot.data['title'];
    this.mode = this.route.snapshot.data['mode'];
    this.route.params.subscribe(params => {
      this.editUserId = params['id'];
      if (this.editUserId) {
        this.getDropDownEditData();
      } else {
        this.initProfileForm();
        this.getDropDownData();
      }
    });
  }

  disableFields() {
    this.userForm.get('username').disable();
    //  this.userForm.get('firstName').disable();
    //  this.userForm.get('lastName').disable();
  }

  initProfileForm(data?: IUser): void {
    this.userForm = this.fb.group({
      firstName: [
        data?.firstName ?? null,
        Validators.compose([
          Validators.required,
          Validators.minLength(5),
          Validators.maxLength(10),
        ]),
      ],
      lastName: [
        data?.lastName ?? null,
        Validators.compose([
          Validators.required,
          Validators.minLength(5),
          Validators.maxLength(10),
        ]),
      ],
      emailId: [data?.emailId ?? null, Validators.compose([CustomValidators.email])],
      phoneNumber: [data?.phoneNumber ?? null, Validators.compose([Validators.required])],
      active: [data?.active ?? true, Validators.compose([Validators.required])],
      isPosUser: [data?.isPosUser ?? false, Validators.compose([Validators.required])],
      password: [null, Validators.compose([Validators.minLength(8), Validators.maxLength(8)])],
      username: [
        data?.username ?? null,
        Validators.compose([Validators.required, Validators.maxLength(15)]),
      ],
      roleIds: [
        data?.isPosUser ? data?.roleIds[0] : data?.roleIds ?? null,
        Validators.compose([Validators.required]),
      ],
      branchIds: [
        data?.isPosUser ? data?.branchIds[0] : data?.branchIds ?? null,
        Validators.compose([Validators.required]),
      ],
      warehouseIds: [
        data?.isPosUser ? data?.warehouseIds[0] : data?.warehouseIds ?? null,
        Validators.compose([Validators.required]),
      ],
      defaultBranchId: [data?.defaultBranchId ?? null, Validators.compose([Validators.required])],
    });

    if (this.isEditMode) {
      this.disableFields();
    }
    if (this.isViewMode) {
      this.userForm.disable();
    }
    // update the list for drop down
    if (data) {
      const branchIds = Array.isArray(this.userForm.get('branchIds').value)
        ? this.userForm.get('branchIds').value
        : [this.userForm.get('branchIds').value];

      this.wareHouseList = this.wareHouseAll.filter(data => {
        return branchIds.includes(data.branchId);
      });
    }

    // when ever value changes reset and show updated drop down values for stores
    this.userForm.controls['branchIds'].valueChanges.subscribe(value => {
      this.userForm.get('warehouseIds').setValue(null);

      // Handle both array and single value
      const branchIds = Array.isArray(value) ? value : [value];

      // Filter warehouse list
      this.wareHouseList = this.wareHouseAll.filter(data => {
        return branchIds.includes(data.branchId);
      });
      // update the default branch id also
      if (branchIds.length === 1) {
        // set the first as default
        this.defaultBranchesList = this.branchesList.filter(data => {
          return branchIds.includes(data.branchId);
        });
        this.userForm.get('defaultBranchId').setValue(branchIds[0]);
      } else {
        // admin will select the default branch
        this.defaultBranchesList = this.branchesList.filter(data => {
          return branchIds.includes(data.branchId);
        });
        this.userForm.get('defaultBranchId').setValue(null);
      }
      this.userForm.get('warehouseIds').updateValueAndValidity();
    });
  }

  onUserTypeChange(): void {
    this.userForm.get('roleIds').setValue(null);
    this.userForm.get('branchIds').setValue(null);
    this.userForm.get('warehouseIds').setValue(null);
  }

  getDropDownData(): void {
    const branches = this.branchService.getAllBranches(new BranchParams());
    const warehouses = this.storeService.getAllStores(new StoreParams());
    const roles = this.roleService.getRoles(new RoleParams());
    forkJoin([branches, warehouses, roles]).subscribe(results => {
      this.branchesList = results[0];
      this.defaultBranchesList = results[0];
      this.wareHouseAll = results[1];
      this.rolesList = results[2];
      this.loading = false;
    });
  }

  getDropDownEditData(): void {
    const branches = this.branchService.getAllBranches(new BranchParams());
    const warehouses = this.storeService.getAllStores(new StoreParams());
    const roles = this.roleService.getRoles(new RoleParams());
    const users = this.userService.getUserById(this.editUserId);
    forkJoin([branches, warehouses, roles, users]).subscribe(results => {
      this.branchesList = results[0];
      this.defaultBranchesList = results[0];
      this.wareHouseAll = results[1];
      this.rolesList = results[2];
      this.initProfileForm(results[3]);
      this.loading = false;
    });
  }

  onSubmit() {
    this.userForm.markAllAsTouched();
    if (this.userForm && this.userForm?.valid) {
      const userData = {
        ...this.userForm.value,
        roleIds: this.userForm.get('isPosUser').value
          ? [this.userForm.get('roleIds').value]
          : this.userForm.get('roleIds').value,
        branchIds: this.userForm.get('isPosUser').value
          ? [this.userForm.get('branchIds').value]
          : this.userForm.get('branchIds').value,
        warehouseIds: this.userForm.get('isPosUser').value
          ? [this.userForm.get('warehouseIds').value]
          : this.userForm.get('warehouseIds').value,
      };
      console.log('userData', userData);
      if (!this.isEditMode) {
        this.userForm.get('password').setValue('password');
        this.userService.createUser(userData).subscribe(response => {
          //this.toastr.success('User Created Successfully');
          this.router.navigate(['../'], { relativeTo: this.route });
        });
      } else {
        this.userService.updateUser(userData, this.editUserId).subscribe(response => {
          //this.toastr.success('User Updated Successfully');
          this.router.navigate(['../../'], { relativeTo: this.route });
        });
      }
    }
  }
}
