<form [formGroup]="paymentForm" autocomplete="off">
  <mat-sub-title class="text-error" *ngIf="paymentError">{{
    'paymentsType.paymentSelectionError' | translate
  }}</mat-sub-title>
  <!-- Cash logic -->
  <div class="col-12" [ngClass]="{ 'control-border': paymentForm?.controls['cashType']?.value }">
    <div class="row align-items-center">
      <div class="col-md-2 col-sm-12 m-t-10 m-b-10" *ngIf="isCashPaymentAllowed">
        <mat-slide-toggle
          [disableRipple]="true"
          [labelPosition]="'after'"
          [disabled]="isCashPaymentDisabled"
          matTooltip="{{
            checkifDueDateEnabled() && !singlePaymentsAllowed && !paymentForm.disabled
              ? ('paymentsType.creditType' | translate)
              : ''
          }}"
          formControlName="cashType">
          {{ 'paymentsType.cash' | translate }}
        </mat-slide-toggle>
      </div>
      <ng-container *ngIf="paymentForm?.controls['cashType']?.value">
        <div class="col-md-5 col-sm-12 m-b-3">
          <mat-label>{{ 'paymentsType.cashAccounts' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <mat-select formControlName="cashAccountId">
              <mat-option *ngFor="let accountName of cashAccounts" [value]="accountName.accountId">
                {{ accountName | localized }}
              </mat-option>
            </mat-select>
            <mat-error
              *ngIf="
                paymentForm?.controls['cashAccountId']?.hasError('required') &&
                paymentForm?.controls['cashAccountId']?.touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>

        <div class="col-md-5 col-sm-12 m-b-3">
          <mat-label>{{ 'paymentsType.amount' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input matInput type="number" formControlName="cashAmount" />
            <mat-error
              *ngIf="
                (paymentForm?.controls['cashAmount']?.hasError('required') ||
                  paymentForm?.controls['cashAmount']?.hasError('gt')) &&
                paymentForm?.controls['cashAmount']?.touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
      </ng-container>
    </div>
    <div class="row align-items-center" *ngIf="paymentForm?.controls['cashType']?.value">
      <div class="col-sm-12" *ngIf="showAcceptCheckBox && !hideFractionAllowed">
        <mat-slide-toggle [disableRipple]="true" color="primary" formControlName="halala">
          {{ 'paymentsType.halala' | translate }}
        </mat-slide-toggle>
      </div>
    </div>
  </div>
  <!-- Card logic -->
  <div class="col-12" [ngClass]="{ 'control-border': paymentForm?.controls['cardType']?.value }">
    <div class="row align-items-center">
      <div class="col-md-2 col-sm-12 m-b-10 m-t-10" *ngIf="isCardPaymentAllowed">
        <mat-slide-toggle
          [disableRipple]="true"
          [labelPosition]="'after'"
          [disabled]="isCardPaymentDisabled"
          matTooltip="{{
            checkifDueDateEnabled() && !singlePaymentsAllowed && !paymentForm.disabled
              ? ('paymentsType.creditType' | translate)
              : isCardPaymentDisabled && singlePaymentsAllowed
              ? ('paymentsType.singleOnly' | translate)
              : ''
          }}"
          color="accent"
          formControlName="cardType">
          {{ 'paymentsType.card' | translate }}
        </mat-slide-toggle>
      </div>
      <ng-container *ngIf="paymentForm?.controls['cardType']?.value">
        <div class="col-md-5 col-sm-12 m-b-3">
          <mat-label>{{ 'paymentsType.cardAccounts' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <mat-select formControlName="cardAccountId">
              <mat-option *ngFor="let bankAccount of bankAccounts" [value]="bankAccount.accountId">
                {{ bankAccount | localized }}
              </mat-option>
            </mat-select>
            <mat-error
              *ngIf="
                paymentForm?.controls['cardAccountId']?.hasError('required') &&
                paymentForm?.controls['cardAccountId']?.touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
        <div class="col-md-5 col-sm-12 m-b-3">
          <mat-label>{{ 'paymentsType.amount' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input matInput type="number" formControlName="cardAmount" />
            <mat-error
              *ngIf="
                (paymentForm?.controls['cardAmount']?.hasError('required') ||
                  paymentForm?.controls['cardAmount']?.hasError('gt')) &&
                paymentForm?.controls['cardAmount']?.touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
      </ng-container>
    </div>
  </div>
  <!-- Bank logic -->
  <div class="col-12" [ngClass]="{ 'control-border': paymentForm?.controls['bankType']?.value }">
    <div class="row align-items-center">
      <div class="col-md-2 col-sm-12 m-b-10 m-t-10" *ngIf="isBankPaymentAllowed">
        <mat-slide-toggle
          [disableRipple]="true"
          [labelPosition]="'after'"
          [disabled]="isBankPaymentDisabled"
          matTooltip="{{
            checkifDueDateEnabled()
              ? ('paymentsType.creditType' | translate)
              : isBankPaymentDisabled && singlePaymentsAllowed
              ? ('paymentsType.singleOnly' | translate)
              : ''
          }}"
          color="accent"
          formControlName="bankType">
          {{ 'paymentsType.bank' | translate }}
        </mat-slide-toggle>
      </div>
      <ng-container *ngIf="paymentForm?.controls['bankType']?.value">
        <div class="col-md-5 col-sm-12 m-b-3">
          <mat-label>{{ 'paymentsType.bankAccounts' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <mat-select formControlName="bankAccountId">
              <mat-option *ngFor="let bankAccount of bankAccounts" [value]="bankAccount.accountId">
                {{ bankAccount | localized }}
              </mat-option>
            </mat-select>
            <mat-error
              *ngIf="
                paymentForm?.controls['bankAccountId']?.hasError('required') &&
                paymentForm?.controls['bankAccountId']?.touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
        <div class="col-md-5 col-sm-12 m-b-3">
          <mat-label>{{ 'paymentsType.amount' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input matInput type="number" formControlName="bankAmount" />
            <mat-error
              *ngIf="
                (paymentForm?.controls['bankAmount']?.hasError('required') ||
                  paymentForm?.controls['bankAmount']?.hasError('gt')) &&
                paymentForm?.controls['bankAmount']?.touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
      </ng-container>
    </div>
  </div>
  <!-- Credit logic -->
  <div class="col-12" [ngClass]="{ 'control-border': paymentForm?.controls['creditType']?.value }">
    <div class="row align-items-center">
      <div class="col-md-2 col-sm-12 m-b-10 m-t-10" *ngIf="isCreditPaymentAllowed">
        <mat-slide-toggle
          [disableRipple]="true"
          [labelPosition]="'after'"
          disabled="{{
            (checkifOtherPaymentsEnabled() && !singlePaymentsAllowed && !paymentForm?.disabled) ||
              (isCreditPaymentDisabled && singlePaymentsAllowed)
          }}"
          matTooltip="{{
            checkifOtherPaymentsEnabled() && !singlePaymentsAllowed && !paymentForm?.disabled
              ? ('paymentsType.otherpaymentsSelected' | translate)
              : isCreditPaymentDisabled && singlePaymentsAllowed
              ? ('paymentsType.singleOnly' | translate)
              : ''
          }}"
          color="accent"
          formControlName="creditType">
          {{ 'paymentsType.credit' | translate }}
        </mat-slide-toggle>
      </div>
      <ng-container *ngIf="paymentForm?.controls['creditType']?.value">
        <div class="col-md-5 col-sm-12 m-b-3">
          <mat-label>{{ 'paymentsType.creditDueDate' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input
              [min]="creditDueDate"
              [max]="creditDueDate"
              [matDatepicker]="picker"
              disabled
              matInput
              formControlName="creditDueDate" />
            <mat-datepicker-toggle [for]="picker" matSuffix></mat-datepicker-toggle>
            <mat-datepicker #picker disabled="!paymentForm?.disabled"></mat-datepicker>
            <mat-error
              *ngIf="
                paymentForm?.controls['creditDueDate']?.hasError('required') &&
                paymentForm?.controls['creditDueDate']?.touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
        <div class="col-md-5 col-sm-12 m-b-3" *ngIf="!paymentViewData?.isViewMode">
          <mat-label>{{ 'paymentsType.availableBalance' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input matInput type="text" readonly value="{{ availableBalance }}" />
            <mat-error *ngIf="balanceError">
              {{ 'paymentsType.balanceError' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
      </ng-container>
    </div>
  </div>
</form>
