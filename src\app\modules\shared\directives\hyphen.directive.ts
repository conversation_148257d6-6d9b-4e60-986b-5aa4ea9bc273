/* eslint-disable @typescript-eslint/no-explicit-any */
import { Directive, ElementRef, Input, SimpleChanges } from '@angular/core';

@Directive({
  selector: '[appHyphen]',
})
export class HyphenDirective {
  @Input('data') data: any;

  constructor(private elementRef: ElementRef) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.data) {
      this.updateCellContent();
    }
  }

  private updateCellContent(): void {
    const cellContent =
      this.data !== undefined && this.data !== null && this.data !== '' ? this.data : '-';
    this.elementRef.nativeElement.innerHTML = cellContent;
  }
}
