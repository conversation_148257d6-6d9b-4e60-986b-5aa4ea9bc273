<ng-container *ngIf="!loading">
  <!-- action bar -->
  <app-create-action
    *appHasPermission="['Product.Create', 'AllPermissions']"
    [label]="'productBaicTab.register_product' | translate"></app-create-action>
  <!-- action bar -->

  <mat-card appearance="outlined">
    <mat-card-content>
      <!-- main filter criteria paenl  -->
      <form [formGroup]="filterForm" autocomplete="off">
        <mat-accordion>
          <mat-expansion-panel class="no-padding">
            <mat-expansion-panel-header>
              <mat-panel-title>
                <i-tabler class="icon-16" name="search"></i-tabler
              ></mat-panel-title>
            </mat-expansion-panel-header>
            <mat-card>
              <div class="row">
                <div class="col-12">
                  <div class="row no-gutters">
                    <div class="p-2 col-md-6">
                      <app-searchbox #searchBoxForm formControlName="searchBoxForm"></app-searchbox>
                    </div>
                    <div class="p-2 col-lg-3 col-md-3 col-sm-12 d-flex align-items-end">
                      <div class="form-group">
                        <mat-label>{{ 'productGrid.category' | translate }}</mat-label>
                        <mat-form-field class="w-100">
                          <mat-select #select multiple formControlName="categoryId">
                            <app-select-check-all
                              [model]="filterForm.get('categoryId')"
                              [values]="select">
                            </app-select-check-all>
                            <mat-option
                              *ngFor="let parentCategory of category"
                              [value]="parentCategory.categoryId">
                              {{
                                parentCategory.parentCategoryId
                                  ? (parentCategory | localized) +
                                    '/' +
                                    getParentName(parentCategory.parentCategoryId)
                                  : (parentCategory | localized)
                              }}
                            </mat-option>
                          </mat-select>
                        </mat-form-field>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <button
                  (click)="getFilterData($event); searchBoxForm.markAllAsTouched(); (false)"
                  mat-raised-button
                  color="primary">
                  {{ 'searchPanel.searchString' | translate }}
                </button>
                <button class="m-l-10" (click)="clearFilters($event); (false)" mat-raised-button>
                  {{ 'searchPanel.clear' | translate }}
                </button>
              </div>
            </mat-card>
          </mat-expansion-panel>
        </mat-accordion>
      </form>
      <!-- main filter criteria paenl  -->

      <mat-card-title class="m-t-10 title-container">
        <span>{{ 'productBaicTab.listing' | translate }}</span>
        <div class="button-container">
          <a class="m-l-10" matTooltip="#.Product Selected" mat-icon-button color="primary">
            <i-tabler
              class="icon-12"
              [matBadge]="selection?.selected?.length"
              matBadgeColor="warn"
              matBadgeSize="small"
              matBadgePosition="above after"
              name="check"></i-tabler>
          </a>
          <a class="m-l-10" matTooltip="No. of Active Filters" mat-icon-button color="primary">
            <i-tabler
              class="icon-12"
              [matBadge]="activeFiltersCount"
              matBadgeColor="warn"
              matBadgeSize="small"
              matBadgePosition="above after"
              name="filter"></i-tabler>
          </a>
          <ng-container>
            <a
              class="m-l-10"
              *appHasPermission="['Product.BulkEdit', 'AllPermissions']"
              [disabled]="!selection?.selected.length"
              (click)="onBulkEditClick($event)"
              matTooltip="Bulk Edit"
              mat-icon-button
              color="primary">
              <i-tabler class="icon-12" name="settings"></i-tabler>
            </a>
          </ng-container>
          <!-- <ng-container>
        <a
          class="m-l-10"
          *appHasPermission="['Product.Delete', 'AllPermissions']"
          [disabled]="!selection?.selected.length"
          (click)="deleteProducts($event); (false)"
          matTooltip="Delete Products"
          mat-icon-button
          color="primary">
          <i-tabler class="icon-12" name="trash"></i-tabler>
        </a>
      </ng-container> -->
          <a
            class="m-l-10"
            (click)="clearFilters($event); (false)"
            matTooltip="Refresh"
            mat-icon-button
            color="primary">
            <i-tabler class="icon-12" name="refresh"></i-tabler>
          </a>
          <a
            class="m-l-10"
            *ngIf="activeFiltersCount > 0"
            (click)="clearFilters($event); (false)"
            matTooltip="Clear Filters"
            mat-icon-button
            color="primary">
            <i-tabler class="icon-12" name="filter-x"></i-tabler>
          </a>
        </div>
      </mat-card-title>

      <!-- <mat-card-title>Product List</mat-card-title> -->
      <div class="table-responsive">
        <table class="w-100" [dataSource]="productList" mat-table matSort>
          <ng-container matColumnDef="itemCode">
            <th *matHeaderCellDef mat-header-cell mat-sort-header>
              {{ 'productGrid.itemCode' | translate }}
            </th>
            <td
              *matCellDef="let element"
              [data]="element.itemCode"
              [copyValue]="element.itemCode"
              mat-cell
              appHyphen
              appCopyClick>
              {{ element.itemCode }}
            </td>
          </ng-container>

          <ng-container matColumnDef="nameArabic">
            <th *matHeaderCellDef mat-header-cell mat-sort-header>
              {{ 'productGrid.nameArabic' | translate }}
            </th>
            <td
              *matCellDef="let element"
              [data]="element.nameArabic"
              [copyValue]="element.nameArabic"
              mat-cell
              appCopyClick
              appHyphen>
              {{ element.nameArabic }}
            </td>
          </ng-container>

          <ng-container matColumnDef="nameEnglish">
            <th *matHeaderCellDef mat-header-cell mat-sort-header>
              {{ 'productGrid.nameEnglish' | translate }}
            </th>
            <td
              *matCellDef="let element"
              [data]="element.nameEnglish"
              [copyValue]="element.nameEnglish"
              mat-cell
              appCopyClick
              appHyphen>
              {{ element.nameEnglish }}
            </td>
          </ng-container>

          <ng-container matColumnDef="partNumber">
            <th *matHeaderCellDef mat-header-cell mat-sort-header>
              {{ 'productGrid.partNumber' | translate }}
            </th>
            <td
              *matCellDef="let element"
              [data]="element.partNumber"
              [copyValue]="element.partNumber"
              mat-cell
              appHyphen
              appCopyClick>
              {{ element.partNumber }}
            </td>
          </ng-container>

          <ng-container matColumnDef="category">
            <th *matHeaderCellDef mat-header-cell mat-sort-header>
              {{ 'productGrid.category' | translate }}
            </th>
            <td *matCellDef="let element" [data]="element.category" mat-cell appHyphen>
              {{ element.category }}
            </td>
          </ng-container>

          <ng-container matColumnDef="vat">
            <th *matHeaderCellDef mat-header-cell mat-sort-header>
              {{ 'productGrid.vat' | translate }}
            </th>
            <td *matCellDef="let element" [data]="element.vat" mat-cell appHyphen>
              {{ element.vat }}
            </td>
          </ng-container>

          <ng-container matColumnDef="action">
            <th *matHeaderCellDef mat-header-cell>{{ 'productGrid.action' | translate }}</th>
            <td *matCellDef="let element" mat-cell>
              <button
                class="d-flex justify-content-center"
                [matMenuTriggerFor]="menu1"
                mat-icon-button>
                <i-tabler class="icon-20 d-flex" name="dots-vertical"></i-tabler>
              </button>
              <mat-menu class="cardWithShadow" #menu1="matMenu">
                <button
                  *appHasPermission="['Product.Update', 'AllPermissions']"
                  [routerLink]="['edit', element.itemId]"
                  (click)="$event.stopPropagation(); $event.preventDefault()"
                  mat-menu-item>
                  <div class="d-flex align-items-center">
                    <i-tabler class="icon-16 m-r-4" name="edit"></i-tabler>
                    <span>{{ 'common.buttons.edit' | translate }}</span>
                  </div>
                </button>
                <button
                  *appHasPermission="['Product.View', 'AllPermissions']"
                  [routerLink]="['view', element.itemId]"
                  (click)="$event.stopPropagation(); $event.preventDefault()"
                  mat-menu-item>
                  <div class="d-flex align-items-center">
                    <i-tabler class="icon-16 m-r-4" name="eye"></i-tabler>
                    <span>{{ 'common.buttons.view' | translate }}</span>
                  </div>
                </button>
              </mat-menu>
            </td>
          </ng-container>

          <!-- Checkbox Column -->
          <ng-container matColumnDef="select">
            <th *matHeaderCellDef mat-header-cell>
              <mat-checkbox
                [checked]="isAllSelected()"
                [indeterminate]="selection.hasValue() && !isAllSelected()"
                (change)="masterToggle()"
                color="primary">
              </mat-checkbox>
            </th>
            <td *matCellDef="let row" mat-cell>
              <mat-checkbox
                [checked]="isChecked(row)"
                (click)="$event.stopPropagation()"
                (change)="$event.checked ? checked(row) : unChecked(row)"
                color="primary">
              </mat-checkbox>
            </td>
          </ng-container>
          <tr class="mat-row" *matNoDataRow>
            <td class="text-center" [attr.colspan]="displayedColumns.length">
              {{ 'common.noDataFound' | translate }}
            </td>
          </tr>
          <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
          <tr
            *matRowDef="let row; columns: displayedColumns"
            (click)="$event.preventDefault(); $event.stopPropagation()"
            mat-row></tr>
        </table>
      </div>
      <mat-paginator
        [length]="totalItems"
        [pageSizeOptions]="pageSizeOptions"
        [pageSize]="pageSize"
        [pageIndex]="pageIndex"
        (page)="onPageChange($event)"
        showFirstLastButtons>
      </mat-paginator>
    </mat-card-content>
  </mat-card>
</ng-container>
