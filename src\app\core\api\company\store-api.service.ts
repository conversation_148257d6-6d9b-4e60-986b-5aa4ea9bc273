import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../../../environments/environment';
import { WareHouse } from 'src/app/modules/featured/catalog/models/store';

@Injectable({
  providedIn: 'root',
})
export class StoreApiService {
  baseUrl = environment.apiUrl + 'company/warehouses';

  constructor(private http: HttpClient) {}

  getAlls(params: HttpParams, companyId: string) {
    return this.http.get(this.baseUrl + `?companyId=${companyId}`, { params: params });
  }

  getById(params: HttpParams, storeID: string) {
    return this.http.get(this.baseUrl + '/' + storeID, { params: params });
  }

  create(params: HttpParams, store: any) {
    return this.http.post(this.baseUrl, store, { params: params });
  }

  update(params: HttpParams, storeId: string, store: any) {
    return this.http.put(this.baseUrl + '/' + storeId, store, { params: params });
  }

  getBrancheStores(params: HttpParams) {
    return this.http.get<WareHouse[]>(this.baseUrl, { params: params });
  }

  getAllStores(params: HttpParams) {
    return this.http.get<WareHouse[]>(this.baseUrl + `/all`, { params: params });
  }

  deleteStore(params: HttpParams, id: string) {
    return this.http.delete(this.baseUrl + '/' + id, { params: params });
  }

  importStore(params: HttpParams, formData: FormData) {
    return this.http.post(this.baseUrl + '/import', formData, {
      withCredentials: true,
      responseType: 'arraybuffer',
      params: params,
    });
  }
}
