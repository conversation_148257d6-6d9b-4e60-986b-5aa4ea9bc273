import { HttpParams } from '@angular/common/http';
import { FormGroup } from '@angular/forms';
import * as moment from 'moment';

/**
 * Converts a date to the backend format (YYYY-MM-DD)
 * @param date Date object or string to convert
 * @returns Formatted date string or empty string if input is invalid
 */
export function convertDateForBE(date: Date | string | null): string {
  if (date === null || date === undefined) {
    return '';
  }

  try {
    // Handle both Date objects and string dates
    const momentDate = moment(date);

    // Check if the date is valid
    if (momentDate.isValid()) {
      return momentDate.format('YYYY-MM-DD');
    }

    return '';
  } catch (error) {
    console.error('Error converting date:', error);
    return '';
  }
}

/**
 * Creates HttpParams from an object, handling dates and other types appropriately
 * @param filters Object containing key-value pairs to convert to HttpParams
 * @returns HttpParams object with all parameters
 */
export function createParamsFromObject(filters: object): HttpParams {
  let params = new HttpParams();

  for (const [key, value] of Object.entries(filters).sort()) {
    if (value !== undefined && value !== null) {
      // Special handling for id and active fields
      if (key === 'id' || key === 'active') {
        params = params.append(key, String(value));
      }
      // Special handling for search-related fields - never treat as dates
      else if (
        key.toLowerCase().includes('search') ||
        key.toLowerCase() === 'searchstring' ||
        key.toLowerCase() === 'searchtype'
      ) {
        params = params.append(key, String(value));
      }
      // Handle actual Date objects
      else if (value instanceof Date) {
        params = params.append(key, convertDateForBE(value));
      }
      // Handle string values that might be dates
      else if (typeof value === 'string') {
        // Use our utility function to determine if this is likely a date
        if (isLikelyDate(value)) {
          params = params.append(key, convertDateForBE(value));
        } else {
          // Not a date, just a string (including numeric strings like "2003")
          params = params.append(key, value);
        }
      }
      // Handle all other types
      else {
        params = params.append(key, String(value));
      }
    }
  }

  return params;
}

/**
 * Determines if a string should be treated as a date
 * @param value String to check
 * @returns True if the string should be treated as a date, false otherwise
 */
export function isLikelyDate(value: string): boolean {
  if (!value) return false;

  // If it's just digits (like "2003"), it's not a date
  if (/^\d+$/.test(value)) return false;

  // Check for date separators
  const hasDateSeparators = /[-/.]/.test(value);
  if (!hasDateSeparators) return false;

  // Check if it's a valid date format
  return moment(value, moment.ISO_8601, true).isValid();
}

/**
 * Extracts form values excluding the searchBoxForm
 * @param form Form to extract values from
 * @returns Object with form values excluding searchBoxForm
 */
export function getFormValueExcludeSearchBox(form: FormGroup) {
  const filterFormWithoutSearchBox: { [key: string]: unknown } = {};
  for (const key in form.controls) {
    if (key !== 'searchBoxForm') {
      filterFormWithoutSearchBox[key] = form.controls[key].value;
    }
  }
  return filterFormWithoutSearchBox;
}
