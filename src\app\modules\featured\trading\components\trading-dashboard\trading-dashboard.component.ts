import { Component, OnInit } from '@angular/core';
import { DashboardModulesHolder } from 'src/app/modules/shared/components/dashboard-modules-holder/modulesHolder';

@Component({
  selector: 'app-trading-dashboard',
  templateUrl: './trading-dashboard.component.html',
  styleUrls: ['./trading-dashboard.component.scss'],
})
export class TradingDashboardComponent implements OnInit {
  productModulesList: DashboardModulesHolder[] = [
    {
      moduleName: 'navigationMenus.sales',
      moduleDescription: 'Manage Sales.',
      modulePermission: ['Sales', 'AllPermissions'],
      moduleImage: 'store',
      moduleRouterLink: '../sales',
      moduleButtonAction: 'POS',
      moduleType: 'subModule',
    },
    {
      moduleName: 'navigationMenus.customers',
      moduleDescription: 'Manage Purchases.',
      modulePermission: ['Customer', 'AllPermissions'],
      moduleImage: 'storefront',
      moduleRouterLink: '../customers',
      moduleButtonAction: 'BackOffice',
      moduleType: 'subModule',
    },
    {
      moduleName: 'navigationMenus.suppliers',
      moduleDescription: 'Manage Purchases.',
      modulePermission: ['Supplier', 'AllPermissions'],
      moduleImage: 'storefront',
      moduleRouterLink: '../suppliers',
      moduleButtonAction: 'BackOffice',
      moduleType: 'subModule',
    },
    {
      moduleName: 'navigationMenus.distributors',
      moduleDescription: 'Manage Purchases.',
      modulePermission: ['Distributor', 'AllPermissions'],
      moduleImage: 'storefront',
      moduleRouterLink: '../distributors',
      moduleButtonAction: 'BackOffice',
      moduleType: 'subModule',
    },
    {
      moduleName: 'navigationMenus.quotations',
      moduleDescription: 'Manage Purchases.',
      modulePermission: ['Quotation', 'AllPermissions'],
      moduleImage: 'storefront',
      moduleRouterLink: '../quotations',
      moduleButtonAction: 'BackOffice',
      moduleType: 'subModule',
    },
    {
      moduleName: 'navigationMenus.purchases',
      moduleDescription: 'Manage Purchases.',
      modulePermission: ['Purchase', 'AllPermissions'],
      moduleImage: 'storefront',
      moduleRouterLink: '../purchases',
      moduleButtonAction: 'BackOffice',
      moduleType: 'subModule',
    },
  ];
  constructor() {}

  ngOnInit(): void {}
}
