import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { MaterialModule } from 'src/app/modules/material/material.module';
import { SharedModule } from '../../shared/shared.module';
import { HomeRoutingModule } from './home-routing.module';
import { ProductModuleService } from './product-modules/product-module.service';
import { ProductModulesComponent } from './product-modules/product-modules.component';

@NgModule({
  declarations: [ProductModulesComponent],
  imports: [CommonModule, HomeRoutingModule, MaterialModule, SharedModule, FlexLayoutModule],
  providers: [ProductModuleService],
})
export class HomeModule {}
