import { Component, OnInit, ViewChild, Optional, Inject } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { fork<PERSON>oin } from 'rxjs';
import { PaginatedFilter } from 'src/app/core/interfaces/PaginatedFilter';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { SearchboxComponent } from 'src/app/modules/shared/components/searchbox/searchbox.component';
import { Branch } from '../../../catalog/models/branch';
import { BulkEditData } from '../../../catalog/models/bulkedit';
import { Category } from '../../../catalog/models/category';
import { CategoryParams } from '../../../catalog/models/categoryParams';
import { CategoryService } from '../../../../../core/api/category.service';
import { BranchParams } from '../../../settings/models/branchParams';
import { StoreParams } from '../../../settings/models/storeParams';
import { BranchService } from '../../../settings/services/branch.service';
import { ReportService } from '../../../settings/services/report.service';
import { UnitService } from 'src/app/core/api/unit.service';
import { StoreService } from 'src/app/core/api/store.service';

@Component({
  selector: 'price-report',
  templateUrl: './price-report.component.html',
  styleUrls: ['./price-report.component.scss'],
})
export class PriceReportComponent implements OnInit {
  @ViewChild('searchBoxForm') searchBoxForm: SearchboxComponent;
  loading = true;
  categoryList: Category[];
  branchList: Branch[];
  warehouseList: any;
  allWarehouses: any;
  unitList: any;
  filterForm: UntypedFormGroup;
  isReportPulling = false;
  isAdvancedSearchEnabled = false;
  isBranchNotSelected = true;
  constructor(
    @Optional() @Inject(MAT_DIALOG_DATA) public data: BulkEditData,
    private branchService: BranchService,
    private storeService: StoreService,
    private categoryService: CategoryService,
    private authService: AuthService,
    private reportService: ReportService,
    private fb: UntypedFormBuilder,
    private unitApiService: UnitService
  ) {
    this.filterForm = this.fb.group({
      categoryId: [[]],
      branchId: [[]],
      warehouseId: [[]],
      unitId: [[]],
      searchBoxForm: [],
      type: [],
      showGrouped: false,
      options: ['PDF'],
    });

    this.filterForm.valueChanges.subscribe(data => {
      if (data) {
        this.warehouseList = this.allWarehouses.filter(data => {
          return this.filterForm.get('branchId').value.includes(data.branchId);
        });
      }
      if (data && data.branchId.length > 0) {
        this.isBranchNotSelected = false;
      } else {
        this.isBranchNotSelected = true;
      }
    });
  }

  ngOnInit(): void {
    this.loading = true;

    this.getAllDropDownData();
  }

  onBranchChange() {
    this.filterForm.get('warehouseId').setValue([[]]);
  }
  getAllDropDownData() {
    const category = this.categoryService.getAllCategories(new CategoryParams());
    const branches = this.branchService.getAllBranches(new BranchParams());
    const warehouses = this.storeService.getAllStores(new StoreParams());
    const units = this.unitApiService.getAllUnits();
    forkJoin([category, branches, warehouses, units]).subscribe(results => {
      this.categoryList = results[0];
      this.branchList = results[1];
      this.allWarehouses = results[2];
      this.unitList = results[3];
      this.loading = false;
    });
  }

  getReportData(event?: Event, pageEvent?: PaginatedFilter) {
    console.log(this.searchBoxForm.searchBoxForm.controls['searchString'].value);
    event?.preventDefault();
    this.filterForm.markAllAsTouched();
    if (this.filterForm.valid && this.searchBoxForm.isValid) {
      const params: any = <any>{};
      params.categoryId = this.filterForm.controls['categoryId'].value;
      params.warehouseId = this.filterForm.controls['warehouseId'].value;
      params.branchId = this.filterForm.controls['branchId'].value;
      params.unitId = this.filterForm.controls['unitId'].value;
      params.type = this.filterForm.controls['options'].value;
      params.showGrouped = this.filterForm.controls['showGrouped'].value;
      params.searchType = this.searchBoxForm.searchBoxForm.controls['searchType'].value;
      params.searchString = this.searchBoxForm.searchBoxForm.controls['searchString'].value;
      this.reportService
        .getPriceReports(params)
        .subscribe(
          result => {
            console.log('Success....');
          },
          error => {
            console.log(error);
          }
        )
        .add(() => (this.isReportPulling = false));
    }
  }

  clearFilters(event: Event) {
    event.preventDefault();
    this.filterForm.markAsUntouched();
    this.filterForm.markAsPristine();
    this.filterForm.reset();
    this.searchBoxForm.resetSearchBox();
  }
}
