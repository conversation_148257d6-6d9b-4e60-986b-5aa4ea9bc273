import { Directionality } from '@angular/cdk/bidi';
import { SelectionModel } from '@angular/cdk/collections';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { UserService } from '../../../../../../core/api/identity/user.service';
import { IUser } from '../../../../../../core/interfaces/user';
import { StoreParams } from '../../../models/storeParams';
import { DeleteConfirmationComponent } from 'src/app/modules/shared/components/delete-confirmation/delete-confirmation.component';
import { StoreService } from 'src/app/core/api/store.service';
import { CommonService } from 'src/app/core/api/common.service';

@Component({
  selector: 'app-stores-list',
  templateUrl: './stores-list.component.html',
  styleUrls: ['./stores-list.component.scss'],
})
export class StoresListComponent implements OnInit {
  @ViewChild('searchInput') searchInput: ElementRef;
  @ViewChild('filter') filter: ElementRef;
  @ViewChild(MatPaginator) set matPaginator(paginator: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = paginator;
    }
  }
  @ViewChild(MatSort) set matSort(sort: MatSort) {
    if (this.dataSource) {
      this.dataSource.sort = sort;
    }
  }
  stores: IUser[];
  displayedColumns: string[];
  dataSource: MatTableDataSource<any[]>;
  selection = new SelectionModel<any>(true, []);
  storeParams = new StoreParams();
  isLoading = true;
  companyId: string;
  formTitle: string;
  constructor(
    private authService: AuthService,
    private storeService: StoreService,
    public dialog: MatDialog,
    public userService: UserService,
    private toastr: ToastrService,
    private direction: Directionality,
    private route: ActivatedRoute,
    private commonService: CommonService
  ) {}

  ngOnInit(): void {
    this.formTitle = this.route.snapshot.data['title'];
    this.companyId = this.authService.getCompanyID;
    this.getStores();
    this.initColumns();
  }

  getStores(): void {
    this.storeService.getStores(this.storeParams, this.companyId).subscribe(result => {
      this.stores = result;
      this.dataSource = new MatTableDataSource<any>(this.stores);
    });
  }

  initColumns(): void {
    this.displayedColumns = [
      'action',
      'branchNameArabic',
      'branchNameEnglish',
      'nameArabic',
      'nameEnglish',
      'phoneNumber',
      'emailId',
    ];
  }

  applyFilter(filterValue: string): void {
    filterValue = filterValue.trim();
    filterValue = filterValue.toLowerCase();
    this.dataSource.filter = filterValue;
  }

  filterParams(): StoreParams {
    this.storeParams.searchString = this.filter.nativeElement.value.toLocaleLowerCase();
    this.storeParams.pageNumber = 0;
    this.storeParams.pageSize = 0;
    return this.storeParams;
  }

  clearSearchInput() {
    this.searchInput.nativeElement.value = '';
    this.getStores();
  }

  deleteStore(warehouseId: string) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.panelClass = 'green_theme';
    dialogConfig.minWidth = '600px';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(DeleteConfirmationComponent, dialogConfig);
    dialogRef.componentInstance.accepted.subscribe(result => {
      console.log('delete itemcode', result);
      this.storeService.deleteStore(warehouseId).subscribe(() => {
        //this.toastr.success('Warehouse deleted Successfully');
        this.commonService.playSuccessSound();
        this.getStores();
      });
    });
  }
}
