import { Component, OnInit, ViewChild, Inject } from '@angular/core';
import {
  FormControl,
  FormGroup,
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { MatInput } from '@angular/material/input';
import { ActivatedRoute, Router } from '@angular/router';
import { DOCUMENT } from '@angular/common';
import { Token } from 'src/app/core/models/identity/token';
import { CookieRequest } from 'src/app/modules/core/core/models/cookieRequest';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { LocalStorageService } from 'src/app/modules/core/core/services/local-storage.service';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { ILoginBranches, IYear } from 'src/app/modules/core/core/models/login';
import { CookieService } from 'ngx-cookie-service';
import { MultilingualService } from 'src/app/modules/core/core/services/multilingual.service';
import { CommonService } from 'src/app/core/api/common.service';

export interface LoginForm {
  username: FormControl<string>;
  password: FormControl<string>;
  tenantId: FormControl<string | null>;
  userType: FormControl<boolean | null>;
}

@UntilDestroy()
@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
})
export class LoginComponent implements OnInit {
  @ViewChild('username', { static: true }) username: MatInput;
  @ViewChild('branch', { static: true }) branch: MatInput;
  showBranchSelectionForm = false;
  form: FormGroup<LoginForm>;
  branchId: UntypedFormControl = new UntypedFormControl(null, Validators.required);
  yearId: UntypedFormControl = new UntypedFormControl(null, Validators.required);
  returnUrl: string;
  isBeingLoggedIn = false;
  passwordhide = true;
  confirmPasswordhide = true;
  token: Token;
  branchList: ILoginBranches[];
  yearList: IYear[];
  preferredLanguage = 'en'; // Default to English

  constructor(
    private authService: AuthService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private localStorage: LocalStorageService,
    private cookieService: CookieService,
    private multilingualService: MultilingualService,
    private commonService: CommonService,
    @Inject(DOCUMENT) private document: Document
  ) {}

  ngOnInit(): void {
    // SIMPLE: Set body direction to LTR when login component loads
    this.document.body.setAttribute('dir', 'ltr');

    this.initializeForm();
    this.returnUrl = this.activatedRoute.snapshot.queryParams['returnUrl'] || '/dashboard';
    this.localStorage.clear();
    this.branchId.valueChanges.subscribe(branchChange => {
      this.yearList = this.branchList
        .filter((data: ILoginBranches) => data.branchId === branchChange)
        .map((data2: ILoginBranches) => data2.years)[0];
      this.yearId.setValue(null);
    });

    // Check for saved language preference
    const savedLanguage = this.cookieService.get('login_language');
    if (savedLanguage) {
      this.setPreferredLanguage(savedLanguage);
    }
  }

  /**
   * Sets the preferred language for the login screen
   * This only affects the login screen, not the entire application
   */
  setPreferredLanguage(lang: string): void {
    this.preferredLanguage = lang;

    // Save preference to cookie
    this.cookieService.set('login_language', lang, 30); // 30 days expiry

    // SIMPLE: Always keep body direction as LTR for login
    this.document.body.setAttribute('dir', 'ltr');
  }

  initializeForm() {
    this.form = new FormGroup<LoginForm>({
      username: new FormControl<string>('', Validators.required),
      password: new FormControl<string>('', Validators.required),
      tenantId: new FormControl<string | null>(null, Validators.required),
      userType: new FormControl<boolean>(true),
    });
    this.subscribeToFormChanges();
  }

  subscribeToFormChanges(): void {
    // userType toggle changes
    this.form.controls.userType.valueChanges.subscribe(value => {
      const tenantIdControl = this.form.controls.tenantId;
      if (value) {
        tenantIdControl.setValidators(Validators.required);
      } else {
        this.form.markAsUntouched();
        tenantIdControl.setValue(null);
        tenantIdControl.clearValidators();
      }
      tenantIdControl.updateValueAndValidity();
    });
  }

  onSubmit() {
    this.form.markAllAsTouched();
    if (this.form.valid) {
      this.isBeingLoggedIn = true;
      this.form.disable();
      // decide the api call
      this.loginforTenant();
    } else {
      this.commonService.playErrorSound();
    }
  }

  loginforTenant(): void {
    this.authService
      .login(this.form.getRawValue())
      .pipe(untilDestroyed(this))
      .subscribe(
        result => {
          this.getUserBranchesYearId();
          this.token = result;
          this.isBeingLoggedIn = true;
        },
        error => {
          this.form.enable();
        }
      )
      .add(() => (this.isBeingLoggedIn = false));
  }

  getUserBranchesYearId(): void {
    this.authService.getUserBranchesYearId(this.authService.getCompanyID).subscribe({
      next: (branches: ILoginBranches[]) => {
        if (branches.length === 1) {
          this.branchList = branches;
          // check if years are multiple
          if (branches[0].years.length > 1) {
            this.showBranchSelection(branches);
          } else {
            this.showBranchSelectionForm = false;
            this.branchId.setValue(branches[0].branchId);
            this.yearId.setValue(branches[0].years[0]);
            this.onSubmitBranchSelection();
          }
        } else {
          this.branchList = branches;
          this.showBranchSelection(branches);
        }
        this.isBeingLoggedIn = false;

        // Store branches data for later use
        this.localStorage.setItem('userBranches', JSON.stringify(branches));
      },
      error: (error: unknown) => {
        console.log(error);
      },
    });
  }

  showBranchSelection(result): void {
    this.branchList = result;
    this.branchId.setValue(result[0].branchId);
    this.yearId.setValue(result[0].years[0]);
    this.yearList = this.branchList
      .filter((data: ILoginBranches) => data.branchId === this.branchId.value)
      .map((data2: ILoginBranches) => data2.years)[0];
    this.showBranchSelectionForm = true;
  }

  onSubmitBranchSelection(event?: Event) {
    event?.stopPropagation();
    // save in storage
    this.localStorage.setItem('currentBranchId', this.branchId.value);
    this.localStorage.setItem(
      'currentBranch',
      this.branchList
        .filter((data: ILoginBranches) => data.branchId === this.branchId.value)
        .map((data2: ILoginBranches) => data2.branchName)
        .toString()
    );
    this.localStorage.setItem('yearId', this.yearId.value['id']);
    this.localStorage.setItem('year', this.yearId.value['year']);
    const cookieRequest: CookieRequest[] = [];
    const branch: CookieRequest = <CookieRequest>{};
    const years: CookieRequest = <CookieRequest>{};
    (branch.name = 'branchId'), (branch.value = this.branchId.value);
    years.name = 'yearId';
    years.value = this.yearId.value['id'];
    cookieRequest.push(branch);
    cookieRequest.push(years);

    this.authService.loginforBranchandYear(cookieRequest).subscribe(
      () => {
        this.authService.navigateToDashBoard();
        this.router.navigateByUrl(this.returnUrl);
      },
      error => {
        console.log(error);
      }
    );
  }

  openInvoiceDemo(invoiceId: string): void {
    this.commonService.openInvoiceDemoLegacy(invoiceId);
  }
}
