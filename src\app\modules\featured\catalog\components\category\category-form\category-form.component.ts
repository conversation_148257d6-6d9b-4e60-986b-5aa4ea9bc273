import { Component, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { Category } from '../../../models/category';
import { CategoryParams } from '../../../models/categoryParams';
import { CategoryService } from '../../../../../../core/api/category.service';
import { CommonService } from 'src/app/core/api/common.service';

@Component({
  selector: 'app-category-form',
  templateUrl: './category-form.component.html',
  styleUrls: ['./category-form.component.scss'],
})
export class CategoryFormComponent implements OnInit {
  // forms
  category: Category;
  categoryForm: UntypedFormGroup;
  // booleans
  isEditMode = false;
  loading = true;
  // data holders
  formTitle: string;
  parentCategories = [];
  editedCategoryId: string;

  constructor(
    private authService: AuthService,
    private route: ActivatedRoute,
    private categoryService: CategoryService,
    private toastr: ToastrService,
    private fb: UntypedFormBuilder,
    private router: Router,
    private translate: TranslateService,
    private commonService: CommonService
  ) {
    //
  }

  ngOnInit() {
    this.loading = true;
    this.getAllParentCategories();
    this.formTitle = this.route.snapshot.data['title'];
    this.route.params.subscribe(params => {
      const id = params['id'];
      if (id) {
        this.editedCategoryId = id;
        this.isEditMode = true;
        this.getCategory(id);
      } else {
        this.editedCategoryId = '';
        this.isEditMode = false;
        this.initializeForm();
        this.loading = false;
      }
    });
  }

  getAllParentCategories(): void {
    this.categoryService.getAllCategories(new CategoryParams()).subscribe(result => {
      this.parentCategories = result.filter(data => data.parentCategoryId === null);
    });
  }

  getCategory(id: string): void {
    this.categoryService.getCategoryById(id).subscribe(result => {
      this.initializeForm(result);
      if (this.parentCategories.length > 0) {
        this.parentCategories = this.parentCategories.filter(
          data => data.categoryId !== +this.editedCategoryId
        );
      }
      this.loading = false;
    });
  }

  initializeForm(category?: Category) {
    this.categoryForm = this.fb.group({
      nameEnglish: [category && category?.nameEnglish, Validators.required],
      nameArabic: [category && category?.nameArabic, Validators.required],
      parentCategoryId: [category && category?.parentCategoryId],
    });
  }

  onSubmit(event: Event) {
    event.preventDefault();
    this.categoryForm.markAllAsTouched();
    if (this.categoryForm && this.categoryForm?.valid) {
      if (!this.isEditMode) {
        this.createCategory();
      } else {
        this.editCategory();
      }
    } else {
      this.commonService.scrollToError();
    }
  }

  createCategory(): void {
    this.categoryService.createCategory(this.categoryForm.getRawValue()).subscribe(response => {
      //this.toastr.success('Category added Successfully');
      this.commonService.playSuccessSound();
      this.router.navigate(['../'], { relativeTo: this.route });
    });
  }

  editCategory(): void {
    this.categoryService
      .updateCategory(this.categoryForm.value, this.editedCategoryId)
      .subscribe(response => {
        //this.toastr.success('Category updated Successfully');
        this.commonService.playSuccessSound();
        this.router.navigate(['../../'], { relativeTo: this.route });
      });
  }
}
