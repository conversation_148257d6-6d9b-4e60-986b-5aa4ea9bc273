.menu {
  .mat-menu-item {
    display: flex;
    align-items: center;
  }
  .icon-16 {
    margin-right: 8px; /* Adjust the spacing as needed */
  }
}
.title-container {
  display: flex;
  justify-content: flex-start; /* Adjusts spacing between title and buttons */
  align-items: center; /* Centers items vertically */
}

// .button-container {
//   display: flex;
//   align-items: center; /* Centers buttons vertically */
// }

.button-container button {
  margin-left: 10px; /* Maintain spacing between buttons */
}

/* Style for mat-card-title */
mat-card-title {
  display: flex;
  justify-content: space-between; /* Ensures space between title and buttons */
  align-items: center; /* Aligns items vertically in the center */
}

/* Style for button-container */
.button-container {
  display: flex;
  align-items: center; /* Aligns buttons vertically in the center */
  margin-left: auto; /* Pushes the container to the right side */
  margin-right: auto; /* Centers the container horizontally */
}
