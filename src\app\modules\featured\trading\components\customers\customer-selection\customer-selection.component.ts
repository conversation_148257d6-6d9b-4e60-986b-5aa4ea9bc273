import { Directionality } from '@angular/cdk/bidi';
import { AfterViewChecked, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { of } from 'rxjs';
import { catchError, distinctUntilChanged, finalize } from 'rxjs/operators';
import { CustomerService } from 'src/app/core/api/trading/customer.service';
import { customerTypes } from 'src/app/core/configs/dropDownConfig';
import { ActionType } from 'src/app/core/enums/actionType';
import {
  ICustomer,
  ICustomerModalData,
  ICustomerSearch,
  ICustomerView,
} from 'src/app/core/interfaces/customer';
import { CustomerParams } from 'src/app/core/models/params/customerParams';
import { IWalkin } from 'src/app/modules/featured/catalog/models/walkin-customer';
import { BaseSearchSelectionComponent } from 'src/app/modules/shared/components/base-search-selection/base-search-selection.component';
import { CustomerFormComponent } from '../customer-form/customer-form.component';
import { CustomerGetAllComponent } from '../customer-get-all/customer-get-all.component';
import { WalkInCustomerComponent } from '../walk-in-customer/walk-in-customer.component';

@Component({
  selector: 'app-customer-selection',
  templateUrl: './customer-selection.component.html',
  styleUrls: ['./customer-selection.component.scss'],
})
export class CustomerSelectionComponent
  extends BaseSearchSelectionComponent<ICustomer>
  implements OnInit, AfterViewChecked
{
  @Input() customerViewData: ICustomerView;
  @Input() mode: ActionType;
  @Output()
  customerTypeSelection: EventEmitter<any> = new EventEmitter();
  @Output() customerProfileSelection: EventEmitter<any> = new EventEmitter();
  public customerSelectionForm: UntypedFormGroup;
  public customerTypes = customerTypes;
  public displayedSearchColumns: string[] = [
    'nameArabic',
    'nameEnglish',
    'vatNumber',
    'accountNumber',
    'phoneNumber',
    'emailId',
  ];
  constructor(
    private customerService: CustomerService,
    private translate: TranslateService,
    formBuilder: UntypedFormBuilder,
    direction: Directionality,
    private dialog: MatDialog
  ) {
    super(formBuilder, direction);
  }

  get isCustomerSelected(): boolean {
    return this.customerSelectionForm?.get('customer').value !== null;
  }

  get isExistingCustomer() {
    return this.customerSelectionForm?.get('existingClient')?.value;
  }

  get hasCustomerData() {
    return this.customerViewData?.isViewMode;
  }

  get isCreateMode() {
    return this.mode === ActionType.create;
  }

  protected clearForms(): void {
    if (this.isCreateMode) {
      this.customerSelectionForm?.get('customer').patchValue(null, { emitEvent: true });
    }
  }
  protected extractData(searchResult: ICustomerSearch): ICustomer[] {
    return searchResult?.customers ?? [];
  }

  protected fetchItems(value: string) {
    const params = new CustomerParams();
    params.searchString = value;
    params.pageSize = 999;
    this.isLoading = true;
    return this.customerService.getCustomers(params).pipe(
      catchError(err => {
        console.error('Error fetching customers', err);
        this.isLoading = false;
        this.resultNotFound = true;
        return of([]);
      }),
      finalize(() => (this.isLoading = false))
    );
  }

  ngOnInit(): void {
    super.ngOnInit();
    console.log('customerViewData', this.customerViewData);
    this.patchData(this.customerViewData);
    this.customerSelectionForm?.get('existingClient').valueChanges.subscribe(value => {
      this.customerTypeSelection.emit(value);
      this.customerSelectionForm?.get('customer').setValue(null);
      if (value) {
        setTimeout(() => this.setFocusInputSearch());
      }
      if (!value) {
        this.autoCompleteInput.get('userInput').patchValue(null, { emitEvent: false });
      }
    });
    //
    this.customerSelectionForm.valueChanges
      .pipe(
        distinctUntilChanged((prev, curr) => JSON.stringify(prev) === JSON.stringify(curr)) // Prevent duplicate emissions
      )
      .subscribe(value => {
        console.log('ddddddddddddddddddddddddddddddddd', value);
        this.customerService.setCustomerSelection(value); // Update the service with the entire form
      });
  }

  ngAfterViewChecked(): void {
    if (this.customerViewData && this.customerViewData.isViewMode) {
      this.customerSelectionForm?.disable({ emitEvent: false });
      this.disbaleInputSearch();
    }
  }

  patchData(customerViewData?: ICustomerView): void {
    this.customerSelectionForm = this.formBuilder.group({
      existingClient: new UntypedFormControl(customerViewData.existingClient ?? false),
      customer: new UntypedFormControl(customerViewData.customer ?? null),
    });
    if (this.hasCustomerData && customerViewData?.existingClient) {
      this.autoCompleteInput
        .get('userInput')
        .setValue(customerViewData.customer, { emitEvent: true });
    }
    if (this.hasCustomerData) {
      console.log('The existingClient control is disabled');
      this.customerService.setCustomerSelection(this.customerSelectionForm.getRawValue());
    }
  }

  onSelection(selectedCustomer: ICustomer) {
    console.log('onSelection', selectedCustomer);
    this.customerSelectionForm?.controls['customer'].patchValue(selectedCustomer, {
      emitEvent: true,
    });
    this.customerProfileSelection.emit(selectedCustomer);
    setTimeout(() => {
      this.autoCompleteInput.get('userInput').setValue(selectedCustomer, { emitEvent: true });
    }, 100);
    this.itemSource = null;
  }

  onViewCustomerSelection(event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    if (!this.customerSelectionForm?.controls['existingClient'].value) {
      this.showWalkInModal();
    } else {
      this.showExistingClientModal();
    }
  }

  showWalkInModal() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '90vw';
    dialogConfig.position = {
      top: '50px',
    };
    dialogConfig.maxHeight = '90vh';
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = this.customerViewData.isViewMode ? false : true;
    dialogConfig.autoFocus = false;
    const data: IWalkin = {
      header: 'Customer Details',
      data: this.customerSelectionForm?.get('customer').value,
      isViewMode: this.customerSelectionForm?.controls['existingClient'].disabled,
    };
    dialogConfig.data = data;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(WalkInCustomerComponent, dialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result === 'update') {
        console.log('result', result, dialogRef.componentInstance.getCustomerDataAsObject());
        this.customerSelectionForm
          .get('customer')
          .setValue(dialogRef.componentInstance.getCustomerDataAsObject());
      }
    });
  }

  showExistingClientModal() {
    const dialogConfig = new MatDialogConfig();
    const data: ICustomerModalData = {
      header: 'Customer Details',
      data: this.customerSelectionForm?.get('customer').value,
      isViewMode: this.customerSelectionForm?.controls['existingClient'].disabled,
    };
    dialogConfig.minWidth = '90vw';
    dialogConfig.position = {
      top: '50px',
    };
    dialogConfig.maxHeight = '90vh';
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.data = data;
    dialogConfig.direction = this.direction.value;
    this.dialog.open(CustomerFormComponent, dialogConfig);
  }

  displayFn(user: ICustomer) {
    if (user) {
      const currentLanguage = this.translate.currentLang;
      const name = currentLanguage === 'ar' ? user.nameArabic : user.nameEnglish;
      return user ? name : undefined;
    }
  }

  clearSelections(event: Event) {
    event.preventDefault();
    event.stopPropagation();
    super.clearSelection();
    this.customerSelectionForm?.get('customer').patchValue(null, { emitEvent: true });
  }

  public customerSelectionFormIsAllValid(): boolean {
    this.customerSelectionForm?.markAllAsTouched();
    return this.customerSelectionForm?.disabled
      ? true
      : this.customerSelectionForm?.valid
      ? true
      : false;
  }

  getAllCustomers(event: Event) {
    event.preventDefault();
    event.stopPropagation();
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '90vw';
    dialogConfig.position = {
      top: '50px',
    };
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.hasBackdrop = true;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(CustomerGetAllComponent, dialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if ((result as ICustomer).customerId !== undefined) {
        console.log('getAllCustomers', result);
        this.onSelection(result);
      }
    });
  }

  resetForm(): void {
    this.customerSelectionForm?.reset();
    this.customerSelectionForm?.get('existingClient').setValue(false);
    this.customerSelectionForm?.markAsPristine();
    this.customerSelectionForm?.markAsUntouched();
    console.log('resetting customer fields', this.customerSelectionForm?.getRawValue());
  }
}
