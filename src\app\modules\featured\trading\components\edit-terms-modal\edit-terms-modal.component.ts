import { CdkTextareaAutosize } from '@angular/cdk/text-field';
import { Component, Inject, NgZone, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { take } from 'rxjs/operators';
import { Provision } from 'src/app/core/interfaces/sales';

@Component({
  selector: 'app-edit-terms-modal',
  templateUrl: './edit-terms-modal.component.html',
  styleUrls: ['./edit-terms-modal.component.scss'],
})
export class EditTermsModalComponent {
  @ViewChild('autosize') autosize: CdkTextareaAutosize;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: Provision,
    private dialogRef: MatDialogRef<EditTermsModalComponent>,
    private _ngZone: NgZone
  ) {}

  triggerResize() {
    // Wait for changes to be applied, then trigger textarea resize.
    this._ngZone.onStable.pipe(take(1)).subscribe(() => this.autosize.resizeToFitContent(true));
  }

  onSave() {
    this.dialogRef.close(this.data.clause);
  }

  onCancel() {
    this.dialogRef.close();
  }
}
