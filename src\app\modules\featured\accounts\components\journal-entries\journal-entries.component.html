<ng-container *ngIf="!isLoading">
  <!-- action bar -->
  <app-create-action
    *appHasPermission="['JournalEntries.Create', 'AllPermissions']"
    [label]="'journals.create' | translate"></app-create-action>
  <!-- action bar -->
  <mat-card appearance="outlined">
    <form [formGroup]="filterForm" autocomplete="off">
      <div class="row no-gutters">
        <div class="p-2 col-md-5">
          <app-journal-entries-search-box
            #searchBoxForm
            formControlName="searchBoxForm"></app-journal-entries-search-box>
        </div>
        <div class="p-2 col-lg-2 col-md-2 col-sm-3 d-flex align-items-end">
          <div class="form-group">
            <mat-label>{{ 'journals.journalType' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <mat-select formControlName="journalType">
                <mat-option *ngFor="let journalType of journalTypes" [value]="journalType.value">
                  {{ journalType.display | translate }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
        <div class="p-2 col-lg-2 col-md-2 col-sm-3 d-flex align-items-end">
          <div class="form-group">
            <mat-label>{{ 'journals.journalCreationType' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <mat-select formControlName="journalCreationType">
                <mat-option
                  *ngFor="let journalCreationType of journalCreationTypes"
                  [value]="journalCreationType.value">
                  {{ journalCreationType.display | translate }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
      </div>
      <ng-container>
        <button (click)="getFilterData($event); (false)" mat-stroked-button color="primary">
          {{ 'searchPanel.searchString' | translate }}
        </button>
        <button class="m-l-10" (click)="clearFilters($event); (false)" mat-stroked-button>
          {{ 'searchPanel.clear' | translate }}
        </button>
      </ng-container>
    </form>
    <mat-card-title class="m-t-10">{{ 'journals.listJournals' | translate }}</mat-card-title>
    <div class="table-responsive">
      <table [dataSource]="dataSource" mat-table matSort>
        <ng-container matColumnDef="journalNumber">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'journals.journalRef' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ element.journalNumber }}
          </td>
        </ng-container>
        <ng-container matColumnDef="journalDate">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'journals.date' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ element.journalDate }}
          </td>
        </ng-container>
        <ng-container matColumnDef="journalType">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'journals.journalType' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ getEntryTypeDisplay(element.journalType) | translate }}
          </td>
        </ng-container>
        <ng-container matColumnDef="journalCreationType">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'journals.journalCreationType' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ getEntryCreationTypeDisplay(element.journalCreationType) | translate }}
          </td>
        </ng-container>
        <!-- <ng-container matColumnDef="isPosted">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'journals.posted' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
           {{ getJournalPosting(element.isPosted) | translate }}
          </td>
        </ng-container> -->
        <ng-container matColumnDef="isPosted">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'journals.posted' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ (element.isPosted ? 'jurposted' : 'jurnotposted') | translate }}
          </td>
        </ng-container>

        <!------------------------------------------------- Action Section -------------------------------- -->
        <ng-container matColumnDef="action">
          <th *matHeaderCellDef mat-header-cell>{{ 'common.action' | translate }}</th>
          <td *matCellDef="let element" mat-cell>
            <a
              class="m-r-10 cursor-pointer"
              class="m-b-10"
              *appHasPermission="['JournalEntries.Update', 'AllPermissions']"
              [routerLink]="['edit', element.journalId]"
              ><i-tabler class="icon-16" name="edit"></i-tabler
            ></a>
            <a
              class="m-r-10 cursor-pointer"
              class="m-b-10"
              *appHasPermission="['JournalEntries.View', 'AllPermissions']"
              [routerLink]="['view', element.journalId]"
              ><i-tabler class="icon-16" name="eye"></i-tabler
            ></a>
          </td>
        </ng-container>
        <tr class="mat-row" *matNoDataRow>
          <td class="text-center text-info" [attr.colspan]="displayedColumns.length">
            {{ 'common.noDataFound' | translate }}
          </td>
        </tr>
        <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
        <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
      </table>
    </div>
    <mat-paginator
      *ngIf="dataSource?.filteredData?.length > 0"
      [length]="resultsLength"
      [pageSize]="5"
      [pageSizeOptions]="[5, 20, 50]"
      (page)="onPageChange($event)"></mat-paginator>
  </mat-card>
</ng-container>
