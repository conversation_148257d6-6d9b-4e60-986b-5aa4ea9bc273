import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';
import { SharedModule } from '../../shared/shared.module';
import { AccountsModule } from '../accounts/accounts.module';
import { AccountAutoSearchComponent } from '../accounts/components/accountAutoSearch/account-auto-search.component';
import { UcvoucherCreateComponent } from './financedashboard/components/ucvoucher-create/ucvoucher-create.component';
import { UcvoucherPaymentComponent } from './financedashboard/components/ucvoucher-payment/ucvoucher-payment.component';
import { UcvoucherReceiptComponent } from './financedashboard/components/ucvoucher-receipt/ucvoucher-receipt.component';
import { UcvoucherComponent } from './financedashboard/components/ucvoucher/ucvoucher.component';
import { FinanceRoutingModule } from './finance-routing.module';
import { FinancedashboardComponent } from './financedashboard/financedashboard.component';

@NgModule({
  declarations: [
    UcvoucherComponent,
    UcvoucherCreateComponent,
    UcvoucherPaymentComponent,
    UcvoucherReceiptComponent,
    FinancedashboardComponent,
  ],
  imports: [CommonModule, FinanceRoutingModule, TranslateModule, SharedModule, AccountsModule],
  providers: [AccountAutoSearchComponent],
})
export class FinanceModule {}
