import { Directionality } from '@angular/cdk/bidi';
import { Component, ElementRef, ViewChild } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { PurchaseService } from 'src/app/core/api/trading/purchase.service';
import { IPurchaseInvoice, IPurchaseResponse } from 'src/app/core/interfaces/purchase';
import { SalesParams } from 'src/app/core/models/params/salesParams';
import { SalesNotesComponent } from '../../sales/sales-notes/sales-notes.component';
import { SaletransactionTypes } from 'src/app/core/interfaces/sales';
import { MultilingualService } from 'src/app/modules/core/core/services/multilingual.service';
import { SelectionModel } from '@angular/cdk/collections';

@Component({
  selector: 'app-purchase-list',
  templateUrl: './purchase-list.component.html',
  styleUrls: ['./purchase-list.component.scss'],
})
export class PurchaseListComponent {
  @ViewChild('filter') filter: ElementRef;
  @ViewChild('searchInput') searchInput: ElementRef;
  @ViewChild(MatPaginator) set matPaginator(paginator: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = paginator;
    }
  }
  @ViewChild(MatSort) set matSort(sort: MatSort) {
    if (this.dataSource) {
      this.dataSource.sort = sort;
    }
  }
  invoices: IPurchaseInvoice[];
  displayedColumns: string[];
  dataSource: MatTableDataSource<IPurchaseInvoice>;
  selection = new SelectionModel<IPurchaseInvoice>();

  isLoading = true;
  constructor(
    public purchaseService: PurchaseService,
    public dialog: MatDialog,
    private direction: Directionality,
    private translateService: MultilingualService
  ) {}

  ngOnInit(): void {
    this.getAllSales();
    this.initColumns();
  }

  getAllSales(): void {
    const salesParams = new SalesParams();
    salesParams.transactionType = SaletransactionTypes.purchase;
    this.purchaseService.getAllPurchaseSales(salesParams).subscribe((result: IPurchaseResponse) => {
      console.log(result, result.transactions);
      this.invoices = result.transactions;
      this.isLoading = false;
      this.dataSource = new MatTableDataSource<IPurchaseInvoice>(this.invoices);
      this.selection = new SelectionModel<IPurchaseInvoice>(true);
    });
  }

  initColumns(): void {
    this.displayedColumns = this.translateService.updateDisplayedColumns([
      'action',
      'documentNumber',
      'issueDate',
      'invoiceTotal',
      'accountNumber',
      'nameArabic',
      'nameEnglish',
      'vatNumber',
      'phoneNumber',
      'paymentMethod',
      'returnStatus',
    ]);
  }

  applyFilter(filterValue: string): void {
    filterValue = filterValue.trim();
    filterValue = filterValue.toLowerCase();
    this.dataSource.filter = filterValue;
  }

  clearSearchInput() {
    this.searchInput.nativeElement.value = '';
    this.getAllSales();
  }
  openSalesNotes(invoiceId: number): void {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '90vw';
    dialogConfig.position = {
      top: '50px',
    };
    dialogConfig.maxHeight = '90vh';
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    dialogConfig.data = {
      invoiceId: invoiceId,
      purchaseNotes: true,
    };
    this.dialog.open(SalesNotesComponent, dialogConfig);
  }
}
