{"workbench.colorTheme": "Visual Studio Dark", "typescript.updateImportsOnFileMove.enabled": "always", "javascript.updateImportsOnFileMove.enabled": "always", "editor.formatOnSave": true, "editor.formatOnPaste": true, "editor.fontFamily": "Monaco", "editor.fontSize": 16, "git.openRepositoryInParentFolders": "never", "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "html"], "files.insertFinalNewline": true, "files.trimFinalNewlines": true, "emmet.preferences": {}, "editor.defaultFormatter": "esbenp.prettier-vscode", "eslint.options": {"extensions": [".ts", ".html"]}, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.fixAll.eslint": "explicit"}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "jsdoc-generator.includeDate": false, "jsdoc-generator.includeTime": false, "jsdoc-generator.descriptionForConstructors": "", "jsdoc-generator.functionVariablesAsFunctions": false, "jsdoc-generator.includeExport": false, "jsdoc-generator.author": "", "editor.inlineSuggest.showToolbar": "always"}