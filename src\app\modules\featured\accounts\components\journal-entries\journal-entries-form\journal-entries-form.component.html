<ng-container>
  <!--Entry section -->
  <form [ngClass]="{ readOnly: IsViewMode }" [formGroup]="journalEntriesForm" autocomplete="off">
    <mat-card appearance="outlined">
      <mat-card-title>{{ formTitle | translate }}</mat-card-title>
      <div class="row no-gutters">
        <div class="col-md-2 col-lg-2 col-sm-2 p-2">
          <mat-label>{{ 'journals.journalNumber' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input type="text" maxlength="40" matInput formControlName="journalNumber" />
          </mat-form-field>
        </div>
        <div class="col-md-2 col-lg-2 col-sm-3 p-2">
          <mat-label>{{ 'journals.journalType' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <mat-select formControlName="journalType">
              <mat-option *ngFor="let type of journalTypes" [value]="type.value">
                {{ type.display | translate }}
              </mat-option>
            </mat-select>
            <mat-error
              *ngIf="
                journalEntriesForm?.controls['journalType']?.hasError('required') &&
                journalEntriesForm?.controls['journalType']?.touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
          </mat-form-field>
        </div>

        <div class="col-md-2 col-lg-2 col-sm-3 p-2">
          <mat-label>{{ 'journals.date' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input
              [matDatepicker]="picker1"
              matInput
              placeholder="mm/dd/yyyy"
              formControlName="journalDate" />
            <mat-error
              *ngIf="
                journalEntriesForm?.controls['journalDate'].hasError('required') &&
                journalEntriesForm?.controls['journalDate'].touched
              "
              >{{ 'common.required' | translate }}</mat-error
            >
            <mat-datepicker-toggle [for]="picker1" matSuffix> </mat-datepicker-toggle>
            <mat-datepicker #picker1></mat-datepicker>
          </mat-form-field>
        </div>

        <div class="col-md-4 col-lg-4 col-sm-4 p-2">
          <mat-label>{{ 'journals.description' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <input
              class="single-line-input"
              #descriptionInput
              (input)="onInput(descriptionInput)"
              type="text"
              maxlength="90"
              matInput
              formControlName="description"
              matTooltip="{{ journalEntriesForm?.controls['description'].value }}"
              dir="rtl" />
          </mat-form-field>
        </div>
      </div>

      <div class="table-responsive">
        <table
          class="w-100"
          [dataSource]="dataSource"
          mat-table
          formArrayName="transactionLineRequests">
          <ng-container matColumnDef="accountId">
            <th *matHeaderCellDef mat-header-cell>{{ 'journals.accountNumber' | translate }}</th>
            <td *matCellDef="let element" [formGroup]="element" mat-cell>
              <app-account-auto-search
                [control]="element.get('accountBasic')"
                [otherControl]="element.get('costCentreId')"
                [showSearch]="IsViewMode ? false : true"
                [disabled]="isViewMode"
                [newAccountSettings]="true"
                placeholder="{{ getAccountIdPlaceholder(element) }}"
                label=""
                accountType="DETAILED"
                searchStringLength="3">
              </app-account-auto-search>
            </td>
          </ng-container>

          <ng-container matColumnDef="costCentreNo">
            <th class="flex-width" *matHeaderCellDef mat-header-cell>
              {{ 'journals.costCenter' | translate }}
            </th>
            <td *matCellDef="let element" [formGroup]="element" mat-cell>
              <mat-form-field class="w-100">
                <mat-select formControlName="costCentreId">
                  <mat-option
                    *ngFor="let costCentre of costCentreList"
                    [value]="costCentre.costCentreId">
                    {{
                      costCentre.accountNumber +
                        '-' +
                        accountSearchComponent.getAccountName(costCentre)
                    }}
                  </mat-option>
                </mat-select>
                <mat-error
                  *ngIf="
                    journalEntriesForm?.controls['costCentreId']?.hasError('required') &&
                    journalEntriesForm?.controls['costCentreId']?.touched
                  "
                  >{{ 'common.required' | translate }}</mat-error
                >
              </mat-form-field>
            </td>
          </ng-container>

          <ng-container matColumnDef="debitAmount">
            <th *matHeaderCellDef mat-header-cell>{{ 'journals.debitAmount' | translate }}</th>
            <td *matCellDef="let element" [formGroup]="element" mat-cell>
              <mat-form-field class="w-100">
                <input matInput formControlName="debitAmount" />
                <mat-error *ngIf="element['controls']['debitAmount']?.hasError('required')">
                  {{ 'common.required' | translate }}
                </mat-error>
              </mat-form-field>
            </td>
          </ng-container>

          <ng-container matColumnDef="creditAmount">
            <th *matHeaderCellDef mat-header-cell>{{ 'journals.creditAmount' | translate }}</th>
            <td *matCellDef="let element" [formGroup]="element" mat-cell>
              <mat-form-field class="w-100">
                <input matInput formControlName="creditAmount" />
                <mat-error *ngIf="element['controls']['creditAmount']?.hasError('required')">
                  {{ 'common.required' | translate }}
                </mat-error>
              </mat-form-field>
            </td>
          </ng-container>

          <ng-container matColumnDef="description">
            <th *matHeaderCellDef mat-header-cell>{{ 'journals.description' | translate }}</th>
            <td *matCellDef="let element" [formGroup]="element" mat-cell>
              <mat-form-field class="w-100">
                <input
                  class="single-line-input"
                  #descriptionInput
                  (input)="onInput(descriptionInput)"
                  maxlength="90"
                  type="text"
                  matInput
                  formControlName="description"
                  matTooltip="{{ element.controls.description.value }}"
                  dir="rtl" />
              </mat-form-field>
            </td>
          </ng-container>

          <ng-container matColumnDef="description2">
            <th *matHeaderCellDef mat-header-cell>{{ 'journals.description2' | translate }}</th>
            <td *matCellDef="let element" [formGroup]="element" mat-cell>
              <mat-form-field class="w-100">
                <input
                  class="single-line-input"
                  #descriptionInput2
                  (input)="onInput(descriptionInput2)"
                  maxlength="90"
                  type="text"
                  matInput
                  formControlName="description2"
                  matTooltip="{{ element.controls.description2.value }}"
                  dir="rtl" />
              </mat-form-field>
            </td>
          </ng-container>

          <!-- Action section-->
          <ng-container matColumnDef="action">
            <th *matHeaderCellDef mat-header-cell>{{ 'common.action' | translate }}</th>
            <td *matCellDef="let element; let i = index; let last = last" mat-cell>
              <a
                class="m-r-10 cursor-pointer"
                *ngIf="(last && isCreateMode) || (last && isEditMode)">
                <i-tabler class="icon-16" (click)="addEntryRow(); (false)" name="plus"></i-tabler>
              </a>
              <a class="cursor-pointer" *ngIf="displayRemoveIcon && !isViewMode">
                <i-tabler class="icon-16" (click)="deleteEntry(element, i)" name="trash"></i-tabler>
              </a>
            </td>
          </ng-container>
          <tr *matHeaderRowDef="columnsToDisplay" mat-header-row></tr>
          <tr *matRowDef="let transactionLine; columns: columnsToDisplay" mat-row></tr>
        </table>
      </div>
      <div class="center-container">
        <p>{{ 'journals.totalCreditAmount' | translate }}: {{ totalCreditAmount.toFixed(2) }} ,</p>
        <p>{{ 'journals.totalDebitAmount' | translate }}: {{ totalDebitAmount.toFixed(2) }}</p>
      </div>
      <div class="center-container-1">
        <mat-error *ngIf="totalCreditAmount.toFixed(2) !== totalDebitAmount.toFixed(2)"
          ><strong>{{ 'journals.message' | translate }}</strong></mat-error
        >
      </div>
    </mat-card>
    <div class="text-center">
      <button
        class="m-l-10"
        *ngIf="isCreateMode"
        (click)="onSubmit($event)"
        mat-stroked-button
        color="primary">
        {{ 'common.submit' | translate }}
      </button>
      <button
        class="m-l-10"
        *ngIf="isEditMode"
        (click)="onSubmit($event)"
        mat-stroked-button
        color="primary">
        {{ 'common.submit' | translate }}
      </button>
      <button
        class="m-l-10"
        *ngIf="isEditMode || isJournalSaved"
        (click)="onPost($event)"
        mat-stroked-button
        color="primary">
        Post
      </button>
      <button
        class="m-l-10"
        *ngIf="isCreateMode"
        [routerLink]="['../']"
        mat-stroked-button
        color="warn">
        {{ 'common.cancel' | translate }}
      </button>
      <button
        class="m-l-10"
        *ngIf="isEditMode"
        [routerLink]="['../../']"
        mat-stroked-button
        color="warn">
        {{ 'common.cancel' | translate }}
      </button>
      <button
        class="m-l-10"
        *ngIf="isViewMode"
        [routerLink]="['../../']"
        type="button"
        mat-stroked-button
        color="primary">
        {{ 'common.cancel' | translate }}
      </button>
    </div>
  </form>
</ng-container>
