<!-- action bar -->
<app-create-action
  *appHasPermission="['Quotation.Create', 'AllPermissions']"
  [label]="'salesQuotes.register_quotes' | translate"></app-create-action>
<!-- action bar -->

<mat-card appearance="outlined">
  <mat-card-content>
    <!-- search field -->
    <div class="row no-gutters">
      <div class="col-md-6 col-lg-6 col-sm-12 d-flex">
        <mat-form-field class="w-100">
          <input
            #searchInput
            (keyup)="applyFilter($event.target.value)"
            matInput
            autocomplete="off" />
          <i-tabler class="icon-16" matPrefix name="search"></i-tabler>
          <a
            class="cursor-pointer"
            *ngIf="searchInput?.value"
            (click)="clearSearchInput()"
            matSuffix>
            <i-tabler class="icon-16 error" name="X"></i-tabler>
          </a>
        </mat-form-field>
      </div>
    </div>
    <!-- search field -->
    <mat-card-title class="m-t-10">{{ 'salesQuotes.quotationList' | translate }}</mat-card-title>
    <div class="table-responsive">
      <table class="w-100" [dataSource]="dataSource" mat-table matSort>
        <ng-container matColumnDef="documentNumber">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'salesQuotes.invoiceNo' | translate }}
          </th>
          <td class="f-s-14" *matCellDef="let element" mat-cell>
            {{ element.documentNumber }}
          </td>
        </ng-container>
        <ng-container matColumnDef="issueDate">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'salesQuotes.invoiceDate' | translate }}
          </th>
          <td class="f-s-14" *matCellDef="let element" mat-cell>
            {{ element.issueDate | date : 'yyyy-MM-dd' }}
          </td>
        </ng-container>
        <ng-container matColumnDef="invoiceTotal">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'salesQuotes.invoiceAmt' | translate }}
          </th>
          <td class="f-s-14" *matCellDef="let element" mat-cell>
            {{ element.invoiceTotal }}
          </td>
        </ng-container>
        <ng-container matColumnDef="accountNumber">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'salesQuotes.invoiceCustomerNo' | translate }}
          </th>
          <td
            class="f-s-14"
            *matCellDef="let element"
            [data]="element.account?.accountNumber"
            appHyphen
            mat-cell>
            {{ element.account?.accountNumber }}
          </td>
        </ng-container>
        <ng-container matColumnDef="nameArabic">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'common.field.nameArabic' | translate }}
          </th>
          <td
            class="f-s-14"
            *matCellDef="let element"
            [data]="element.account?.nameArabic"
            mat-cell
            appHyphen>
            {{ element.account?.nameArabic }}
          </td>
        </ng-container>
        <ng-container matColumnDef="nameEnglish">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'common.field.nameEnglish' | translate }}
          </th>
          <td
            class="f-s-14"
            *matCellDef="let element"
            [data]="element.account?.nameEnglish"
            mat-cell
            appHyphen>
            {{ element.account?.nameEnglish }}
          </td>
        </ng-container>
        <ng-container matColumnDef="vatNumber">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'common.field.vatNo' | translate }}
          </th>
          <td
            class="f-s-14"
            *matCellDef="let element"
            [data]="element.vatNumber"
            mat-cell
            appHyphen>
            {{ element.vatNumber }}
          </td>
        </ng-container>
        <ng-container matColumnDef="phoneNumber">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'common.field.phone' | translate }}
          </th>
          <td
            class="f-s-14"
            *matCellDef="let element"
            [data]="element.phoneNumber"
            mat-cell
            appHyphen>
            {{ element.phoneNumber }}
          </td>
        </ng-container>
        <ng-container matColumnDef="isPosted">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'sales.returnStatus' | translate }}
          </th>
          <td class="f-s-14" *matCellDef="let element" mat-cell>
            <div
              class="text-center rounded cursor-pointer"
              [ngClass]="{
                ' bg-light-error': !element.isPosted,
                'bg-light-success': element.isPosted
              }">
              {{ (element.isPosted ? 'posted' : 'notposted') | translate }}
            </div>
          </td>
        </ng-container>
        <ng-container matColumnDef="action">
          <th *matHeaderCellDef mat-header-cell>
            {{ 'common.action' | translate }}
          </th>
          <td class="f-s-14" *matCellDef="let element" mat-cell>
            <button
              class="d-flex justify-content-center"
              [matMenuTriggerFor]="menu1"
              mat-icon-button>
              <i-tabler class="icon-20 d-flex" name="dots-vertical"></i-tabler>
            </button>
            <mat-menu class="cardWithShadow" #menu1="matMenu">
              <ng-container *ngIf="!element.isPosted">
                <button
                  *appHasPermission="['Quotation.Edit', 'AllPermissions']"
                  [routerLink]="['edit', element.id]"
                  mat-menu-item>
                  <div class="d-flex align-items-center">
                    <i-tabler class="icon-16 m-r-4" name="edit"></i-tabler>
                    <span>{{ 'common.editAction' | translate }}</span>
                  </div>
                </button>
              </ng-container>
              <button
                *appHasPermission="['Quotation.View', 'AllPermissions']"
                [routerLink]="['view', element.id]"
                mat-menu-item>
                <div class="d-flex align-items-center">
                  <i-tabler class="icon-16 m-r-4" name="eye"></i-tabler>
                  <span>{{ 'common.viewAction' | translate }}</span>
                </div>
              </button>
              <ng-container *ngIf="!element.isPosted">
                <button
                  *appHasPermission="['Quotation.Post', 'AllPermissions']"
                  [routerLink]="['post', element.id]"
                  mat-menu-item>
                  <div class="d-flex align-items-center">
                    <i-tabler class="icon-16 m-r-4" name="edit"></i-tabler>
                    <span>{{ 'common.postAction' | translate }}</span>
                  </div>
                </button>
              </ng-container>
              <ng-container *ngIf="!element.isPosted">
                <button
                  *appHasPermission="['Quotation.Delete', 'AllPermissions']"
                  [routerLink]="['delete', element.id]"
                  mat-menu-item>
                  <div class="d-flex align-items-center">
                    <i-tabler class="icon-16 m-r-4" name="trash"></i-tabler>
                    <span>{{ 'common.deleteAction' | translate }}</span>
                  </div>
                </button>
              </ng-container>
              <ng-container>
                <button
                  *appHasPermission="['Sales.View', 'AllPermissions']"
                  (click)="printQuotation(element); (false); $event.preventDefault()"
                  mat-menu-item>
                  <div class="d-flex align-items-center">
                    <i-tabler class="icon-16 m-r-4" name="printer"></i-tabler>
                    <span>{{ 'common.print' | translate }}</span>
                  </div>
                </button>
              </ng-container>
            </mat-menu>
          </td>
        </ng-container>
        <tr class="mat-row" *matNoDataRow>
          <td class="f-s-14" class="text-center" [attr.colspan]="displayedColumns.length">
            {{ 'common.noDataFound' | translate }}
          </td>
        </tr>
        <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
        <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
      </table>
    </div>
    <mat-paginator
      *ngIf="dataSource?.filteredData?.length > 0"
      [length]="dataSource.filteredData.length"
      [pageIndex]="0"
      [pageSize]="10"
      [pageSizeOptions]="[5, 10, 20]"></mat-paginator>
  </mat-card-content>
</mat-card>
