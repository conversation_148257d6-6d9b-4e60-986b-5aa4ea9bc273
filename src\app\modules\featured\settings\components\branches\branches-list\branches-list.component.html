<!-- action bar -->
<app-create-action
  *appHasPermission="['Branch.Create', 'AllPermissions']"
  [label]="'branch.createBranch' | translate"></app-create-action>
<!-- action bar -->

<!----------------------------------- mat table content --------------------------------------->

<mat-card appearance="outlined">
  <mat-card-content>
    <!-- search field -->
    <div class="row no-gutters">
      <div class="col-md-6 col-lg-6 col-sm-12 d-flex">
        <mat-form-field class="w-100">
          <input
            #searchInput
            (keyup)="applyFilter($event.target.value)"
            matInput
            autocomplete="off" />
          <i-tabler class="icon-16" matPrefix name="search"></i-tabler>
          <a
            class="cursor-pointer"
            *ngIf="searchInput?.value"
            (click)="clearSearchInput()"
            matSuffix>
            <i-tabler class="icon-16 error" name="X"></i-tabler>
          </a>
        </mat-form-field>
      </div>
    </div>
    <mat-card-title class="m-t-10">{{ formTitle | translate }}</mat-card-title>
    <!-- search field -->
    <div class="table-responsive">
      <table [dataSource]="dataSource" mat-table matSort>
        <!-- Name Column -->
        <ng-container matColumnDef="nameEnglish">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'branch.nameEnglish' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ element.nameEnglish }}
          </td>
        </ng-container>
        <!-- Name Column -->
        <ng-container matColumnDef="nameArabic">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'branch.nameArabic' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ element.nameArabic }}
          </td>
        </ng-container>
        <!-- Description Column -->
        <ng-container matColumnDef="vatNumber">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'branch.vatNo' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ element.vatNumber }}
          </td>
        </ng-container>
        <!-- Description Column -->
        <ng-container matColumnDef="isActive">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>Status</th>
          <td *matCellDef="let element" mat-cell>
            <mat-chip-listbox *ngIf="element.isActive">
              <mat-chip-option class="mat-chip-status bg-light-success">Active</mat-chip-option>
            </mat-chip-listbox>
            <mat-chip-listbox *ngIf="!element.isActive">
              <mat-chip-option class="mat-chip-status bg-danger">Inactive</mat-chip-option>
            </mat-chip-listbox>
          </td>
        </ng-container>
        <!-- User Actions Column -->
        <ng-container matColumnDef="action">
          <th *matHeaderCellDef mat-header-cell>{{ 'common.action' | translate }}</th>
          <td class="f-s-14" class="action-link" *matCellDef="let element" tabindex="-1" mat-cell>
            <a
              class="m-r-10 cursor-pointer"
              *appHasPermission="['Branch.Delete', 'AllPermissions']"
              (click)="deleteBranch(element.branchId)"
              ><i-tabler class="icon-16" name="trash"></i-tabler
            ></a>
            <a
              class="m-r-10 cursor-pointer"
              *appHasPermission="['Branch.Update', 'AllPermissions']"
              [routerLink]="['edit', element.branchId]">
              <i-tabler class="icon-16" name="edit"></i-tabler>
            </a>
            <a
              class="m-r-10 cursor-pointer"
              *appHasPermission="['Branch.View', 'AllPermissions']"
              [routerLink]="['view', element.branchId]">
              <i-tabler class="icon-16" name="eye"></i-tabler>
            </a>
          </td>
        </ng-container>
        <tr class="mat-row" *matNoDataRow>
          <td class="text-center" [attr.colspan]="displayedColumns.length">
            {{ 'common.noDataFound' | translate }}
          </td>
        </tr>
        <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
        <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
      </table>
    </div>
  </mat-card-content>
</mat-card>
