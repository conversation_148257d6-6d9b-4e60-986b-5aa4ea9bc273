<app-dialog-header></app-dialog-header>
<h2 class="text-center text-primary" mat-dialog-title>{{ data.name | translate }}</h2>
<mat-dialog-content>
  <mat-form-field class="w-100">
    <textarea
      #autosize="cdkTextareaAutosize"
      [(ngModel)]="data.clause"
      cdkTextareaAutosize
      cdkAutosizeMinRows="5"
      cdkAutosizeMaxRows="10"
      matInput></textarea>
  </mat-form-field>
</mat-dialog-content>
<mat-dialog-actions align="center">
  <button (click)="onSave(); (false)" mat-stroked-button color="primary">
    {{ 'common.buttons.submit' | translate }}
  </button>
  <button (click)="onCancel(); (false)" mat-button color="warn" mat-stroked-button>
    {{ 'common.buttons.cancel' | translate }}
  </button>
</mat-dialog-actions>
