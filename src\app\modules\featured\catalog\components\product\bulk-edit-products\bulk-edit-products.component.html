<app-dialog-header></app-dialog-header>

<h2
  class="text-center text-warning"
  [innerHTML]="
    'bulkEdit.bulkEditText' | translate : { count: data?.selectedRecords.length } | safehtml
  "></h2>

<mat-dialog-content>
  <form [formGroup]="productBulkEditForm" autocomplete="off">
    <mat-card-subtitle class="text-error">
      {{ 'bulkEdit.selectOption' | translate }}
    </mat-card-subtitle>

    <div class="row no-gutters m-t-10">
      <!-- Category -->
      <div class="col-md-6 col-lg-6 col-sm-6">
        <mat-checkbox [formControl]="categoryUpdate">
          {{ 'bulkEdit.category' | translate }}
        </mat-checkbox>
      </div>

      <!-- Category ID Value -->
      <div class="col-md-6 col-lg-6 col-sm-6" *ngIf="categoryUpdate?.value">
        <mat-form-field class="w-100">
          <mat-select formControlName="categoryId">
            <mat-option
              *ngFor="let parentCategory of categoryList"
              [value]="parentCategory.categoryId">
              {{ parentCategory | localized }}
            </mat-option>
          </mat-select>
          <mat-error
            *ngIf="
              productBulkEditForm.controls['categoryId'].hasError('required') &&
              productBulkEditForm.controls['categoryId'].touched
            ">
            {{ 'common.required' | translate }}
          </mat-error>
        </mat-form-field>
      </div>
    </div>

    <div class="row no-gutters m-t-10">
      <!-- VAT -->
      <div class="col-md-6 col-lg-6 col-sm-6">
        <mat-checkbox [formControl]="vatUpdate">
          {{ 'bulkEdit.vat' | translate }}
        </mat-checkbox>
      </div>

      <!-- VAT Value -->
      <div class="col-md-6 col-lg-6 col-sm-6" *ngIf="vatUpdate?.value">
        <mat-form-field class="w-100">
          <input [min]="0" matInput type="number" formControlName="vat" />
          <mat-error
            *ngIf="
              productBulkEditForm.controls['vat'].hasError('required') &&
              productBulkEditForm.controls['vat'].touched
            ">
            {{ 'common.required' | translate }}
          </mat-error>
        </mat-form-field>
      </div>
    </div>

    <!-- Status -->
    <div class="row no-gutters m-t-10">
      <!-- Product Sale Frozen -->
      <div class="col-md-6 col-lg-6 col-sm-6">
        <mat-checkbox [formControl]="statusUpdate">
          {{ 'bulkEdit.status' | translate }}
        </mat-checkbox>
      </div>

      <!-- Status Value -->
      <div class="col-md-6 col-lg-6 col-sm-6" *ngIf="statusUpdate?.value">
        <mat-radio-group formControlName="isActive">
          <mat-radio-button value="true">{{ 'bulkEdit.active' | translate }}</mat-radio-button>
          <mat-radio-button value="false">{{ 'bulkEdit.inactive' | translate }}</mat-radio-button>
        </mat-radio-group>
      </div>
    </div>
  </form>
</mat-dialog-content>

<mat-dialog-actions align="center">
  <button
    *ngIf="vatUpdate?.value || categoryUpdate?.value || statusUpdate?.value"
    (click)="onSubmit($event)"
    mat-flat-button
    color="primary">
    {{ 'common.buttons.submit' | translate }}
  </button>

  <button (click)="onNoClick($event)" mat-button color="warn" mat-stroked-button>
    {{ 'common.buttons.cancel' | translate }}
  </button>
</mat-dialog-actions>
