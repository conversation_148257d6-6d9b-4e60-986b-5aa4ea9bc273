import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Provision } from 'src/app/core/interfaces/sales';

@Component({
  selector: 'app-preview-terms-modal',
  templateUrl: './preview-terms-modal.component.html',
  styleUrls: ['./preview-terms-modal.component.scss'],
})
export class PreviewTermsModalComponent {
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: Provision,
    private dialogRef: MatDialogRef<PreviewTermsModalComponent>
  ) {}

  onClose() {
    this.dialogRef.close();
  }
}
