<!-- Filter Place Holders -->
<div fxLayout="row wrap">
  <div fxFlex="100">
    <mat-card appearance="outlined">
      <mat-card-content>
        <mat-label>Copy Accounts</mat-label>
        <form [formGroup]="branchSetupForm" autocomplete="off">
          <ng-container>
            <div fxLayout="row wrap">
              <div class="p-2" fxFlex.gt-sm="25" fxFlex.gt-xs="25" fxFlex="100">
                <mat-label>Source Branch</mat-label>
                <mat-form-field class="w-100">
                  <mat-select
                    #branch
                    placeholder="select branch"
                    formControlName="sourceBranchId"
                    required>
                    <mat-option *ngFor="let branch of branchList" [value]="branch.branchId">
                      {{ branch.branchName }}</mat-option
                    >
                  </mat-select>
                </mat-form-field>
              </div>
              <div class="p-2" fxFlex.gt-sm="25" fxFlex.gt-xs="25" fxFlex="100">
                <mat-label>Target Branch</mat-label>
                <mat-form-field class="w-100">
                  <mat-select
                    #branch
                    placeholder="select branch"
                    formControlName="targetBranchId"
                    required>
                    <mat-option *ngFor="let branch of branchList" [value]="branch.branchId">
                      {{ branch.branchName }}</mat-option
                    >
                  </mat-select>
                </mat-form-field>
              </div>
            </div>

            <div fxLayout="row wrap">
              <div class="button" fxFlex.gt-sm="25" fxFlex.gt-xs="25" fxFlex="100">
                <button (click)="onFormSubmit($event); (false)" mat-stroked-button color="primary">
                  {{ 'common.submit' | translate }}
                </button>
              </div>
            </div>
          </ng-container>
        </form>
      </mat-card-content>
    </mat-card>
  </div>
</div>

<!-- Filter Place Holders -->
