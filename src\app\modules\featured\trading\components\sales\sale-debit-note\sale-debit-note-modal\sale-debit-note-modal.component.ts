/* eslint-disable @typescript-eslint/no-explicit-any */
import { ChangeDetectionStrategy, Component, Inject } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { priceTypes } from 'src/app/core/configs/dropDownConfig';
import { ISalesItem } from 'src/app/core/interfaces/sales';

interface Change {
  item: ISalesItem;
  changes?: any;
  status: ChangeStatus;
}
type ChangeStatus = 'added' | 'deleted' | 'changed' | 'unchanged';

@Component({
  selector: 'app-sale-debit-note-modal',
  templateUrl: './sale-debit-note-modal.component.html',
  styleUrls: ['./sale-debit-note-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SaleDebitNoteModalComponent {
  form1: FormGroup;
  form2: FormGroup;
  differences: Change[] = [];
  constructor(
    public dialogRef: MatDialogRef<SaleDebitNoteModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit(): void {
    console.log('data', this.data, this.data.form1, this.data.form2);
    this.differences = this.compareItems(this.data.form1.value, this.data.form2.value);
    console.log('differences->', this.differences);
  }

  compareItems = (originalItems: ISalesItem[], modifiedItems: ISalesItem[]) => {
    const changes: Change[] = [];
    // Find added items
    modifiedItems.forEach(modifiedItem => {
      const foundItem = originalItems.find(
        (originalItem: ISalesItem) =>
          originalItem.product.itemCode === modifiedItem.product.itemCode &&
          originalItem.product.unitName === modifiedItem.product.unitName &&
          originalItem.product.warehouseId === modifiedItem.product.warehouseId
      );
      if (!foundItem) {
        changes.push({ status: 'added', item: modifiedItem });
      }
    });

    // Find changed or deleted items
    originalItems.forEach(originalItem => {
      const modifiedItem = modifiedItems.find(
        (item: ISalesItem) =>
          item.product.itemCode === originalItem.product.itemCode &&
          item.product.unitName === originalItem.product.unitName &&
          item.product.warehouseId === originalItem.product.warehouseId
      );

      console.log(modifiedItem);
      if (!modifiedItem) {
        changes.push({ status: 'deleted', item: originalItem });
      } else {
        const changedFields = {};

        for (const field in modifiedItem) {
          if (field !== 'product' && modifiedItem[field] !== originalItem[field]) {
            if (field === 'quantity' && modifiedItem[field] > 0) {
              changedFields[field] = {
                old: originalItem[field],
                new: modifiedItem[field],
              };
            }
            if (field === 'price') {
              changedFields[field] = {
                old: originalItem[field],
                new: modifiedItem[field],
              };
            }
            if (field === 'discount') {
              changedFields[field] = {
                old: originalItem[field],
                new: modifiedItem[field],
              };
            }
          }
        }

        if (Object.keys(changedFields).length > 0) {
          changes.push({ status: 'changed', item: modifiedItem, changes: changedFields });
        }
        // else {
        //   changes.push({ item: modifiedItem, status: 'unchanged' });
        // }
      }
    });

    return changes;
  };

  public priceTypes(priceType: number) {
    return priceTypes.filter(data => data.value === priceType)[0].display;
  }

  onNoClick(): void {
    this.dialogRef.close();
  }
}
