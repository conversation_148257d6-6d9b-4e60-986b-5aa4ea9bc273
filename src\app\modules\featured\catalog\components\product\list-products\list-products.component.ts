import { Directionality } from '@angular/cdk/bidi';
import { SelectionModel } from '@angular/cdk/collections';
import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { forkJoin } from 'rxjs';

import { CategoryService } from '../../../../../../core/api/category.service';
import { ProductService } from '../../../../../../core/api/product.service';

import { CommonService } from 'src/app/core/api/common.service';
import { PaginatedComponentBase } from 'src/app/core/base-classes/paginated-component.base';
import { PaginatedFilter } from 'src/app/core/interfaces/PaginatedFilter';
import { DeleteConfirmationComponent } from 'src/app/modules/shared/components/delete-confirmation/delete-confirmation.component';
import { SearchboxComponent } from 'src/app/modules/shared/components/searchbox/searchbox.component';
import { BulkEditData } from '../../../models/bulkedit';
import { Category } from '../../../models/category';
import { CategoryParams } from '../../../models/categoryParams';
import { Product } from '../../../models/product';
import { ProductParams } from '../../../models/productParams';
import { BulkEditProductsComponent } from '../bulk-edit-products/bulk-edit-products.component';

@Component({
  selector: 'app-list-products',
  templateUrl: './list-products.component.html',
  styleUrls: ['./list-products.component.scss'],
})
export class ListProductsComponent extends PaginatedComponentBase implements OnInit {
  @ViewChild('searchBoxForm') searchBoxForm!: SearchboxComponent;
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  productList!: MatTableDataSource<Product>;
  filterForm!: UntypedFormGroup;

  displayedColumns: string[] = [
    'select',
    'action',
    'itemCode',
    'nameArabic',
    'nameEnglish',
    'partNumber',
    'category',
    'vat',
  ];
  allComplete = false;
  category: Category[];
  loading = false;
  parentCategories: Category[];
  products: Product[] = [];
  selection = new SelectionModel<Product>(true, []);
  isAdvancedSearchEnabled = false;
  selectedIds: Set<number> = new Set(); // Track selected items by ID
  isFilterApplied = false; // Track if filters are applied
  activeFiltersCount = 0; // Count of active filters

  constructor(
    private fb: UntypedFormBuilder,
    private categoryService: CategoryService,
    private dialog: MatDialog,
    private productService: ProductService,
    private direction: Directionality,
    private commonService: CommonService
  ) {
    super();
  }

  ngOnInit(): void {
    super.ngOnInit();
    this.initializeFilterForm();
    this.loadInitialData();

    // Listen for form value changes to update filter status
    this.filterForm.valueChanges.subscribe(() => {
      this.checkIfFiltersApplied();
    });

    // Listen for search box value changes
    this.filterForm.get('searchBoxForm')?.valueChanges.subscribe(() => {
      this.checkIfFiltersApplied();
    });
  }

  protected override initPagination(): void {
    this.pageSize = 10;
    this.pageIndex = 0;
  }

  private initializeFilterForm(): void {
    this.filterForm = this.fb.group({
      categoryId: [[]],
      searchBoxForm: [],
    });
  }

  private loadInitialData(): void {
    const category$ = this.categoryService.getAllCategories(new CategoryParams());
    const products$ = this.productService.getAllProducts({
      pageNumber: this.pageIndex + 1,
      pageSize: this.pageSize,
    });

    forkJoin([category$, products$]).subscribe(([categories, products]) => {
      this.category = categories;
      this.splitCategoryAndParentCategory();
      this.updatePaginationState(products.totalRecordsCount);
      this.products = products.items;

      // Create new MatTableDataSource with the items
      this.productList = new MatTableDataSource(products.items);

      // Apply sort if it exists
      if (this.sort) {
        this.productList.sort = this.sort;
      }
    });
  }

  private splitCategoryAndParentCategory(): void {
    this.parentCategories = this.category.filter(data => data.parentCategoryId === null);
  }

  getParentName(parentId: number): string {
    return (
      this.parentCategories.find((data: Category) => data.categoryId === parentId)?.nameArabic || ''
    );
  }

  /**
   * Check if all rows are selected
   * @returns true if all rows are selected
   */
  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.productList?.data.length || 0;
    return numSelected === numRows && numRows > 0;
  }

  /**
   * Toggle selection of all rows
   */
  masterToggle(): void {
    if (this.isAllSelected()) {
      // Clear selection if all are selected
      this.selection.clear();
      this.selectedIds.clear();
    } else {
      // Select all rows
      this.productList.data.forEach(row => {
        this.selection.select(row);
        this.selectedIds.add(row.itemId);
      });
    }
  }

  /**
   * Mark a row as checked
   * @param row The row to check
   */
  checked(row: Product): void {
    this.selection.select(row);
    this.selectedIds.add(row.itemId); // Add selected item's ID
  }

  /**
   * Mark a row as unchecked
   * @param row The row to uncheck
   */
  unChecked(row: Product): void {
    this.selection.deselect(row);
    this.selectedIds.delete(row.itemId); // Remove deselected item's ID
  }

  /**
   * Check if a row is selected
   * @param row The row to check
   * @returns true if the row is selected
   */
  isChecked(row: Product): boolean {
    return this.selectedIds.has(row.itemId); // Check if item's ID is in selectedIds
  }

  onBulkEditClick(event: Event): void {
    event.preventDefault();
    const modalData: BulkEditData = {
      selectedRecords: this.selection.selected.map(result => result.itemId),
    };
    const dialogConfig: MatDialogConfig = {
      data: modalData,
      minWidth: '90vw',
      maxHeight: '90vh',
      panelClass: ['green_theme'],
      hasBackdrop: true,
      disableClose: false,
      autoFocus: false,
      direction: this.direction.value,
    };
    const dialogRef = this.dialog.open(BulkEditProductsComponent, dialogConfig);
    dialogRef.componentInstance.accepted.subscribe(() => {
      this.selectedIds.clear();
      this.selection.clear();
      this.getFilterData();
    });
    dialogRef.afterClosed().subscribe(() => {
      this.selection.clear();
      this.selectedIds.clear();
    });
  }

  /**
   * Clear all filters and reset the form
   * @param event The event that triggered the clear
   */
  clearFilters(event: Event): void {
    event.preventDefault();

    // Reset form state
    this.filterForm.markAsUntouched();
    this.filterForm.markAsPristine();
    this.filterForm.reset();
    this.searchBoxForm.resetSearchBox();

    // Reset filter state
    this.isFilterApplied = false;
    this.activeFiltersCount = 0;

    // Clear selection
    this.selection.clear();
    this.selectedIds.clear();

    // Reload data
    this.getFilterData();
  }

  // Override the base class method
  override onPageChange(pageEvent: PageEvent): void {
    super.onPageChange(pageEvent);
  }

  // Implement the abstract method from the base class
  protected override loadPagedData(filter: PaginatedFilter): void {
    this.getFilterData(null, filter);
  }

  getFilterData(event?: Event, pageEvent?: PaginatedFilter): void {
    event?.preventDefault();
    if (!this.searchBoxForm.isValid) return;

    // Create base params with pagination
    const params: ProductParams = {
      pageNumber: pageEvent?.pageNumber ?? this.pageIndex + 1,
      pageSize: pageEvent?.pageSize ?? this.pageSize,
    };

    // Add sort information if available
    // if (this.sort && this.sort.active && this.sort.direction) {
    //   params.orderBy = `${this.sort.active} ${this.sort.direction}`;
    // }

    // Get filter values
    const searchString = this.searchBoxForm.searchBoxForm.controls['searchString'].value;
    const searchType = this.searchBoxForm.searchBoxForm.controls['searchType'].value;
    const categoryIds = this.filterForm.controls['categoryId'].value;

    // Only add filter parameters if they have values
    if (searchString && searchString.trim().length > 0) {
      params.searchString = searchString;
      params.searchType = searchType;
    }

    if (categoryIds && categoryIds.length > 0) {
      params.categoryIds = categoryIds;
    }

    // Check if filters are applied
    this.checkIfFiltersApplied();

    this.productService.getAllProducts(params).subscribe(result => {
      this.products = result.items;

      // Create new MatTableDataSource with the items
      this.productList = new MatTableDataSource(result.items);

      // Apply sort if it exists
      if (this.sort) {
        this.productList.sort = this.sort;
      }

      this.updatePaginationState(result.totalRecordsCount);

      // Clear selection when data is refreshed
      this.selection.clear();
      this.selectedIds.clear();
    });
  }

  /**
   * Check if any filters are applied and count them
   * @returns true if filters are applied, false otherwise
   */
  checkIfFiltersApplied(): boolean {
    // Get filter values
    const searchString = this.searchBoxForm.searchBoxForm.controls['searchString'].value;
    const categoryIds = this.filterForm.controls['categoryId'].value;

    // Check if search string is not empty or categories are selected
    const hasSearchString = searchString && searchString.trim().length > 0;
    const hasCategoryFilter = categoryIds && categoryIds.length > 0;

    // Count active filters
    this.activeFiltersCount = 0;
    if (hasSearchString) this.activeFiltersCount++;
    if (hasCategoryFilter) this.activeFiltersCount += categoryIds ? categoryIds.length : 0;

    this.isFilterApplied = this.activeFiltersCount > 0;

    return this.isFilterApplied;
  }

  deleteProducts(event: Event): void {
    event.preventDefault();
    const dialogConfig: MatDialogConfig = {
      minWidth: '600px',
      panelClass: 'green_theme',
      hasBackdrop: true,
      disableClose: false,
      autoFocus: false,
      direction: this.direction.value,
    };
    const selectedRecords = this.selection.selected.map(result => result.itemId);
    const dialogRef = this.dialog.open(DeleteConfirmationComponent, dialogConfig);
    dialogRef.componentInstance.accepted.subscribe(() => {
      this.productService.deleteProduct(selectedRecords[0].toString()).subscribe(() => {
        //this.toastr.success('Product is deleted successfully');
        this.commonService.playSuccessSound();
        this.selectedIds.clear();
        this.selection.clear();
        this.getFilterData();
      });
    });
  }
}
