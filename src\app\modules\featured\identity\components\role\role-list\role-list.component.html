<ng-container *ngIf="!isLoading">
  <!-- action bar -->
  <app-create-action
    *appHasPermission="['Role.Create', 'AllPermissions']"
    [label]="'roles.register_role_Permission' | translate"></app-create-action>
  <!-- action bar -->
  <mat-card appearance="outlined">
    <mat-card-content>
      <!-- search field -->
      <div class="row no-gutters">
        <div class="col-md-6 col-lg-6 col-sm-12 d-flex">
          <mat-form-field class="w-100">
            <input
              #searchInput
              (keyup)="applyFilter($event.target.value)"
              matInput
              autocomplete="off" />
            <i-tabler class="icon-16" matPrefix name="search"></i-tabler>
            <a
              class="cursor-pointer"
              *ngIf="searchInput?.value"
              (click)="clearSearchInput()"
              matSuffix>
              <i-tabler class="icon-16 error" name="X"></i-tabler>
            </a>
          </mat-form-field>
        </div>
      </div>
      <!-- search field -->
      <mat-card-title class="m-t-10">{{ formTitle | translate }}</mat-card-title>
      <!-- table -->
      <div class="table-responsive">
        <table [dataSource]="dataSource" mat-table matSort>
          <ng-container matColumnDef="id">
            <th *matHeaderCellDef mat-header-cell mat-sort-header>
              {{ 'roles.roleId' | translate }}
            </th>
            <td *matCellDef="let element" mat-cell>
              {{ element.id }}
            </td>
          </ng-container>
          <ng-container matColumnDef="name">
            <th *matHeaderCellDef mat-header-cell mat-sort-header>
              {{ 'roles.roleName' | translate }}
            </th>
            <td *matCellDef="let element" mat-cell>
              {{ element.name }}
            </td>
          </ng-container>
          <ng-container matColumnDef="description">
            <th *matHeaderCellDef mat-header-cell mat-sort-header>
              {{ 'roles.roleDesc' | translate }}
            </th>
            <td *matCellDef="let element" mat-cell>
              {{ element.description }}
            </td>
          </ng-container>
          <ng-container matColumnDef="action">
            <th *matHeaderCellDef mat-header-cell>{{ 'common.action' | translate }}</th>
            <td *matCellDef="let element" mat-cell>
              <ng-container *ngIf="element.id !== getUserId">
                <a
                  class="cursor-pointer"
                  *appHasPermission="['Role.Update', 'AllPermissions']"
                  [routerLink]="['edit', element.id]"
                  ><i-tabler class="icon-16" name="edit"></i-tabler>
                </a>
              </ng-container>
              <a
                class="cursor-pointer"
                *appHasPermission="['Role.View', 'AllPermissions']"
                [routerLink]="['view', element.id]"
                ><i-tabler class="icon-16" name="eye"></i-tabler>
              </a>
            </td>
          </ng-container>
          <tr class="mat-row" *matNoDataRow>
            <td class="text-center text-info" [attr.colspan]="displayedColumns.length">
              {{ 'common.noDataFound' | translate }}
            </td>
          </tr>
          <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
          <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
        </table>
      </div>
      <mat-paginator
        *ngIf="dataSource?.filteredData?.length > 0"
        [length]="dataSource.filteredData.length"
        [pageIndex]="0"
        [pageSize]="25"
        [pageSizeOptions]="[25, 50]"></mat-paginator>
    </mat-card-content>
  </mat-card>
</ng-container>
