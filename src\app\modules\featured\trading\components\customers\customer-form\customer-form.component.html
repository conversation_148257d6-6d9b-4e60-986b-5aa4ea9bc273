<app-dialog-header *ngIf="modalData?.data"></app-dialog-header>

<ng-container>
  <mat-card appearance="outlined">
    <form [formGroup]="customerForm" [ngClass]="{ readOnly: IsViewMode }" autocomplete="off">
      <mat-card-title>{{ 'customer.customerTitle' | translate }}</mat-card-title>
      <div class="row no-gutters m-t-10">
        <!-- nameArabic  -->
        <div class="p-2 col-md-4 col-sm-6">
          <mat-label>{{ 'customer.parentId' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <mat-select formControlName="parentAccountId">
              <mat-option
                *ngFor="let parentAccount of parentAccounts"
                [value]="parentAccount.accountId">
                {{ parentAccount | localized }}
              </mat-option>
            </mat-select>
            <mat-error
              *ngIf="
                customerForm?.controls['parentAccountId'].hasError('required') &&
                customerForm?.controls['parentAccountId'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
        <div class="p-2 col-md-4 col-sm-6">
          <mat-label>{{ 'customer.name' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input type="text" maxlength="40" matInput formControlName="nameArabic" />
            <mat-error
              *ngIf="
                customerForm?.controls['nameArabic'].hasError('required') &&
                customerForm?.controls['nameArabic'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
        <div class="p-2 col-md-4 col-sm-6">
          <mat-label>{{ 'customer.englishName' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input type="text" maxlength="40" matInput formControlName="nameEnglish" />
            <mat-error
              *ngIf="
                customerForm?.controls['nameEnglish'].hasError('required') &&
                customerForm?.controls['nameEnglish'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="row no-gutters">
        <div class="p-2 col-md-3 col-sm-6">
          <mat-label>{{ 'common.field.accountNumber' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input readonly type="text" maxlength="40" matInput formControlName="accountNumber" />
          </mat-form-field>
        </div>
        <div class="p-2 col-md-3 col-sm-6">
          <mat-label>{{ 'common.field.vatNo' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input type="text" maxlength="40" matInput formControlName="vatNumber" />
            <mat-error
              *ngIf="
                customerForm?.controls['vatNumber'].hasError('required') &&
                customerForm?.controls['vatNumber'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
            <mat-error
              *ngIf="
                customerForm?.controls['vatNumber'].hasError('invalidLength') &&
                customerForm?.controls['vatNumber'].touched
              ">
              {{ 'common.minLength' | translate : { length: 15 } }}
            </mat-error>
          </mat-form-field>
        </div>
        <div class="p-2 col-md-3 col-sm-6">
          <mat-label>{{ 'common.field.phone' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input type="text" maxlength="40" matInput formControlName="phoneNumber" />
            <mat-error
              *ngIf="
                customerForm?.controls['phoneNumber'].hasError('required') &&
                customerForm?.controls['phoneNumber'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
        <div class="p-2 col-md-3 col-sm-6">
          <mat-label>{{ 'common.field.email' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input type="text" maxlength="40" matInput formControlName="emailId" />
            <mat-error
              *ngIf="
                customerForm.controls['emailId'].hasError('required') &&
                customerForm.controls['emailId'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
            <mat-error
              *ngIf="
                customerForm.controls['emailId'].errors?.email &&
                customerForm.controls['emailId'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="row no-gutters">
        <div class="p-2 col-md-6 col-sm-6">
          <mat-label>{{ 'tenants.identification' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input [appMaxlength]="30" matInput type="text" formControlName="identification" />
            <mat-error
              *ngIf="
                customerForm?.controls['identification'].hasError('required') &&
                customerForm?.controls['identification'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
        <div class="p-2 col-md-6 col-sm-6">
          <mat-label>{{ 'tenants.identificationCode' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <mat-select formControlName="identificationCode">
              <mat-option
                *ngFor="let identificationCode of identificationCodes"
                [value]="identificationCode.value">
                {{ identificationCode.display | translate }}({{ identificationCode.value }})
              </mat-option>
            </mat-select>
            <mat-error
              *ngIf="
                customerForm?.controls['identificationCode'].hasError('required') &&
                customerForm?.controls['identificationCode'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
      </div>
      <mat-card-title>{{ 'common.field.transactionDetails' | translate }}</mat-card-title>
      <div class="row no-gutters m-t-10">
        <!-- discountPercent -->
        <div class="p-2 col-md-3 col-sm-6">
          <mat-label>{{ 'common.field.percDiscount' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input type="text" maxlength="40" matInput formControlName="discountPercent" />
            <mat-error
              *ngIf="
                customerForm?.controls['discountPercent'].hasError('required') &&
                customerForm?.controls['discountPercent'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
        <div class="p-2 col-md-3 col-sm-6">
          <mat-label>{{ 'common.field.allowedBalance' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input type="number" matInput formControlName="allowedBalance" />
            <mat-error
              *ngIf="
                customerForm?.controls['allowedBalance'].hasError('required') &&
                customerForm?.controls['allowedBalance'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
        <div class="p-2 col-md-3 col-sm-6">
          <mat-label>{{ 'common.field.allowedDays' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input type="text" maxlength="40" matInput formControlName="paymentToleranceDays" />
            <mat-error
              *ngIf="
                customerForm?.controls['paymentToleranceDays'].hasError('required') &&
                customerForm?.controls['paymentToleranceDays'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
        <div class="p-2 col-md-3 col-sm-6">
          <mat-label>{{ 'common.field.commission' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input type="number" matInput formControlName="commission" />
            <mat-error
              *ngIf="
                customerForm?.controls['commission'].hasError('required') &&
                customerForm?.controls['commission'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
        <div class="p-2 col-md-3 col-sm-6">
          <mat-label>{{ 'common.field.yrlyTarget' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input type="number" matInput formControlName="yearlyTarget" />
            <mat-error
              *ngIf="
                customerForm?.controls['yearlyTarget'].hasError('required') &&
                customerForm?.controls['yearlyTarget'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
        <div class="p-2 col-md-3 col-sm-6">
          <mat-label>{{ 'common.field.costAccount' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <mat-select formControlName="costCentreId">
              <mat-option>{{ 'common.reset' | translate }}</mat-option>
              <mat-option
                *ngFor="let costCentreAccount of costCentreAccounts"
                [value]="costCentreAccount.costCentreId">
                {{ costCentreAccount | localized }}
              </mat-option>
            </mat-select>
            <mat-error
              *ngIf="
                customerForm?.controls['costCentreId'].hasError('required') &&
                customerForm?.controls['costCentreId'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
        <div class="p-2 col-md-3 col-sm-6">
          <mat-label>{{ 'common.field.distributorAccount' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <mat-select formControlName="distributorAccountId">
              <mat-option>{{ 'common.reset' | translate }}</mat-option>
              <mat-option
                *ngFor="let distributorAccount of distributorAccounts"
                [value]="distributorAccount.accountId">
                {{ distributorAccount | localized }}
              </mat-option>
            </mat-select>
            <mat-error
              *ngIf="
                customerForm?.controls['distributorAccountId'].hasError('required') &&
                customerForm?.controls['distributorAccountId'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="row no-gutters">
        <div class="p-2 col-md-2 col-lg-2 col-sm-3">
          <mat-slide-toggle
            class="m-l-10 m-t-5"
            [labelPosition]="'after'"
            formControlName="isFreezed">
            {{ 'common.field.freezed' | translate }}
          </mat-slide-toggle>
        </div>

        <div class="p-2 col-md-2 col-lg-2 col-sm-3">
          <mat-slide-toggle
            class="m-l-10 m-t-5"
            [labelPosition]="'after'"
            formControlName="hasCashPrivilege">
            {{ 'paymentsType.cash' | translate }}
          </mat-slide-toggle>
        </div>
        <div class="p-2 col-md-2 col-lg-2 col-sm-3">
          <mat-slide-toggle
            class="m-l-10 m-t-5"
            [labelPosition]="'after'"
            formControlName="hasCreditPrivilege">
            {{ 'paymentsType.credit' | translate }}
          </mat-slide-toggle>
        </div>
        <div class="p-2 col-md-2 col-lg-2 col-sm-3">
          <mat-slide-toggle
            class="m-l-10 m-t-5"
            [labelPosition]="'after'"
            formControlName="hasTransferPrivilege">
            {{ 'paymentsType.bank' | translate }}
          </mat-slide-toggle>
        </div>
        <div class="p-2 col-md-2 col-lg-2 col-sm-3">
          <mat-slide-toggle
            class="m-l-10 m-t-5"
            [labelPosition]="'after'"
            formControlName="hasCardPrivilege">
            {{ 'paymentsType.card' | translate }}
          </mat-slide-toggle>
        </div>
      </div>
      <div
        class="row no-gutters"
        *ngIf="customerForm.errors?.atLeastOneRequired && customerForm?.touched">
        <mat-error> {{ 'paymentsType.paymentSelectionError' | translate }} </mat-error>
      </div>
      <div class="row no-gutters">
        <div class="col-md-22">
          <app-address #addressForm formControlName="address"></app-address>
        </div>
      </div>
    </form>
  </mat-card>

  <app-action-buttons [mode]="mode" (submitAction)="onSubmit($event)"></app-action-buttons>
</ng-container>
