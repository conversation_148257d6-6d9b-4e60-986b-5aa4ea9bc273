import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DashBoardGuard } from '../../core/core/guards/dashboard.guard';
import { PermissionGuard } from '../../core/core/guards/permission.guard';
import { BranchFormComponent } from './components/branches/branch-form/branch-form.component';
import { BranchesListComponent } from './components/branches/branches-list/branches-list.component';
import { CompanyComponent } from './components/company/company.component';
import { CompanyDashboardComponent } from './components/dashboard/company-dashboard.component';
import { StoreFormComponent } from './components/stores/store-form/store-form.component';
import { StoresListComponent } from './components/stores/stores-list/stores-list.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'dashboard',
    pathMatch: 'full',
  },
  {
    path: 'dashboard',
    component: CompanyDashboardComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Enterprise Settings Features',
      urls: [{ title: 'Main Dashboard', url: '/dashboard' }],
      allowedPermissions: ['EnterpriseManagement', 'AllPermissions'],
    },
  },
  {
    path: 'company',
    component: CompanyComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'company.registration',
      urls: [{ title: 'Enterprise Settings Dashboard', url: '/enterprise/dashboard' }],
      allowedPermissions: ['Company.Update', 'AllPermissions'],
    },
  },
  {
    path: 'warehouses',
    component: StoresListComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'stores.listStores',
      urls: [{ title: 'Enterprise Settings Dashboard', url: '/enterprise/dashboard' }],
      allowedPermissions: ['WareHouse.View', 'AllPermissions'],
    },
  },

  {
    path: 'warehouses/create',
    component: StoreFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'stores.createStore',
      mode: 'create',
      urls: [{ title: 'WareHouse Listing', url: '/enterprise/warehouses' }],
      allowedPermissions: ['WareHouse.Create', 'AllPermissions'],
    },
  },
  {
    path: 'warehouses/view/:id',
    component: StoreFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'stores.viewStore',
      mode: 'view',
      urls: [{ title: 'WareHouse Listing', url: '/enterprise/warehouses' }],
      allowedPermissions: ['WareHouse.View', 'AllPermissions'],
    },
  },
  {
    path: 'branches',
    component: BranchesListComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'branch.listbranch',
      urls: [{ title: 'Enterprise Settings Dashboard', url: '/enterprise/dashboard' }],
      allowedPermissions: ['Branch.View', 'AllPermissions'],
    },
  },
  {
    path: 'branches/create',
    component: BranchFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'branch.createBranch',
      mode: 'create',
      urls: [{ title: 'Branch Listing', url: '/enterprise/branches' }],
      allowedPermissions: ['Branch.Create', 'AllPermissions'],
    },
  },
  {
    path: 'branches/edit/:id',
    component: BranchFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'branch.editBranch',
      mode: 'edit',
      urls: [{ title: 'Enterprise Settings Dashboard', url: '/enterprise/dashboard' }],
      allowedPermissions: ['Branch.Update', 'AllPermissions'],
    },
  },
  {
    path: 'branches/view/:id',
    component: BranchFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'branch.viewBranch',
      mode: 'view',
      urls: [{ title: 'Enterprise Settings Dashboard', url: '/enterprise/dashboard' }],
      allowedPermissions: ['Branch.View', 'AllPermissions'],
    },
  },
  {
    path: 'warehouses/edit/:id',
    component: StoreFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'stores.editStore',
      mode: 'edit',
      urls: [{ title: 'WareHouse Listing', url: '/enterprise/warehouses' }],
      allowedPermissions: ['WareHouse.Update', 'AllPermissions'],
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class SettingsRoutingModule {}
