import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { PaginatedFilter } from 'src/app/core/interfaces/PaginatedFilter';
import { Journal } from '../../models/journal';
import { JournalEntriesSearchBoxComponent } from '../journal-entries-search-box/journal-entries-search-box.component';
import { JournalParams } from './../../models/journalParams';
import { JournalEntriesService } from './../../services/journal-entries.service';
import { journalEntryCreationTypes, journalEntryType } from 'src/app/core/configs/dropDownConfig';
import { MultilingualService } from 'src/app/modules/core/core/services/multilingual.service';

@Component({
  selector: 'app-journal-entries',
  templateUrl: './journal-entries.component.html',
  styleUrls: ['./journal-entries.component.scss'],
})
export class JournalEntriesComponent implements OnInit {
  @ViewChild('searchBoxForm') searchBoxForm: JournalEntriesSearchBoxComponent;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  journals: Journal[];
  displayedColumns: string[];
  resultsLength: number;
  filteredDataTriggered = false;
  isLoading = true;
  filterForm: UntypedFormGroup;
  dataSource: MatTableDataSource<Journal>;
  journalTypes = journalEntryType;
  journalCreationTypes = journalEntryCreationTypes;

  constructor(
    private journalEntriesService: JournalEntriesService,
    private fb: UntypedFormBuilder,
    private ms: MultilingualService
  ) {}

  ngOnInit(): void {
    this.filterForm = this.fb.group({
      journalType: [null],
      journalCreationType: [null],
      description: [],
      searchBoxForm: [],
    });
    this.getJournals();
    this.initColumns();
  }

  ngAfterViewInit(): void {
    if (this.dataSource) {
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
    }
  }

  getJournals() {
    const journalParams: JournalParams = new JournalParams();
    journalParams.pageNumber = 1;
    journalParams.pageSize = 5;
    this.journalEntriesService.getAllJournalEntries(journalParams).subscribe(result => {
      this.journals = result.journalResponses;
      this.dataSource = new MatTableDataSource<Journal>(this.journals);
      this.dataSource.paginator = this.paginator;
      this.resultsLength = result.totalRecordsCount;
      this.isLoading = false;
    });
  }
  initColumns(): void {
    this.displayedColumns = [
      'action',
      'journalNumber',
      'isPosted',
      'journalDate',
      'journalType',
      'journalCreationType',
    ];
  }

  applyFilter(filterValue: string): void {
    filterValue = filterValue.trim();
    filterValue = filterValue.toLowerCase();
    this.dataSource.filter = filterValue;
  }

  onPageChange(pageEvent: PageEvent) {
    const event: PaginatedFilter = {
      pageNumber: pageEvent.pageIndex + 1 ?? 1,
      pageSize: pageEvent.pageSize ?? 5,
    };
    this.getFilterData(null, event);
  }

  getEntryTypeDisplay(columnName: string): string {
    const journalType = this.journalTypes.find(type => type.value === columnName);
    return journalType ? journalType.display : '';
  }
  getEntryCreationTypeDisplay(columnName: string): string {
    const journalCreationType = this.journalCreationTypes.find(type => type.value === columnName);
    return journalCreationType ? journalCreationType.display : '';
  }

  getFilterData(event?: Event, pageEvent?: PaginatedFilter) {
    event?.preventDefault();
    console.log(this.journalCreationTypes);
    const journalParams: JournalParams = new JournalParams();
    journalParams.searchString = this.searchBoxForm.searchBoxForm.controls['searchString'].value;
    journalParams.searchType = this.searchBoxForm.searchBoxForm.controls['searchType'].value;
    journalParams.pageNumber = pageEvent?.pageNumber ?? 0;
    journalParams.pageSize = pageEvent?.pageSize ?? 5;
    journalParams.journalType = this.filterForm.controls['journalType'].value;
    journalParams.journalCreationType = this.filterForm.controls['journalCreationType'].value;
    this.journalEntriesService.getAllJournalEntries(journalParams).subscribe(result => {
      this.filteredDataTriggered = true;
      this.journals = null;
      this.journals = result.journalResponses;
      setTimeout(() => {
        this.dataSource = new MatTableDataSource<Journal>(this.journals);
        this.resultsLength = result.totalRecordsCount;
      });
    });
  }

  clearFilters(event: Event) {
    event.preventDefault();
    this.filterForm.markAsUntouched();
    this.filterForm.markAsPristine();
    this.filterForm.reset();
    this.searchBoxForm.resetSearchBox();
    this.getFilterData();
  }
}
