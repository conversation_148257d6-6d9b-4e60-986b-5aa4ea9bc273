import { Component, OnInit } from '@angular/core';
import { DashboardModulesHolder } from 'src/app/modules/shared/components/dashboard-modules-holder/modulesHolder';

@Component({
  selector: 'app-company-dashboard',
  templateUrl: './company-dashboard.component.html',
  styleUrls: ['./company-dashboard.component.scss'],
})
export class CompanyDashboardComponent implements OnInit {
  productModulesList: DashboardModulesHolder[] = [
    {
      moduleName: 'navigationMenus.companyProfile',
      moduleDescription: 'Manage Company Profile.',
      modulePermission: ['Company.Update', 'AllPermissions'],
      moduleImage: 'store',
      moduleRouterLink: '../company',
      moduleButtonAction: 'POS',
      moduleType: 'subModule',
    },
    {
      moduleName: 'navigationMenus.branchSetup',
      moduleDescription: 'Manage Branches.',
      modulePermission: ['Branch', 'AllPermissions'],
      moduleImage: 'storefront',
      moduleRouterLink: '../branches',
      moduleButtonAction: 'BackOffice',
      moduleType: 'subModule',
    },
    {
      moduleName: 'navigationMenus.wareHouseSetup',
      moduleDescription: 'Manage Warehouse.',
      modulePermission: ['WareHouse', 'AllPermissions'],
      moduleImage: 'warehouse',
      moduleRouterLink: '../warehouses',
      moduleButtonAction: 'BackOffice',
      moduleType: 'subModule',
    },
  ];
  constructor() {}

  ngOnInit(): void {}
}
