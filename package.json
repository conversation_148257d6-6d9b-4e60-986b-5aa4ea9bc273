{"name": "sawami", "version": "0.0.1", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build:preview": "ng build --configuration=preview", "test": "ng test", "e2e": "ng e2e", "lint": "eslint \"src/app/**/*.{ts,html}\" --fix --max-warnings=0", "format": "prettier \"src/app/**/*.{js,json,css,scss,less,ts,html}\" --write", "format-all": "npm run lint && npm run format", "prepare": "husky install"}, "private": true, "dependencies": {"@angular/animations": "15.2.9", "@angular/cdk": "^15.2.9", "@angular/common": "15.2.9", "@angular/compiler": "15.2.9", "@angular/core": "15.2.9", "@angular/flex-layout": "^9.0.0-beta.31", "@angular/forms": "15.2.9", "@angular/localize": "^15.2.9", "@angular/material": "^15.2.9", "@angular/material-moment-adapter": "^15.2.9", "@angular/platform-browser": "15.2.9", "@angular/platform-browser-dynamic": "15.2.9", "@angular/router": "15.2.9", "@ngneat/until-destroy": "^10.0.0", "@ngx-translate/core": "^13.0.0", "@ngx-translate/http-loader": "^6.0.0", "ajv": "^6.12.0", "angular-tabler-icons": "3.1.0", "angularx-qrcode": "^15.0.1", "core-js": "^3.6.4", "date-fns": "^1.30.1", "extend": "^3.0.2", "handlebars": "^4.7.3", "howler": "^2.2.3", "husky": "^9.1.6", "lodash": "^4.17.15", "lodash-es": "^4.17.21", "material-design-icons-iconfont": "^6.5.0", "moment": "^2.24.0", "ng-multiselect-dropdown": "^0.2.10", "ng2-file-upload": "^1.4.0", "ngx-cookie-service": "^16.0.0", "ngx-custom-validators": "8.0.0", "ngx-image-cropper": "^6.1.0", "ngx-loading": "^15.0.0", "ngx-mat-select-search": "^5.0.0", "ngx-order-pipe": "^3.0.0", "ngx-perfect-scrollbar": "^8.0.0", "ngx-scrollbar": "^11.0.0", "ngx-scrolltop": "^4.1.2", "number-to-arabic-words": "^1.5.3", "number-to-words": "^1.2.4", "prettier-plugin-organize-attributes": "^0.0.5", "rxjs": "~6.6.0", "rxjs-compat": "^6.5.4", "sass": "^1.29.0", "sawami": "file:", "tslib": "^2.0.0", "uuid": "^10.0.0", "xml-formatter": "^3.6.3", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "^15.2.8", "@angular-eslint/builder": "13.5.0", "@angular-eslint/eslint-plugin": "13.5.0", "@angular-eslint/eslint-plugin-template": "13.5.0", "@angular-eslint/schematics": "13.5.0", "@angular-eslint/template-parser": "13.5.0", "@angular/cli": "15.2.8", "@angular/compiler-cli": "15.2.9", "@auth0/angular-jwt": "^5.0.2", "@types/chartist": "0.9.46", "@types/howler": "^2.2.7", "@types/jasmine": "~3.6.0", "@types/jasminewd2": "~2.0.3", "@types/node": "^12.11.1", "@types/number-to-words": "^1.2.3", "@typescript-eslint/eslint-plugin": "^5.56.0", "@typescript-eslint/parser": "5.27.1", "codelyzer": "^0.0.28", "eslint": "^8.36.0", "eslint-config-prettier": "^8.8.0", "eslint-config-standard-with-typescript": "^34.0.1", "eslint-plugin-import": "^2.27.5", "eslint-plugin-n": "^15.6.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "^6.1.1", "jasmine-core": "~3.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.3.2", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "lint-staged": "^13.0.3", "ngx-toastr": "^16.2.0", "prettier": "^2.7.1", "pretty-quick": "^3.1.3", "protractor": "~7.0.0", "ts-node": "~8.3.0", "tslint": "~6.1.0", "typescript": "~4.8.4"}}