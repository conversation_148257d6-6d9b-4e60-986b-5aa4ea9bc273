<!-- action bar -->
<app-create-action
  *appHasPermission="['Adjustments.Create', 'AllPermissions']"
  [label]="'createAdjsutments.createAdjustment' | translate"></app-create-action>
<!-- action bar -->

<mat-card appearance="outlined">
  <form [formGroup]="filterForm" autocomplete="off">
    <div class="row no-gutters">
      <div class="col-md-4 col-lg-4 col-sm-12">
        <mat-label>{{ 'createAdjsutments.warehouse' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <mat-select #wareHouse placeholder="WareHouse" multiple formControlName="warehouseIds">
            <app-select-check-all [model]="filterForm.get('warehouseIds')" [values]="wareHouse">
            </app-select-check-all>
            <mat-option *ngFor="let wareHouse of warehouseList" [value]="wareHouse.warehouseId">
              {{ wareHouse | localized }}
            </mat-option>
          </mat-select>
          <mat-error
            *ngIf="
              filterForm?.controls['warehouseIds']?.hasError('required') &&
              filterForm?.controls['warehouseIds']?.touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>
    </div>
  </form>
  <div class="row no-gutters">
    <div class="col-md-3 col-lg-3 col-sm-12 d-flex">
      <button (click)="getFilterData($event); (false)" mat-stroked-button color="primary">
        {{ 'searchPanel.searchString' | translate }}
      </button>
    </div>
  </div>
  <!-- Main Table Contents -->
</mat-card>
<div class="table-responsive">
  <table class="w-100" [dataSource]="tableData" mat-table matSort>
    <ng-container matColumnDef="action">
      <th *matHeaderCellDef mat-header-cell>{{ 'createAdjsutments.action' | translate }}</th>
      <td class="f-s-14" *matCellDef="let element" mat-cell>
        <button class="d-flex justify-content-center" [matMenuTriggerFor]="menu1" mat-icon-button>
          <i-tabler class="icon-20 d-flex" name="dots-vertical"></i-tabler>
        </button>
        <mat-menu class="cardWithShadow" #menu1="matMenu">
          <ng-container *ngIf="!element.isPosted">
            <button
              *appHasPermission="['Adjustments.Update', 'AllPermissions']"
              [routerLink]="['edit', element.docId]"
              mat-menu-item>
              <i-tabler class="icon-16 m-r-4" name="edit"></i-tabler>
              <span>{{ 'common.buttons.edit' | translate }}</span>
            </button>
          </ng-container>
          <ng-container class="m-b-10" *appHasPermission="['Adjustments.View', 'AllPermissions']">
            <button [routerLink]="['view', element.docId]" mat-menu-item>
              <i-tabler class="icon-16 m-r-4" name="eye"></i-tabler>
              <span>{{ 'common.buttons.view' | translate }}</span>
            </button>
          </ng-container>
        </mat-menu>
      </td>
    </ng-container>
    <ng-container matColumnDef="docId">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'createAdjsutments.document' | translate }}
      </th>
      <td class="f-s-14" *matCellDef="let element; let i = index" mat-cell>
        {{ element.docId }}
      </td>
    </ng-container>
    <ng-container matColumnDef="warehouseName">
      <th *matHeaderCellDef mat-header-cell>{{ 'createAdjsutments.warehouse' | translate }}</th>
      <td class="f-s-14" *matCellDef="let element; let i = index" mat-cell>
        {{ element.warehouseName }}
      </td>
    </ng-container>
    <!-- Cost Price -->
    <ng-container matColumnDef="inventoryChkDate">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'createAdjsutments.adjustmentDate' | translate }}
      </th>
      <td class="f-s-14" *matCellDef="let element; let i = index" mat-cell>
        {{ element.inventoryChkDate }}
      </td>
    </ng-container>
    <ng-container matColumnDef="createdBy">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'createAdjsutments.createdBy' | translate }}
      </th>
      <td class="f-s-14" *matCellDef="let element; let i = index" mat-cell>
        {{ element.createdBy }}
      </td>
    </ng-container>
    <!-- Purchase Price  -->
    <ng-container matColumnDef="isPosted">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'createAdjsutments.status' | translate }}
      </th>
      <td class="f-s-14" *matCellDef="let element; let i = index" mat-cell>
        <div class="text-center bg-light-success rounded" *ngIf="element.isPosted">Posted</div>
        <div class="text-center bg-light-warning rounded" *ngIf="!element.isPosted">Not Posted</div>
      </td>
    </ng-container>
    <tr class="mat-row" *matNoDataRow>
      <td class="f-s-14" class="text-center" [attr.colspan]="displayedColumns.length">
        {{ 'common.noDataFound' | translate }}
      </td>
    </tr>
    <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
    <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
  </table>
</div>
<!-- Main Table Contents -->
