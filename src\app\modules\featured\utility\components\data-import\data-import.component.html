<!-- Filter Place Holders -->
<div fxLayout="row wrap">
  <div fxFlex="100">
    <mat-card appearance="outlined">
      <mat-card-content>
        <form [formGroup]="fileUploadForm" autocomplete="off">
          <ng-container>
            <div fxLayout="row wrap">
              <div class="p-2" fxFlex.gt-sm="25" fxFlex.gt-xs="25" fxFlex="100">
                <mat-label>Select Target Table</mat-label>

                <mat-form-field class="w-100">
                  <mat-select
                    #branch
                    placeholder="Table name"
                    formControlName="targetTable"
                    required>
                    <mat-option *ngFor="let table of tableList" [value]="table">
                      {{ table }}</mat-option
                    >
                  </mat-select>
                </mat-form-field>
              </div>
            </div>
            <div fxLayout="row wrap">
              <div class="p-2" fxFlex.gt-sm="25" fxFlex.gt-xs="25" fxFlex="100">
                <input
                  class="form-control"
                  id="customFile"
                  #UploadFileInput
                  (change)="onFileSelect($event)"
                  outlined
                  formControlName="fileName"
                  type="file"
                  accept=".xlsx,.xls"
                  name="myfile"
                  required />
                <div
                  class="alert"
                  *ngIf="
                    fileUploadForm.controls['fileName'].invalid &&
                    (fileUploadForm.controls['fileName'].dirty ||
                      fileUploadForm.controls['fileName'].touched)
                  ">
                  <mat-label
                    class="alert"
                    *ngIf="fileUploadForm.controls['fileName'].errors.required">
                    Please select a file
                  </mat-label>
                </div>
              </div>
            </div>
            <div fxLayout="row wrap">
              <div class="button" fxFlex.gt-sm="25" fxFlex.gt-xs="25" fxFlex="100">
                <button (click)="onFormSubmit($event); (false)" mat-stroked-button color="primary">
                  {{ 'common.submit' | translate }}
                </button>
              </div>
            </div>
          </ng-container>
        </form>
      </mat-card-content>
    </mat-card>
  </div>
</div>

<!-- Filter Place Holders -->
