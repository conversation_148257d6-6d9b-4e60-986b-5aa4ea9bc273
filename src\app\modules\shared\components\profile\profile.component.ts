import { Component, OnInit, ViewChild } from '@angular/core';
import {
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { CustomValidators } from 'ngx-custom-validators';
import { Subscription } from 'rxjs';
import { CommonService } from 'src/app/core/api/common.service';
import { Events } from 'src/app/modules/core/core/enums/events';
import { EventBusService } from 'src/app/modules/core/core/services/event-bus.service';

export interface DisplayValue {
  value: string;
  viewValue: string;
}

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss'],
})
export class ProfileComponent implements OnInit {
  @ViewChild('tabs', { static: false }) tabs;
  //
  public dir = 'ltr';
  public dark = false;
  public horizontal = false;
  public green = false;
  public blue = false;
  public danger = false;
  //
  public profileForm: UntypedFormGroup = Object.create(null);
  public securityForm: UntypedFormGroup = Object.create(null);
  public settingsForm: UntypedFormGroup = Object.create(null);
  public languages: DisplayValue[] = [
    { value: 'ar', viewValue: 'Arabic' },
    { value: 'en', viewValue: 'English' },
  ];
  imageChangedEvent: any = '';
  croppedImage: any = '';
  showCropper = false;
  defaultImage = 'assets/images/users/default.png';
  file: File;
  passwordhide = true;
  confirmPasswordhide = true;
  returnUrl: string;
  eventbusSub: Subscription;
  constructor(
    private router: Router,
    private fb: UntypedFormBuilder,
    private eventBusService: EventBusService,
    private commonService: CommonService
  ) {}

  ngOnInit(): void {
    this.eventbusSub = this.eventBusService.on(
      Events.PreviousUrl,
      (status: string) => (this.returnUrl = status)
    );
    this.initProfileForm();
    this.initSecurityForm();
  }

  initProfileForm(): void {
    this.profileForm = this.fb.group({
      fname: [
        null,
        Validators.compose([
          Validators.required,
          Validators.minLength(1),
          Validators.maxLength(40),
        ]),
      ],
      lname: [
        null,
        Validators.compose([
          Validators.required,
          Validators.minLength(1),
          Validators.maxLength(40),
        ]),
      ],
      email: [null, Validators.compose([])],
      date: [new Date(), Validators.compose([])],
      phone: [null, Validators.compose([])],
      language: ['ar', Validators.required],
    });
  }

  initSecurityForm(): void {
    const password = new UntypedFormControl('', Validators.required);
    const confirmPassword = new UntypedFormControl('', CustomValidators.equalTo(password));
    this.securityForm = this.fb.group({
      password: password,
      confirmPassword: confirmPassword,
    });
  }

  profileFormSubmitted(event: Event): void {
    event.preventDefault();
    this.profileForm.markAllAsTouched();
    if (this.profileForm.invalid) {
      this.commonService.scrollToError();
      return;
    } else {
      console.log('good to go with profile form....', this.profileForm.value);
      console.log('return url..', this.returnUrl);
      //  window.location.reload();
    }
  }

  securityFormSubmitted(event: Event): void {
    this.securityForm.markAllAsTouched();
    event.preventDefault();
    if (this.securityForm.invalid) {
      this.commonService.scrollToError();
      return;
    } else {
      console.log('good to go....', this.securityForm.value);
      console.log('return url..', this.returnUrl);
      //  window.location.reload();
    }
  }
}
