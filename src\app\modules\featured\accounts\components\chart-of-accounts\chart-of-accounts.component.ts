import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { Account } from '../../models/account';
import { ChartOfAccountsService } from '../../services/chart-of-accounts.service';
import { AccountParams } from '../../models/accountParams';
import { PaginatedFilter } from 'src/app/core/interfaces/PaginatedFilter';
import { AccountsSearchBoxComponent } from '../accounts-search-box/accounts-search-box.component';
import { accountGroups, accountTypes, businessGroups } from 'src/app/core/configs/dropDownConfig';
import { CookieService } from 'ngx-cookie-service';

@Component({
  selector: 'app-chart-of-accounts',
  templateUrl: './chart-of-accounts.component.html',
  styleUrls: ['./chart-of-accounts.component.scss'],
})
export class ChartOfAccountsComponent implements OnInit {
  @ViewChild('searchBoxForm') searchBoxForm: AccountsSearchBoxComponent;
  @ViewChild(MatPaginator) matPaginator: MatPaginator;
  @ViewChild(MatSort) matSort: MatSort;
  accounts: Account[];
  displayedColumns: string[];
  resultsLength: number;
  filteredDataTriggered = false;
  isLoading = true;
  dataSource: MatTableDataSource<Account>;
  filterForm: UntypedFormGroup;
  isAdvancedSearchEnabled = false;
  accountGroups = accountGroups;

  accountTypes = accountTypes;

  businessGroups = businessGroups;
  language: string;
  constructor(
    private chartOfAccountsService: ChartOfAccountsService,
    private fb: UntypedFormBuilder,
    private cookieService: CookieService
  ) {}

  ngOnInit(): void {
    this.language = this.cookieService.get('locale').toUpperCase();
    this.filterForm = this.fb.group({
      accountGroup: [null],
      businessGroup: [null],
      accountType: [null],
      searchBoxForm: [],
    });
    this.getAccounts();
    this.initColumns();
  }

  ngAfterViewInit(): void {
    //
  }

  getAccounts(): void {
    const accountParams: AccountParams = new AccountParams();
    accountParams.pageNumber = 1;
    accountParams.pageSize = 50;
    this.chartOfAccountsService.getAllChartOfAccounts(accountParams).subscribe(result => {
      this.accounts = result.accounts;
      this.dataSource = new MatTableDataSource<Account>(this.accounts);
      this.resultsLength = result.totalRecordsCount;
      this.isLoading = false;
      this.dataSource.paginator = this.matPaginator;
      this.dataSource.sort = this.matSort;
    });
  }

  initColumns(): void {
    this.displayedColumns = [
      'action',
      'accountNumber',
      'accountType',
      'accountGroup',
      'accountNature',
    ];
    if (this.language === 'AR') {
      this.displayedColumns.splice(2, 0, 'nameArabic');
    } else if (this.language === 'EN') {
      this.displayedColumns.splice(2, 0, 'nameEnglish');
    }
  }

  applyFilter(filterValue: string): void {
    filterValue = filterValue.trim();
    filterValue = filterValue.toLowerCase();
    this.dataSource.filter = filterValue;
  }

  onPageChange(pageEvent: PageEvent) {
    const event: PaginatedFilter = {
      pageNumber: pageEvent.pageIndex + 1 ?? 1,
      pageSize: pageEvent.pageSize ?? 50,
    };
    this.getFilterData(null, event);
  }

  getFilterData(event?: Event, pageEvent?: PaginatedFilter) {
    event?.preventDefault();
    const accountParams: AccountParams = new AccountParams();
    accountParams.searchString = this.searchBoxForm.searchBoxForm.controls['searchString'].value;
    accountParams.searchType = this.searchBoxForm.searchBoxForm.controls['searchType'].value;
    accountParams.pageNumber = pageEvent?.pageNumber ?? 0;
    accountParams.pageSize = 50;
    accountParams.accountGroup = this.filterForm.controls['accountGroup'].value;
    accountParams.businessGroup = this.filterForm.controls['businessGroup'].value;
    accountParams.accountType = this.filterForm.controls['accountType'].value;
    this.chartOfAccountsService.getAllChartOfAccounts(accountParams).subscribe(result => {
      this.filteredDataTriggered = true;
      this.accounts = result.accounts;
      this.resultsLength = result.totalRecordsCount;
      if (this.dataSource) {
        this.dataSource.data = this.accounts;
      } else {
        this.dataSource = new MatTableDataSource<Account>(this.accounts);
        this.dataSource.paginator = this.matPaginator;
        this.dataSource.sort = this.matSort;
      }
    });
  }

  clearFilters(event: Event) {
    event.preventDefault();
    this.filterForm.markAsUntouched();
    this.filterForm.markAsPristine();
    this.filterForm.reset();
    this.searchBoxForm.resetSearchBox();
    this.getFilterData();
  }

  getAccountTypeDisplay(columnName: string): string {
    const accountType = this.accountTypes.find(type => type.value === columnName);
    return accountType ? accountType.display : '';
  }
  getAccountGroupDisplay(columnName: string): string {
    const accountGroup = this.accountGroups.find(type => type.value === columnName);
    return accountGroup ? accountGroup.display : '';
  }

  getBusinessGroupDisplay(columnName: string): string {
    const businessGroup = this.businessGroups.find(type => type.value === columnName);
    return businessGroup ? businessGroup.display : '';
  }
}
