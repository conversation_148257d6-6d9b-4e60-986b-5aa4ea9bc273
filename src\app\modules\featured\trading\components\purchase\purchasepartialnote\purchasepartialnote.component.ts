import { animate, state, style, transition, trigger } from '@angular/animations';
import { Directionality } from '@angular/cdk/bidi';
import { Component, NgZone, OnInit, ViewChild } from '@angular/core';
import {
  AbstractControl,
  UntypedFormArray,
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatSelectChange } from '@angular/material/select';
import { MatTable } from '@angular/material/table';
import { ActivatedRoute, Router } from '@angular/router';
import { CustomValidators } from 'ngx-custom-validators';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject, forkJoin, of } from 'rxjs';
import { CostCentresApiService } from 'src/app/core/api/accounts/cost-centres-api.service';
import { CommonService } from 'src/app/core/api/common.service';
import { StaticDataService } from 'src/app/core/api/static-data.service';
import { DistributorService } from 'src/app/core/api/trading/distributor.service';
import { SalesAdjustmentService } from 'src/app/core/api/trading/sales-adjustments.service';
import { ActionType } from 'src/app/core/enums/actionType';
import { ICustomer, ICustomerView } from 'src/app/core/interfaces/customer';
import { IDistributor } from 'src/app/core/interfaces/distributor';
import { InvalidQuantity, error } from 'src/app/core/interfaces/error';
import { IPaymentViewData } from 'src/app/core/interfaces/payment';
import {
  IInvoice,
  ISaleDetails,
  ISalesItem,
  SaletransactionTypes,
} from 'src/app/core/interfaces/sales';
import { DistributorParams } from 'src/app/core/models/params/distributorParams';
import { SalesParams } from 'src/app/core/models/params/salesParams';
import { displayValue, miscOptions } from 'src/app/core/models/wrappers/displayValue';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { CostCentre } from 'src/app/modules/featured/accounts/models/costCentre';
import { IInventory } from 'src/app/modules/featured/catalog/models/product';
import { CompareResultsComponent } from 'src/app/modules/shared/components/compare-results/compare-results.component';
import { PaymentsPostSaleComponent } from 'src/app/modules/shared/components/payments-post-sale/payments-post-sale.component';
import { CustomerSelectionComponent } from '../../customers/customer-selection/customer-selection.component';
import { SalesCalculation } from '../../sales/sales-calculation';
import { PurchaseService } from 'src/app/core/api/trading/purchase.service';
import { IPurchaseDetails, IPurchaseItem } from 'src/app/core/interfaces/purchase';

@Component({
  selector: 'app-purchasepartialnote',
  templateUrl: './purchasepartialnote.component.html',
  styleUrls: ['./purchasepartialnote.component.scss'],
  animations: [
    trigger('detailExpand', [
      state('collapsed', style({ height: '0px', minHeight: '0' })),
      state('expanded', style({ height: '*' })),
      transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
    ]),
  ],
})
export class PurchasepartialnoteComponent extends SalesCalculation implements OnInit {
  @ViewChild(MatTable) table: MatTable<AbstractControl[]>;
  @ViewChild('paymentpostsales') private paymentForm: PaymentsPostSaleComponent;
  @ViewChild('customerSelection') private customerSelection: CustomerSelectionComponent;
  // Matatable
  dataSource = new BehaviorSubject<AbstractControl[]>([]);
  displayedColumns: string[] = [
    'itemCode',
    'itemName',
    'unitName',
    'returnableQty',
    'quantity',
    'purchasePrice',
    'discount',
    'retailPrice',
    'wholesalePrice',
    'distributorPrice',
    'vatAmount',
    'subtotal',
    'action',
  ];
  expandedFieldNames: Array<displayValue | miscOptions>;
  columnsToDisplayWithExpand = [...this.displayedColumns, 'expand'];
  // Holders

  priceTypes: displayValue[];
  distributorAccounts: IDistributor[] = [];
  costCentreAccounts: CostCentre[] = [];
  currentSalesData: IInvoice;
  saleDetails: IPurchaseDetails;
  products: IInventory[];
  salesId: string;
  quantityUpdated: boolean;
  customerViewData: ICustomerView = <ICustomerView>{};
  paymentViewData: IPaymentViewData = <IPaymentViewData>{};
  // Main Form
  itemRows: UntypedFormArray = this.formBuilder.array([]);
  salesForm: UntypedFormGroup = this.formBuilder.group({
    issueDate: new UntypedFormControl(new Date()),
    orderNumber: new UntypedFormControl(null),
    orderDate: new UntypedFormControl(new Date()),
    costCentreId: new UntypedFormControl(null),
    distributorAccountId: new UntypedFormControl(null),
    invoiceDiscount: new UntypedFormControl(0),
    notes: new UntypedFormControl(null),
    items: this.itemRows,
  });
  loading = true;
  mode: ActionType;
  expandedRowIndex: number | null = null;
  formTitle: string;
  constructor(
    private formBuilder: UntypedFormBuilder,
    private dialog: MatDialog,
    private costCentresApiService: CostCentresApiService,
    private distributorService: DistributorService,
    private purchaseService: PurchaseService,
    private toastr: ToastrService,
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute,
    private staticDataService: StaticDataService,
    private readonly zone: NgZone,
    private commonService: CommonService,
    private salesAdjustmentService: SalesAdjustmentService,
    private direction: Directionality
  ) {
    super();
  }

  ngOnInit(): void {
    this.loading = true;
    this.formTitle = this.route.snapshot.data['title'];
    this.mode = this.route.snapshot.data['mode'];
    this.route.params.subscribe(params => {
      this.salesId = params['id'];
      this.getAllDropDownData();
    });
    //this.setPageDisabled();
  }

  get fractionTotals() {
    return 0;
  }

  get balanceAmounts() {
    return 0;
  }

  get changeAmounts() {
    return 0;
  }

  get canSelectProduct() {
    return !(this.mode === ActionType.view || this.mode === ActionType.partialCreditNote);
  }

  get actionType() {
    return ActionType;
  }

  get canDeleteProduct() {
    return this.canSelectProduct;
  }

  setPageDisabled(): void {
    if (this.mode === ActionType.view || this.mode === ActionType.partialCreditNote) {
      this.salesForm.disable();
    }
  }

  patchData(data: IPurchaseDetails): void {
    const viewData: ICustomerView = {};
    viewData.isViewMode = true;
    viewData.customer = data.supplier;
    viewData.existingClient = data.existingClient;
    this.customerViewData = viewData;
    //this.paymentViewData = data.payments;
    //this.paymentViewData.isViewMode = true;
    // hack for grandtotal
    //this.paymentViewData.grandTotal = 0;
    this.saleDetails = data;
    if (data?.items && data.items.length) {
      data.items.forEach(element => {
        this.addSaleDetailForView(element);
      });
    }
    this.salesForm.patchValue({
      issueDate: data.issueDate,
      orderNumber: data.orderNumber,
      orderDate: data.orderDate,
      costCentreId: data.costCentreId,
      distributorAccountId: data.distributorAccountId,
      invoiceDiscount: data.invoiceDiscount,
      notes: data.notes,
      //items: this.itemRows,
    });
    //this.setPageDisabled();
    this.items.controls.forEach(controls => controls.get('quantity').enable());
  }

  getAllDropDownData(): void {
    const salesParams = new SalesParams();
    salesParams.transactionType = SaletransactionTypes.purchase;
    const salesById = this.salesId
      ? this.purchaseService.getPurchaseSalesById(this.salesId, salesParams)
      : of(null);
    const distributorAccounts = this.distributorService.getAllDistributors(new DistributorParams());
    const costCentreAccounts = this.costCentresApiService.getAllCostCentres();
    forkJoin([distributorAccounts, costCentreAccounts, salesById]).subscribe(results => {
      console.log(results);
      this.distributorAccounts = results[0]?.distributors;
      this.costCentreAccounts = results[1];
      if (results[2]) {
        this.currentSalesData = results[2];
        this.patchData(results[2]);
      }
      this.loading = false;
    });
    this.expandedFieldNames = this.staticDataService.getSalesExpandedColumns;
    this.priceTypes = this.staticDataService.getSalesPriceTypes;
  }

  addSaleDetailForView(product: IPurchaseItem): void {
    product.quantity = 0;
    const saleDetail: IPurchaseItem = {
      returnableQty: product.returnableQty,
      transactionItemId: product.transactionItemId,
      itemId: product.itemId,
      itemCode: product.itemCode,
      quantity: product.quantity,
      itemUnitId: product.itemUnitId,
      price: product.price,
      subtotal: product.subtotal,
      vat: product.product.vat,
      vatAmount: product.vatAmount,
      product: product.product,
      discount: product.discount,
      isGeneralDscntMethod: product.product.isGeneralDscntMethod,
      priceType: product.priceType,
      warehouseName: product.warehouseName,
      warehouseId: product.warehouseId,
      itemName: product.itemName,
      unitName: product.unitName,
      subTotalVat: product.subTotalVat,
      retailPrice: product.retailPrice,
      wholesalePrice: product.wholesalePrice,
      distributorPrice: product.distributorPrice,
      purchasePrice: product.purchasePrice,
      profit: product.profit,
    };
    this.onAddNewItem(saleDetail);
    this.dataSource.next(this.itemRows.controls);
    this.formItems(this.itemRows);
    this.updateAllRelevantCounts();
  }

  getControlsIndexFromArray(controlErrors: InvalidQuantity) {
    console.log('getControlsIndexFromArray -> ', controlErrors);
    return this.items.controls.findIndex(product => {
      console.log(product);
      return (
        product.value.itemCode === controlErrors.itemCode &&
        product.value.warehouseId === controlErrors.warehouseId &&
        product.value.unitName === controlErrors.unitName
      );
    });
  }

  onPriceTypeChange(event: MatSelectChange, index: number) {
    console.log('onPriceTypeChange -> ', event, event.source.triggerValue, index);
    const data = this.getSpecificFormArray(index);
    const priceType = this.priceTypes.find(data => data.value === event.value);
    const productData = data.get('product').value;
    data.patchValue({
      price: productData[priceType.display],
      priceType: event.value,
      subtotal: this.countSubTotal(
        data.get('quantity').value,
        data.get('price').value,
        data.get('discount').value,
        data.get('isGeneralDscntMethod').value,
        this.subTotalVat(
          data.get('price').value,
          data.get('vat').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value
        )
      ),
      subTotalVat:
        this.subTotalVat(
          data.get('price').value,
          data.get('vat').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value
        ) * data.get('quantity').value,
      vatAmount:
        this.vatAmount(
          data.get('price').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value,
          data.get('vat').value
        ) * data.get('quantity').value,
    });
    this.dataSource.next(this.itemRows.controls);
    this.formItems(this.itemRows);
    this.updateAllRelevantCounts();
  }

  onQuantityChange(event, index: number) {
    console.log('onQuantityChange -> ', event.srcElement.value, index);
    const data = this.getSpecificFormArray(index);
    console.log('onQuantityChange', data);
    data.patchValue({
      quantity: event.srcElement.value || 0,
      subtotal: this.countSubTotal(
        event.srcElement.value,
        data.get('price').value,
        data.get('discount').value,
        data.get('isGeneralDscntMethod').value,
        this.subTotalVat(
          data.get('price').value,
          data.get('vat').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value
        )
      ),
      subTotalVat:
        this.subTotalVat(
          data.get('price').value,
          data.get('vat').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value
        ) * event.srcElement.value,
      vatAmount:
        this.vatAmount(
          data.get('price').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value,
          data.get('vat').value
        ) * event.srcElement.value,
    });
    this.dataSource.next(this.itemRows.controls);
    this.formItems(this.itemRows);
    this.updateAllRelevantCounts();
    this.isQuantityUpdated();
  }

  onPriceChange(event, index: number) {
    console.log('onPriceChange -> ', event, index);
    const data = this.getSpecificFormArray(index);
    data.patchValue({
      price: event.srcElement.value || 0,
      subtotal: this.countSubTotal(
        data.get('quantity').value,
        event.srcElement.value,
        data.get('discount').value,
        data.get('isGeneralDscntMethod').value,
        this.subTotalVat(
          event.srcElement.value,
          data.get('vat').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value
        )
      ),
      subTotalVat:
        this.subTotalVat(
          event.srcElement.value,
          data.get('vat').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value
        ) * data.get('quantity').value,
      vatAmount:
        this.vatAmount(
          event.srcElement.value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value,
          data.get('vat').value
        ) * data.get('quantity').value,
    });
    this.dataSource.next(this.itemRows.controls);
    this.formItems(this.itemRows);
    this.updateAllRelevantCounts();
  }

  onDiscountChange(event, index: number) {
    console.log('onDiscountChange -> ', event.srcElement.value, index);
    const data = this.getSpecificFormArray(index);
    data.patchValue({
      discount: event.srcElement.value || 0,
      subtotal: this.countSubTotal(
        data.get('quantity').value,
        data.get('price').value,
        event.srcElement.value,
        data.get('isGeneralDscntMethod').value,
        this.subTotalVat(
          data.get('price').value,
          data.get('vat').value,
          event.srcElement.value,
          data.get('isGeneralDscntMethod').value
        )
      ),
      subTotalVat:
        this.subTotalVat(
          data.get('price').value,
          data.get('vat').value,
          event.srcElement.value,
          data.get('isGeneralDscntMethod').value
        ) * data.get('quantity').value,
      vatAmount:
        this.vatAmount(
          data.get('price').value,
          event.srcElement.value,
          data.get('isGeneralDscntMethod').value,
          data.get('vat').value
        ) * data.get('quantity').value,
    });
    this.dataSource.next(this.itemRows.controls);
    this.formItems(this.itemRows);
    this.updateAllRelevantCounts();
  }

  getSpecificFormArray(index: number): AbstractControl {
    return (<UntypedFormArray>this.salesForm.get('items')).at(index);
  }

  get itemsArray(): UntypedFormArray {
    return this.salesForm.get('items') as UntypedFormArray;
  }

  // auto jump logic

  jumpToNext(event: Event, index: number) {
    console.log('jump to next field  -> ', event, index);
    event.preventDefault();
    const nextField = document.querySelectorAll('.next');
    nextField.forEach((element, index) => {
      if (element === event.target) {
        const nextfield = nextField[index + 1] as HTMLInputElement;
        nextfield.focus();
      }
    });
  }

  onAddNewItem(product: IPurchaseItem) {
    console.log('onAddNewItem -> ', product);
    if (this.itemsArray && this.itemsArray?.length > 0) {
      this.itemsArray.insert(0, this.addnewFormGroup(product));
    } else {
      this.itemsArray.push(this.addnewFormGroup(product));
    }
    console.log('see items here', this.items);
  }

  addnewFormGroup(saleData: IPurchaseItem): UntypedFormGroup {
    console.log('addnewFormGroup -> ', saleData);
    const row = this.formBuilder.group({
      itemId: saleData.itemId,
      itemCode: saleData.itemCode,
      quantity: [
        saleData.quantity,
        Validators.compose([Validators.required, CustomValidators.lte(saleData.returnableQty)]),
      ],
      itemUnitId: saleData.itemUnitId,
      price: saleData.price,
      subtotal: [saleData.subtotal],
      vat: saleData.vat,
      product: saleData.product,
      discount: [saleData.discount],
      isGeneralDscntMethod: saleData.isGeneralDscntMethod,
      priceType: saleData.priceType,
      warehouseName: saleData.warehouseName,
      itemName: saleData.itemName,
      unitName: saleData.unitName,
      notes: saleData.notes,
      subTotalVat: saleData.subTotalVat,
      warehouseId: saleData.warehouseId,
      vatAmount: [saleData.vatAmount, Validators.compose([CustomValidators.gte(0)])],
      transactionItemId: saleData.transactionItemId,
      returnableQty: saleData.returnableQty,
      retailPrice: saleData.retailPrice,
      wholesalePrice: saleData.wholesalePrice,
      distributorPrice: saleData.distributorPrice,
      purchasePrice: saleData.purchasePrice,
    });
    return row;
  }

  submitSales(event: Event): void {
    event.preventDefault();
    this.processSalesPartialCreditNotes();
  }

  processSalesPartialCreditNotes(): void {
    const isAllValid = this.paymentForm.isValid() && this.salesForm.valid;
    console.log('is all valid', isAllValid);
    if (isAllValid) {
      this.onPostClick();
    } else {
      this.commonService.playErrorSound();
    }
  }

  onPostClick() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = {
      message: 'compareTable.partialReturnText',
      compareData: this.getUpdatedQuantituesEntries(),
      purchase: true,
    };
    dialogConfig.minWidth = '90vw';
    dialogConfig.position = {
      top: '50px',
    };
    dialogConfig.maxHeight = '90vh';
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(CompareResultsComponent, dialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      console.log('result', result);
      if (result) {
        this.creditProcess();
      }
    });
  }

  creditProcess(): void {
    const data: IPurchaseDetails = {
      payments: {
        ...this.paymentForm.getNoteConfig().notesPaymentDetails,
        balanceAmount: 0,
        changeAmount: 0,
        grandTotal: this.grandTotal,
        totalDiscount: this.discount,
        totalVat: this.totalVat,
        totalExclVatDiscount: this.totalExcVatDisc,
      },
      referenceDocumentId: this.currentSalesData.id,
      transactionType: SaletransactionTypes.partialCreditNote,
      notes: this.paymentForm.getNoteConfig().notesCommentsDate.creditNoteText,
      items: this.salesForm.get('items').value,
      issueDate: this.paymentForm.getNoteConfig().notesCommentsDate.creditNoteDate,
      referenceDocumentDate: this.currentSalesData.issueDate,
      supplier: this.saleDetails.supplier,
      existingClient: this.saleDetails.existingClient,
    };
    console.log('partial credit note structure', data);
    this.purchaseService.createpurchaseSales(data).subscribe(
      result => {
        console.log('sales adjustment response', result);
        //this.toastr.success('Sales Adjustment successfully');
        this.commonService.playSuccessSound();
        this.router.navigate(['../../'], { relativeTo: this.route });
      },
      error => {
        console.log('errors =>', error);
        if (error.status === 422) {
          this.setFormErrors(error);
        }
      }
    );
  }

  setFormErrors(validationErrors: error): void {
    const serverErrors = validationErrors.details.invalidQuantity;
    serverErrors.forEach((element: InvalidQuantity) => {
      const controlIndex = this.getControlsIndexFromArray(element);
      console.log('index => ', controlIndex);
      const formArray = this.getSpecificFormArray(controlIndex);
      console.log('formArray => ', formArray);
      formArray.get('quantity').setErrors({ serverSideError: element.error });
      formArray.updateValueAndValidity();
    });
  }

  getUpdatedQuantituesEntries() {
    return this.items.controls
      .filter(control => {
        return control.get('quantity').value > 0;
      })
      .map(data => data.value);
  }

  isQuantityUpdated(): void {
    this.quantityUpdated =
      this.salesForm.valid &&
      this.items.controls.some(control => {
        return control.get('quantity').value > 0;
      });
  }

  customerTypeSelection(isExistingCustomer: boolean): void {
    if (!isExistingCustomer) {
      this.salesForm.get('distributorAccountId').setValue(null);
    }
  }

  customerProfileSelection(customer: ICustomer): void {
    console.log('customer profile selected ->', customer);
    if (customer) {
      this.patchCustomerProfileData(customer);
    }
  }

  patchCustomerProfileData(customer: ICustomer): void {
    this.salesForm.get('costCentreId').setValue(customer.costCentreId);
    this.salesForm.get('distributorAccountId').setValue(customer.distributorAccountId);
  }

  toggleExpansion(rowIndex: number): void {
    this.expandedRowIndex = this.expandedRowIndex === rowIndex ? null : rowIndex;
  }

  isRowExpanded(index: number): boolean {
    return this.expandedRowIndex === index;
  }
}
