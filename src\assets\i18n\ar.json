{"retailPrice": "سعر التجزئة", "wholesalePrice": "سعر الجملة", "distributorPrice": "سعر الموزعين", "openPurchasePrice": "سعر الشراء", "purchasePrice": "سعر الشراء", "avgPurchasePrice": "معدل سعر الشراء", "NOTRETURNED": "غير مرجعه", "PARTIALLYRETURNED": " مرجعه جزئيا", "RETURNED": "مرجعه بالكامل", "fullCreditNote": " إشعار دائن - ارجاع كامل", "partialCreditNote": "اشعار دائن - ارجاع جزئي", "debitNote": "إشعار مدين", "payment": "طريقة الدفع", "offer": "مدة العرض", "guarante": "مدة الضمان", "delivery": "مدة التسليم", "note": "ملاحظات", "notposted": "<PERSON>ير معتمد", "posted": "معت<PERSON>د", "jurnotposted": "<PERSON>ير مرحل", "jurposted": "مر<PERSON><PERSON>", "site": {}, "layouts": {"admin": {"catalogSubhead": "Catalog Management", "brands": "Brands", "categories": "Categories", "products": "Products", "peopleSubhead": "People Management", "customers": "Customers", "about": "About", "appSubhead": "Application", "roles": "Roles", "users": "Users", "accSubhead": "Access Management", "eventLogs": "Event Logs", "salesSubhead": "Sales Management", "orders": "Orders"}}, "dashboard": {"title": "Dashboard", "subtitle": "Quick Overview of your Business."}, "about": {}, "settings": {"title": "Settings", "subtitle": "Manage Application Settings.", "generalSettings": "General Settings", "language": "Language", "english": "English", "khmer": "Khmer", "russian": "Russian", "french": "French", "spanish": "Spanish"}, "userCard": {"profile": "Profile", "settings": "Setting", "logout": "Logout"}, "dialogs": {"logout": {"title": "خروج", "message": "هل انت متاكد ؟", "button": "موافق"}, "checkOut": {"title": "Checkout", "message": "Make an Order!", "button": "Confirm", "buttonProcessing": "Processing..."}, "delete": {"title": "Delete Confirmation", "buttons": {"cancel": "Cancel", "confirm": "Confirm"}}, "confirmPost": {"title": "Confirm Submission", "buttons": {"cancel": "Cancel", "confirm": "Confirm"}}}, "components": {"accessDenial": {"header": "Access Denial.", "text": "You do not have permission to access the page you were looking for, contact to system administator.", "button": "Go Home"}, "notFound": {"header": "Page not found.", "text": "The page you were looking for could not be found.It might have been removed,renamed or did not exist in the first place.", "button": "Go Home"}, "serverError": {"header": "Internal Server Error.", "text": "There is problem with the internal server", "button": "Go Home"}}, "common": {"buttons": {"edit": "تعديل", "view": "<PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON>", "cancel": "خروج", "submit": "تخزين", "ok": "Ok", "yes": "نعم", "no": "لا", "confirm": "Confirm", "save": "تخزين", "back": "خروج", "logout": "خروج", "logoutCancel": "رجوع", "fullCreditNote": "اصدار الاشعار الدائن", "partialCreditNoteAction": "اصدار الاشعار الدائن", "debitNoteAction": "إصدار الإشعار المدين"}, "minLength": " يجب ان تكون عدد الخانات {{length}}", "searchnodata": "السجل غير موجود", "success_operation": "تمت العملية بنجاح", "chooseFile": "<PERSON><PERSON><PERSON> الملف", "grantAllPermission": " منح جميع الصلاحيات", "selectAll": "تحديد جميع الصلاحيات", "required": "الحقل مطلوب", "reset": "بدون", "cancel": "خروج", "submit": "تخزين", "ok": "Ok", "yes": "Yes", "no": "No", "confirm": "متأكد", "confirmSubmission": "Confirm Submission", "bulkEdit": "Bulk Edit Product Details", "action": "الاجراء", "itemsPerPage": "العدد/صفحه", "editAction": "تعديل", "viewAction": "<PERSON><PERSON><PERSON>", "postAction": "إعتماد", "deleteAction": "<PERSON><PERSON><PERSON>", "noDataFound": "لايوجد بيانات", "copyright": "حقوق النشر©  مؤسسة سوامي لتقنية نظم المعلومات", "print": "طباعة", "searching": "يبحث....", "active": "نشيط", "confirmAction": "هل انت متاكد", "confirmDeleteAction": "سيتم حذف بيانا<PERSON> عدد {{count}} صنف هل انت متاكد", "field": {"accountNumber": "رقم الحساب", "nameArabic": "الاسم العربي", "nameEnglish": "الاسم الإنجليزي", "vatNo": "الرقم الضريبي", "phone": "رقم الهاتف", "email": "البريد الالكتروني", "freezed": "تجميد", "percDiscount": "نسبة الخصم", "allowedBalance": "الحد المسموح به", "allowedDays": "مدة تسهيل السداد", "yrlyTarget": "الهد<PERSON> السنوي", "costAccount": "مركز التكلفة", "distributorAccount": "الموزع", "transactionDetails": "طريقة التعامل", "accountDetails": "بيانات الحساب", "commission": "العمولة"}, "pagination": {"itemsPerPage": "العدد/صفحه", "nextPage": "Next Page", "previousPage": "Previous Page", "ofLabel": "xxxx"}}, "customer": {"customerTitle": "بيانات العميل", "customerList": "قائمة العملاء", "register_customer": "إضافة عميل جديد", "parentId": "حس<PERSON><PERSON> الاب", "name": "الاسم العربي", "englishName": "الاسم الانجليزي", "customerSelection": "اختيار العميل"}, "supplier": {"supplierTitle": "بيانات المورد", "supplierList": "قائمة الموردين", "register_supplier": "اضافة مورد جديد", "parentId": "حس<PERSON><PERSON> الاب", "name": "الاسم العربي", "englishName": "الاسم الانجليزي", "vendorType": "نوع المورد", "supplierSelection": "المورد "}, "distributor": {"distributorTitle": "بيانات الموزع", "distributorList": "قائمة الموزعين", "register_distributor": "إضافة موزع جديد", "parentId": "حس<PERSON><PERSON> الاب", "name": "الاسم العربي", "englishName": "الاسم الانجليزي"}, "address": {"address": "العنوان", "bldgnNo": "رق<PERSON> المبنى", "addnNo": "الرقم الفرعي", "streetName": "الشارع", "city": "المدينة", "district": "الحي", "country": "الدوله", "shortAddress": "العنوان المختصر", "otherAddress": "الرقم الإضافي", "postalCode": "الر<PERSON>ز البريدي"}, "paymentsType": {"paymentMethod": "طريقة السداد", "0": "نقدا", "3": "آجل", "2": "تحويل", "1": "شبكة", "4": "متعدد", "cash": "نقدا", "credit": "آجل", "bank": "تحويل", "card": "شبكة", "creditDueDate": "تاريخ الاستحقاق", "cashAccounts": "الحسابات النقدية", "bankAccounts": "حسابات بنكية", "cardAccounts": "حسابات البطاقة", "amount": "المبلغ", "paymentLabel": "أنواع الدفع المتاحة", "paymentSelectionError": "فظلاً تحديد طريقة الدفع", "balanceError": "رصيد غير كافي", "availableBalance": "الر<PERSON>يد المتوفر", "returnDate": "تاريخ الارجاع", "returnText": "سبب الارجاع", "paymentSelectionErrorDebitNote": "فظلاً تحديد طريقة الدفع", "singleOnly": "يسمح فقط باختيار طريقة دفع واحده", "creditType": "لايمكن اختيار طريقة دفع أخرى", "halala": "استبعاد الكسور", "otherpaymentsSelected": "لا يمكن اختيار آجل عند تحديد طريقة دفع آخر"}, "login": {"password-required": "يج<PERSON> ادخال الكلمة السرية", "username-required": "يج<PERSON> ادخال رقم المستخدم", "await-login": "تسجيل الدخول", "branch-select": "فظلا اختيار الفرع والسنة", "branch": "الفرع", "branch-year": "السنة", "login": "دخول", "userName": "أسم المستخدم", "password": "الكلمة السرية", "signin": "تسجيل الدخول", "tenant": "<PERSON><PERSON><PERSON> المشترك", "tenant-required": " فظلا تاكد من رقم المشترك"}, "sales": {"salesListing": "قائمة فواتير المبيعات", "salesReceivables": "تسديد الفواتير الآجلة", "updateSales": "updateSales", "viewSales": "عرض فاتوره", "createSales": "فاتوره جديده", "createSalesCreditNote": "اصدار اشعار دائن (كامل)", "createSalesPartialNote": "اصدار اشعار دائن (جزئي)", "createSalesDebitNote": "اصدار اشعار مدين", "salesList": "قائمة فواتير المبيعات", "saleInvoice": "فاتورة مبيعات رقم", "fullCreditInvoice": "اشعار دائن- إرجاع كامل للفاتورة رقم", "partilCreditInvoice": "اشعار دائن- إرجاع جزئى للفاتورة رقم", "debitInvoice": "اشعار مدين للفاتورة رقم", "register_sales": "فاتورة جديدة ", "salesDate": "تاريخ الفاتورة", "referenceNo": "رقم المرجع", "referenceDate": "تاريخ المرجع", "invoiceNote": "ملاحظات الفاتورة", "customerSelection": "اختيار العميل", "code": "رقم الصنف", "name": "اسم الصنف", "unit": "الوحدة", "priceType": "طريقة التسعير", "quantity": "الكمية", "vatAmt": "مبلغ الضريبة", "subTotal": "اجمالي الصنف", "note": "ملاحظات", "discount": "الخصم", "price": "سعر", "returnableQty": "الكمية المستردة", "exp-itemCode": "رقم الصنف", "exp-wareHouse": "المخزن", "exp-vatPct": "% الضريبة", "exp-currentQty": "الكمية الحالية", "exp-partNo": "الرقم المصنعي", "exp-discount": "الخصم", "exp-discountType": "نوع الخصم", "invoiceNo": "رقم الفاتورة", "invoiceDate": "تاريخ الفاتورة", "invoiceAmt": "قيمة الفاتورة", "invoiceCustomerNo": "رقم الحساب", "invoiceCustomerName": "اسم العميل", "amount": "كمية", "fullCreditNote": " إشعار دائن - ارجاع كامل", "viewNotes": "عرض الاشعارات الدائنة والمدينة", "partialCreditNote": "اشعار دائن - ارجاع جزئي", "debitNote": "إشعار مدين", "partial-qty-sold": "الكمية المباعة", "partial-ref-quantity": "الكمية المرجعة", "partial-price": "السعر", "fullCreditConfirmation": "هل ترغب في إتمام عملية الارجاع للاصناف ادناه", "returnStatus": "الحالة", "unpaidAmount": "القيمة المتبقية", "fullCreditNoteButton": "اصدار الاشعار الدائن", "zatcaInvoiceError": "خطاء - المستند غير مطابق لشروط الامتثال لا يمكن ارساله", "zatcaInvoiceWarnings": "المستند مطابق حزئيا لشروط الامتثال (يوجد تحذيرات) فظلا تأكيد ارسال الملف", "zatcaInvoiceResubmit": "ارسال الملف", "zatcaEinvoices": "الفواتير الالكترونية", "zatcaInvoiceNoErrors": "ل<PERSON><PERSON><PERSON><PERSON><PERSON> خطاء لهذا المستند"}, "salesNotes": {"fullCredit": "الاشعارات الدائنة - ارجاع كامل", "partialCredit": "الاشعارات الدائنة  - ارجاع جزئي", "debitNote": "الاشعارات المدينة", "allNotes": "جميع الاشعارات", "fullCreditNote": " إشعار دائن - ارجاع كامل", "partialCreditNote": "اشعار دائن - ارجاع جزئي"}, "purchaseNotes": {"fullCredit": "ارجاع كامل", "partialCredit": "ارجاع جزئي", "allNotes": "جميع المرتجعات", "fullCreditNote": "ارجاع كامل", "partialCreditNote": "ارجاع جزئي"}, "customerTypes": {"walkIn": "عميل نقدي", "Existing": "عميل مسجل"}, "supplierTypes": {"cashPurchase": "مورد نقدي", "registeredSupplier": "مورد مسجل"}, "priceTypes": {"retail": "سعر التجزئة", "wholeSale": "سعر الجملة", "distributor": "سعر الموزعين", "purchase": "سعر الشراء", "avgPurchase": "معدل سعر الشراء"}, "salesSummary": {"totalExVatDisc": "الإجمالي بدون الخصم والقيمة المضافة", "totalDiscount": "اجمالي الخصم", "totalVat": "القيمة المضافة", "grandTotal": "الإجمالي", "balanceAmt": "الرصيد", "changeAmt": "المتبقي", "fractionAmt": "خصم الكسور", "salesLabel": "ملخص أسعار المبيعات"}, "placeHolder": {"customerSearch": "الحد الادني للبحث 3 حروف/ارقام", "customerNotFound": "العميل غير موجود"}, "productSearch": {"productSearch": "اختيار الصنف", "placeHolder": "الحد الادني للبحث 3 حروف/ارقام", "productNotFound": "الصنف غير موجود", "itemCode": "رقم الصنف", "itemName": "اسم الصنف", "unitName": "الوحدة", "warehouseName": "المخزن", "currentQty": "الكمية الحالية", "retailPrice": "سعر التجزئة", "wholesalePrice": "سعر الجملة", "distributorPrice": "سعر الموزعين", "purchasePrice": "سعر الشراء", "discount": "الخصم", "category": "المجموعة", "totalQuantityPerUnit": "الكمية الحالية"}, "dashBoardModules": {"posFeatures": "الأنظمة المناحة", "inventoryMgt": "المخازن", "identityMgt": "نظام الصلاحيات", "enterpriseMgt": "اعدادات النظام", "acountingMgt": "نظام المحاسبة", "reportMgt": "إدارة التقارير", "utilityMgt": "Utility Management", "tradeMgt": "المبيعات والمشتريات", "sales": "المبيعات", "purchase": "المشتريات", "customers": "العملاء", "suppliers": "الموردين", "distributors": "الموزعين"}, "navigationMenus": {"reportsSetup": "Reports Set-Up", "posFeatures": "الأنظمة المناحة", "inventory": "المخازن", "products": "بيانات الأصناف", "units": "وحدات الأصناف", "catagories": "المجموعات", "stock": "إدارة المخزون", "openquantityAdjustment": "إضافة وتعديل كميات اول المدة", "stockTracking": "<PERSON><PERSON><PERSON> المخزون", "Inventory": "قائمة تقارير المخازن", "priceAdjustment": "إضافة وتعديل الأسعار", "trading": "نظام المبيعات والمشتريات", "sales": "المبيعات", "customers": "العملاء", "purchase": "المشتريات", "suppliers": "الموردين", "distributors": "الموزعين", "reports": "التقارير", "itemPriceReport": "كشف أسعار الأصناف", "stockValueReport": "كشف قيمة المخزن", "accountingReport": "قائمة تقارير المحاسبة", "enterprise": "اعدادات النظام", "companyProfile": "اعدادات المنشأة", "branchSetup": "اعدادات الفروع", "wareHouseSetup": "اعدادات المخازن", "accounting": "المحاسبة", "chartOfAccounts": "الدليل المحاسبي", "journalEntries": "القيود الحاسبية", "costCenter": "مراكز التكلفة", "accountReports": "التقارير", "accountBookSetup": "اعدادات المحاسبة", "fixedAsset": "إدارة الأصول الثابتة", "listOfAccount": "كشف دليل الحسابات", "statementOfAccount": "كشف حسا<PERSON> معبن", "identity": "الصلاحيات", "rolesPermissions": "الأدوار والصلاحيات", "users": "المستخدمين", "utility": "خدمات", "depreciation": "الاستهلاك", "quotations": "عروض الاسعار", "purchases": "المشتريات", "ucvoucher": "نظام سندات القبض والصرف", "tenantConfiguration": "Tenant Configuration", "tenantMgt": "Tenant Management", "branchPartner": "تعريف حسابات الفروع", "stockTransfer": "التحويلات الصاردة والواردة", "zatcaListings": "Zatca Registration Listings", "zatcaReports": "Zatca Reports", "salesReports": "تقارير المبيعات", "purchaseReports": "تقارير الشراء"}, "invoiceStatus": {"notReturned": "لم يتم إرجاعها", "partiallyReturned": "مرتجعة جزئيا", "returned": "عاد"}, "transactionTypes": {"sales": "مبيعات", "creditNote": "ملاحظة الائتمان", "debitNote": "ملاحظة الائتمان", "quotation": "اقتباس", "all": "All", "allNotes": "جميع الاشعارات"}, "settlementStatus": {"unpaid": "<PERSON>ير المسدده", "partiallyPaid": "مدفوع جزئيا", "fullyPaid": "مدفوعة بالكامل"}, "roles": {"createRole": "إنشاء دور", "editRole": "تعديل بيانات دور", "viewRole": "عرض بيانات دور", "listRole": "قائمة الأدوار", "register_role_Permission": "أنشاء الأدوار والصلاحيات", "roleId": "رقم الدور", "roleName": "الاسم", "roleDesc": "ملاحظات", "roleTab": "الأدوار", "permissionTab": "الصلاحيات", "roleNameField": "اسم الدور", "profile": "اسم الدور"}, "users": {"userListings": "قائمة المستخدمين", "editUser": "تعديل بيانات مستحدم", "viewUser": "عرض بيانات مستخدم", "register_user": "مستخدم جديد", "userName": "اسم المستخدم", "userFname": "الاسم الأول", "userLname": "اسم العائله", "userRole": "دور المستخدم", "firstName": "الاسم الأول", "lastName": "اسم العائلة", "userId": "رقم المستخدم", "userEmail": "البريد الاكتروني", "userPhone": "الهاتف", "userRoles": "الدور", "userBranches": "الفروع المصرح بها", "userDefaultBranch": "الفرع الافتراضي", "userWarehouse": "المخازن المصرح بها", "userActive": "نشط", "defaultPassword": "الكلمة السريه الافتراضية", "userPasswordReset": "كلمة المرور الجديدة"}, "notes": {"notesNo": "رقم الاشعار", "notesDate": "تاريخ الاشعار", "invoiceNo": "رقم الفاتورة", "invoiceDate": "تاريخ الفاتورة", "customerName": "اسم العميل", "vatNo": "الرقم الضريبي", "noteType": "النوع", "grandTotal": "قيمة الاشعار "}, "compareTable": {"code": " رقم الصنف", "name": "اسم الصنف", "unit": "الوحده", "warehouse": "المخزن", "existingQty": "الكمية المباعة", "purchasedQty": "الكمية المشتراه", "updatedQty": "الكمية المرجعه", "partialReturnText": "هل ترغب في إتمام عملية الارجاع للاصناف ادناه"}, "compareTableDebitNote": {"code": " رقم الصنف", "name": "اسم الصنف", "unit": "الوحده", "priceType": "طريقة التسعير", "price": "السعر", "quantity": "الكمية", "discount": "الخصم", "debitNoteReturnText": "هل ترغب في إتمام العملية للاصناف ادناه"}, "searchPanel": {"itemCode": "رقم الصنف", "nameArabic": "الاسم العربي", "nameEnglish": "الاسم الإنجليزي", "accountNo": "رقم الحساب", "accountName": "اسم الحساب", "searchType": "البحث بواسطة", "searchString": "ا<PERSON><PERSON><PERSON>", "clear": "الغاء", "advancedSearch": "<PERSON><PERSON><PERSON> متقدم", "searchByEnglishName": "البحث بواسطة الاسم الانجليزي", "searchByArabicName": "البحث بواسطة الاسم العربي", "searchByCode": "البحث بواسطة رقم الصنف", "searchByAccountNumber": "البحث برقم الحساب", "searchByJournalNumber": "رق<PERSON> القيد", "accountNumber": "رقم الحساب", "searchByJournalRef": "البحث يواسطة رقم القيد", "searchByAccountNo": "البحث برقم الحساب", "searchByAccountName": "البحث باسم الحساب", "min3charsEntry": "الح<PERSON> الأدنى للبحث 3 حروف/ارقام"}, "productGrid": {"action": "الاجراء", "itemCode": "رقم الصنف", "nameArabic": "الاسم العربي", "nameEnglish": "الاسم الإنجليزي", "partNumber": "الرقم المصنعي", "category": "المجموعة", "vat": "% الضريبة", "Bulk Update Product": "تعديل عدة اصاف", "Reload": "إعادة التحميل", "Delete": "<PERSON><PERSON><PERSON>"}, "productBaicTab": {"listing": "قائمة الأصناف", "productConfiguration": "بيانات الصنف", "register_product": "اضافة صنف جديد", "itemCode": "رقم الصنف", "basicInformation": "البيانات الأساسية", "unitsAndPrices": "الوحدات والاسعار", "discountAndProfits": "الحصم و الربح", "images": "الصورة", "specialInstructions": "تعليمات خاصه", "nameArabic": "الاسم العربي", "nameEnglish": "الاسم الإنجليزي", "partNumber": "الرقم المصنعي", "category": "المجموعة", "vat": "% الضريبة", "itemTypes": "النوع", "warehouse": "المخزن", "maxQty": "ال<PERSON><PERSON> الأعلى", "minQty": "ال<PERSON><PERSON> الأدنى", "color": "اللون", "size": "المقاس", "daysToAlert": "أيام الاخطار", "active": "نشط", "expiryDate": "له تاريخ صلاحية", "weighted": "له وزن", "private": "خاص", "general": "عام", "description": "ملاحظات", "cancel": "خروج", "submit": "تخزين"}, "productUnitPricesTab": {"action": "الاجراء", "unitBarcode": "باركود", "unitOfMeasureCost": "الوحدة", "costPrice": "سعر التكلفة", "openPurRice": "سعر شراء اول المدة", "purchasePrice": "سعر شراء", "wholesalePrice": "سعر الجملة", "distributorPrice": "سعر الموزعين", "retailPrice": "سعر التجزئة", "transportCost": "تكلفه إضافية", "cancel": "خروج", "submit": "تخزين"}, "productDiscountsTab": {"unitBarcode": "باركود", "unitOfMeasure": "الوحدة", "discountMethod": "طريقة الخصم", "discount": "الخصم", "profitMethod": "طريقة الربح", "profit": "الربح", "cancel": "خروج", "submit": "تخزين"}, "productSpecialInstructionsTab": {"itemFree": "صن<PERSON> مجاني", "freeStartDate": "تاريخ البداية", "freeEndDate": "تاريخ الانتهاء", "itemPrintFrozen": "تجميد الطباعة", "itemSalesFrozen": "تجميد البيع", "itemTransferFrozen": "تجميد التحويل", "reason": "السبب", "cancel": "خروج", "submit": "تخزين"}, "productUnits": {"createUnit": "إضافة وحده", "listing": "قائمة الوحدات", "action": "الاجراء", "edit": "تعديل", "view": "<PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON>", "name": "الاسم", "type": "النوع", "factor": "الشد"}, "productCategory": {"createCatgory": "اضافة مجموعه", "listings": "قائمة المجموعات", "action": "الاجراء", "edit": "تعديل مجموعه", "view": "<PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON>", "nameArabic": "الاسم العربي", "nameEnglish": "الاسم الانجليزي", "parentCategory": "المجموعة الرئيسية"}, "openqty": {"action": "الاجراء", "edit": "تعديل", "view": "<PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON>", "warehouse": "المخزن", "category": "المجموعة", "itemCode": "رقم الصنف", "nameArabic": "الاسم العربي", "nameEnglish": "الاسم الإنجليزي", "unit": "الوحدة", "openQty": "كمية اول المدة", "cancel": "خروج", "updateQty": "تحديث الكميات"}, "createAdjsutments": {"createAdjustment": "إضافة مستند جرد", "warehouse": "المخزن", "search": "ا<PERSON><PERSON><PERSON>", "action": "الاجراء", "document": "رقم مستند الجرد", "adjustmentDate": "تاريخ مستند الجرد", "createdBy": "بواسطة", "status": "الحالة", "itemCode": "رقم الصنف", "nameArabic": "الاسم العربي", "nameEnglish": "الاسم الإنجليزي", "unit": "الوحدة", "OpenQty": "كمية الافتتاحية", "UpdateQty": "تحديث الكمية", "actualqty": "الكمية الفعلية", "difference": "الفرق", "remark": "ملاحظات", "post": "اعتماد"}, "bulkEdit": {"bulkEditText": "سيتم تحديث بيانات  <span class='text-error'>{{count}}</span> صنف هل انت متاكد ", "category": "تحديث المجموعة", "vat": "تحديث نسبة الضريبة المضافة", "status": "تحديث حالة الصنف ", "active": "نشط", "inactive": "غير نشط", "selectOption": "فظلا الاختيار"}, "priceUpdate": {"itemCode": "رقم الصنف", "name": "اسم الصنف", "unit": "الوحدة", "wholesalesPrice": "سعر الجملة", "distributorPrice": "سعر الموزعين", "retailPrice": "سعر التجزئة", "openPurchasePrice": "سعر شراء اول المدة", "purchasePrice": "سعر الشراء", "costPrice": "سعر التكلفة"}, "salesQuotes": {"updateQuotation": "تعديل عرض اسعار", "deleteQuotation": "حذف عرض أسعار", "viewQuotation": "عرض عرض أسعار", "postQuotation": "اعتماد عرض أسعار", "createQuotation": "عرض أسعار جديد", "quotationList": "قائمة عروض الاسعار", "quotationInfo": "عرض أسعار رقم", "register_quotes": "عر<PERSON> جديد", "invoiceNo": "رقم العرض", "invoiceDate": "تاريخ العرض", "invoiceAmt": "قيمة العرض", "invoiceCustomerNo": "رقم العميل", "invoiceCustomerName": "اسم العميل", "amount": "كمية", "salesDate": "تاريخ الفاتورة", "referenceNo": "رقم المرجع", "referenceDate": "تاريخ المرجع", "invoiceNote": "ملاحظات الفاتورة", "customerSelection": "اختيار العميل", "code": "رقم الصنف", "name": "اسم الصنف", "unit": "الوحدة", "priceType": "طريقة التسعير", "quantity": "الكمية", "vatAmt": "مبلغ الضريبة", "subTotal": "اجمالي الصنف", "note": "ملاحظات", "discount": "الخصم", "price": "سعر", "returnableQty": "الكمية المستردة", "exp-itemCode": "رقم الصنف", "exp-wareHouse": "المخزن", "exp-vatPct": "% الضريبة", "exp-currentQty": "الكمية الحالية", "exp-partNo": "الرقم المصنعي", "exp-discount": "الخصم", "exp-discountType": "نوع الخصم", "fullCreditNote": " إشعار دائن - ارجاع كامل", "viewNotes": "عرض الاشعارات الدائنة والمدينة", "partialCreditNote": "اشعار دائن - ارجاع جزئي", "debitNote": "إشعار مدين", "partial-qty-sold": "الكمية المباعة", "partial-ref-quantity": "الكمية المرجعة", "partial-price": "السعر", "fullCreditConfirmation": "هل ترغب في إتمام عملية الارجاع للاصناف ادناه", "returnStatus": "الحالة", "notes": "ملاحظات", "termsAndConditions": "الشروط والالتزامات"}, "purchase": {"purchaseList": "قائمة فواتير المشتريات", "register_purchase": "فاتورة جديدة ", "invoiceNo": "رقم العرض", "invoiceDate": "تاريخ الفاتورة", "invoiceAmt": "قيمة الفاتورة", "invoiceCustomerNo": "رقم الحساب", "invoiceCustomerName": "اسم العميل", "amount": "كمية", "salesDate": "تاريخ الفاتورة", "referenceNo": "رقم المرجع", "referenceDate": "تاريخ المرجع", "invoiceNote": "ملاحظات الفاتورة", "customerSelection": "اختيار العميل", "code": "رقم الصنف", "name": "اسم الصنف", "unit": "الوحدة", "priceType": "طريقة التسعير", "quantity": "الكمية", "vatAmt": "مبلغ الضريبة", "subTotal": "اجمالي الصنف", "note": "ملاحظات", "discount": "الخصم", "price": "سعر", "returnableQty": "الكمية المستردة", "distributorPrice": "سعر الموزعين", "wholeSalePrice": "سعر الجمله", "retailPrice": "سعر التجزئة", "purchasePrice": "سعر الشراء", "profit": "الربح", "exp-itemCode": "رقم الصنف", "exp-wareHouse": "المخزن", "exp-vatPct": "% الضريبة", "exp-currentQty": "الكمية الحالية", "exp-partNo": "الرقم المصنعي", "exp-discount": "الخصم", "exp-discountType": "نوع الخصم", "fullCreditNote": "ارجاع كامل للفاتوره رقم", "fullCreditNoteButton": "ارجاع الفاتوره", "viewNotes": "عرض مستندات الارجاع", "partialCreditNote": "ارجاع جزئي للفاتوره رقم", "debitNote": "إشعار مدين", "partial-qty-sold": "الكمية المباعة", "partial-ref-quantity": "الكمية المرجعة", "partial-price": "السعر", "fullCreditConfirmation": "هل ترغب في إتمام عملية الارجاع للاصناف ادناه", "returnStatus": "الحالة", "fullReturn": "ارجاع كامل", "partialReturn": "ارجاع جزئي", "purchaseView": "فاتورة مشتريات رقم", "partialCreditNoteAction": "ارجاع الفاتوره", "newfullCreditNote": "اصدار ارجاع كامل", "newpartialCreditNote": "ارجاع جزئي", "edit": "purchase Edit", "purchaseQty": "الكمية المشتراه"}, "company": {"registration": "اعدادات المنشأة", "nameArabic": "الاسم العربي", "nameEnglish": "الاسم الإنجليزي", "vatNo": "الرقم الضريبي", "phone": "رقم الهاتف", "email": "البريد الالكتروني"}, "branch": {"listbranch": "قائمة الفروع", "createBranch": "إنشاء فرع جديد", "editBranch": "تعديل بيانات فرع", "viewBranch": "عرض بيانات فرع", "nameArabic": "الاسم العربي", "nameEnglish": "الاسم الإنجليزي", "vatNo": "الرقم الضريبي", "phone": "رقم الهاتف", "email": "البريد الالكتروني", "businessCategoryArabic": "فئة الأعمال عربي", "businessCategoryEnglish": "فئة الأعمال الإنجليزية", "invoiceTrailing": "Invoice Trailing"}, "stores": {"branchName": "branchName", "listStores": "قائمة المخازن", "createStore": "أضافة مخزن جديد", "editStore": "تعديل بيانات مخزن", "viewStore": "عرض بيانات مخزن", "nameArabic": "الاسم العربي", "nameEnglish": "الاسم الإنجليزي", "vatNo": "الرقم الضريبي", "phone": "رقم الهاتف", "email": "البريد الالكتروني"}, "caccounts": {"createAccount": "إضافة حساب", "viewAccount": "عرض بيانات حساب", "editAccount": "تعديل حساب", "listAccounts": "الدليل المحاسبي", "accountNo": "رقم الحساب", "accountEnglishName": "الاسم الإنجليزي", "accountArabicName": "الاسم العربي", "accountType": "نوع الحساب", "accountGroup": "المجموعة المحاسبية", "bsnsGroup": "مجموعة العمل", "parentAccount": "حسا<PERSON> الأب", "costAccount": "costAccount", "finalAccountGroup": "الحساب الختامي", "notes": "ملاحظات", "contraAccount": "حسا<PERSON> مساند", "depreciating": "حساب مستهلك", "frozen": "تجميد الحساب", "hidden": "إخفاء", "calistings": "قائمة الدليل المحاسبي", "catreeview": "شجرة الدليل المحاسبي"}, "finalAccountGrpDropDown": {"budget": "ميزانية", "operation": "تشغيل", "trading": "متاجرة", "ProfitandLoss": "أرباح وخسائر", "income": "صافي دخل"}, "accountTypeDropDown": {"general": "رئيسي", "detailed": "فرعي"}, "bsnsGroupDropDown": {"general": "عام", "cashier": "صناديق", "bank": "بنوك", "customer": "عملاء", "supplier": "موردين", "branch": "الفرع", "distributor": "موزعين"}, "accountGrpDropDown": {"assets": "الأصول", "liabilities": "الخصوم", "capital": "رأس المال", "revenues": "الإيرادات", "expenses": "المصروفات"}, "journals": {"listJournals": "قائمة القيود المحاسبية", "create": "اضافة بيانات قيد", "journalNumber": "رق<PERSON> القيد", "journalType": "نوع القيد", "journalCreationType": "طريقة الانشاء", "journalRef": "رق<PERSON> القيد", "date": "التاريخ", "posted": "مر<PERSON><PERSON>", "editJournal": "تعديل بيانات قيد", "viewJournal": "عرض بيانات قيد", "journalCreationDate": "التاريخ", "description": "ملاحظات", "description2": "ملاحظات", "accountNumber": "رقم الحساب", "accountArabicName": "الاسم العربي", "accountEnglishName": "الاسم الإنجليزي", "costCenter": "مركز التكلفة", "debitAmount": "مدين", "creditAmount": "دائن", "totalDebitAmount": "اجمالي المدين", "totalCreditAmount": "اجمالي الدائن", "message": "يجب ان يتساوى اجمالي الجانب المدين و اجمالي الجانب الدائن "}, "journalEntryDropDowns": {"normal": "قيد يومية", "opening": "قيد افتتا<PERSON>ي", "operationClose": "قيد اقفال - تشغيل", "tradeClose": "قيد اقفال - متاجرة", "P/LClose": "قيد اقفال - أرباح وخسائر", "incClose": "قيد اقفال - صا<PERSON>ى الدخل"}, "journalEntryCreationTypesDropDowns": {"manual": "بواسطة المستخدم", "automatic": "بواسطة النظام"}, "costCenter": {"view": "عرض بيانات مركز تكلفه", "edit": "تعديل بيانات مركز تكلفه", "listings": "قائمة مراكز التكلفة", "costCentres": "مراكز التكلفة", "create": "إضافة مركز تكلفة", "accountNumber": "رقم المر<PERSON>ز", "arabicName": "الاسم العربي", "englishName": "الاسم الانجليزي"}, "accountSetupSales": {"accountsSetup": "الاعدادات", "salesAccount": "اعدادات المبيعات", "cashSalesAccount": "حساب المبيعات النقدية", "creditSalesAccount": "حساب المبيعات الآجلة", "cardSalesAccount": "حساب مبيعات الشبكة", "wireTransSalesAccount": "حساب مبيعات الحوالات", "vatSalesAccount": "حساب الضريبة المضافة - مبيعات", "discountSalesAccount": "حساب الخصم المسموح به", "returnSalesAccount": "حساب مردودات المبيعات", "roundingFractionSalesAccount": "حساب الكسور"}, "accountSetupPurchase": {"accountsSetup": "الاعدادات", "purchaseAccount": "اعدادات المشتريات", "cashPurchaseAccount": "حساب المشتريات النقدية", "creditPurchaseAccount": "حساب المشتريات الآجلة", "cardPurchaseAccount": "حساب مشتريات الشبكة", "wireTransPurchaseAccount": "حساب مشتريات الحوالات", "vatPurchaseAccount": "حساب الضريبة المضافة - مشتريات ومصروفات", "discountPurchaseAccount": "حساب الخصم المكتسب", "returnPurchaseAccount": "حساب مردودات المشتريات"}, "accountSetupTransfer": {"accountsSetup": "الاعدادات", "transferAccount": "اعدادات التحويلات", "transferInAccount": "حساب الحوالات الواردة", "transferOutAccount": "حساب الحوالات الصادرة", "transferInReturnAccount": "حساب مردودات الحوالات الواردة", "transferOutReturnAccount": "حساب مردودات الحوالات الصادرة"}, "accountSetupInventory": {"accountsSetup": "الاعدادات", "inventoryAccount": "اعدادات المخزون", "badInventoryDebitAccount": "حساب البضاعة التالفة المدين", "badInventoryCreditAccount": "حساب البضاعة التالفة الدائن", "shortageInventoryDebitAccount": "حساب العجز في المخزون مدين", "shortageInventoryCreditAccount": "حساب العجز في المخزون دائن", "surplusInventoryDebitAccount": "حساب الزيادة في المخزون مدين", "surplusInventoryCreditAccount": "حساب الزيادة في المخزون دائن", "costOfGoodsAccount": "حساب تكلفة المبيعات", "goodsEndAccount": "حساب بضاعة اخر المدة - مخزون"}, "header": {"mainProfile": "بيانات المستخدم", "profile": "الصلاحيات", "logout": "الخروج من النظام"}, "accountListReport": {"businessGroup": "مجموعة العمل", "accountType": "نوع الحساب", "fromAccount": "من حساب", "toAccount": "الى حساب", "accountGroup": "المجموعة المحاسبية"}, "reportSetup": {"branch": "الفرع", "warehouse": "المخزن", "qrText": "نص رمز الاستجابة السريعة", "mainText1": "النص الرئيسي الأول", "mainText2": "النص الرئيسي الثاني", "mainText3": "النص الرئيسي الثالث", "addrLine1": "سطر العنوان الأول", "addrLine2": "سطر العنوان الثاني", "addrLine3": "سطر العنوان الثالث", "subText1": "النص الفرعي الأول", "subText2": "النص الفرعي الثاني", "subText3": "النص الفرعي الثالث", "subAddrLine1": "سطر العنوان الفرعي الأول", "subAddrLine2": "سطر العنوان الفرعي الثاني", "subAddrLine3": "سطر العنوان الفرعي الثالث", "showAdditionalFields": "إظهار الحقول الإضافية", "configuration": "إعداد التقارير", "branchWarehouse": "اختيار الفرع والمخزن", "mainHeader": "معلومات الرأس الرئيسي (عربي)", "subHeader": "معلومات الرأس الفرعي (إنجليزي)", "qrSection": "قسم رمز الاستجابة السريعة", "preview": "معاينة مباشرة", "previewNote": "تُظهر هذه المعاينة كيف سيبدو رأس التقرير. التغييرات تنعكس في الوقت الفعلي.", "viewA4": "عرض بحجم A4"}, "report": {"TotalSales": "إجمالي المبيعات", "transactionType": "نوع المعاملة", "invoiceStatus": "حالة الفاتورة", "documentNumber": "رقم المستند", "searchString": "سلسلة البحث", "InventoryAgeing": "شيخوخة المخزون", "ChartOfAccounts": "دليل الحسابات", "AccountStatements": "كشوف الحساب ", "StockValue": "قيمة السهم", "ItemDetailed": "البند مفصلة", "Pricing": "التسعير", "ItemMovement": "حركة العنصر", "dateRange": "النطاق الزمني", "submit": "تخزين", "dateFrom": "من تاريخ", "dateTo": "الى تاريخ", "accountGroup": "المجموعة المحاسبية", "businessGroup": "مجموعة العمل", "fromAccount": "من حساب", "toAccount": "الى حساب", "account": "ح<PERSON><PERSON><PERSON>", "report": "النوع التقارير", "easySearch": "سهولة البحث", "inventoryReportSearch": "بحث تقرير المخزون", "accountingReportSearch": "بحث التقارير المحاسبية", "warehouse": "المخزن", "categories": "المجموعة", "branch": "الفرع", "units": "الوحدة", "year": "السنة", "itemCode": "الصنف", "TotalPurchase": "إجمالي الشراء", "DetailedPurchase": "الشراء التفصيلي"}, "accountStatReport": {"dateFrom": "من تاريخ", "dateTo": "الى تاريخ", "accountGroup": "المجموعة المحاسبية", "businessGroup": "مجموعة العمل", "accountType": "نوع الحساب", "fromAccount": "من حساب", "toAccount": "الى حساب", "account": "ح<PERSON><PERSON><PERSON>", "reportType": "النوع التقارير", "advanceSearch": "البحث المتقدم", "easySearch": "سهولة البحث", "inventoryReportSearch": "بحث تقرير المخزون", "accountingReportSearch": "بحث التقارير المحاسبية"}, "depreciation": {"listing": "قائمة الاستهلاكات", "depreciationCreate": "أضافة بيانات اصل", "deprciationEdit": "تعديل بيانات استهلاك اصل", "depriciationDetails": "عرض بيانات استهلاك اصل", "depreciationListings": "قائمة الاستهلاكات", "supplierName": "اسم المورد", "assetAccountId": "رقم حساب الأصل", "assetLocation": "موقع الأصل", "deprecationMethod": "طريقة الاستهلاك", "search": "ب<PERSON><PERSON>", "clear": "الغاء", "action": "الاجراء", "underAsset": "رقم حساب الأصل", "cumDeprAccount": "رقم حساب مجمع الاستهلاك", "deprExpAccount": "رقم حساب مصاريف الاستهلاك", "deprPercentage": "نسبة الاستهلاك", "deprMethod": "طريقة الاستهلاك", "straightLine": "القسط الثابت", "decliningBalance": "القسط المتناقص", "purchaseDate": "تاريخ الشراء", "purchaseAmount": "قيمة الشراء", "salvageValue": "قيمة الخرده", "notes": "ملاحظات", "lastDepreciationDate": "اخر تاريخ اهلاك", "cumDepreciation": "اجمالى قيمة الاستهلاك", "costAccountNo": "رقم حساب مركز التكلفة"}, "depreciationMethodDropDown": {"straightLine": "القسط الثابت", "decliningBalance": "القسط المتناقص"}, "salesVoucher": {"totalInvoice": "اجمالي قيمة الفواتير المختارة", "totalAmountPaid": "اجمالي المبلغ المدفوع"}, "voucher": {"createVoucher": "انشاء سند التسديد", "create": "سند جديد", "notes": "ملاحظات", "paidAmount": "المبلغ المدفوع", "distributor": "الموزع", "costCenter": "مركز التكلفة", "name": "الاسم", "accountNo": "رقم الحساب", "invoiceAmount": "قيمة الفاتورة", "invoiceDate": "تاريخ الفاتورة", "invoiceNo": "رقم الفاتورة", "voucherDate": "تاريخ السند", "fullPayment": "تسديد كامل القيمة", "salesListing": "قائمة فواتير المبيعات", "salesReceivables": "تسديد الفواتير الاجلة", "status": "النوع", "dateRange": "خلال الفترة", "startDate": "من تاريخ", "endDate": "الى تاريخ", "voucherStatus": "سندات التسديد", "voucherNumber": "رقم السند", "noVouchersCreated": "لم يتم انشاء السند", "invoiceVouchersListings": "قائمة سندات التسديد", "costCentreId": "مركز التكلفة", "distributorAccountId": "الموزع", "paymentMethod": "طريقة السداد", "voucherAmountError": "المبلغ أكبر من قيمة الفاتورة", "voucherTotal": "المجموع"}, "salesStatusDropDown": {"notPaid": "<PERSON>ير مسدده", "partiallyPaid": "مسدده جزئيا", "fullyPaid": "مسدده بالكامل", "allInvoices": "الجميع"}, "priceReports": {"warehouse": "المخزن", "category": "المجموعة", "branch": "الفرع", "unit": "الوحدة"}, "stockReports": {"warehouse": "المخزن", "category": "المجموعة", "branch": "الفرع", "unit": "الوحدة"}, "profile": {"personalDetails": "بيانات المستخدم", "security": "تغيير الكلمة السرية", "firstName": "الاسم", "lastName": "العائلة", "email": "البريد الالكتروني", "birthdate": "تاريخ الميلاد", "phone": "الهاتف", "language": "لغة النظام", "password": "الكلمة السرية", "confirmPassword": "تأكيد الكلمة السرية", "passwordsDoNotMatch": "الكلمة السرية غير مطابقة"}, "ucvoucher": {"listings": "قائمة سندات القبض والصرف", "createReceipt": "سند جديد", "createPurchase": "سند جديد", "purchaseList": "قائمة سندات الصرف", "receiveList": "قائمة سندات القبض", "newPaymentVoucher": "سند صرف جديد", "newReceiptVoucher": "سند قبض جديد", "ucvoucher": "سندات القبض والصرف", "receipt": "سن<PERSON> قبض", "payment": "سند صرف", "costCentreId": "مركز التكلفة", "distributorAccountId": "الموزع", "amount": "المبلغ", "issueType": "نوع العملية", "notes": "ملاحظات", "voucherTotal": "م<PERSON><PERSON><PERSON> السند", "voucherDate": "تاريخ السند", "invoiceNo": "رقم الفاتورة", "paidAmount": "المبلغ", "voucherNumber": "رقم السند", "voucherType": "نوع السند", "accountNo": "رقم الحساب", "voucherAmount": "م<PERSON><PERSON><PERSON> السند"}, "issueTypeDropDown": {"general": "عام", "vatexpenses": "ضريبة مصروفات", "vatrecon": "سداد ضريبة القيمة المضافة"}, "voucherTypesDisplay": {"0": "سند تسديد مبيعات", "1": "سند تسديد مشتريات", "2": "سن<PERSON> قبض", "3": "سند دفع"}, "stockTransfer": {"stock": "إدارة المخازن", "create": "<PERSON><PERSON><PERSON><PERSON> جديد", "configurePartner": "<PERSON><PERSON><PERSON><PERSON> جديد", "branchParent": "تعريف حسابات الفروع", "branchParentListings": "قائمة حسابات الفروع", "partnerNameArabic": "اسم الفرع العربي", "partnerNameEnglish": "اسم الفرع الإنجليزي", "partnerBranch": "للفرع", "inward": "فرع داخلي", "outward": "فرع خارجي", "issueDate": "تاريخ الإصدار", "docno": "رقم التحويل", "transferType": "خارجي", "accno": "رقم الحساب", "branchout": "الفرع المحول اليه", "branchin": "الفرع المحول منه", "grandTotal": "قيمة التحويل", "outgoingStock": "قائمة التحويلات", "outgoingStocktab": "التحويلات الصادرة", "incomingStocktab": "التحويلات الواردة", "transferCreate": "تحويل جديد", "stockOutward": "تحويل صادر", "stockInward": "تحويل وارد", "crStocksalesDate": "تاريخ الفاتورة", "crStockreferenceNo": "رقم المرجع", "crStockreferenceDate": "تاريخ المرجع", "crStockinvoiceNote": "ملاحظات", "crStockbrselection": "الفرع", "crStockstocktype": "نوع التحويل", "crStockpaymentnote": "جميع التحويلات تعتبر تحويلات آجله", "wareHouse": "المخزن", "accountEnglish": "اسم الحساب الإنجليزي", "accountArabic": "اسم الحساب العربي", "branchArabic": "اسم الفرع العربي", "branchEnglish": "اسم الفرع الإنجليزي"}, "identitymodes": {"SelectAll": "تحدي<PERSON> الكل", "Create": "إضافة", "Delete": "<PERSON><PERSON><PERSON>", "Update": "تعديل", "View": "<PERSON><PERSON><PERSON>", "AccountsManagement": "المحاسبة", "EnterpriseManagement": "اعدادات النظام", "IdentityManagement": "الصلاحيات", "InventoryManagement": "المخازن", "ReportsManagement": "التقارير", "TradeManagement": "المبيعات والمشتريات", "PaymentsManagement": "سندات القبض والصرف", "voucher": "xxx", "PriceUpdate": "تحديث أسعار الأصناف", "BulkEdit": "تحديث البيانات الإضافية للصنف", "CreateTRPA": "تعريف حساب فرع", "ViewTRPA": "عرض بيانات حساب فرع", "CreateTIO": "إضافة مستند تحويل صارد/وارد", "ViewTIO": "عرض مستند تحويل صارد/وارد", "CreateAdj": "انشاء مستند جرد جديد", "PostAdj": "اعتماد مستند جرد معين", "PriceUpdateAdj": "تحديث أسعار الأصناف", "UpdateAdj": "تعديل مستند جرد معين", "ViewAdj": "عرض مستند جرد معين", "CreateOQA": "تحديث كميات اول المدة", "CreateUPA": "تحديث أسعار الأصناف", "Accounts": "كشف دليل الحسابات", "AccountStatement": "كشف حسا<PERSON> معبن", "PriceReports": "كشف أسعار الأصناف", "StockValueReports": "كشف قيمة المخزن", "ucvoucher": "نظام سندات القبض والصرف", "Post": "اعتماد"}, "feature": {"Accounting": "قائمة تقارير المحاسبة", "Adjustments": "<PERSON><PERSON><PERSON> الكميات", "Categories": "المجموعات", "OpenQtyAdjustments": "كميات اول المدة", "Product": "بيانات الاصناف", "Transfer": "تحويلات الفروع", "UnitPriceAdjustment": "تحديث أسعار الأصناف", "Units": "الوحدات", "Role": "الأدوار", "User": "المستخدمين", "Branch": "الفروع", "Company": "المنشأة/الشركة", "WareHouse": "المخازن", "ChartOfAccounts": "الدليل المحاسبي", "CostCentre": "مراكز التكلفة", "Depreciation": "الأستهلاكات", "JournalEntries": "القيود المحاسبية", "Customer": "العملاء", "Distributor": "الموزعين", "Purchase": "المشتريات", "Quotation": "عروض الأسعار", "Sales": "المبيعات", "Supplier": "الموردين", "Inventory": "قائمة تقارير المخازن"}, "tenants": {"enterpriseType": "نوع المنشأة", "tenantInfo": "Tenant Information", "tenantLists": "Tenant Listings", "tenantCreate": "Add Tenant", "tenantConfiguration": "Tenant Configuration", "id": "Tenant Id", "name": "Tenant Name", "regNo": "Registration No", "phone": "Phone Number", "email": "Email", "installDate": "Installation Date", "expDate": "Expiration Date", "contactNameManager": "Manager Contact Name", "active": "Active", "notes": "Notes", "maxBranch": "<PERSON>", "maxWareHouse": "Max <PERSON>ouse", "maxUsers": "Max Users", "storageLim": "Max Storage Limit", "vatNo": "الرقم الضريبي", "identification": "رقم التعريف السجل/الهوية", "identificationCode": "ر<PERSON>ز التعريف"}, "zatcaInvoiceListings": {"documentNumber": "رقم المستند", "issueDate": "تاريخ المستند", "documentType": "نوع المستند", "invoiceType": "نوع الملف", "status": "حالة الملف", "reportToZatca": "ارسال الملف"}, "zatcaInvoiceStatus": {"STANDARD": "مستند ضريبي", "SIMPLIFIED": "مستن<PERSON> مبسط", "REJECTED": "مرفو<PERSON>", "CLEARED": "معت<PERSON>د", "REPORTED": "مرسل", "EINVOICE_SAVED": "تم الانشاء ولم يرسل", "SALES": "فاتورة بيع", "CREDIT_NOTE": "اشعار دائن", "DEBIT_NOTE": "اشعار مدين", "CLEARED_WITH_WARNINGS": "معتمد مع وجود تحذيرات", "INVOICE_CREATED": "لم يتم انشاء الملف", "TO_BE_RESUBMITTED": "يتطلب اعادة الإرسال"}, "xmlViewer": {"download": "تنزيل الملف", "xmlInvoice": "Invoice XML"}, "confirmationModal": {"add": " إضافه", "delete": "<PERSON><PERSON><PERSON>", "update": "تعديل", "confirm": "متا<PERSON>د", "confirmationText": "هل انت متأكد ", "logout": "logout"}, "identificationcodes": {"CRN": "رقم السجل التجاري", "MOM": "ترخيص وزارة الشؤون البلدية والقروية", "MLS": "ترخيص وزارة الموارد البشرية و التنمية الاجتماعية", "SAG": "ترخيص الهيئة العامة للاستثمار", "NAT": "الهوية الوطنية", "TIN": "الرقم المميز", "IQA": "رقم الاقامة", "PAS": "رقم الجواز", "GCC": "رقم هوية مجلس التعاون الخليجي", "OTH": "OTH"}, "inventoryReport": {"itemId": "معر<PERSON> العنصر", "itemCode": "<PERSON><PERSON><PERSON> الصنف", "nameArabic": "اسم", "category": "باب", "unitName": "اسم الوحدة", "unitBarcode": "الباركود للوحدة", "retailPrice": "سعر التجزئة", "wholesalePrice": "سعر الجملة", "distributorPrice": "سعر الموزع", "purchasePrice": "سعر الشراء", "avgPurchasePrice": "متوسط سعر الشراء", "openPurchasePrice": "سعر الشراء المفتوح", "warehouseName": "اسم المستودع", "currentQty": "الكمية الحالية", "quantityOfItemsOlderThan90days": "Items Agedd > 90 days", "quantityOfItemsAged61To90days": "Items Aged 61-90 days", "quantityOfItemsAged31To60days": "Items Aged 31-60 days", "quantityOfItemsAged1To30days": "Items Aged 1-30 days"}, "salesReport": {"invoiceNumber": "رقم الفاتورة", "issueDate": "تاريخ الإصدار", "grandTotal": "المجموع الكلي ", "customerPhoneNumber": "رقم هاتف العميل"}, "accountingReport": {"accountId": "معر<PERSON> الح<PERSON>اب", "branchId": "رقم الفرع", "accountNumber": "رقم الحساب", "nameArabic": "اسم", "nameEnglish": "اسم", "accountType": "نوع الحساب", "accountGroup": "المجموعة المحاسبية", "businessGroup": "مجموعة العمل", "entryDate": "تاريخ المجلة", "debitAmount": "م<PERSON><PERSON><PERSON> الخصم", "creditAmount": "مب<PERSON>غ الائتمان", "balance": "توازن", "description": "وصف"}, "accountColumns": {"BUDGET": "ميزانية", "OPERATION": "تشغيل", "TRADING": "متاجرة", "PROFITANDLOSS": "أرباح وخسائر", "INCOME": "صافي دخل", "DETAILED": "فرعي", "GENERAL": "عام", "CASHIER": "صناديق", "BANK": "بنوك", "CUSTOMER": "عملاء", "SUPPLIER": "موردين", "BRANCH": "الفرع", "DISTRIBUTOR": "موزعين", "ASSETS": "الأصول", "LIABILITIES": "الخصوم", "EQUITY": "رأس المال", "REVENUES": "الإيرادات", "EXPENSES": "المصروفات"}, "itemReportingColumns": {"totalQuantityPerUnit": "الكمية الإجمالية لكل وحدة", "lastInventoryCheckDate": "تاريخ آخر فحص للمخزون", "avgPurchasePrice": "متوسط سعر الشراء", "currentQty": "الكمية الحالية", "discount": "تخفيض", "distributorPrice": "سعر الموزع", "itemQty": "كمية السلعة", "openPurchasePrice": "فتح سعر الشراء", "openQty": "الكمية المفتوحة", "purchasePrice": "سعر الشراء", "retailPrice": "سعر التجزئة", "totalQuantity": "الكمية الإجمالية", "vat": "% الضريبة", "wholesalePrice": "سعر الجملة", "factorRefUom": "عا<PERSON>ل", "category": "فئة", "itemCode": "ر<PERSON>ز السلعة", "itemName": "اسم العنصر", "nameArabic": "الاسم عربي", "nameEnglish": "اسم الانجليزية", "partNumber": "رقم الجزء", "unitBarcode": "وحدة الباركود", "unitName": "اسم الوحدة", "warehouseName": "اسم المستودع", "brand": "ماركة", "parentCategory": "فئة الوالدين", "color": "لون", "size": "مقاس", "maxQty": "الكمية القصوى", "minQty": "الح<PERSON> الأدنى للكمية", "costPrice": "تكلفة الأمير", "dscntAmt": "م<PERSON><PERSON><PERSON> الخصم", "dscntMthd": "طريقة الخصم", "dscntPct": "نسبة الخصم", "profit": "<PERSON><PERSON><PERSON>", "profitPct": "نسبة الربح"}, "purchaseReport": {"invoiceNumber": "رقم الفاتورة", "issueDate": "تاريخ الإصدار", "grandTotal": "المجموع الكلي ", "customerPhoneNumber": "رقم هاتف العميل", "itemCode": "رقم الصنف", "itemNameArabic": "الاسم عربي", "itemNameEnglish": "اسم الانجليزية", "unitName": "الوحدة", "documentNumber": "رقم المستند", "quantity": "الكمية", "price": "سعر", "vat": "% الضريبة"}, "purchaseTransactionTypes": {"purchase": "المشتريات", "creditNote": "ملاحظة الائتمان"}}