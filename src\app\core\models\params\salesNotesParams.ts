import { PaginatedFilter } from 'src/app/core/interfaces/PaginatedFilter';

export class SalesNotesParams implements PaginatedFilter {
  searchString?: string;
  pageNumber: number;
  pageSize: number;
  orderBy?: string;
  invoiceId?: number;
  documentNumber?: string;
  documentType?: string;
  issueDate?: Date;
}

export interface NotesDetails {
  invoiceId: number;
  purchaseNotes: boolean;
  salesProcessing: boolean;
}
