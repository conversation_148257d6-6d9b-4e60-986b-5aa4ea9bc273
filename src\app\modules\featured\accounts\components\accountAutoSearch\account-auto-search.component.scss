/*TODO(mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version.*/
mat-button-toggle-group {
  height: 44px;
  align-items: center;
}

/*TODO(mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version.*/
.mat-button-toggle-checked {
  background-color: #2196f3;
}

.input-group {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  width: 100%;
}

.input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
}

.input-group > .input-addons {
  position: relative;
  flex: 1 1 auto;
  width: 1%;
  margin-bottom: 0;
}

.input-group-append {
  margin-left: -1px;
}

.input-group-append,
.input-group-prepend {
  display: flex;
}

.example-radio-group {
  display: flex;
  flex-direction: column;
  margin: 15px 0;
  align-items: flex-start;
}

.example-radio-button {
  margin: 5px;
}

.example-margin {
  margin: 0 10px;
}

.pe {
  pointer-events: auto;
}

/*TODO(mdc-migration): The following rule targets internal classes of option that may no longer apply for the MDC version.*/
mat-option .mat-row,
.mat-header-row,
.mat-footer-row {
  display: flex;
}

.spinner-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

/*TODO(mdc-migration): The following rule targets internal classes of option that may no longer apply for the MDC version.*/
mat-option .mat-cell,
.mat-header-cell,
.mat-footer-cell {
  flex: 1;
  align-items: center;
  overflow: hidden;
  word-wrap: break-word;
  min-height: inherit;
  word-wrap: break-word !important;
  white-space: pre-line !important;
  line-height: initial;
}

//.bigger-mat-ac mat-option {}

.bigger-mat-ac .mat-mdc-row,
.mat-mdc-header-row,
.mat-mdc-footer-row {
  display: flex;
  border-width: 0;
  border-bottom-width: 1px;
  border-style: solid;
  align-items: center;
  box-sizing: border-box;
  word-wrap: break-word !important;
  white-space: pre-line !important;
}

.bigger-mat-ac .mat-mdc-cell,
.mat-mdc-header-cell,
.mat-mdc-footer-cell {
  flex: 1;
  align-items: center;
  overflow: hidden;
  word-wrap: break-word;
  min-height: inherit;
  word-wrap: break-word !important;
  white-space: pre-line !important;
}

.search.mat-mdc-option.mat-active {
  background: #2196f3 !important;
}

.search.mat-mdc-option {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  /* line-height: 48px; */
  height: 50px;
  padding: 0px;
  text-decoration: none;
  max-width: 100%;
  position: relative;
  cursor: pointer;
  outline: none;
  display: flex;
  flex-direction: row;
  max-width: 100%;
  box-sizing: border-box;
  align-items: center;
  -webkit-tap-highlight-color: transparent;
}

.no-row-center {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.mat-mdc-option.mat-active {
  background: #1e88e5 !important;
}

.custom-icon {
  padding: 0 0 0 0;
  margin-left: 5px;
}
