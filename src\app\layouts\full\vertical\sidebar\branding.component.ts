import { Component } from '@angular/core';
import { MultilingualService } from 'src/app/modules/core/core/services/multilingual.service';

@Component({
  selector: 'app-branding',
  template: `
    <div class="branding d-none d-lg-flex align-items-center">
      <a class="d-flex" [routerLink]="['/']">
        <img class="align-middle m-2" src="./assets/images/logos/light-logo.svg" alt="logo" />
      </a>
    </div>
  `,
})
export class BrandingComponent {
  options = this.multilingualService.getOptions();

  constructor(private multilingualService: MultilingualService) {}
}
