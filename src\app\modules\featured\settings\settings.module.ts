import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { TranslateModule } from '@ngx-translate/core';
import { NgxScrollTopModule } from 'ngx-scrolltop';
import { SharedModule } from 'src/app/modules/shared/shared.module';
import { BranchFormComponent } from './components/branches/branch-form/branch-form.component';
import { BranchesListComponent } from './components/branches/branches-list/branches-list.component';
import { CompanyComponent } from './components/company/company.component';
import { CompanyDashboardComponent } from './components/dashboard/company-dashboard.component';
import { StoreFormComponent } from './components/stores/store-form/store-form.component';
import { StoresListComponent } from './components/stores/stores-list/stores-list.component';
import { CompanyService } from './services/company.service';
import { SettingsRoutingModule } from './settings-routing.module';
@NgModule({
  declarations: [
    CompanyComponent,
    BranchesListComponent,
    StoresListComponent,
    StoreFormComponent,
    BranchFormComponent,
    CompanyDashboardComponent,
  ],
  imports: [
    CommonModule,
    TranslateModule,
    SettingsRoutingModule,
    SharedModule,
    FlexLayoutModule,
    NgxScrollTopModule,
  ],
  providers: [CompanyService],
})
export class SettingsModule {}
