<div class="p-24 p-b-0">
  <div class="row">
    <div class="col-10">
      <mat-form-field class="w-100" class="hide-hint w-100" color="primary">
        <input [(ngModel)]="searchText" matInput placeholder="Search here" />
      </mat-form-field>
    </div>
    <div class="col-2 d-flex justify-content-end">
      <button class="d-flex justify-content-center" mat-icon-button mat-dialog-close>
        <i-tabler class="icon-16 d-flex" name="x"></i-tabler>
      </button>
    </div>
  </div>
</div>
<mat-divider></mat-divider>
<mat-dialog-content class="mat-typography search-dialog">
  <h4 class="f-s-18 f-w-500 mat-body-1 m-b-16">Quick Page Links</h4>

  <a
    class="p-y-12 text-decoration-none d-block"
    *ngFor="let item of navItemsData | appFilter : searchText"
    [routerLink]="[item.route]"
    mat-dialog-close>
    <h5 class="f-s-14 f-w-500 d-block mat-subtitle-2 m-0">
      {{ item.displayName }}
    </h5>
    <span class="f-s-12 mat-body-2">{{ item.route }}</span>
  </a>
</mat-dialog-content>
