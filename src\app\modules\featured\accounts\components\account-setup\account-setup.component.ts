import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatAccordion } from '@angular/material/expansion';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { Account, ConfiguredAccount } from '../../models/account';
import { AccountParams } from '../../models/accountParams';
import { ChartOfAccountsService } from '../../services/chart-of-accounts.service';
import { SetupRequest } from './account-setup.payload';
import { AccountBasic } from '../../models/account';
import { LocalStorageService } from 'src/app/modules/core/core/services/local-storage.service';
import { CommonService } from 'src/app/core/api/common.service';

@Component({
  selector: 'app-product-form',
  templateUrl: './account-setup.component.html',
  styleUrls: ['./account-setup.component.scss'],
})
export class AccountSetupComponent implements OnInit {
  @ViewChild(MatAccordion) accordion: MatAccordion;
  salesAccountForm: UntypedFormGroup;
  purchaseAccountForm: UntypedFormGroup;
  discountAccountForm: UntypedFormGroup;
  accountForm: UntypedFormGroup;
  accounts: Account[];
  configuredAccount: ConfiguredAccount = {
    cashSalesAccount: null,
    creditSalesAccount: null,
    cardSalesAccount: null,
    wireTransferSalesAccount: null,
    vatSalesAccount: null,
    discountSalesAccount: null,
    returnSalesAccount: null,
    roundingDiscountAccount: null,
    cashPurchaseAccount: null,
    creditPurchaseAccount: null,
    cardPurchaseAccount: null,
    wireTransferPurchaseAccount: null,
    vatPurchaseAccount: null,
    discountPurchaseAccount: null,
    returnPurchaseAccount: null,
    transferInAccount: null,
    transferOutAccount: null,
    transferInReturnAccount: null,
    transferOutReturnAccount: null,
    badInventoryDebitAccount: null,
    badInventoryCreditAccount: null,
    shortageInventoryDebitAccount: null,
    shortageInventoryCreditAccount: null,
    surplusInventoryDebitAccount: null,
    surplusInventoryCreditAccount: null,
    cogsAccount: null,
    cogsEndAccount: null,
  };
  isLoading = true;
  submitted = false;

  @ViewChild(MatSort) sort: MatSort = Object.create(null);
  @ViewChild(MatPaginator) paginator: MatPaginator = Object.create(null);

  constructor(
    public dialog: MatDialog,
    private chartOfAccountsService: ChartOfAccountsService,
    private fb: UntypedFormBuilder,
    private toastr: ToastrService,
    private authService: AuthService,
    private router: Router,
    private localStorage: LocalStorageService,
    private commonService: CommonService
  ) {}

  ngOnInit(): void {
    this.accountForm = this.fb.group({
      cashSalesAccount: [],
      creditSalesAccount: [],
      cardSalesAccount: [],
      wireTransferSalesAccount: [],
      vatSalesAccount: [],
      discountSalesAccount: [],
      returnSalesAccount: [],
      roundingDiscountAccount: [],
      cashPurchaseAccount: [],
      creditPurchaseAccount: [],
      cardPurchaseAccount: [],
      wireTransferPurchaseAccount: [],
      vatPurchaseAccount: [],
      discountPurchaseAccount: [],
      returnPurchaseAccount: [],
      transferInAccount: [],
      transferOutAccount: [],
      transferInReturnAccount: [],
      transferOutReturnAccount: [],
      badInventoryDebitAccount: [],
      badInventoryCreditAccount: [],
      shortageInventoryDebitAccount: [],
      shortageInventoryCreditAccount: [],
      surplusInventoryDebitAccount: [],
      surplusInventoryCreditAccount: [],
      cogsAccount: [],
      cogsEndAccount: [],
    });
    this.getAccounts();
  }

  getAccounts(): void {
    const accountParams: AccountParams = new AccountParams();
    accountParams.accountType = 'DETAILED';
    this.chartOfAccountsService.getAllChartOfAccounts(accountParams).subscribe(result => {
      this.accounts = result.accounts;
      this.isLoading = false;
    });

    this.chartOfAccountsService.getAllConfiguredAccounts().subscribe(result => {
      if (result) {
        this.configuredAccount = result;
        this.accountForm.patchValue(this.configuredAccount);
      }
    });
  }

  onSubmit(event: Event) {
    this.submitted = true;
    event.preventDefault();
    this.accountForm.markAllAsTouched();

    let data: ConfiguredAccount = <ConfiguredAccount>{};
    data = this.accountForm.value;
    console.log(data);

    console.log(JSON.stringify(data));
    this.chartOfAccountsService.setUpAccounts(data).subscribe(result => {
      console.log(result);
      this.isLoading = false;
      this.commonService.playSuccessSound();
      this.router.navigate(['../accounts/dashboard']);
    });
    console.log(this.accountForm);
  }

  getAccountName(val: number) {
    const result = this.accounts.filter(account => account.accountId === val);
    console.log(result[0].nameArabic);
    return result[0].nameArabic;
  }

  getAccountIdPlaceholder(account: any): string {
    if (this.localStorage.getItem('locale') === 'AR' && account && account.nameArabic !== null) {
      return account.nameArabic + '-' + account.accountNumber;
    } else if (
      this.localStorage.getItem('locale') === 'EN' &&
      account &&
      account.nameEnglish !== null
    ) {
      return account.nameEnglish + '-' + account.accountNumber;
    } else if (account && account.nameEnglish !== null) {
      return account.nameEnglish + '-' + account.accountNumber;
    } else if (account && account.nameArabic !== null) {
      return account.nameArabic + '-' + account.accountNumber;
    } else {
      return '';
    }
  }
}
