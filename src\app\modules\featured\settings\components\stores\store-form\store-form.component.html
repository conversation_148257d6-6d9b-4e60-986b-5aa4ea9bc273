<ng-container>
  <mat-card appearance="outlined">
    <form [ngClass]="{ readOnly: isViewMode }" [formGroup]="storeForm" autocomplete="off">
      <mat-card-title>{{ formTitle | translate }}</mat-card-title>
      <div class="row no-gutters">
        <div class="col-md-6 col-sm-6 p-2">
          <mat-label>{{ 'stores.nameArabic' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input #arabic type="text" maxlength="40" matInput formControlName="nameArabic" />
            <mat-error
              *ngIf="
                storeForm.controls['nameEnglish'].hasError('required') &&
                storeForm.controls['nameEnglish'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>

        <div class="col-md-6 col-sm-6 p-2">
          <mat-label>{{ 'stores.nameEnglish' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input #english type="text" maxlength="40" matInput formControlName="nameEnglish" />
            <mat-error
              *ngIf="
                storeForm.controls['nameEnglish'].hasError('required') &&
                storeForm.controls['nameEnglish'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>

        <div class="col-md-3 col-lg-3 col-sm-6 p-2">
          <mat-label>{{ 'stores.phone' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input #phone type="text" maxlength="15" matInput formControlName="phoneNumber" />
            <mat-error
              *ngIf="
                storeForm.controls['phoneNumber'].hasError('required') &&
                storeForm.controls['phoneNumber'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
            <mat-error
              *ngIf="
                storeForm.controls['phoneNumber'].hasError('minlength') &&
                storeForm.controls['phoneNumber'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
            <mat-error
              *ngIf="
                storeForm.controls['phoneNumber'].hasError('maxlength') &&
                storeForm.controls['phoneNumber'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>

        <div class="col-md-3 col-lg-3 col-sm-6 p-2">
          <mat-label>{{ 'stores.email' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input #email type="text" maxlength="50" matInput formControlName="emailId" />
            <mat-error
              *ngIf="
                storeForm.controls['emailId'].hasError('required') &&
                storeForm.controls['emailId'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
            <mat-error
              *ngIf="
                storeForm.controls['emailId'].errors?.email && storeForm.controls['emailId'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>

        <div class="col-md-3 col-lg-3 col-sm-6 p-2">
          <mat-label>{{ 'stores.branchName' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <mat-select formControlName="branchId">
              <mat-option *ngFor="let branch of branches" [value]="branch.branchId">
                {{ (branch | localized) + ' - BranchId(' + branch.branchId + ')' }}
              </mat-option>
            </mat-select>
            <mat-error
              *ngIf="
                storeForm.controls['branchId'].hasError('required') &&
                storeForm.controls['branchId'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>

        <div class="col-md-4 col-lg-4 col-sm-6 p-2 align-toggle">
          <mat-slide-toggle [labelPosition]="'after'" [formControl]="storeForm.controls['isPos']"
            >IsPos</mat-slide-toggle
          >
        </div>
        <app-address class="hide" #addressForm formControlName="address"></app-address>
      </div>
    </form>
  </mat-card>

  <div class="text-center">
    <ng-container *ngIf="isCreateMode">
      <button
        class="m-l-10"
        (click)="onSubmit($event); (false)"
        mat-flat-button
        color="primary"
        type="button">
        {{ 'common.buttons.save' | translate }}
      </button>
      <button class="m-l-10" [routerLink]="['../']" type="button" mat-stroked-button color="warn">
        {{ 'common.buttons.cancel' | translate }}
      </button>
    </ng-container>
    <ng-container *ngIf="isEditMode">
      <button
        class="m-l-10"
        (click)="onSubmit($event); (false)"
        mat-flat-button
        color="primary"
        type="button">
        {{ 'common.buttons.save' | translate }}
      </button>
      <button
        class="m-l-10"
        [routerLink]="['../../']"
        type="button"
        mat-stroked-button
        color="warn">
        {{ 'common.buttons.cancel' | translate }}
      </button>
    </ng-container>
    <ng-container *ngIf="isViewMode">
      <button
        class="m-l-10"
        [routerLink]="['../../']"
        mat-stroked-button
        color="primary"
        type="button">
        {{ 'common.buttons.back' | translate }}
      </button>
    </ng-container>
  </div>
</ng-container>
