/* src/app/modules/shared/components/payment-dialog/payment-dialog.component.scss */
.payment-row {
  display: flex;
  align-items: center; /* Align items vertically centered */
  margin-bottom: 16px; /* Space between rows */
}

.payment-selection,
.payment-amount {
  margin-right: 16px; /* Space between selection and amount */
}

.add-button {
  margin-left: 8px; /* Space between amount input and add button */
}

.dense-0 .mat-mdc-text-field-wrapper.mdc-text-field--outlined .mat-mdc-form-field-infix {
  padding: 0px !important;
}

.centered-form-field {
  text-align: center; /* Center aligns the entire form field */
}

.centered-form-field .mat-label {
  text-align: center !important; /* Center aligns the label */
  display: block !important; /* Ensures that the label takes up full width */
}

.centered-form-field .mat-form-field-wrapper {
  justify-content: center; /* Centers the input field within the form field */
}

.dense-0 .mat-mdc-text-field-wrapper.mdc-text-field--outlined .mat-mdc-form-field-infix {
  padding: 0px !important;
}

.action-buttons {
  height: 40px;
  font-size: 20px;
}

html {
  font-size: 28px;
}
