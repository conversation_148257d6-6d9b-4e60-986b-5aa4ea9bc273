*,
:after,
:before {
  box-sizing: border-box;
}

body {
  font-family: $font-family;
  color: $textPrimary;
  font-size: 14px;
  line-height: 1.334rem;
}

html .mat-drawer-container {
  background-color: $white;
}

.mainWrapper {
  display: flex;
  min-height: calc(100vh - 50px); /* Subtract footer height */
  width: 100%;
  flex: 1;
  height: 100%;
  position: relative;
}

.pageWrapper {
  padding: 12px;
  min-height: calc(100vh - 110px); /* Account for header and footer */
  // background: $darktoolbarmenuoptions;
  margin: 0 auto;
  padding-bottom: 50px; /* Add padding to prevent content from being hidden by footer */

  &.maxWidth {
    max-width: $boxedWidth;
  }
}

.w-100 {
  width: 100%;
}

.h-100 {
  height: 100%;
}

.shadow-none {
  box-shadow: none !important;
}

.rounded {
  border-radius: $border-radius !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

.rounded-pill {
  border-radius: 25px !important;
}

.overflow-hidden {
  overflow: hidden;
}

.text-decoration-none {
  text-decoration: none;
}

.position-relative {
  position: relative;
}

.table-responsive {
  overflow-x: auto;

  td,
  mat-cell {
    white-space: nowrap;
    //padding: 16px;
  }
}

@media (max-width: 991px) {
  .table-responsive-sm {
    overflow-x: auto;
  }
}

.op-4 {
  opacity: 0.5;
}

.cursor-pointer {
  cursor: pointer;
}

.avatar-group {
  img {
    border: 2px solid white;
    margin-right: -5px;

    &:last-child {
      margin-right: 0;
    }
  }
}

.text-hover-primary:hover {
  .hover-text {
    color: $primary;
  }
}

.filter-sidebar {
  width: 290px;
}

//
.readOnly {
  .mat-mdc-select-disabled {
    .mat-mdc-select-arrow-wrapper {
      display: none;
    }
  }
}

.mat-expansion-panel-header.mat-expanded {
  height: 25px;
}

.mat-mdc-form-field-infix {
  min-height: 30px;
}

.mat-mdc-form-field-icon-suffix .mat-icon {
  font-size: 14px;
  height: 14px;
  width: 14px;
}

mat-datepicker-toggle .mat-mdc-button-base {
  margin-top: 0px !important;
}

.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mat-mdc-form-field-infix {
  padding-bottom: 0;
}

.dense-0 {
  @include mat.form-field-density(-1);
}
