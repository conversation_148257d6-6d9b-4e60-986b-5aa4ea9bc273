<!-- action bar -->
<app-create-action
  *appHasPermission="['Distributor.Create', 'AllPermissions']"
  [label]="'distributor.register_distributor' | translate"></app-create-action>
<!-- action bar -->

<mat-card appearance="outlined">
  <mat-card-content>
    <!-- search field -->
    <div class="row no-gutters">
      <div class="col-md-6 col-lg-6 col-sm-12 d-flex">
        <mat-form-field class="w-100">
          <input
            #searchInput
            (keyup)="applyFilter($event.target.value)"
            matInput
            autocomplete="off" />
          <i-tabler class="icon-16" matPrefix name="search"></i-tabler>
          <a
            class="cursor-pointer"
            *ngIf="searchInput?.value"
            (click)="clearSearchInput()"
            matSuffix>
            <i-tabler class="icon-16 error" name="X"></i-tabler>
          </a>
        </mat-form-field>
      </div>
    </div>
    <!-- search field -->
    <mat-card-title class="m-t-10">{{ 'distributor.distributorList' | translate }}</mat-card-title>
    <div class="table-responsive">
      <table [dataSource]="dataSource" mat-table matSort>
        <!-- Unit Name Column -->
        <ng-container matColumnDef="accountNumber">
          <th class="font-14" *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'common.field.accountNumber' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ element.accountNumber }}
          </td>
        </ng-container>
        <!-- Unit Type Column -->
        <ng-container matColumnDef="nameArabic">
          <th class="font-14" *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'common.field.nameArabic' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ element.nameArabic }}
          </td>
        </ng-container>
        <!-- Unit Factor Column -->
        <ng-container matColumnDef="nameEnglish">
          <th class="font-14" *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'common.field.nameEnglish' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ element.nameEnglish }}
          </td>
        </ng-container>

        <ng-container matColumnDef="vatNumber">
          <th class="font-14" *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'common.field.vatNo' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ element.vatNumber }}
          </td>
        </ng-container>

        <ng-container matColumnDef="phoneNumber">
          <th class="font-14" *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'common.field.phone' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ element.phoneNumber }}
          </td>
        </ng-container>
        <!-- User Actions Column -->
        <ng-container matColumnDef="action">
          <th class="font-14" *matHeaderCellDef mat-header-cell>
            {{ 'common.action' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            <button
              class="d-flex justify-content-center"
              [matMenuTriggerFor]="menu1"
              mat-icon-button>
              <i-tabler class="icon-20 d-flex" name="dots-vertical"></i-tabler>
            </button>
            <mat-menu class="cardWithShadow" #menu1="matMenu">
              <button
                class="m-b-10"
                *appHasPermission="['Distributor.Update', 'AllPermissions']"
                [routerLink]="['edit', element.distributorId]"
                mat-menu-item>
                <div class="d-flex align-items-center">
                  <i-tabler class="icon-16 m-r-4" name="edit"></i-tabler>
                  <span>{{ 'common.editAction' | translate }}</span>
                </div>
              </button>
              <button
                class="m-b-10"
                *appHasPermission="['Distributor.View', 'AllPermissions']"
                [routerLink]="['view', element.distributorId]"
                mat-menu-item>
                <div class="d-flex align-items-center">
                  <i-tabler class="icon-16 m-r-4" name="eye"></i-tabler>
                  <span>{{ 'common.viewAction' | translate }}</span>
                </div>
              </button>
            </mat-menu>
          </td>
        </ng-container>
        <tr class="mat-row" *matNoDataRow>
          <td class="text-center text-info" [attr.colspan]="displayedColumns.length">
            {{ 'common.noDataFound' | translate }}
          </td>
        </tr>
        <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
        <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
      </table>
    </div>
    <mat-paginator
      *ngIf="dataSource?.filteredData?.length > 0"
      [length]="dataSource.filteredData.length"
      [pageIndex]="0"
      [pageSize]="10"
      [pageSizeOptions]="[5, 10, 20]"></mat-paginator>
  </mat-card-content>
</mat-card>
