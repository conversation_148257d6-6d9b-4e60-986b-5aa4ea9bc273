import { NavItem } from '../../vertical/sidebar/nav-item/nav-item';

export const navItems: NavItem[] = [
  {
    navCap: 'Application Features',
  },
  {
    route: 'accounts',
    displayName: 'navigationMenus.accounting',
    iconName: 'point',
    permissions: ['AccountsManagement', 'AllPermissions'],
    children: [
      {
        route: 'accounts/chartOfAccounts',
        displayName: 'navigationMenus.chartOfAccounts',
        iconName: 'point',
        permissions: ['ChartOfAccounts', 'AllPermissions'],
      },
      {
        route: 'accounts/journalEntries',
        displayName: 'navigationMenus.journalEntries',
        iconName: 'point',
        permissions: ['ChartOfAccounts', 'AllPermissions'],
      },
      {
        route: 'accounts/depreciation',
        displayName: 'navigationMenus.depreciation',
        iconName: 'point',
        permissions: ['ChartOfAccounts', 'AllPermissions'],
      },
      {
        route: 'accounts/costCentres',
        displayName: 'navigationMenus.costCenter',
        iconName: 'point',
        permissions: ['ChartOfAccounts', 'AllPermissions'],
      },
      {
        route: 'accounts/accountSetup',
        displayName: 'navigationMenus.accountBookSetup',
        iconName: 'point',
        permissions: ['ChartOfAccounts', 'AllPermissions'],
      },
    ],
  },
  {
    route: 'trading',
    displayName: 'navigationMenus.trading',
    iconName: 'point',
    permissions: ['TradeManagement', 'AllPermissions'],
    children: [
      {
        route: 'trading/sales',
        displayName: 'navigationMenus.sales',
        iconName: 'point',
        permissions: ['Sales', 'AllPermissions'],
      },
      {
        route: 'trading/quotations',
        displayName: 'navigationMenus.quotations',
        iconName: 'point',
        permissions: ['Quotation', 'AllPermissions'],
      },
      {
        route: 'trading/purchases',
        displayName: 'navigationMenus.purchases',
        iconName: 'point',
        permissions: ['Quotation', 'AllPermissions'],
      },
      {
        route: 'trading/customers',
        displayName: 'navigationMenus.customers',
        iconName: 'point',
        permissions: ['Customer', 'AllPermissions'],
      },
      {
        route: 'trading/suppliers',
        displayName: 'navigationMenus.suppliers',
        iconName: 'point',
        permissions: ['Supplier', 'AllPermissions'],
      },
      {
        route: 'trading/distributors',
        displayName: 'navigationMenus.distributors',
        iconName: 'point',
        permissions: ['Distributor', 'AllPermissions'],
      },
    ],
  },
  {
    route: 'inventory',
    displayName: 'navigationMenus.inventory',
    iconName: 'point',
    permissions: ['InventoryManagement', 'AllPermissions'],
    children: [
      {
        route: 'inventory/products',
        displayName: 'navigationMenus.products',

        iconName: 'point',
        permissions: ['Product', 'AllPermissions'],
      },
      {
        route: 'inventory/units',
        displayName: 'navigationMenus.units',

        iconName: 'point',
        permissions: ['Units', 'AllPermissions'],
      },
      {
        route: 'inventory/category',
        displayName: 'navigationMenus.catagories',

        iconName: 'point',
        permissions: ['Categories', 'AllPermissions'],
      },
      {
        route: 'stocks/adjustments',
        displayName: 'navigationMenus.openquantityAdjustment',

        iconName: 'point',
        permissions: ['OpenQtyAdjustments.CreateOQA', 'AllPermissions'],
      },
      {
        route: 'stocks/stockadjustment',
        displayName: 'navigationMenus.stockTracking',

        iconName: 'point',
        permissions: ['Adjustments.ViewAdj', 'AllPermissions'],
      },
      {
        route: 'stocks/priceupdate',
        displayName: 'navigationMenus.priceAdjustment',

        iconName: 'point',
        permissions: ['Adjustments.PriceUpdateAdj', 'AllPermissions'],
      },
      {
        route: 'stocks/branchpartner',
        displayName: 'navigationMenus.branchPartner',

        iconName: 'point',
        permissions: ['Adjustments.ViewTRPA', 'AllPermissions'],
      },
      {
        route: 'stocks/transfer',
        displayName: 'navigationMenus.stockTransfer',

        iconName: 'point',
        permissions: ['Adjustments.ViewTRPA', 'AllPermissions'],
      },
    ],
  },
  {
    route: 'identity',
    displayName: 'navigationMenus.identity',
    iconName: 'point',
    permissions: ['IdentityManagement', 'AllPermissions'],
    children: [
      {
        route: 'identity/roles',
        displayName: 'navigationMenus.rolesPermissions',
        iconName: 'point',
        permissions: ['Role', 'AllPermissions'],
      },
      {
        route: 'identity/users',
        displayName: 'navigationMenus.users',
        iconName: 'point',
        permissions: ['User', 'AllPermissions'],
      },
    ],
  },

  {
    route: 'enterprise',
    displayName: 'navigationMenus.enterprise',
    iconName: 'point',
    permissions: ['EnterpriseManagement', 'AllPermissions'],
    children: [
      {
        route: 'enterprise/company',
        displayName: 'navigationMenus.companyProfile',
        iconName: 'point',
        permissions: ['Company.Update', 'AllPermissions'],
      },
      {
        route: 'enterprise/branches',
        displayName: 'navigationMenus.branchSetup',
        iconName: 'point',
        permissions: ['Branch', 'AllPermissions'],
      },
      {
        route: 'enterprise/warehouses',
        displayName: 'navigationMenus.wareHouseSetup',
        iconName: 'point',
        permissions: ['WareHouse', 'AllPermissions'],
      },
    ],
  },
  {
    route: 'ucvoucher',
    displayName: 'navigationMenus.ucvoucher',
    iconName: 'point',
    permissions: ['TradeManagement', 'AllPermissions'],
    children: [
      {
        route: 'finance/ucvoucher',
        displayName: 'navigationMenus.ucvoucher',
        iconName: 'point',
        permissions: ['TradeManagement', 'AllPermissions'],
      },
    ],
  },
  {
    route: 'reports',
    displayName: 'navigationMenus.reports',
    iconName: 'point',
    permissions: ['ReportsManagement', 'AllPermissions'],
    children: [
      {
        route: 'reports/inventoryReports',
        displayName: 'navigationMenus.Inventory',
        iconName: 'point',
        permissions: ['ReportsManagement.Inventory', 'AllPermissions'],
      },
      {
        route: 'reports/accountingReports',
        displayName: 'navigationMenus.accountingReport',
        iconName: 'point',
        permissions: ['ReportsManagement.Accounting', 'AllPermissions'],
      },
      {
        route: 'reports/salesReports',
        displayName: 'navigationMenus.salesReports',
        iconName: 'point',
        permissions: ['ReportsManagement.Accounting', 'AllPermissions'],
      },
      {
        route: 'reports/zatcaReports',
        displayName: 'navigationMenus.zatcaReports',
        iconName: 'point',
        permissions: ['Accounting.AccountStatement', 'AllPermissions'],
      },
      {
        route: 'reports/purchaseReports',
        displayName: 'navigationMenus.purchaseReports',
        iconName: 'point',
        permissions: ['ReportsManagement.Accounting', 'AllPermissions'],
      },
      {
        route: 'reports/reportsSetup',
        displayName: 'navigationMenus.reportsSetup',
        iconName: 'point',
        permissions: ['ReportsManagement.Accounting', 'AllPermissions'],
      },
    ],
  },
  {
    route: 'utility',
    displayName: 'navigationMenus.utility',
    iconName: 'point',
    permissions: ['ReportsManagement', 'AllPermissions'],
    children: [
      {
        route: 'utility/dataImport',
        displayName: 'Data Import',
        iconName: 'point',
        permissions: ['PriceReports', 'AllPermissions'],
      },
      {
        route: 'utility/branchSetup',
        displayName: 'Branch Setup',
        iconName: 'point',
        permissions: ['StockValueReports', 'AllPermissions'],
      },
    ],
  },
  {
    route: 'owner',
    displayName: 'Tenant Management',
    iconName: 'point',
    permissions: ['Provider.TenantManagement', 'AllPermissions'],
    children: [
      {
        route: 'owner/tenants',
        displayName: 'Tenant Management',
        iconName: 'point',
        permissions: ['Provider.TenantManagement', 'AllPermissions'],
      },
      {
        route: 'owner/tenants/zatcalistings',
        displayName: 'Zatca Listings',
        iconName: 'point',
        permissions: ['Provider.TenantManagement', 'AllPermissions'],
      },
    ],
  },
];
