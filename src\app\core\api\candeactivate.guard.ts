import { CanDeactivateFn } from '@angular/router';
import { CandeactivateService } from './candeactivate.service';
import { inject } from '@angular/core';
import { ComponentCanDeactivate } from './can-deactivate';

export const hasUnsavedChangesGuard: CanDeactivateFn<ComponentCanDeactivate> = (
  component: ComponentCanDeactivate
) => {
  const canDeactivateService = inject(CandeactivateService);
  const form = component.getForm();

  if (form) {
    return canDeactivateService.canDeactivate(form);
  }

  return true; // Default to true if there's no form or the form can't be accessed
};
