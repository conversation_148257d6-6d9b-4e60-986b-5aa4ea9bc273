import { Component, OnInit } from '@angular/core';
import { fork<PERSON>oin } from 'rxjs';
import { ReportConfig } from '../dynamic-report/dynamic-report.component';
import { BranchService } from '../../../settings/services/branch.service';
import { CategoryService } from '../../../../../core/api/category.service';
import { StoreService } from '../../../../../core/api/store.service';
import { UnitService } from '../../../../../core/api/unit.service';
import { ProductService } from '../../../../../core/api/product.service';
import { ReportService } from '../../../settings/services/report.service';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { BranchParams } from '../../../settings/models/branchParams';
import { CategoryParams } from '../../../catalog/models/categoryParams';
import { StoreParams } from '../../../settings/models/storeParams';

@Component({
  selector: 'app-inventory-reports',
  templateUrl: './inventory-reports.component.html',
  styleUrls: ['./inventory-reports.component.scss'],
})
export class InventoryReportsComponent implements OnInit {
  reportConfigs: ReportConfig[] = [];
  dataProviders: { [key: string]: () => any[] } = {};

  // Data sources
  private branches: any[] = [];
  private categories: any[] = [];
  private warehouses: any[] = [];
  private units: any[] = [];
  private years: any[] = [];
  private items: any[] = [];

  isLoading = true;

  constructor(
    private branchService: BranchService,
    private categoryService: CategoryService,
    private storeService: StoreService,
    private unitService: UnitService,
    private productService: ProductService,
    private reportService: ReportService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.loadInitialData();
  }

  private loadInitialData(): void {
    const requests = [
      this.branchService.getAllBranchesWithYear(new BranchParams()),
      this.categoryService.getAllCategories(new CategoryParams()),
      this.storeService.getAllStores(new StoreParams()),
      this.unitService.getAllUnits(),
      this.reportService.getAllReportType(1), // Inventory reports type
      this.authService.getUserBranchesYearId(this.authService.getCompanyID),
    ];

    forkJoin(requests).subscribe({
      next: ([branches, categories, warehouses, units, reportTypes, years]) => {
        this.branches = branches;
        this.categories = categories;
        this.warehouses = warehouses;
        this.units = units;
        this.years = years;
        this.reportConfigs = reportTypes;

        this.setupDataProviders();
        this.isLoading = false;
      },
      error: error => {
        console.error('Failed to load initial data:', error);
        this.isLoading = false;
      },
    });
  }

  private setupDataProviders(): void {
    this.dataProviders = {
      // Branch data provider
      branchId: () => this.branches,

      // Year data provider (filtered by selected branch)
      yearId: () => {
        const selectedBranchId = this.getSelectedBranchId();
        if (!selectedBranchId) return [];

        const branch = this.years.find(y => y.branchId === selectedBranchId);
        return branch ? branch.years : [];
      },

      // Category data provider
      categoryIds: () => this.categories,

      // Warehouse data provider (filtered by selected branch)
      warehouseIds: () => {
        const selectedBranchId = this.getSelectedBranchId();
        if (!selectedBranchId) return this.warehouses;

        return this.warehouses.filter(w => w.branchId === selectedBranchId);
      },

      // Unit data provider
      unitIds: () => this.units,

      // Item search data provider
      searchString: () => this.items,
    };
  }

  private getSelectedBranchId(): number | null {
    // This would be called from the dynamic report component
    // You might need to implement a way to get the current form values
    // For now, return null as a placeholder
    return null;
  }

  // Method to load items based on branch and year selection
  loadItems(branchId: number, yearId: number): void {
    this.productService.getProductByBranchesAndYearId(branchId, yearId).subscribe({
      next: items => {
        this.items = items;
      },
      error: error => {
        console.error('Failed to load items:', error);
      },
    });
  }
}
