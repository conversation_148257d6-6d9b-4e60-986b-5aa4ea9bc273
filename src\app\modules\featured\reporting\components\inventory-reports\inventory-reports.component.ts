import { Component, OnInit } from '@angular/core';
import { ReportConfig } from '../dynamic-report/dynamic-report.component';

@Component({
  selector: 'app-inventory-reports',
  templateUrl: './inventory-reports.component.html',
  styleUrls: ['./inventory-reports.component.scss'],
})
export class InventoryReportsComponent implements OnInit {
  reportConfigs: ReportConfig[] = [];
  dataProviders: { [key: string]: () => any[] } = {};

  // Hardcoded data for testing
  private branches: any[] = [
    { branchId: 1, name: 'Main Branch', display: 'Main Branch' },
    { branchId: 2, name: 'Secondary Branch', display: 'Secondary Branch' },
  ];

  private categories: any[] = [
    { categoryId: 1, name: 'Electronics' },
    { categoryId: 2, name: 'Clothing' },
    { categoryId: 3, name: 'Food & Beverages' },
  ];

  private warehouses: any[] = [
    { warehouseId: 1, name: 'Main Warehouse', branchId: 1 },
    { warehouseId: 2, name: 'Secondary Warehouse', branchId: 1 },
    { warehouseId: 3, name: 'Branch 2 Warehouse', branchId: 2 },
  ];

  private units: any[] = [
    { unitId: 1, name: 'Pieces' },
    { unitId: 2, name: 'Kilograms' },
    { unitId: 3, name: 'Liters' },
  ];

  private years: any[] = [
    { id: 1, year: '2024', branchId: 1 },
    { id: 2, year: '2023', branchId: 1 },
    { id: 3, year: '2024', branchId: 2 },
    { id: 4, year: '2023', branchId: 2 },
  ];

  private items: any[] = [
    { itemId: 1, itemCode: 'ITEM001', nameEnglish: 'Sample Item 1', nameArabic: 'عنصر عينة 1' },
    { itemId: 2, itemCode: 'ITEM002', nameEnglish: 'Sample Item 2', nameArabic: 'عنصر عينة 2' },
  ];

  isLoading = false; // Set to false since we're using hardcoded data

  ngOnInit(): void {
    this.loadHardcodedData();
  }

  private loadHardcodedData(): void {
    // Hardcoded JSON report configurations from your example
    this.reportConfigs = [
      {
        id: 1,
        type: 1,
        name: 'Pricing',
        nameArabic: null,
        authority: 'ReportsManagement.Inventory.PriceReports',
        languageCode: 'AR',
        searchConfigs: [
          {
            field: 'branch',
            type: 'dropdown',
            mandatory: true,
            placeholder: ' ',
            position: 1,
            isAdvanced: false,
            fieldLabel: 'branch',
            backendParam: 'branchId',
            idField: 'branchId',
          },
          {
            field: 'year',
            type: 'dropdown',
            mandatory: true,
            placeholder: ' ',
            position: 2,
            isAdvanced: false,
            fieldLabel: 'year',
            backendParam: 'yearId',
            idField: 'id',
          },
          {
            field: 'itemCode',
            type: 'input',
            mandatory: false,
            placeholder: 'Search By ItemCode/Name',
            position: 3,
            isAdvanced: false,
            fieldLabel: 'item',
            backendParam: 'searchString',
            idField: 'itemId',
          },
          {
            field: 'categories',
            type: 'multiSelectDropdown',
            mandatory: false,
            placeholder: ' ',
            position: 4,
            isAdvanced: true,
            fieldLabel: 'categories',
            backendParam: 'categoryIds',
            idField: 'categoryId',
          },
          {
            field: 'units',
            type: 'multiSelectDropdown',
            mandatory: false,
            placeholder: ' ',
            position: 5,
            isAdvanced: true,
            fieldLabel: 'units',
            backendParam: 'unitIds',
            idField: 'unitId',
          },
        ],
        endPoint: '/pages/price-list',
        jasperEndPoint: '/dynamic/price-list',
      },
      {
        id: 2,
        type: 1,
        name: 'Stock Value',
        nameArabic: null,
        authority: 'ReportsManagement.Inventory.StockValueReports',
        languageCode: 'AR',
        searchConfigs: [
          {
            field: 'branch',
            type: 'dropdown',
            mandatory: true,
            placeholder: ' ',
            position: 1,
            isAdvanced: false,
            fieldLabel: 'branch',
            backendParam: 'branchId',
            idField: 'branchId',
          },
          {
            field: 'year',
            type: 'dropdown',
            mandatory: true,
            placeholder: ' ',
            position: 2,
            isAdvanced: false,
            fieldLabel: 'year',
            backendParam: 'yearId',
            idField: 'id',
          },
          {
            field: 'warehouse',
            type: 'multiSelectDropdown',
            mandatory: true,
            placeholder: ' ',
            position: 5,
            isAdvanced: false,
            fieldLabel: 'warehouse',
            backendParam: 'warehouseIds',
            idField: 'warehouseId',
          },
          {
            field: 'categories',
            type: 'multiSelectDropdown',
            mandatory: false,
            placeholder: ' ',
            position: 6,
            isAdvanced: true,
            fieldLabel: 'categories',
            backendParam: 'categoryIds',
            idField: 'categoryId',
          },
        ],
        endPoint: '/pages/stock-value',
        jasperEndPoint: '/dynamic/stock-value',
      },
      {
        id: 4,
        type: 1,
        name: 'Item Movement',
        nameArabic: null,
        authority: 'ReportsManagement.Inventory.StockValueReports',
        languageCode: 'AR',
        searchConfigs: [
          {
            field: 'branch',
            type: 'dropdown',
            mandatory: true,
            placeholder: ' ',
            position: 1,
            isAdvanced: false,
            fieldLabel: 'branch',
            backendParam: 'branchId',
            idField: 'branchId',
          },
          {
            field: 'year',
            type: 'dropdown',
            mandatory: true,
            placeholder: ' ',
            position: 2,
            isAdvanced: false,
            fieldLabel: 'year',
            backendParam: 'yearId',
            idField: 'id',
          },
          {
            field: 'date',
            type: 'dateRange',
            mandatory: false,
            placeholder: ' ',
            position: 4,
            isAdvanced: true,
            fieldLabel: 'dateRange',
            backendParam: 'date',
            idField: 'id',
          },
        ],
        endPoint: '/pages/movement',
        jasperEndPoint: '/dynamic/movement',
      },
    ];

    this.setupDataProviders();
  }

  private setupDataProviders(): void {
    this.dataProviders = {
      // Branch data provider
      branchId: () => this.branches,

      // Year data provider (filtered by selected branch)
      yearId: () => {
        const selectedBranchId = this.getSelectedBranchId();
        if (!selectedBranchId) return [];

        const branch = this.years.find(y => y.branchId === selectedBranchId);
        return branch ? branch.years : [];
      },

      // Category data provider
      categoryIds: () => this.categories,

      // Warehouse data provider (filtered by selected branch)
      warehouseIds: () => {
        const selectedBranchId = this.getSelectedBranchId();
        if (!selectedBranchId) return this.warehouses;

        return this.warehouses.filter(w => w.branchId === selectedBranchId);
      },

      // Unit data provider
      unitIds: () => this.units,

      // Item search data provider
      searchString: () => this.items,
    };
  }

  private getSelectedBranchId(): number | null {
    // This would be called from the dynamic report component
    // You might need to implement a way to get the current form values
    // For now, return null as a placeholder
    return null;
  }
}
