// Angular Material RTL Positioning Fixes
// Only essential positioning fixes for overlay components

// RTL positioning fixes
[dir='rtl'] {
  .mat-mdc-menu-panel {
    transform-origin: top right !important;
  }

  .mat-mdc-select-panel {
    transform-origin: top right !important;
  }

  .mat-mdc-autocomplete-panel {
    transform-origin: top right !important;
  }
}

// LTR positioning fixes
[dir='ltr'] {
  .mat-mdc-menu-panel {
    transform-origin: top left !important;
  }

  .mat-mdc-select-panel {
    transform-origin: top left !important;
  }

  .mat-mdc-autocomplete-panel {
    transform-origin: top left !important;
  }
}
