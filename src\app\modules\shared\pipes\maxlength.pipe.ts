import { Pipe, PipeTransform } from '@angular/core';
import { AbstractControl } from '@angular/forms';

@Pipe({
  name: 'maxlength',
})
export class MaxlengthPipe implements PipeTransform {
  transform(control: AbstractControl, maxLength: number): unknown {
    if (control.value && control.value.length > maxLength) {
      control.setValue(control.value.slice(0, maxLength));
    }
    return control;
  }
}
