import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionGuard } from '../../core/core/guards/permission.guard';
import { UcvoucherCreateComponent } from './financedashboard/components/ucvoucher-create/ucvoucher-create.component';
import { UcvoucherComponent } from './financedashboard/components/ucvoucher/ucvoucher.component';
import { FinancedashboardComponent } from './financedashboard/financedashboard.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'dashboard',
    pathMatch: 'full',
  },
  {
    path: 'dashboard',
    component: FinancedashboardComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'ucvoucher.listings',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['PaymentsManagement', 'AllPermissions'],
    },
  },
  {
    path: 'ucvoucher',
    component: UcvoucherComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'ucvoucher.listings',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Ucvoucher.View', 'AllPermissions'],
    },
  },
  {
    path: 'ucvoucher/view/:id',
    component: UcvoucherCreateComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'ucvoucher.listings',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Ucvoucher.View', 'AllPermissions'],
      mode: 'view',
    },
  },
  {
    path: 'ucvoucher/receipt',
    component: UcvoucherCreateComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'ucvoucher.newReceiptVoucher',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Ucvoucher.Create', 'AllPermissions'],
      process: 'receipt',
    },
  },
  {
    path: 'ucvoucher/payment',
    component: UcvoucherCreateComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'ucvoucher.newPaymentVoucher',
      urls: [{ title: 'dashBoardModules.tradeMgt', url: '/trading/dashboard' }],
      allowedPermissions: ['Ucvoucher.Create', 'AllPermissions'],
      process: 'payment',
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class FinanceRoutingModule {}
