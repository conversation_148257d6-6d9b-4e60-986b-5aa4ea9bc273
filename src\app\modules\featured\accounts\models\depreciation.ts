import { AccountBasic } from './account';

export interface Depreciation {
  cumulativeDepreciationAccount: AccountBasic;
  assetAccount: AccountBasic;
  depreciationExpenseAccount: AccountBasic;
  // cumulativeDepreciationAccountNumber: number;
  // assetAccountNumber:                  number;
  // depreciationExpenseAccountNumber:   number;
  depreciationPct: number;
  purchaseDate: Date;
  supplierName: string;
  purchaseAmount: number;
  salvationValue: number;
  assetLocation: string;
  notes: string;
  depreciationMethod: string;
  lastDepreciationDate: Date;
  depreciatedValue: number;
  costAccountNumber: null;
}
