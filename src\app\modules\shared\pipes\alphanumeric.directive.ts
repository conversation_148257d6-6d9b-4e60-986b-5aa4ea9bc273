import { Directive, ElementRef, HostListener, Pipe, PipeTransform, Renderer2 } from '@angular/core';

@Directive({
  selector: '[alphanumeric]',
})
export class Alphanumeric {
  private regex = new RegExp(/[^a-zA-Z0-9]/g); // Regex for non-alphanumeric characters

  constructor(private el: ElementRef, private renderer: Renderer2) {}

  @HostListener('input', ['$event.target.value'])
  onInput(value: string): void {
    this.cleanValue(value);
  }

  @HostListener('paste', ['$event'])
  onPaste(event: ClipboardEvent): void {
    event.preventDefault();
    const pastedText = (event.clipboardData || (window as any).clipboardData).getData('text');
    const cleanText = pastedText.replace(this.regex, '');
    const inputElement = this.el.nativeElement as HTMLInputElement;
    const start = inputElement.selectionStart;
    const end = inputElement.selectionEnd;
    inputElement.value =
      inputElement.value.slice(0, start) + cleanText + inputElement.value.slice(end);
    this.renderer.setProperty(inputElement, 'value', inputElement.value);
    inputElement.setSelectionRange(start + cleanText.length, start + cleanText.length);
  }

  private cleanValue(value: string): void {
    if (value) {
      const cleanValue = value.replace(this.regex, ''); // Remove non-alphanumeric characters
      this.renderer.setProperty(this.el.nativeElement, 'value', cleanValue);
    }
  }
}
