import { Injectable } from '@angular/core';
import { MatPaginatorIntl } from '@angular/material/paginator';
import { MultilingualService } from '../../modules/core/core/services/multilingual.service';

@Injectable()
export class CustomMatPaginatorIntl extends MatPaginatorIntl {
  constructor(private multilingualService: MultilingualService) {
    super();

    // Subscribe to language changes from the MultilingualService
    this.multilingualService.languageChanged$.subscribe(() => {
      this.getAndInitTranslations();
    });

    this.getAndInitTranslations();
  }

  getAndInitTranslations() {
    this.multilingualService
      .translate([
        'common.pagination.itemsPerPage',
        'common.pagination.nextPage',
        'common.pagination.previousPage',
      ])
      .subscribe(translation => {
        this.itemsPerPageLabel = translation['common.pagination.itemsPerPage'];
        this.nextPageLabel = translation['common.pagination.nextPage'];
        this.previousPageLabel = translation['common.pagination.previousPage'];
        this.changes.next();
      });
  }

  getRangeLabel = (page: number, pageSize: number, length: number) => {
    if (length === 0 || pageSize === 0) {
      return `0 / ${length}`;
    }
    length = Math.max(length, 0);
    const startIndex = page * pageSize;
    const endIndex =
      startIndex < length ? Math.min(startIndex + pageSize, length) : startIndex + pageSize;
    return `${startIndex + 1} - ${endIndex} / ${length}`;
  };
}
