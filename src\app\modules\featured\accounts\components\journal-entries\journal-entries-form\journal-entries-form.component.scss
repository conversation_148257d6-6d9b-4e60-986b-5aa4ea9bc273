.mdc-data-table__cell,
.mdc-data-table__header-cell {
  padding: 0 1px 0 0px;
}

.mat-mdc-header-cell {
  text-align: center !important;
}

.mat-mdc-cell {
  text-align: center;
  justify-content: center;
}

.mat-column-action {
  max-width: 100px;
}

.center-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 100%;
  margin-top: 20px;
  background-color: rgb(115, 152, 228);
}

.center-container-1 {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 100%;
  margin-top: 00px;
  background-color: rgb(115, 152, 228);
}
.single-line-input {
  white-space: nowrap;
  overflow-x: auto;
  text-overflow: ellipsis;
  display: block;
  direction: rtl; /* Ensure right-to-left direction for Arabic text */
}
