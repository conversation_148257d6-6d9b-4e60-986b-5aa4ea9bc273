import { Component, OnInit } from '@angular/core';
import {
  AbstractControl,
  UntypedFormArray,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { Account } from '../../../models/account';
import { AccountParams } from '../../../models/accountParams';
import { Journal, TransactionLine } from '../../../models/journal';
import { ChartOfAccountsService } from '../../../services/chart-of-accounts.service';
import { JournalEntriesService } from '../../../services/journal-entries.service';
import { ConfirmDialogComponent } from 'src/app/modules/shared/components/confirm-dialog/confirm-dialog.component';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { CostCentresService } from '../../../services/cost-centres.service';
import { CostCentre } from '../../../models/costCentre';
import { LocalStorageService } from 'src/app/modules/core/core/services/local-storage.service';
import { AccountAutoSearchComponent } from '../../accountAutoSearch/account-auto-search.component';
import { FormControl } from '@angular/forms';
import { journalEntryCreationTypes, journalEntryType } from 'src/app/core/configs/dropDownConfig';
import { ActionType } from 'src/app/core/enums/actionType';
import { CommonService } from 'src/app/core/api/common.service';
import { MultilingualService } from 'src/app/modules/core/core/services/multilingual.service';

@Component({
  selector: 'app-journal-entries-form',
  templateUrl: './journal-entries-form.component.html',
  styleUrls: ['./journal-entries-form.component.scss'],
  providers: [
    AccountAutoSearchComponent, // added class in the providers
  ],
})
export class JournalEntriesFormComponent implements OnInit {
  loading = true;
  isLoading = true;
  isJournalSaved = false;
  accounts: Account[];
  displayRemoveIcon = false;
  formTitle: string;
  journalId: number;
  journal: Journal;

  submitted = false;
  dataSource: MatTableDataSource<any>;

  transactionLineRows: UntypedFormArray;
  journalEntriesForm: UntypedFormGroup;

  isEditMode: boolean;
  isViewMode: boolean;
  isCreateMode: boolean;

  totalCreditAmount = 0;
  totalDebitAmount = 0;

  journalTypes = journalEntryType;
  journalCreationTypes = journalEntryCreationTypes;

  columnsToDisplay: string[] = this.translateService.updateDisplayedColumns1([
    'action',
    'accountId',
    'debitAmount',
    'creditAmount',
    'costCentreNo',
    'description',
    'description2',
  ]);

  editedJournalId: any;
  direction: any;
  accountList: any;
  editedEntryId: any;
  costCentreList: any;
  mode: ActionType;
  myMap = new Map<number, string>();
  isDisabled: any;

  constructor(
    private authService: AuthService,
    private toastr: ToastrService,
    private route: ActivatedRoute,
    private router: Router,
    private journalEntriesService: JournalEntriesService,
    private formBuilder: UntypedFormBuilder,
    private chartOfAccountsService: ChartOfAccountsService,
    private dialog: MatDialog,
    private costCentreService: CostCentresService,
    private localStorage: LocalStorageService,
    private accountSearchComponent: AccountAutoSearchComponent,
    private commonService: CommonService,
    private translateService: MultilingualService
  ) {
    console.log(new FormControl(new Date()));
    this.journalEntriesForm = this.formBuilder.group({
      journalNumber: [],
      journalType: ['NORMAL', Validators.required],
      journalDate: new FormControl(new Date()),
      description: [],
      transactionLineRequests: this.formBuilder.array([]),
    });
    this.dataSource = new MatTableDataSource<any>();
  }

  get IsViewMode() {
    return this.mode === ActionType.view;
  }

  ngOnInit(): void {
    console.log('ngOnInit method');
    this.getAllCostCentre();
    this.totalCreditAmount = 0;
    this.totalDebitAmount = 0;
    this.loading = true;
    this.mode = this.route.snapshot.data['mode'];
    this.formTitle = this.route.snapshot.data['title'];
    this.route.params.subscribe(params => {
      const id = params['id'];
      console.log('Id', id);
      if (id) {
        if (this.router.url.includes('edit')) {
          this.isEditMode = true;
        } else if (this.router.url.includes('view')) {
          this.isViewMode = true;
        }
        this.journalId = id;
        this.getJournalById();
        //  console.log(this.journal);
        console.log('final Form', this.journalEntriesForm.value);
      } else {
        this.journalId = null;
        this.isCreateMode = true;
        this.isEditMode = false;
        this.isViewMode = false;
        // this.getAllDropDownData();
        this.initializeForm(null);
        //  this.addEntryRow();
        this.loading = false;
      }
    });

    setInterval(() => {
      this.updateData();
    }, 1000);
  }
  updateData() {
    this.totalCreditAmount = 0;
    this.totalDebitAmount = 0;
    // Your logic for updating data here
    this.dataSource.data.forEach(item => {
      // Access the properties of each item in the data source
      this.totalCreditAmount += parseFloat(item.value.creditAmount);
      this.totalDebitAmount += parseFloat(item.value.debitAmount);
    });
  }
  ngOnChanges() {
    // Handle changes in the data array here
    console.log('Data changed:');
    // You can perform any custom logic based on the changes in the data array
  }

  ngAfterViewInit() {
    // Detect changes in the data source data
    setTimeout(() => {
      this.dataSource.connect().subscribe(data => {
        // Handle changes in the data here
        console.log('Data changed:**********************************', data);
        // You can perform any custom logic based on the changes in the data
      });
    }, 0);
  }

  getJournalById(): void {
    this.journalEntriesService.getJournalById(this.journalId).subscribe((response: Journal) => {
      this.journal = response;
      console.log(this.journal);
      this.initializeForm(this.journal);
    });
  }

  getAllCostCentre(): void {
    this.costCentreService.getAllCostCentres().subscribe((response: CostCentre[]) => {
      this.costCentreList = response;
      console.log(this.costCentreList);
    });
  }

  initializeForm(data: Journal) {
    console.log('Form is being initialised');
    console.log('data', data);
    this.getAllDetailedAccounts();
    this.transactionLineRows = this.formBuilder.array([]);
    console.log('transactionRows---->>> ', this.transactionLineRows),
      (this.journalEntriesForm = this.formBuilder.group({
        journalNumber: [''],
        journalType: ['NORMAL', Validators.compose([Validators.required])],
        journalCreationType: ['MANUAL'],
        isPosted: [false],
        journalDate: new FormControl(new Date()),
        description: [''],
        transactionLineRequests: this.transactionLineRows,
      }));
    if (data) {
      this.journalEntriesForm.patchValue({
        journalNumber: data.journalNumber,
        journalType: data.journalType,
        journalDate: data.journalDate,
        description: data.description,
      });
      console.log('Journal Entries Form', this.journalEntriesForm.value);
      this.journal?.transactionLineRespons.forEach(data => this.addTransactionLineRow(data));
      if (this.isViewMode) {
        this.journalEntriesForm.disable();
      }
    } else {
      this.addTransactionLineRow();
      this.journalEntriesForm.controls['journalNumber'].disable();
    }
  }

  getAllJournalEntriesData() {
    throw new Error('Method not implemented.');
  }
  getAllDropDownData() {
    console.log(this.accounts);
    const accountParams = new AccountParams();
    accountParams.accountType = 'DETAILED';
    const accounts = this.chartOfAccountsService
      .getAllChartOfAccounts(accountParams)
      .subscribe((response: Account[]) => {
        this.accounts = response;
        this.initializeForm(null);
        this.addTransactionLineRow();
        this.updateActionButtonStatus();
        this.loading = false;
      });
  }
  getAccountIdPlaceholder(control: any): string {
    if (
      this.localStorage.getItem('locale') === 'AR' &&
      control.value.accountBasic !== null &&
      control.value.accountBasic.nameArabic !== null
    ) {
      return control.value.accountBasic.nameArabic + '-' + control.value.accountBasic.accountNumber;
    } else if (
      this.localStorage.getItem('locale') === 'EN' &&
      control.value.accountBasic !== null &&
      control.value.accountBasic.nameEnglish !== null
    ) {
      return (
        control.value.accountBasic.nameEnglish + '-' + control.value.accountBasic.accountNumber
      );
    } else if (
      control.value.accountBasic !== null &&
      control.value.accountBasic.nameEnglish !== null
    ) {
      return (
        control.value.accountBasic.nameEnglish + '-' + control.value.accountBasic.accountNumber
      );
    } else if (
      control.value.accountBasic !== null &&
      control.value.accountBasic.nameArabic !== null
    ) {
      return control.value.accountBasic.nameArabic + '-' + control.value.accountBasic.accountNumber;
    } else {
      return '';
    }
  }
  addTransactionLineRow(data?) {
    console.log('Row is to be added!');
    console.log('Data:', data);
    const row = this.formBuilder.group({
      accountBasic: [data?.accountBasic ?? null],
      debitAmount: [(data && data?.debitAmount) || 0, Validators.required],
      creditAmount: [(data && data?.creditAmount) || 0, Validators.required],
      description: [data && data?.description],
      description2: [data && data?.description2],
      costCentreId: [data && data?.costCentreId],
    });
    console.log('Row:', row.value);
    this.transactionLineRows.push(row);
    console.log('JournalEntry:');
    console.log(this.journalEntriesForm.value);
    console.log('EntryRows:');
    console.log(this.journalEntriesForm.controls['transactionLineRequests'].value);

    console.log('DataSource:');
    console.log(this.dataSource);
    this.updateActionButtonStatus();
  }

  updateActionButtonStatus() {
    this.dataSource = new MatTableDataSource(
      (this.journalEntriesForm.get('transactionLineRequests') as UntypedFormArray).controls
    );

    if ((this.journalEntriesForm.get('transactionLineRequests') as UntypedFormArray).length === 1) {
      this.displayRemoveIcon = false;
    } else {
      this.displayRemoveIcon = true;
    }
  }

  onSubmit(event: Event) {
    if (this.totalCreditAmount !== this.totalDebitAmount) {
      // Open the popup dialog
      this.commonService.playErrorSound();
      this.isJournalSaved = false;
      event.preventDefault();
      const dialogConfig = new MatDialogConfig();
      dialogConfig.panelClass = ['green_theme'];
      dialogConfig.minWidth = '600px';
      dialogConfig.hasBackdrop = true;
      dialogConfig.disableClose = false;
      dialogConfig.autoFocus = false;
      dialogConfig.direction = this.direction.value;
      dialogConfig.data = `Total credit amount: ${this.totalCreditAmount}  is not equal to total debit amount:  ${this.totalDebitAmount}!`;
      const dialogRef = this.dialog.open(ConfirmDialogComponent, dialogConfig);
      // You can subscribe to dialog events if needed, for example to handle when the dialog is closed
      dialogRef.afterClosed().subscribe(result => {
        console.log('Dialog closed', result);
      });
    } else {
      event.preventDefault();
      this.journalEntriesForm.markAllAsTouched();
      if (this.journalEntriesForm && this.journalEntriesForm?.valid) {
        if (this.isCreateMode) {
          this.journalEntriesService
            .createJournalEntries(this.journalEntriesForm.value)
            .subscribe(res => {
              //this.toastr.success('Journal Added Successfully');
              this.commonService.playSuccessSound();
              this.isJournalSaved = true;
              this.journalId = res.journalId;
            });
        } else if (this.isEditMode) {
          this.journalEntriesService
            .updateJournalEntries(this.journalId, this.journalEntriesForm.value)
            .subscribe(() => {
              //this.toastr.success('Journal Updated Successfully');
              this.commonService.playSuccessSound();
              this.router.navigate(['../../'], { relativeTo: this.route });
            });
        }
      } else {
        this.commonService.scrollToError();
      }
    }
  }
  onPost(event: Event) {
    event.preventDefault();
    this.journalEntriesForm.markAllAsTouched();
    if (this.journalEntriesForm && this.journalEntriesForm?.valid) {
      if (this.isEditMode || this.isJournalSaved) {
        this.journalEntriesService.postJournalEntries(this.journalId).subscribe(() => {
          //this.toastr.success('Journal Updated Successfully');
          this.commonService.playSuccessSound();
          this.router.navigate(['../../'], { relativeTo: this.route });
        });
      }
    } else {
      this.commonService.scrollToError();
    }
  }
  deleteEntry(element: any, index: number) {
    console.log(index);
    const myFormArray = this.journalEntriesForm.get('transactionLineRequests') as UntypedFormArray;
    myFormArray.removeAt(index);
    this.updateActionButtonStatus();
  }

  getAllDetailedAccounts(): void {
    const accountParams: AccountParams = new AccountParams();
    accountParams.accountType = 'DETAILED';
    this.chartOfAccountsService.getAllChartOfAccounts(accountParams).subscribe(result => {
      this.accounts = result.accounts;
      console.log('Accounts:', this.accounts);
      result.accounts.forEach(account => {
        this.myMap.set(account.accountId, account.nameEnglish);
      });
    });
  }

  onInput(input: HTMLInputElement): void {
    setTimeout(() => {
      input.scrollLeft = input.scrollWidth;
    }, 0);
  }
}
