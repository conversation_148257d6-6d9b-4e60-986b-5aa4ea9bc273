<div class="main-container">
  <div class="row no-gutters" *ngIf="!selectedReportType">
    <div class="col-sm-12 col-md-4 col-lg-4 p-2 custom-card" *ngFor="let report of reportTypeList">
      <ng-container>
        <mat-card
          class="justify-content-between align-items-center cursor-pointer bg-primary text-white"
          (click)="selectReportType(report)">
          <mat-card-title class="align-items-center text-white">
            <p>{{ 'report.' + transformReportName(report.name) | translate }}</p>
          </mat-card-title>
        </mat-card>
      </ng-container>
    </div>
  </div>
  <div class="edit-report-type" *ngIf="selectedReportType">
    <div class="report-type-container">
      <div class="report-type">
        <span>{{ 'report.' + transformReportName(selectedReportType) | translate }}</span>
      </div>
      <div class="report-type-buttons">
        <button (click)="resetReportType()" mat-icon-button>
          <mat-icon>edit</mat-icon>
        </button>
        <button *ngIf="reportData.length" (click)="downloadPdf()" mat-icon-button>
          <mat-icon>download</mat-icon>
        </button>
      </div>
    </div>

    <div class="filter-data">
      <mat-form-field *ngIf="reportData.length" appearance="outline">
        <mat-label>Filter Data</mat-label>
        <mat-select
          [(ngModel)]="displayedColumns"
          (selectionChange)="updateDisplayedColumns(displayedColumns)"
          multiple>
          <mat-option *ngFor="let column of allColumns" [value]="column">
            {{ column }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
  </div>

  <mat-card class="filter-card" appearance="outlined">
    <app-filter-form
      [formName]="formName"
      [reportConfigs]="reportTypeList"
      [searchConfigs]="searchConfigs"
      [filtersForm]="filtersForm"
      [isFullScreen]="false"
      [isAdvancedSearchVisible]="isAdvancedSearchVisible"
      [getListForField]="getListForField.bind(this)"
      [getReportData]="getReportData.bind(this)"
      [onSearchInput]="onSearchInput.bind(this)"
      [onItemCodeSelected]="onItemCodeSelected.bind(this)"
      (reportTypeChanged)="onReportTypeChanged($event)">
    </app-filter-form>
  </mat-card>
  <mat-card class="result-card" *ngIf="selectedReportType" appearance="outlined">
    <div class="no-data" *ngIf="!reportData.length">
      <p>No Data Found</p>
    </div>

    <div class="table-container" *ngIf="reportData.length">
      <table class="mat-elevation-z8 sticky-table" [dataSource]="dataSource" mat-table matSort>
        <ng-container *ngFor="let column of displayedColumns" [matColumnDef]="column">
          <th *matHeaderCellDef mat-header-cell mat-sort-header sticky>
            {{ 'inventoryReport.' + column | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>{{ element[column] }}</td>
        </ng-container>

        <tr class="sticky-header" *matHeaderRowDef="displayedColumns" mat-header-row></tr>
        <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
      </table>
    </div>
    <div class="pagination-container" *ngIf="reportData.length">
      <mat-paginator
        [length]="totalItems"
        [pageSize]="pageSize"
        [pageIndex]="currentPage"
        [pageSizeOptions]="[5, 10, 25, 100]"
        (page)="onPageChange($event)">
      </mat-paginator>
    </div>
  </mat-card>
</div>
