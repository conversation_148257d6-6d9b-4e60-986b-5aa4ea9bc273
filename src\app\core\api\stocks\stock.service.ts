import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { ISaleDetails } from '../../interfaces/sales';
import {
  IStock,
  PaginatedTransferListResponse,
  StockRead,
  TransferType,
} from 'src/app/modules/featured/stock/models/stocks';

@Injectable({
  providedIn: 'root',
})
export class StockService {
  baseUrl = environment.apiUrl + 'stock/transfer';

  constructor(private http: HttpClient) {}

  createStock(stock: IStock) {
    return this.http.post(this.baseUrl, stock);
  }

  getAll(transferType: TransferType) {
    const params: HttpParams = new HttpParams().set('transferType', transferType);
    return this.http.get<PaginatedTransferListResponse>(this.baseUrl + `/pages`, {
      params: params,
    });
  }

  getById(transferId: string, transferType: TransferType) {
    const params: HttpParams = new HttpParams().set('transferType', transferType);
    return this.http.get<StockRead>(this.baseUrl + `/${transferId}`, {
      params: params,
    });
  }
}
