import { Directionality } from '@angular/cdk/bidi';
import { Component, OnInit, ViewChild } from '@angular/core';
import {
  UntypedFormArray,
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ActivatedRoute, Router } from '@angular/router';
import { forkJoin } from 'rxjs';
import { CommonService } from 'src/app/core/api/common.service';
import { StoreService } from 'src/app/core/api/store.service';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { StoreParams } from 'src/app/modules/featured/settings/models/storeParams';
import { ConfirmDialogComponent } from 'src/app/modules/shared/components/confirm-dialog/confirm-dialog.component';
import { SearchboxComponent } from 'src/app/modules/shared/components/searchbox/searchbox.component';
import { CategoryService } from '../../../../../core/api/category.service';
import { ProductService } from '../../../../../core/api/product.service';
import { CategoryParams } from '../../../catalog/models/categoryParams';
import { ProductAdjustmentsParams } from '../../../catalog/models/productAdjustmentParams';
import { Inventory } from '../../models/unitPrice';

@Component({
  selector: 'app-stock-adjustment',
  templateUrl: './stock-adjustment.component.html',
  styleUrls: ['./stock-adjustment.component.scss'],
})
export class StockAdjustmentComponent implements OnInit {
  @ViewChild('searchBoxForm') searchBoxForm: SearchboxComponent;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  productForm: UntypedFormGroup = Object.create(null);
  imageChangedEvent: any = '';
  croppedImage: any = '';
  showCropper = false;
  defaultImage = 'assets/images/company-placeholder-image.png';
  loading = true;
  title: string;
  companyId: string;
  companyError = false;
  displayRemoveIcon = false;
  loaded = false;
  tableData = new MatTableDataSource<Inventory>();
  adjustmentRows: UntypedFormArray = this.formBuilder.array([]);
  adjustmentForm: UntypedFormGroup = this.formBuilder.group({
    InventoryOpeningRequest: this.adjustmentRows,
  });
  warehouse = new UntypedFormControl([]);

  warehouseList: any;
  categoryList: any;
  units: any;
  products: any;
  submitted = false;
  postSaved = false;

  allComplete = false;

  displayedColumns: string[];

  editedUnitId = '';
  formTitle: string;
  isEditMode = false;
  parentCategories: any;
  subCategories: any;
  filterForm: UntypedFormGroup;
  isViewMode = false;
  isAdvancedSearchEnabled = true;
  constructor(
    private router: Router,
    private dialog: MatDialog,
    private route: ActivatedRoute,
    private storeService: StoreService,
    private categoryService: CategoryService,
    private formBuilder: UntypedFormBuilder,
    private direction: Directionality,
    private authService: AuthService,
    private fb: UntypedFormBuilder,
    private productService: ProductService,
    private commonService: CommonService
  ) {}

  ngOnInit(): void {
    this.loading = true;

    this.route.params.subscribe(params => {
      const id = params['id'];
      if (id) {
        this.editedUnitId = id;
        this.formTitle = 'Edit Unit of Measurements';
        this.router.url.includes('view')
          ? ((this.isViewMode = true), (this.formTitle = 'Warehouse Details'))
          : null;
        this.router.url.includes('edit')
          ? ((this.isEditMode = true), (this.formTitle = 'Edit Warehouse Details'))
          : null;
        // this.isEditMode = true;
        this.getDocumentById();
        this.displayedColumns = [
          'itemCode',
          'itemNameArabic',
          'itemNameEnglish',
          'unitName',
          'currentQty',
          'actualQty',
          'qtyDifference',
          'status',
          'remark',
        ];
      } else {
        this.editedUnitId = null;
        // this.isEditMode = false;
        this.formTitle = 'Register Unit of Measurements';
        this.getAllDropDownData();
        this.initializeFilterForm();
        // this.initializeForm(null);
        this.displayedColumns = [
          'action',
          'itemCode',
          'itemNameArabic',
          'itemNameEnglish',
          'unitName',
          'currentQty',
        ];
        this.loading = false;
      }
    });
  }

  ngAfterViewInit(): void {
    if (this.tableData) {
      this.tableData.paginator = this.paginator;
      this.tableData.sort = this.sort;
    }
  }

  initializeFilterForm() {
    this.filterForm = this.fb.group({
      itemCode: [null],
      categoryId: [[]],
      warehouseIds: [[], Validators.required],
      searchBoxForm: [],
    });
  }

  getDocumentById() {
    this.productService.getDocumentById(this.editedUnitId).subscribe(result => {
      this.products = result;
      this.tableData = new MatTableDataSource(result);
      setTimeout(() => {
        this.tableData.paginator = this.paginator;
        this.tableData.sort = this.sort;
      });
      this.adjustmentRows.clear();
      this.products.forEach(data => this.addUnitsPriceRow(data));
    });
  }

  getParentName(parentId: number) {
    return this.parentCategories.filter(data => data.categoryId === parentId)[0]?.nameArabic;
  }

  getAllDropDownData() {
    const warehouses = this.storeService.getStores(
      new StoreParams(),
      this.authService.getCompanyID
    );
    const category = this.categoryService.getAllCategories(new CategoryParams());
    forkJoin([warehouses, category]).subscribe(results => {
      this.warehouseList = results[0];
      this.categoryList = results[1];
      this.splitCategoryandParentCategory();
      this.loading = false;
    });
  }

  splitCategoryandParentCategory() {
    this.parentCategories = this.categoryList.filter(data => data.parentCategoryId === null);
    this.subCategories = this.categoryList.filter(data => data.parentCategoryId !== null);
  }

  deleteEntry(id: number) {
    this.adjustmentRows.removeAt(id);
    this.tableData.data.splice(id, 1);
    this.tableData = this.tableData;
    setTimeout(() => {
      this.tableData.paginator = this.paginator;
      this.tableData.sort = this.sort;
    });
  }

  addUnitsPriceRow(data) {
    const row = this.formBuilder.group({
      docId: [data && data?.docId],
      actualQty: [data && data?.actualQty],
      branchId: [data && data?.branchId],
      companyId: [data && data?.companyId],
      inventoryChkDate: [data && data?.inventoryChkDate],
      inventoryChkType: [data && data?.inventoryChkType],
      isPosted: [data && data?.isPosted],
      itemLocation: [data && data?.itemLocation],
      currentQty: [data && data?.currentQty],
      itemUnitId: [data && data?.itemUnitId],
      qtyDifference: [data && data?.qtyDifference],
      remark: [data && data?.remark],
      status: [data && data?.status],
      warehouseId: [data && data?.warehouseId],
      yearId: [data && data?.yearId],
      unitName: [data && data?.unitName],
      itemCode: [data && data?.itemCode],
      itemName: [data && data?.itemName],
      itemId: [data && data?.itemId],
    });
    this.adjustmentRows.push(row);
    if (this.isViewMode) {
      this.adjustmentForm.disable();
    }
  }

  actualQuantityValueChange(index: number) {
    const value = this.getFieldValue(index, 'actualQty') - this.getFieldValue(index, 'currentQty');
    (<UntypedFormArray>this.adjustmentForm.get('InventoryOpeningRequest'))?.controls[index][
      'controls'
    ]['qtyDifference']?.setValue(value);
    if (value === 0) {
      (<UntypedFormArray>this.adjustmentForm.get('InventoryOpeningRequest'))?.controls[index][
        'controls'
      ]['status']?.setValue(2);
    }
    if (value > 0) {
      (<UntypedFormArray>this.adjustmentForm.get('InventoryOpeningRequest'))?.controls[index][
        'controls'
      ]['status']?.setValue(1);
    }
    if (value < 0) {
      (<UntypedFormArray>this.adjustmentForm.get('InventoryOpeningRequest'))?.controls[index][
        'controls'
      ]['status']?.setValue(-1);
    }
  }

  getFieldValue(i: number, field: string) {
    return this.getControls()[i].value[field];
  }

  getControls() {
    return (<UntypedFormArray>this.adjustmentForm.get('InventoryOpeningRequest')).controls;
  }

  markFormArrayTouched() {
    const formData = (this.adjustmentForm.get('InventoryOpeningRequest') as UntypedFormArray)
      .controls;
    formData.forEach(formArrayData => {
      Object.keys(formArrayData['controls']).forEach(key => {
        formArrayData.get(key).markAsTouched();
      });
    });
  }

  getFilterData(event: Event) {
    event.preventDefault();
    this.filterForm.markAllAsTouched();
    if (this.filterForm.valid && this.searchBoxForm.isValid) {
      this.loading = true;
      const params: ProductAdjustmentsParams = <ProductAdjustmentsParams>{};
      console.log(this.filterForm.controls);
      params.categoryIds = this.filterForm.controls['categoryId'].value;
      params.warehouseIds = this.filterForm.controls['warehouseIds'].value;
      params.searchString = this.searchBoxForm.searchBoxForm.controls['searchString'].value;
      params.searchType = this.searchBoxForm.searchBoxForm.controls['searchType'].value;
      this.productService.getProductsByFilter(params).subscribe(result => {
        this.products = null;
        this.products = result.inventories;
        setTimeout(() => {
          this.tableData = new MatTableDataSource(result.inventories);
          this.tableData.paginator = this.paginator;
          this.tableData.sort = this.sort;
        });
        this.adjustmentRows.clear();
        this.products.forEach(data => this.addUnitsPriceRow(data));
        this.loading = false;
        this.submitted = true;
      });
    } else {
      this.commonService.scrollToError();
    }
  }

  clearFilters(event: Event) {
    event.preventDefault();
    this.filterForm.markAsUntouched();
    this.filterForm.markAsPristine();
    this.filterForm.reset();
    this.searchBoxForm.resetSearchBox();
  }

  onSubmit(event: Event) {
    console.log('adjustmentForm', this.adjustmentForm.value['InventoryOpeningRequest']);
    this.submitted = true;
    event.preventDefault();
    this.adjustmentForm.markAllAsTouched();
    this.markFormArrayTouched();
    if (this.adjustmentForm.valid && this.getControls()?.length > 0) {
      if (this.isEditMode) {
        this.preAdjustmentSave();
      } else {
        // add logic to update
        this.productService
          .createStockAdjustment(this.adjustmentForm.value['InventoryOpeningRequest'])
          .subscribe(response => {
            //this.toastr.success('Stock Adjustment Document Created Successfully');
            this.commonService.playSuccessSound();
          });
      }
    } else {
      this.commonService.scrollToError();
    }
  }

  onPostClick(event: Event) {
    event.preventDefault();
    const dialogConfig = new MatDialogConfig();
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.minWidth = '600px';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(ConfirmDialogComponent, dialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      console.log('result', result);
      if (result) {
        this.postAdjustment();
      }
    });
  }

  postAdjustment() {
    console.log('adjustmentForm', this.adjustmentForm.value['InventoryOpeningRequest']);
    this.submitted = true;
    event.preventDefault();
    this.adjustmentForm.markAllAsTouched();
    this.markFormArrayTouched();
    if (this.adjustmentForm.valid) {
      // add logic to update
      this.productService
        .postStockAdjustment(
          this.adjustmentForm.value['InventoryOpeningRequest'],
          this.editedUnitId
        )
        .subscribe(response => {
          this.postSaved = true;
          //this.toastr.success('Stock Adjustment Posted Successfully');
          this.commonService.playSuccessSound();
        });
    } else {
      this.commonService.scrollToError();
    }
  }

  preAdjustmentSave() {
    console.log('adjustmentForm', this.adjustmentForm.value['InventoryOpeningRequest']);
    this.submitted = true;
    event.preventDefault();
    this.adjustmentForm.markAllAsTouched();
    this.markFormArrayTouched();
    if (this.adjustmentForm.valid) {
      // add logic to update
      this.productService
        .preAdjustmentsUpdate(
          this.adjustmentForm.value['InventoryOpeningRequest'],
          this.editedUnitId
        )
        .subscribe(response => {
          //this.toastr.success('Stock Adjustment Saved Successfully');
          this.commonService.playSuccessSound();
        });
    } else {
      this.commonService.scrollToError();
    }
  }

  getActualIndex(index: number) {
    if (this.paginator) {
      return index + this.paginator.pageSize * this.paginator.pageIndex;
    }
    return index;
  }
}
