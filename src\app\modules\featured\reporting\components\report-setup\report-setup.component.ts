import { Component, OnInit } from '@angular/core';

import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';

import { HttpParams } from '@angular/common/http';

import { ReportService } from '../../../settings/services/report.service';
import { fork<PERSON>oin } from 'rxjs';
import { BranchService } from '../../../settings/services/branch.service';
import { BranchParams } from '../../../settings/models/branchParams';
import { Branch } from '../../../catalog/models/branch';
import { CookieService } from 'ngx-cookie-service';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';

import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonService } from 'src/app/core/api/common.service';
import { StoreService } from 'src/app/core/api/store.service';
import { StoreParams } from '../../../settings/models/storeParams';
import { distinctUntilChanged } from 'rxjs/operators';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { TemplateRef, ViewChild } from '@angular/core';

@Component({
  selector: 'report-setup',
  templateUrl: './report-setup.component.html',
  styleUrls: ['./report-setup.component.scss'],
})
export class ReportSetupComponent implements OnInit {
  @ViewChild('previewTemplate') previewTemplate!: TemplateRef<any>;
  @ViewChild('a4ModalTemplate') a4ModalTemplate!: TemplateRef<any>;

  myForm: FormGroup;
  showAdditionalFields = false;
  private currentDialogRef: MatDialogRef<any> | null = null;
  branchList: Branch[];
  warehouseList: any;
  allWarehouses: any;
  mandatoryFields = [
    { label: 'reportSetup.branch', controlName: 'branch', placeholder: '' },
    { label: 'reportSetup.warehouse', controlName: 'warehouse', placeholder: '' },
  ];
  defaultFields = [
    {
      label: 'reportSetup.mainText1',
      controlName: 'mainText1',
      placeholder: 'اسم الشركة\nCompany Name',
    },
    {
      label: 'reportSetup.mainText2',
      controlName: 'mainText2',
      placeholder: 'وصف الشركة\nCompany Description',
    },
    {
      label: 'reportSetup.mainText3',
      controlName: 'mainText3',
      placeholder: 'معلومات إضافية\nAdditional Information',
    },
    {
      label: 'reportSetup.addrLine1',
      controlName: 'addrLine1',
      placeholder: 'العنوان الأول\nAddress Line 1',
    },
    {
      label: 'reportSetup.addrLine2',
      controlName: 'addrLine2',
      placeholder: 'العنوان الثاني\nAddress Line 2',
    },
    {
      label: 'reportSetup.addrLine3',
      controlName: 'addrLine3',
      placeholder: 'العنوان الثالث\nAddress Line 3',
    },
  ];

  additionalFields = [
    {
      label: 'reportSetup.subText1',
      controlName: 'subText1',
      placeholder: 'Company Name (EN)\nاسم الشركة',
    },
    {
      label: 'reportSetup.subText2',
      controlName: 'subText2',
      placeholder: 'Company Description (EN)\nوصف الشركة',
    },
    {
      label: 'reportSetup.subText3',
      controlName: 'subText3',
      placeholder: 'Additional Info (EN)\nمعلومات إضافية',
    },
    {
      label: 'reportSetup.subAddrLine1',
      controlName: 'subAddrLine1',
      placeholder: 'Address Line 1 (EN)\nالعنوان الأول',
    },
    {
      label: 'reportSetup.subAddrLine2',
      controlName: 'subAddrLine2',
      placeholder: 'Address Line 2 (EN)\nالعنوان الثاني',
    },
    {
      label: 'reportSetup.subAddrLine3',
      controlName: 'subAddrLine3',
      placeholder: 'Address Line 3 (EN)\nالعنوان الثالث',
    },
  ];
  requiredFields = [
    {
      label: 'reportSetup.qrText',
      controlName: 'qrText',
      placeholder: 'Enter QR code text or URL\nأدخل نص رمز الاستجابة السريعة أو الرابط',
    },
  ];

  constructor(
    private fb: FormBuilder,
    private branchService: BranchService,
    private reportService: ReportService,
    private authService: AuthService,
    private toastr: ToastrService,
    private route: ActivatedRoute,
    private router: Router,
    private commonService: CommonService,
    private cookieService: CookieService,
    private storeService: StoreService,
    private dialog: MatDialog
  ) {}
  ngOnInit(): void {
    this.myForm = this.fb.group({
      mainText1: [''],
      mainText2: [''],
      mainText3: [''],
      addrLine1: [''],
      addrLine2: [''],
      addrLine3: [''],
      subText1: [''],
      subText2: [''],
      subText3: [''],
      subAddrLine1: [''],
      subAddrLine2: [''],
      subAddrLine3: [''],
      qrText: [''],
      branchId: ['', Validators.required],
      warehouseId: ['0'],
      hasSubHeader: [true],
    });
    this.getAllDropDownData();

    const branchIdFromCookie = +this.cookieService.get('branchId');
    this.myForm.patchValue({ branchId: branchIdFromCookie });
    if (branchIdFromCookie) {
      this.getReportSetup();
    }
    this.myForm.get('branchId')?.valueChanges.subscribe(value => {
      this.warehouseList = this.allWarehouses.filter(
        item => item.branchId === value && item?.isPos
      );
      this.getReportSetup();
    });
    this.myForm
      .get('warehouseId')
      ?.valueChanges.pipe(distinctUntilChanged())
      .subscribe(value => {
        console.log('warehouse', value);
        this.getReportSetup();
      });
  }
  getAllDropDownData(): void {
    const branches = this.branchService.getAllBranchesWithYear(new BranchParams());
    const warehouses = this.storeService.getAllStores(new StoreParams());
    forkJoin([branches, warehouses]).subscribe(results => {
      this.branchList = results[0];
      this.allWarehouses = results[1];
      this.warehouseList = this.allWarehouses.filter(item => item?.isPos);
    });
  }

  getReportSetup(): void {
    let params: HttpParams = new HttpParams();
    const branchId = this.myForm.get('branchId').value;
    const warehouseId = this.myForm.get('warehouseId').value;
    params = params.append('branchId', branchId);
    params = params.append('warehouseId', warehouseId);

    this.reportService.getReportSetup(params).subscribe(result => {
      if (result) {
        this.myForm.patchValue({ ...result });
      }
    });
  }
  toggleAdditionalFields(event: any): void {
    this.showAdditionalFields = event.checked;
  }

  onSubmit(event: Event): void {
    event?.preventDefault();
    this.myForm.markAllAsTouched();

    if (this.myForm.valid) {
      console.log('Form Submitted:', this.myForm.value);
      this.reportService.createReportSetup(this.myForm.value).subscribe(result => {
        this.toastr.success('Report Setup Added Successfully');
        this.commonService.playSuccessSound();
        if (result) {
          this.myForm.patchValue({ ...result });
        } else {
          this.router.navigate(['../'], { relativeTo: this.route });
        }
      });
    } else {
      this.commonService.scrollToError();
    }
  }

  openA4Preview(): void {
    this.currentDialogRef = this.dialog.open(this.a4ModalTemplate, {
      width: '100vw',
      height: '100vh',
      maxWidth: 'none',
      maxHeight: 'none',
      panelClass: 'a4-preview-modal',
      hasBackdrop: true,
      disableClose: false,
    });
  }

  closeA4Preview(): void {
    if (this.currentDialogRef) {
      this.currentDialogRef.close();
      this.currentDialogRef = null;
    }
  }
}
