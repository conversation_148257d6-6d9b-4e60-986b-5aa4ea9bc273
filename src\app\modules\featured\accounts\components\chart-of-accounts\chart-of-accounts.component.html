<ng-container *ngIf="!isLoading">
  <mat-card appearance="outlined">
    <mat-card-content>
      <mat-tab-group #tabs [dynamicHeight]="true" mat-stretch-tabs="false" animationDuration="0ms">
        <mat-tab>
          <ng-template mat-tab-label>{{ 'caccounts.calistings' | translate }}</ng-template>
          <!-- action bar -->
          <app-create-action
            *appHasPermission="['ChartOfAccounts.Create', 'AllPermissions']"
            [label]="'caccounts.createAccount' | translate"></app-create-action>
          <!-- action bar -->
          <form class="advancedSearch" [formGroup]="filterForm" autocomplete="off">
            <div class="row no-gutters">
              <div class="p-2 col-md-5">
                <app-accounts-search-box
                  #searchBoxForm
                  formControlName="searchBoxForm"></app-accounts-search-box>
              </div>
              <div class="p-2 col-lg-2 col-md-2 col-sm-6 d-flex align-items-end">
                <div class="form-group">
                  <mat-label>{{ 'caccounts.accountGroup' | translate }}</mat-label>
                  <mat-form-field class="w-100">
                    <mat-select formControlName="accountGroup">
                      <mat-option *ngFor="let group of accountGroups" [value]="group.value">
                        {{ group.display | translate }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>
              </div>
              <div class="p-2 col-lg-2 col-md-2 col-sm-6 d-flex align-items-end">
                <div class="form-group">
                  <mat-label>{{ 'caccounts.bsnsGroup' | translate }}</mat-label>
                  <mat-form-field class="w-100">
                    <mat-select formControlName="businessGroup">
                      <mat-option *ngFor="let bGroup of businessGroups" [value]="bGroup.value">
                        {{ bGroup.display | translate }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>
              </div>
              <div class="p-2 col-lg-2 col-md-2 col-sm-6 d-flex align-items-end">
                <div class="form-group">
                  <mat-label>{{ 'caccounts.accountType' | translate }}</mat-label>
                  <mat-form-field class="w-100">
                    <mat-select formControlName="accountType">
                      <mat-option *ngFor="let actType of accountTypes" [value]="actType.value">
                        {{ actType.display | translate }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>
              </div>
            </div>
            <button (click)="getFilterData($event); (false)" mat-stroked-button color="primary">
              {{ 'searchPanel.searchString' | translate }}
            </button>
            <button class="m-l-10" (click)="clearFilters($event); (false)" mat-stroked-button>
              {{ 'searchPanel.clear' | translate }}
            </button>
          </form>
          <mat-card-title class="m-t-10">{{ 'caccounts.listAccounts' | translate }}</mat-card-title>
          <div class="table-responsive">
            <table class="w-100" [dataSource]="dataSource" mat-table matSort>
              <ng-container matColumnDef="accountNumber">
                <th *matHeaderCellDef mat-header-cell mat-sort-header>
                  {{ 'caccounts.accountNo' | translate }}
                </th>
                <td *matCellDef="let element" mat-cell>
                  {{ element.accountNumber }}
                </td>
              </ng-container>
              <ng-container matColumnDef="nameArabic">
                <th *matHeaderCellDef mat-header-cell mat-sort-header>
                  {{ 'caccounts.accountArabicName' | translate }}
                </th>
                <td *matCellDef="let element" mat-cell>
                  {{ element.nameArabic }}
                </td>
              </ng-container>
              <ng-container matColumnDef="nameEnglish">
                <th *matHeaderCellDef mat-header-cell mat-sort-header>
                  {{ 'caccounts.accountEnglishName' | translate }}
                </th>
                <td *matCellDef="let element" mat-cell>
                  {{ element.nameEnglish }}
                </td>
              </ng-container>
              <ng-container matColumnDef="accountType">
                <th *matHeaderCellDef mat-header-cell mat-sort-header>
                  {{ 'caccounts.accountType' | translate }}
                </th>
                <td *matCellDef="let element" mat-cell>
                  {{ getAccountTypeDisplay(element.accountType) | translate }}
                </td>
              </ng-container>
              <ng-container matColumnDef="accountGroup">
                <th *matHeaderCellDef mat-header-cell mat-sort-header>
                  {{ 'caccounts.accountGroup' | translate }}
                </th>
                <td *matCellDef="let element" mat-cell>
                  {{ getAccountGroupDisplay(element.accountGroup) | translate }}
                </td>
              </ng-container>
              <ng-container matColumnDef="accountNature">
                <th *matHeaderCellDef mat-header-cell mat-sort-header>
                  {{ 'caccounts.bsnsGroup' | translate }}
                </th>
                <td *matCellDef="let element" mat-cell>
                  {{ getBusinessGroupDisplay(element.businessGroup) | translate }}
                </td>
              </ng-container>
              <ng-container matColumnDef="action">
                <th *matHeaderCellDef mat-header-cell>{{ 'common.action' | translate }}</th>
                <td *matCellDef="let element" mat-cell>
                  <a
                    class="m-r-10 cursor-pointer"
                    class="m-b-10"
                    *appHasPermission="['ChartOfAccounts.Update', 'AllPermissions']"
                    [routerLink]="['edit', element.accountId]"
                    ><i-tabler class="icon-16" name="edit"></i-tabler
                  ></a>
                  <a
                    class="m-r-10 cursor-pointer"
                    class="m-b-10"
                    *appHasPermission="['ChartOfAccounts.View', 'AllPermissions']"
                    [routerLink]="['view', element.accountId]"
                    ><i-tabler class="icon-16" name="eye"></i-tabler
                  ></a>
                  <span *ngIf="element.accountType === 'GENERAL'"
                    ><a
                      class="m-r-10 cursor-pointer"
                      class="m-b-10"
                      *appHasPermission="['ChartOfAccounts.Create', 'AllPermissions']"
                      [routerLink]="['create']"
                      [queryParams]="{ parentAccountId: element.accountId }"
                      ><i-tabler class="icon-16" name="plus"></i-tabler></a
                  ></span>
                </td>
              </ng-container>
              <tr class="mat-row" *matNoDataRow>
                <td class="text-center" [attr.colspan]="displayedColumns.length">
                  {{ 'common.noDataFound' | translate }}
                </td>
              </tr>
              <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
              <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
            </table>
          </div>
          <mat-paginator
            *ngIf="dataSource?.data?.length > 0"
            [length]="resultsLength"
            [pageSize]="50"
            [pageSizeOptions]="[50, 100]"
            (page)="onPageChange($event)"></mat-paginator>
        </mat-tab>
        <mat-tab>
          <ng-template mat-tab-label>{{ 'caccounts.catreeview' | translate }}</ng-template>
          <app-accounts-tree [accounts]="accounts"></app-accounts-tree>
        </mat-tab>
      </mat-tab-group>
    </mat-card-content>
  </mat-card>
</ng-container>
