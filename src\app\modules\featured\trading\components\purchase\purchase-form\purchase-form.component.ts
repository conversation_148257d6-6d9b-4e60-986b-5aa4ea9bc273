import { animate, state, style, transition, trigger } from '@angular/animations';
import { Directionality } from '@angular/cdk/bidi';
import { ChangeDetectorRef, Component, NgZone, OnInit, ViewChild } from '@angular/core';
import {
  AbstractControl,
  UntypedFormArray,
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatTable } from '@angular/material/table';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { CustomValidators } from 'ngx-custom-validators';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject, forkJoin, of } from 'rxjs';
import { CostCentresApiService } from 'src/app/core/api/accounts/cost-centres-api.service';
import { CommonService } from 'src/app/core/api/common.service';
import { StaticDataService } from 'src/app/core/api/static-data.service';
import { DistributorService } from 'src/app/core/api/trading/distributor.service';
import { PurchaseService } from 'src/app/core/api/trading/purchase.service';
import { SalesAdjustmentService } from 'src/app/core/api/trading/sales-adjustments.service';
import { ActionType } from 'src/app/core/enums/actionType';
import { IActionEventType } from 'src/app/core/interfaces/actionEventType';
import { ICustomerView } from 'src/app/core/interfaces/customer';
import { IDistributor } from 'src/app/core/interfaces/distributor';
import { InvalidQuantity, error } from 'src/app/core/interfaces/error';
import { IPaymentViewData } from 'src/app/core/interfaces/payment';
import {
  IPurchaseDetails,
  IPurchaseInvoice,
  IPurchaseItem,
} from 'src/app/core/interfaces/purchase';
import { DeleteItemRows, SaletransactionTypes } from 'src/app/core/interfaces/sales';
import { DistributorParams } from 'src/app/core/models/params/distributorParams';
import { SalesParams } from 'src/app/core/models/params/salesParams';
import { displayValue, miscOptions } from 'src/app/core/models/wrappers/displayValue';
import { convertDateForBE } from 'src/app/core/utils/date-utils';
import { CostCentre } from 'src/app/modules/featured/accounts/models/costCentre';
import { IInventory } from 'src/app/modules/featured/catalog/models/product';
import { ConfirmDialogComponent } from 'src/app/modules/shared/components/confirm-dialog/confirm-dialog.component';
import { DeleteConfirmationComponent } from 'src/app/modules/shared/components/delete-confirmation/delete-confirmation.component';
import { PaymentsPostSaleComponent } from 'src/app/modules/shared/components/payments-post-sale/payments-post-sale.component';
import { ProductSearchSelectionComponent } from 'src/app/modules/shared/components/product-search/product-search-selection/product-search-selection.component';
import { StandardPaymentsComponent } from 'src/app/modules/shared/components/standard-payments/standard-payments.component';
import { SalesCalculation } from '../../sales/sales-calculation';
import { SupplierSelectionComponent } from '../../suppliers/supplier-selection/supplier-selection.component';
import { ISupplier } from 'src/app/core/interfaces/supplier';

@Component({
  selector: 'app-purchase-form',
  templateUrl: './purchase-form.component.html',
  styleUrls: ['./purchase-form.component.scss'],
  animations: [
    trigger('detailExpand', [
      state('collapsed', style({ height: '0px', minHeight: '0' })),
      state('expanded', style({ height: '*' })),
      transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
    ]),
  ],
})
export class PurchaseFormComponent extends SalesCalculation implements OnInit {
  @ViewChild(MatTable) table: MatTable<AbstractControl[]>;
  @ViewChild('paymentForm') private paymentForm: StandardPaymentsComponent;
  @ViewChild('supplierSelection') private suplierSelection: SupplierSelectionComponent;
  @ViewChild('paymentpostsales') private paymentpostsales: PaymentsPostSaleComponent;
  @ViewChild('productSearch') private productSearch: ProductSearchSelectionComponent;
  // Matatable
  dataSource = new BehaviorSubject<AbstractControl[]>([]);
  displayedColumns: string[] = [
    'itemCode',
    'itemName',
    'unitName',
    'quantity',
    'purchasePrice',
    'discount',
    'retailPrice',
    'wholesalePrice',
    'distributorPrice',
    'vatAmount',
    'subtotal',
    'action',
  ];
  expandedFieldNames: Array<displayValue | miscOptions>;
  columnsToDisplayWithExpand = [...this.displayedColumns, 'expand'];
  // Holders

  priceTypes: displayValue[];
  distributorAccounts: IDistributor[] = [];
  costCentreAccounts: CostCentre[] = [];
  currentSalesData: IPurchaseInvoice;
  saleDetails: IPurchaseDetails;
  products: IInventory[];
  salesId: string;
  customerViewData: ICustomerView = <ICustomerView>{};
  paymentViewData: IPaymentViewData = <IPaymentViewData>{};
  // Main Form
  itemRows: UntypedFormArray = this.formBuilder.array([]);
  salesForm: UntypedFormGroup = this.formBuilder.group({
    issueDate: new UntypedFormControl(new Date(), Validators.required),
    orderNumber: new UntypedFormControl(null),
    orderDate: new UntypedFormControl(new Date(), Validators.required),
    costCentreId: new UntypedFormControl(null),
    distributorAccountId: new UntypedFormControl(null),
    invoiceDiscount: new UntypedFormControl(0),
    notes: new UntypedFormControl(null),
    items: this.itemRows,
  });
  loading = true;
  mode: ActionType;
  expandedRowIndex: number | null = null;
  formTitle: string;
  constructor(
    private formBuilder: UntypedFormBuilder,
    private dialog: MatDialog,
    private costCentresApiService: CostCentresApiService,
    private distributorService: DistributorService,
    private purchaseService: PurchaseService,
    private toastr: ToastrService,
    private router: Router,
    private route: ActivatedRoute,
    private staticDataService: StaticDataService,
    private readonly zone: NgZone,
    private commonService: CommonService,
    private salesAdjustmentService: SalesAdjustmentService,
    private translateService: TranslateService,
    private direction: Directionality,
    private changeDetectorRef: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.loading = true;
    this.mode = this.route.snapshot.data['mode'];
    this.formTitle = this.route.snapshot.data['title'];
    this.route.params.subscribe(params => {
      this.salesId = params['id'];
      this.getAllDropDownData();
    });
    this.setPageDisabled();
  }

  get canSelectProduct() {
    return !(this.mode === ActionType.view || this.mode === ActionType.fullCreditNote);
  }

  get IsFullCreditMode(): boolean {
    return this.mode === ActionType.fullCreditNote;
  }

  get IsViewMode(): boolean {
    return this.mode === ActionType.view;
  }

  get IsEditMode(): boolean {
    return this.mode === ActionType.edit;
  }

  get canDeleteProduct() {
    return this.canSelectProduct;
  }

  setPageDisabled(): void {
    if (this.mode === ActionType.view || this.mode === ActionType.fullCreditNote) {
      this.salesForm.disable();
    }
  }

  patchData(data: IPurchaseDetails): void {
    const viewData: ICustomerView = {};
    viewData.isViewMode = this.IsViewMode ? true : false;
    viewData.customer = data.supplier;
    viewData.existingClient = data.existingClient;
    this.customerViewData = viewData;
    this.paymentViewData = data.payments;
    this.paymentViewData.isViewMode = this.IsViewMode ? true : false;
    this.saleDetails = data;
    if (data?.items && data.items.length) {
      data.items.forEach(element => {
        this.addSaleDetailForView(element);
      });
    }
    this.salesForm.patchValue({
      issueDate: data.issueDate,
      orderNumber: data.orderNumber,
      orderDate: data.orderDate,
      invoiceDiscount: data.invoiceDiscount,
      notes: data.notes,
      costCentreId: data.costCentreId,
      distributorAccountId: data.distributorAccountId,
    });
    this.setPageDisabled();
  }

  getAllDropDownData(): void {
    const salesParams = new SalesParams();
    salesParams.transactionType = SaletransactionTypes.purchase;
    const salesById = this.salesId
      ? this.purchaseService.getPurchaseSalesById(this.salesId, salesParams)
      : of(null);
    const distributorAccounts = this.distributorService.getAllDistributors(new DistributorParams());
    const costCentreAccounts = this.costCentresApiService.getAllCostCentres();
    forkJoin([distributorAccounts, costCentreAccounts, salesById]).subscribe(results => {
      console.log(results);
      this.distributorAccounts = results[0]?.distributors;
      this.costCentreAccounts = results[1];
      if (results[2]) {
        this.currentSalesData = results[2];
        this.patchData(results[2]);
      }
      this.loading = false;
    });
    this.expandedFieldNames = this.staticDataService.getSalesExpandedColumns;
    this.priceTypes = this.staticDataService.getSalesPriceTypes;
  }

  addSaleDetailForView(product: IPurchaseItem): void {
    const saleDetail: IPurchaseItem = {
      returnableQty: product.returnableQty,
      transactionItemId: product.transactionItemId,
      itemId: product.itemId,
      itemCode: product.itemCode,
      quantity: product.quantity,
      itemUnitId: product.itemUnitId,
      price: product.price,
      subtotal: product.subtotal,
      vat: product.product.vat,
      vatAmount: product.vatAmount,
      product: product.product,
      discount: product.discount,
      isGeneralDscntMethod: product.product.isGeneralDscntMethod,
      priceType: product.priceType,
      warehouseName: product.warehouseName,
      warehouseId: product.warehouseId,
      itemName: product.itemName,
      unitName: product.unitName,
      subTotalVat: product.subTotalVat,
      retailPrice: product.retailPrice,
      wholesalePrice: product.wholesalePrice,
      distributorPrice: product.distributorPrice,
      purchasePrice: product.purchasePrice,
      profit: product.discount,
      isGeneralProfitMethod: product.isGeneralProfitMethod,
    };
    this.onAddNewItem(saleDetail);
    this.dataSource.next(this.itemRows.controls);
    this.formItems(this.itemRows);
    this.updateAllRelevantPurchaseCounts();
  }

  addSaleDetail(product: IInventory) {
    console.log('addSaleDetail ->', product);
    if (!this.checkProductExist(product)) {
      const saleDetail: IPurchaseItem = {
        itemId: product.itemId,
        itemCode: product.itemCode,
        quantity: 1,
        itemUnitId: product.itemUnitId,
        price: product.purchasePrice,
        subtotal: this.countSubTotal(
          1,
          product.purchasePrice,
          product.discount,
          product.isGeneralDscntMethod,
          this.subTotalVat(
            product.purchasePrice,
            product.vat,
            product.discount,
            product.isGeneralDscntMethod
          )
        ),
        vat: product.vat,
        vatAmount: this.vatAmount(
          product.purchasePrice,
          product.discount,
          product.isGeneralDscntMethod,
          product.vat
        ),
        product: product,
        discount: product.discount,
        isGeneralDscntMethod: product.isGeneralDscntMethod,
        priceType: 0,
        warehouseName: product.warehouseName,
        warehouseId: product.warehouseId,
        itemName: product.itemName,
        unitName: product.unitName,
        subTotalVat: this.subTotalVat(
          product.purchasePrice,
          product.vat,
          product.discount,
          product.isGeneralDscntMethod
        ),
        retailPrice: product.retailPrice,
        wholesalePrice: product.wholesalePrice,
        distributorPrice: product.distributorPrice,
        purchasePrice: product.purchasePrice,
        profit: product.profit,
        isGeneralProfitMethod: product.isGeneralProfitMethod,
      };
      this.onAddNewItem(saleDetail);
      this.dataSource.next(this.itemRows.controls);
      this.formItems(this.itemRows);
      this.updateAllRelevantPurchaseCounts();
      const field = document.querySelectorAll('.next')[0] as HTMLInputElement;
      setTimeout(() => field.focus());
    } else {
      console.log('product was found so update ->', product.itemCode);
      const index = this.itemsArray.controls.findIndex(
        products =>
          products.value.itemCode === product.itemCode &&
          products.value.warehouseId === product.warehouseId &&
          products.value.unitName === product.unitName
      );
      const data = this.getSpecificFormArray(index);
      data.patchValue({
        quantity: data.get('quantity').value + 1,
        subtotal: this.countSubTotal(
          data.get('quantity').value + 1,
          data.get('price').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value,
          this.subTotalVat(
            data.get('purchasePrice').value,
            data.get('vat').value,
            data.get('discount').value,
            data.get('isGeneralDscntMethod').value
          )
        ),
        vatAmount:
          this.vatAmount(
            data.get('purchasePrice').value,
            data.get('discount').value,
            data.get('isGeneralDscntMethod').value,
            data.get('vat').value
          ) *
          (data.get('quantity').value + 1),
      });
      this.dataSource.next(this.itemRows.controls);
      this.formItems(this.itemRows);
      this.updateAllRelevantPurchaseCounts();
    }
  }

  removeSaleDetail(data: DeleteItemRows) {
    console.log('removeSaleDetail ->', data);
    const index = this.itemsArray.controls.findIndex(product => {
      console.log(product);
      return (
        product.value.itemCode === data.itemCode &&
        product.value.warehouseId === data.warehouseId &&
        product.value.unitName === data.unitName
      );
    });
    this.itemRows.removeAt(index);
    this.dataSource.next(this.itemRows.controls);
    this.formItems(this.itemRows);
    this.updateAllRelevantPurchaseCounts();
  }

  checkProductExist(products: IInventory) {
    console.log('checkProductExist -> ', products);
    return this.itemsArray.controls.find(product => {
      console.log(product);
      return (
        product.value.itemCode === products.itemCode &&
        product.value.warehouseId === products.warehouseId &&
        product.value.unitName === products.unitName
      );
    });
  }

  getControlsIndexFromArray(controlErrors: InvalidQuantity) {
    console.log('getControlsIndexFromArray -> ', controlErrors);
    return this.itemsArray.controls.findIndex(product => {
      console.log(product);
      return (
        product.value.itemCode === controlErrors.itemCode &&
        product.value.warehouseId === controlErrors.warehouseId &&
        product.value.unitName === controlErrors.unitName
      );
    });
  }

  onSelection(selectedProduct: IInventory) {
    this.addSaleDetail(selectedProduct);
  }

  onQuantityChange(event, index: number) {
    console.log('onQuantityChange -> ', event.srcElement.value, index);
    const data = this.getSpecificFormArray(index);
    data.patchValue({
      quantity: event.srcElement.value || 0,
      subtotal: this.countSubTotal(
        event.srcElement.value,
        data.get('purchasePrice').value,
        data.get('discount').value,
        data.get('isGeneralDscntMethod').value,
        this.subTotalVat(
          data.get('purchasePrice').value,
          data.get('vat').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value
        )
      ),
      subTotalVat:
        this.subTotalVat(
          data.get('purchasePrice').value,
          data.get('vat').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value
        ) * event.srcElement.value,
      vatAmount:
        this.vatAmount(
          data.get('purchasePrice').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value,
          data.get('vat').value
        ) * event.srcElement.value,
    });
    this.dataSource.next(this.itemRows.controls);
    this.formItems(this.itemRows);
    this.updateAllRelevantPurchaseCounts();
  }

  onPriceChange(event, index: number) {
    console.log('onPriceChange -> ', event, index);
    const data = this.getSpecificFormArray(index);
    data.patchValue({
      purchasePrice: event.srcElement.value || 0,
      subtotal: this.countSubTotal(
        data.get('quantity').value,
        event.srcElement.value,
        data.get('discount').value,
        data.get('isGeneralDscntMethod').value,
        this.subTotalVat(
          event.srcElement.value,
          data.get('vat').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value
        )
      ),
      subTotalVat:
        this.subTotalVat(
          event.srcElement.value,
          data.get('vat').value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value
        ) * data.get('quantity').value,
      vatAmount:
        this.vatAmount(
          event.srcElement.value,
          data.get('discount').value,
          data.get('isGeneralDscntMethod').value,
          data.get('vat').value
        ) * data.get('quantity').value,
    });
    this.dataSource.next(this.itemRows.controls);
    this.formItems(this.itemRows);
    this.updateAllRelevantPurchaseCounts();
  }

  onDiscountChange(event, index: number) {
    console.log('onDiscountChange -> ', event.srcElement.value, index);
    const data = this.getSpecificFormArray(index);
    data.patchValue({
      discount: event.srcElement.value || 0,
      subtotal: this.countSubTotal(
        data.get('quantity').value,
        data.get('purchasePrice').value,
        event.srcElement.value,
        data.get('isGeneralDscntMethod').value,
        this.subTotalVat(
          data.get('purchasePrice').value,
          data.get('vat').value,
          event.srcElement.value,
          data.get('isGeneralDscntMethod').value
        )
      ),
      subTotalVat:
        this.subTotalVat(
          data.get('purchasePrice').value,
          data.get('vat').value,
          event.srcElement.value,
          data.get('isGeneralDscntMethod').value
        ) * data.get('quantity').value,
      vatAmount:
        this.vatAmount(
          data.get('purchasePrice').value,
          event.srcElement.value,
          data.get('isGeneralDscntMethod').value,
          data.get('vat').value
        ) * data.get('quantity').value,
    });
    this.dataSource.next(this.itemRows.controls);
    this.formItems(this.itemRows);
    this.updateAllRelevantPurchaseCounts();
  }

  getSpecificFormArray(index: number): AbstractControl {
    return (<UntypedFormArray>this.salesForm.get('items')).at(index);
  }

  get itemsArray(): UntypedFormArray {
    return this.salesForm.get('items') as UntypedFormArray;
  }

  openDeleteConfirmationDialog(itemCode: number, warehouseId: number, unitName: string) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '600px';
    dialogConfig.panelClass = 'green_theme';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(DeleteConfirmationComponent, dialogConfig);
    dialogRef.componentInstance.accepted.subscribe(result => {
      console.log('delete itemcode', result);
      const data: DeleteItemRows = { itemCode, warehouseId, unitName };
      this.removeSaleDetail(data);
    });
  }

  jumpToNext(event: Event, index: number) {
    console.log('jump to next field  -> ', event, index);
    event.preventDefault();
    const nextField = document.querySelectorAll('.next');
    nextField.forEach((element, index) => {
      if (element === event.target) {
        const nextfield = nextField[index + 1] as HTMLInputElement;
        nextfield.focus();
      }
    });
  }

  onAddNewItem(product: IPurchaseItem) {
    console.log('onAddNewItem -> ', product);
    if (this.itemsArray && this.itemsArray?.length > 0) {
      this.itemsArray.insert(0, this.addnewFormGroup(product));
    } else {
      this.itemsArray.push(this.addnewFormGroup(product));
    }
  }

  addnewFormGroup(saleData: IPurchaseItem): UntypedFormGroup {
    console.log('addnewFormGroup -> ', saleData);
    const row = this.formBuilder.group({
      itemId: saleData.itemId,
      itemCode: saleData.itemCode,
      quantity: [
        saleData.quantity,
        Validators.compose([Validators.required, CustomValidators.gt(0)]),
      ],
      itemUnitId: saleData.itemUnitId,
      price: saleData.price,
      subtotal: [saleData.subtotal, Validators.compose([CustomValidators.gt(0)])],
      vat: saleData.vat,
      product: saleData.product,
      discount: [saleData.discount, this.discountValueValid],
      isGeneralDscntMethod: saleData.isGeneralDscntMethod,
      priceType: saleData.priceType,
      warehouseName: saleData.warehouseName,
      itemName: saleData.itemName,
      unitName: saleData.unitName,
      notes: saleData.notes,
      subTotalVat: saleData.subTotalVat,
      warehouseId: saleData.warehouseId,
      vatAmount: [saleData.vatAmount, Validators.compose([CustomValidators.gte(0)])],
      transactionItemId: saleData.transactionItemId,
      returnableQty: saleData.returnableQty,
      retailPrice: [
        saleData.retailPrice,
        Validators.compose([Validators.required, CustomValidators.gte(0)]),
      ],
      wholesalePrice: [
        saleData.wholesalePrice,
        Validators.compose([Validators.required, CustomValidators.gte(0)]),
      ],
      distributorPrice: [
        saleData.distributorPrice,
        Validators.compose([Validators.required, CustomValidators.gte(0)]),
      ],
      purchasePrice: [
        saleData.purchasePrice,
        Validators.compose([Validators.required, CustomValidators.gt(0)]),
      ],
      profit: saleData.profit,
      isGeneralProfitMethod: saleData.isGeneralProfitMethod,
    });
    return row;
  }

  submitSales(event: IActionEventType): void {
    event.event.preventDefault();
    if (event.actionType === ActionType.create) {
      this.salesForm.markAllAsTouched();
      this.processSalesCreation();
    }
    if (event.actionType === ActionType.edit) {
      this.salesForm.markAllAsTouched();
      this.processSalesCreation();
    }
    if (event.actionType === ActionType.fullCreditNote) {
      this.processSalesFullCreditNotes();
    }
  }

  processSalesCreation(): void {
    console.log(
      this.salesForm.valid,
      this.paymentForm.paymentFormIsAllValidPurchase(),
      this.suplierSelection.customerSelectionFormIsAllValid(),
      this.itemsArray.length > 0
    );
    const isAllValid =
      this.salesForm.valid &&
      this.paymentForm.paymentFormIsAllValidPurchase() &&
      this.suplierSelection.customerSelectionFormIsAllValid() &&
      this.itemsArray.length > 0;
    console.log('is all valid', isAllValid);
    if (isAllValid) {
      const data: IPurchaseDetails = {
        ...this.salesForm.value,
        payments: this.paymentForm.getPaymentsFormValue(),
        orderDate: convertDateForBE(this.salesForm.get('orderDate')?.value),
        issueDate: convertDateForBE(this.salesForm.get('issueDate')?.value),
        supplier: this.suplierSelection.supplierSelectionForm.get('customer').value,
        existingClient: this.suplierSelection.supplierSelectionForm.get('existingClient').value,
        transactionType: SaletransactionTypes.purchase,
        documentNumber: this.currentSalesData?.documentNumber ?? null,
      };
      console.log('Final purchase structure -> ', data);
      if (this.IsEditMode) {
        this.purchaseService.editpurchaseSales(data, this.salesId).subscribe(
          result => {
            console.log('sales service response', result);
            this.commonService.playSuccessSound();
            this.router.navigate(['../../'], { relativeTo: this.route });
          },
          error => {
            console.log('errors =>', error);
            if (error.status === 422) {
              this.setFormErrors(error);
            }
          }
        );
      } else {
        this.purchaseService.createpurchaseSales(data).subscribe(
          result => {
            console.log('sales service response', result);
            //this.toastr.success('Purchase added successfully');
            this.commonService.playSuccessSound();
            this.resetPageForNewCreation();
            //this.router.navigate(['../../'], { relativeTo: this.route });
          },
          error => {
            console.log('errors =>', error);
            if (error.status === 422) {
              this.setFormErrors(error);
            }
          }
        );
      }
    } else {
      this.commonService.playErrorSound();
    }
  }

  resetPageForNewCreation(): void {
    this.resetSalesForm();
    this.suplierSelection.resetForm();
    this.paymentForm.resetForm();
    this.resetCounts();
    this.changeDetectorRef.detectChanges();
  }

  resetSalesForm(): void {
    this.salesForm.reset();
    this.itemRows.clear();
    this.salesForm?.get('issueDate').setValue(new Date());
    this.salesForm?.get('orderDate').setValue(new Date());
    this.dataSource = new BehaviorSubject<AbstractControl[]>([]);
    this.salesForm.markAsUntouched();
  }

  processSalesFullCreditNotes(): void {
    const isAllValid = this.paymentpostsales.isValid();
    console.log('is all valid', isAllValid);
    if (isAllValid) {
      this.onPostClick();
    } else {
      this.commonService.playErrorSound();
    }
  }

  onPostClick() {
    let text = '';
    this.translateService.get('sales.fullCreditConfirmation').subscribe(translation => {
      text = translation;
    });
    const dialogConfig = new MatDialogConfig();
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.minWidth = '600px';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(ConfirmDialogComponent, dialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      console.log('result', result);
      if (result) {
        this.creditProcess();
      }
    });
  }

  creditProcess(): void {
    const data: IPurchaseDetails = {
      payments: this.paymentpostsales.getNoteConfig().notesPaymentDetails,
      referenceDocumentId: this.currentSalesData.id,
      issueDate: this.paymentpostsales.getNoteConfig().notesCommentsDate.creditNoteDate,
      referenceDocumentDate: this.currentSalesData.issueDate,
      transactionType: SaletransactionTypes.fullCreditNote,
      notes: this.paymentpostsales.getNoteConfig().notesCommentsDate.creditNoteText,
      supplier: this.suplierSelection.supplierSelectionForm.get('customer').value,
      existingClient: this.suplierSelection.supplierSelectionForm.get('existingClient').value,
      items: this.salesForm.get('items').value,
    };
    console.log('full credit note structure', data);
    this.purchaseService.createpurchaseSales(data).subscribe(
      result => {
        console.log('sales adjustment response', result);
        //this.toastr.success('Sales Adjustment successfully');
        this.commonService.playSuccessSound();
        this.router.navigate(['../../'], { relativeTo: this.route });
      },
      error => {
        console.log('errors =>', error);
        if (error.status === 422) {
          this.setFormErrors(error);
        }
      }
    );
  }

  setFormErrors(validationErrors: error): void {
    const serverErrors = validationErrors.details.invalidQuantity;
    serverErrors.forEach((element: InvalidQuantity) => {
      const controlIndex = this.getControlsIndexFromArray(element);
      console.log('index => ', controlIndex);
      const formArray = this.getSpecificFormArray(controlIndex);
      console.log('formArray => ', formArray);
      formArray.get('quantity').setErrors({ serverSideError: element.error });
      formArray.updateValueAndValidity();
    });
  }

  customerTypeSelection(isExistingCustomer: boolean): void {
    if (!isExistingCustomer) {
      this.salesForm.get('distributorAccountId').setValue(null);
    }
  }

  discountValueValid(c: AbstractControl): ValidationErrors | null {
    //     - Show ItemLineDiscount field on Grid
    // - Fill ItemLineDiscount field with Item data(% or $)
    // - Allow edit
    // - If $ then value entered should not exceed Price
    // - If % then value entered should not exceed 100

    if (c && c?.parent) {
      if (c.parent?.controls['isGeneralDscntMethod'].value) {
        // discount is % and should not be more than 100
        if (c.value > 100) {
          return { percentageError: true };
        }
      } else {
        //If $ then value entered should not exceed Price
        if (c.value > c.parent?.controls['purchasePrice'].value) {
          return { priceError: true };
        }
      }
    }
  }

  focusOnSearch(): void {
    this.productSearch.setFocus();
  }

  toggleExpansion(rowIndex: number): void {
    this.expandedRowIndex = this.expandedRowIndex === rowIndex ? null : rowIndex;
  }

  isRowExpanded(index: number): boolean {
    return this.expandedRowIndex === index;
  }

  customerProfileSelection(customer: ISupplier): void {
    if (customer) {
      this.patchCustomerProfileData(customer);
    }
  }

  patchCustomerProfileData(customer: ISupplier): void {
    this.salesForm.patchValue({
      costCentreId: customer.costCentreId,
      distributorAccountId: customer.distributorAccountId,
    });
    this.salesForm.markAsDirty();
  }
}
