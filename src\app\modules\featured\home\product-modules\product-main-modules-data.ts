import { DashboardModulesHolder } from 'src/app/modules/shared/components/dashboard-modules-holder/modulesHolder';

export const productModulesList: DashboardModulesHolder[] = [
  {
    moduleName: 'navigationMenus.accounting',
    moduleDescription: 'Manage and update stocks.',
    modulePermission: ['AccountsManagement', 'AllPermissions'],
    moduleImage: 'price_change',
    moduleRouterLink: '/accounts',
    moduleButtonAction: 'BackOffice',
    moduleType: 'mainModule',
  },
  {
    moduleName: 'navigationMenus.trading',
    moduleDescription: 'Manage sales and purchases.',
    modulePermission: ['TradeManagement', 'AllPermissions'],
    moduleImage: 'price_change',
    moduleRouterLink: '/trading',
    moduleButtonAction: 'BackOffice',
    moduleType: 'mainModule',
  },
  {
    moduleName: 'navigationMenus.inventory',
    moduleDescription: 'Manage Products, Stock and Configuration POS.',
    modulePermission: ['InventoryManagement', 'AllPermissions'],
    moduleImage: 'inventory',
    moduleRouterLink: '/inventory',
    moduleButtonAction: 'BackOffice',
    moduleType: 'mainModule',
  },
  {
    moduleName: 'navigationMenus.identity',
    moduleDescription: 'Manage Customers, Suppliers, Users.',
    modulePermission: ['IdentityManagement', 'AllPermissions'],
    moduleImage: 'perm_identity',
    moduleRouterLink: '/identity',
    moduleButtonAction: 'Reporting',
    moduleType: 'mainModule',
  },

  {
    moduleName: 'navigationMenus.enterprise',
    moduleDescription: 'Manage Settings(Company Profile, Branch , Warehouses , Taxes, Users).',
    modulePermission: ['EnterpriseManagement', 'AllPermissions'],
    moduleImage: 'settings',
    moduleRouterLink: '/enterprise',
    moduleButtonAction: 'Reporting',
    moduleType: 'mainModule',
  },
  {
    moduleName: 'navigationMenus.ucvoucher',
    moduleDescription: 'Manage Products, Stock and Configuration POS.',
    modulePermission: ['TradeManagement', 'AllPermissions'],
    moduleImage: 'price_change',
    moduleRouterLink: '/finance',
    moduleButtonAction: 'BackOffice',
    moduleType: 'mainModule',
  },
  {
    moduleName: 'navigationMenus.reports',
    moduleDescription: 'Manage and update stocks.',
    modulePermission: ['ReportsManagement', 'AllPermissions'],
    moduleImage: 'price_change',
    moduleRouterLink: '/reports',
    moduleButtonAction: 'BackOffice',
    moduleType: 'mainModule',
  },
  {
    moduleName: 'navigationMenus.utility',
    moduleDescription: 'Manage and update utility.',
    modulePermission: ['StockManagement', 'AllPermissions'],
    moduleImage: 'price_change',
    moduleRouterLink: '/utility',
    moduleButtonAction: 'BackOffice',
    moduleType: 'mainModule',
  },
  {
    moduleName: 'navigationMenus.tenantMgt',
    moduleDescription: 'Manage and update stocks.',
    modulePermission: ['Provider.TenantManagement', 'AllPermissions'],
    moduleImage: 'price_change',
    moduleRouterLink: '/owner',
    moduleButtonAction: 'BackOffice',
    moduleType: 'mainModule',
  },
];
