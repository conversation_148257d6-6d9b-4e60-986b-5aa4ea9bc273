import { ISaleDetails } from 'src/app/core/interfaces/sales';

export interface InvoiceItem {
  invoiceLineNumber: number;
  transactionItemId: number;
  itemId: number;
  itemCode: string;
  itemName: string;
  quantity: number;
  qtyToAdd?: number;
  returnableQty: number;
  itemUnitId: number;
  priceType: number;
  price: number;
  subtotal: number;
  vat: number;
  vatAmount: number;
  subTotalVat: number;
  taxableAmount: number;
  discount: number;
  warehouseId: number;
  warehouseName: string;
  notes?: string;
  cogs: number;
  isFree: boolean;
  product: Product;
  generalDscntMethod: boolean;
}

export interface Product {
  warehouseId: number;
  warehouseName: string;
  itemId: number;
  itemName: string;
  itemCode: string;
  nameEnglish: string;
  nameArabic: string;
  partNumber: string;
  category: string;
  vat: number;
  itemUnitId: number;
  unitBarcode: string;
  unitName: string;
  currentQty: number;
  branchId: number;
  openQty: number;
  reservedQty: number;
  retailPrice: number;
  wholesalePrice: number;
  distributorPrice: number;
  purchasePrice: number;
  avgPurchasePrice: number;
  openPurchasePrice: number;
  isGeneralDscntMethod: boolean;
  discount: number;
  factorRefUom: number;
  totalQuantity: number;
  totalQuantityPerUnit: number;
  yearId: number;
}

export interface CustomerAddress {
  addressId?: number;
  buildingNumber: string;
  streetName: string;
  district: string;
  postalCode: string;
  additionalNumber: string;
  shortAddress: string;
  address2: string;
  city: string;
  country: string;
}

export interface Customer {
  customerId?: number;
  accountId?: number;
  parentAccountId?: number;
  branchId?: number;
  accountNumber?: string;
  nameArabic?: string;
  nameEnglish?: string;
  costCentreId?: number;
  isHidden?: boolean;
  isFreezed?: boolean;
  hasCashPrivilege?: boolean;
  hasCardPrivilege?: boolean;
  hasCreditPrivilege?: boolean;
  hasTransferPrivilege?: boolean;
  discountPercent?: number;
  paymentToleranceDays?: number;
  distributorAccountId?: number;
  allowedBalance?: number;
  yearlyTarget?: number;
  commission?: number;
  phoneNumber?: string;
  emailId?: string;
  vatNumber?: string;
  customerNote?: string;
  balance?: number;
  address: CustomerAddress;
  identification?: string;
  identificationCode?: string;
}

export interface Payments {
  bankType: boolean;
  bankAmount: number;
  bankAccountId: number;
  cashType: boolean;
  cashAmount: number;
  cashAccountId: number;
  cardType: boolean;
  cardAmount: number;
  cardAccountId: number;
  creditType: boolean;
  creditDueDate?: string;
  creditAmount: number;
  grandTotal: number;
  totalExclVatDiscount: number;
  totalVat: number;
  totalDiscount: number;
  halala: boolean;
  fractionAmount: number;
  balanceAmount: number;
  changeAmount: number;
  taxableAmount?: number; // Added this property
}

export interface ReportInvoice extends ISaleDetails {
  templateStaticData: TemplateHeader;
  qrInvoice?: string;
  distributorNameEnglish?: string;
  distributorNameArabic?: string;
}

export interface TemplateHeader {
  mainText1?: string;
  mainText2?: string;
  mainText3: string;
  contactLine1: string;
  contactLine2: string;
  contactLine3: string;
  optionalText1: string;
  optionalText2: string;
  optionalText3: string;
  optionalContactLine1: string;
  optionalContactLine2: string;
  optionalContactLine3: string;
  warehouseId?: number;
  qrText?: string; // Base64 QR code for footer
}
