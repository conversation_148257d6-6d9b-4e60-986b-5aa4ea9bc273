<mat-toolbar class="topbar">
  <app-branding></app-branding>

  <!-- --------------------------------------------------------------- -->
  <!-- Desktop Menu -->
  <button
    class="d-flex justify-content-center"
    *ngIf="showToggle"
    (click)="toggleCollapsed.emit()"
    mat-icon-button>
    <i-tabler class="icon-20 d-flex" name="menu-2"></i-tabler>
  </button>

  <!-- Mobile Menu -->
  <button
    class="d-flex justify-content-center"
    *ngIf="!showToggle"
    (click)="toggleMobileNav.emit()"
    mat-icon-button>
    <i-tabler class="icon-20 d-flex" name="menu-2"></i-tabler>
  </button>
  <!-- --------------------------------------------------------------- -->

  <!-- --------------------------------------------------------------- -->
  <!--  Search -->
  <!-- --------------------------------------------------------------- -->
  <button class="d-flex justify-content-center" (click)="openDialog()" mat-icon-button>
    <i-tabler class="icon-20 d-flex" name="search"></i-tabler>
  </button>

  <span class="flex-1-auto"></span>

  <!-- --------------------------------------------------------------- -->
  <!-- langugage Dropdown -->
  <!-- --------------------------------------------------------------- -->
  <button class="m-r-5" [matMenuTriggerFor]="flags" mat-icon-button>
    <img class="rounded object-cover icon-20" [src]="selectedLanguage.icon" />
  </button>
  <mat-menu class="cardWithShadow" #flags="matMenu">
    <button *ngFor="let lang of languages" (click)="changeLanguage(lang)" mat-menu-item>
      <div class="d-flex align-items-center">
        <img class="rounded object-cover m-r-8 icon-20" [src]="lang.icon" />
        <span class="mat-subtitle-1 f-s-14">{{ lang.language }}</span>
      </div>
    </button>
  </mat-menu>

  <!-- --------------------------------------------------------------- -->
  <!-- Notification Dropdown -->
  <!-- --------------------------------------------------------------- -->
  <button [matMenuTriggerFor]="notificationmenu" mat-icon-button aria-label="Notifications">
    <mat-icon class="header-badge" matBadge="1" matBadgeOverlap="false" matBadgeColor="warn"
      >message</mat-icon
    >
  </button>
  <mat-menu class="topbar-dd cardWithShadow" #notificationmenu="matMenu">
    <div class="d-flex align-items-center p-x-32 p-y-16">
      <h6 class="f-s-16 f-w-600 m-0">Notifications</h6>
      <span class="m-l-auto">
        <span class="bg-primary p-x-8 p-y-4 f-w-500 rounded f-s-12">5 new</span>
      </span>
    </div>
    <button class="p-x-32 p-y-16" *ngFor="let notification of notifications" mat-menu-item>
      <div class="d-flex align-items-center">
        <img class="rounded-circle" [src]="notification.img" width="48" />
        <div class="m-l-16">
          <h5 class="f-s-14 f-w-600 m-0 mat-subtitle-1">
            {{ notification.title }}
          </h5>
          <span>{{ notification.subtitle }}</span>
        </div>
      </div>
    </button>
    <div class="p-y-12 p-x-32">
      <button class="w-100" mat-stroked-button color="primary">See all notifications</button>
    </div>
  </mat-menu>

  <!-- --------------------------------------------------------------- -->
  <!-- Messages Dropdown -->
  <!-- --------------------------------------------------------------- -->
  <button [matMenuTriggerFor]="msgmenu" mat-icon-button aria-label="Messages">
    <mat-icon class="header-badge" matBadge="2" matBadgeOverlap="false" matBadgeColor="warn"
      >email</mat-icon
    >
  </button>
  <mat-menu class="topbar-dd cardWithShadow" #msgmenu="matMenu">
    <div class="d-flex align-items-center p-x-32 p-y-16">
      <h6 class="f-s-16 f-w-600 m-0">Messages</h6>
      <span class="m-l-auto">
        <span class="bg-primary p-x-8 p-y-4 f-w-500 rounded f-s-12">5 new</span>
      </span>
    </div>
    <button class="p-x-32 p-y-16" *ngFor="let msg of msgs" mat-menu-item>
      <div class="d-flex align-items-center">
        <img class="rounded-circle" [src]="msg.img" width="48" />
        <div class="m-l-16">
          <h5 class="f-s-14 f-w-600 m-0 mat-subtitle-1">
            {{ msg.title }}
          </h5>
          <span>{{ msg.subtitle }}</span>
        </div>
      </div>
    </button>
    <div class="p-y-12 p-x-32">
      <button class="w-100" mat-stroked-button color="primary">See all messages</button>
    </div>
  </mat-menu>

  <!-- --------------------------------------------------------------- -->
  <!-- profile Dropdown -->
  <!-- --------------------------------------------------------------- -->
  <button [matMenuTriggerFor]="profilemenu" mat-icon-button aria-label="Notifications">
    <img class="rounded-circle object-cover" src="/assets/images/profile/profile.png" width="35" />
  </button>
  <mat-menu class="topbar-dd cardWithShadow" #profilemenu="matMenu">
    <div class="position-relative" [perfectScrollbar]="config" style="height: 647px">
      <div class="p-x-32 p-y-16">
        <h6 class="f-s-16 f-w-600 m-0">User Profile</h6>

        <div class="d-flex align-items-center p-b-24 b-b-1 m-t-16">
          <img class="rounded-circle" src="/assets/images/profile/profile.png" width="95" />
          <div class="m-l-16">
            <h6 class="f-s-14 f-w-600 m-0">Mathew Anderson</h6>
            <span class="f-s-14 d-block m-b-4">Designer</span>
            <span class="d-flex align-items-center">
              <i-tabler class="icon-15 m-r-4" name="mail"></i-tabler>
              <EMAIL>
            </span>
          </div>
        </div>
      </div>
      <div class="p-x-32">
        <a
          class="p-y-16 text-decoration-none d-block text-hover-primary"
          *ngFor="let profile of profiledd"
          [routerLink]="[profile.link]">
          <div class="d-flex align-items-center">
            <button class="text-primary bg-light-primary shadow-none rounded" mat-mini-fab>
              <img [src]="profile.img" width="20" />
            </button>

            <div class="m-l-16">
              <h5 class="f-s-14 f-w-600 m-0 textprimary mat-subtitle-1 hover-text">
                {{ profile.title }}
              </h5>
              <span class="mat-body-1">{{ profile.subtitle }}</span>
            </div>
          </div>
        </a>

        <!-- upgrade -->
        <div class="p-24 overflow-hidden bg-light-primary rounded position-relative m-y-16">
          <div class="d-flex align-items-center">
            <div>
              <h5 class="f-s-18 m-0 f-w-600 m-b-12">
                Unlimited <br />
                Access
              </h5>
              <button mat-stroked-button color="primary">Upgrade</button>
            </div>
            <div class="m-l-auto">
              <img
                class="upgrade-bg"
                src="/assets/images/backgrounds/unlimited-bg.png"
                alt="upgrade-bg" />
            </div>
          </div>
        </div>
      </div>

      <div class="p-y-12 p-x-32">
        <a
          class="w-100"
          [routerLink]="['/authentication/side-login']"
          mat-stroked-button
          color="primary"
          >Logout</a
        >
      </div>
    </div>
  </mat-menu>
</mat-toolbar>
