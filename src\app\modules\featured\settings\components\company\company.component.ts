import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { CustomValidators } from 'ngx-custom-validators';
import { ImageCroppedEvent } from 'ngx-image-cropper';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/core/api/common.service';
import { customerIndentifactionCode } from 'src/app/core/configs/dropDownConfig';
import { ICompany } from 'src/app/core/interfaces/company';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { AddressComponent } from 'src/app/modules/shared/components/address/address.component';
import { CompanyService } from '../../services/company.service';

@Component({
  selector: 'app-company',
  templateUrl: './company.component.html',
  styleUrls: ['./company.component.scss'],
})
export class CompanyComponent implements OnInit {
  @ViewChild('addressForm') addressForm: AddressComponent;
  companyForm: UntypedFormGroup;
  imageChangedEvent: any = '';
  croppedImage: any = '';
  showCropper = false;
  defaultImage = 'assets/images/company-placeholder-image.png';
  loading = true;
  title: string;
  companyId: string;
  companyError = false;
  formTitle: string;
  identificationCodes = customerIndentifactionCode;

  constructor(
    private toastr: ToastrService,
    private authService: AuthService,
    private companyService: CompanyService,
    private fb: UntypedFormBuilder,
    private sanitizer: DomSanitizer,
    private route: ActivatedRoute,
    private commonService: CommonService
  ) {}

  ngOnInit(): void {
    this.formTitle = this.route.snapshot.data['title'];
    this.getCompanyDetails();
  }

  getCompanyDetails(): void {
    this.companyError = false;
    this.companyId = this.authService.getCompanyID;

    this.companyService.getCompanyById().subscribe(
      response => {
        if (response?.base64EncodedImageString) {
          this.croppedImage = this.sanitizer.bypassSecurityTrustResourceUrl(
            'data:image/jpg;base64,' + response.base64EncodedImageString
          );
        }
        this.initializeForm(response);
        this.loading = false;
      },
      error => {
        this.loading = false;
        this.companyError = true;
        console.error(error);
      }
    );
  }

  updateCompanyDetails(formData: FormData): void {
    this.companyService.updateCompany(formData).subscribe(
      () => {
        this.showCropper = false;
        //this.toastr.success('Company details updated successfully');
        this.commonService.playSuccessSound();
      },
      error => {
        console.error(error);
      }
    );
  }

  initializeForm(data: ICompany): void {
    this.companyForm = this.fb.group({
      nameArabic: [data?.nameArabic ?? null, [Validators.required, Validators.maxLength(40)]],
      nameEnglish: [data?.nameEnglish ?? null, [Validators.maxLength(40)]],
      regCertNo: [data?.regCertNo ?? null, [Validators.required, Validators.maxLength(15)]],
      phoneNo: [data?.phoneNo ?? null, [Validators.required, Validators.maxLength(15)]],
      emailId: [
        data?.emailId ?? null,
        [Validators.required, CustomValidators.email, Validators.maxLength(50)],
      ],
      address: [data?.address],
      identification: [data?.identification ?? null, Validators.required],
      identificationCode: [data?.identificationCode ?? null, Validators.required],
    });
  }

  fileChangeEvent(event: any): void {
    const file = event?.target?.files[0];
    if (file && file.type.match(/image\/*/)) {
      this.showCropper = true;
      this.imageChangedEvent = event;
    }
  }

  imageCropped(event: ImageCroppedEvent): void {
    this.croppedImage = event.base64;
  }

  imageLoaded(): void {
    // show cropper
  }

  cropperReady(): void {
    // cropper ready
  }

  loadImageFailed(): void {
    // show message
  }

  private markFormGroupTouched(form: UntypedFormGroup): void {
    Object.values(form.controls).forEach(control => {
      control.markAsTouched();
      if ((control as any).controls) {
        this.markFormGroupTouched(control as UntypedFormGroup);
      }
    });
  }

  onSubmit(event: Event): void {
    event.preventDefault();
    this.addressForm.markFormAsTouched();
    this.companyForm.markAllAsTouched();

    if (this.companyForm.valid && this.addressForm.isValid()) {
      const formData = new FormData();
      formData.append('updateCompanyRequest', JSON.stringify(this.companyForm.value));

      if (this.showCropper) {
        const fileToUpload = new File([this.dataURItoBlob(this.croppedImage)], 'profile.png');
        formData.append('file', fileToUpload);
      }

      this.updateCompanyDetails(formData);
    } else {
      this.commonService.playErrorSound();
    }
  }

  dataURItoBlob(dataURI: string): Blob {
    const [header, data] = dataURI.split(',');
    const mimeString = header.split(':')[1].split(';')[0];
    const byteString = atob(data);
    const ab = new ArrayBuffer(byteString.length);
    const ia = new Uint8Array(ab);

    for (let i = 0; i < byteString.length; i++) {
      ia[i] = byteString.charCodeAt(i);
    }

    return new Blob([ab], { type: mimeString });
  }
}
