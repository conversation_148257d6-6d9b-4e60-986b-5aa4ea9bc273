import {
  Component,
  OnInit,
  ViewChild,
  Optional,
  Inject,
  ViewChildren,
  QueryList,
} from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { fork<PERSON>oin } from 'rxjs';
import { PaginatedFilter } from 'src/app/core/interfaces/PaginatedFilter';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { Branch } from '../../../catalog/models/branch';
import { BulkEditData } from '../../../catalog/models/bulkedit';
import { Category } from '../../../catalog/models/category';
import { CategoryParams } from '../../../catalog/models/categoryParams';
import { CategoryService } from '../../../../../core/api/category.service';
import { BranchParams } from '../../../settings/models/branchParams';
import { StoreParams } from '../../../settings/models/storeParams';
import { BranchService } from '../../../settings/services/branch.service';
import { ReportService } from '../../../settings/services/report.service';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { IReportType } from '../../../catalog/models/reports';
import { ReportParams } from '../../../settings/models/reportParams';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { UnitService } from 'src/app/core/api/unit.service';
import { StoreService } from 'src/app/core/api/store.service';
import { MatSort, Sort } from '@angular/material/sort';
import { ProductService } from 'src/app/core/api/product.service';
import { CookieService } from 'ngx-cookie-service';
import { MatSelect } from '@angular/material/select';
import { MultilingualService } from 'src/app/modules/core/core/services/multilingual.service';
import { debounceTime } from 'rxjs/operators';
import * as moment from 'moment';

@Component({
  selector: 'inventory-report',
  templateUrl: './inventory-report.component.html',
  styleUrls: ['./inventory-report.component.scss'],
})
export class InventoryReportComponent implements OnInit {
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  dataSource: MatTableDataSource<any>;
  totalItems = 0;
  pageSize = 100;
  currentPage = 0;
  totalPages = 0;
  morePages = false;
  request: any;
  loading = true;
  categoryList: Category[];
  branchList: Branch[];
  warehouseList: any;
  yearIdList: any;
  filteredYearIdList: any;
  allWarehouses: any;
  unitList: any;
  filtersForm: UntypedFormGroup;
  isReportPulling = false;
  isAdvancedSearchEnabled = false;
  isBranchNotSelected = true;
  pdfUrl: SafeResourceUrl | null = null;
  reportTypeList: IReportType[];
  reportData: any[] = [];
  selectedReportType: string;
  selectedReportEndPoint: string;
  selectedJasperReportEndPoint: string;
  templateId: any;
  displayedColumns: string[] = [];
  allColumns: string[] = [];
  itemCodeList: any;
  filteredItemCodes: any;
  isAdvancedSearchVisible = false;
  searchConfigs: any;
  selectedItemId: any;
  columnNames: { [key: string]: string } = {
    warehouseId: 'Warehouse ID',
    warehouseName: 'Warehouse Name',
    itemId: 'Item ID',
    itemName: 'Item Name',
    itemCode: 'Item Code',
    nameEnglish: 'Name (English)',
    nameArabic: 'Name (Arabic)',
    partNumber: 'Part Number',
    category: 'Category',
    vat: 'VAT',
    itemUnitId: 'Item Unit ID',
    unitBarcode: 'Unit Barcode',
    unitName: 'Unit Name',
    currentQty: 'Current Quantity',
    branchId: 'Branch ID',
    itemLocation: 'Item Location',
    lastInventoryCheckDate: 'Last Inventory Check Date',
    openQty: 'Open Quantity',
    reservedQty: 'Reserved Quantity',
    retailPrice: 'Retail Price',
    wholesalePrice: 'Wholesale Price',
    distributorPrice: 'Distributor Price',
    purchasePrice: 'Purchase Price',
    avgPurchasePrice: 'Average Purchase Price',
    openPurchasePrice: 'Open Purchase Price',
    isGeneralDscntMethod: 'Is General Discount Method',
    discount: 'Discount',
    factorRefUom: 'Factor Reference UOM',
    totalQuantity: 'Total Quantity',
    totalQuantityPerUnit: 'Total Quantity Per Unit',
    yearId: 'Year ID',
    purchaseStockValue: 'Stock Value(Purchase Price)',
    openStockValue: 'Stock Value(Open Purchase Price)',
    retailStockValue: 'Stock Value(Retail Price)',
    wholesaleStockValue: 'Stock Value(Wholesale Price)',
    avgPurchaseStockValue: 'Stock Value(Avg Purchase Price)',
    distributorStockValue: 'Stock Value(Distributor Price)',
    rowNumber: 'Row Number',
  };
  @ViewChildren(MatSelect) matSelects!: QueryList<MatSelect>;
  formName = 'inventoryReportSearch';
  constructor(
    @Optional() @Inject(MAT_DIALOG_DATA) public data: BulkEditData,
    private branchService: BranchService,
    private storeService: StoreService,
    private categoryService: CategoryService,
    private authService: AuthService,
    private reportService: ReportService,
    private fb: UntypedFormBuilder,
    private unitApiService: UnitService,
    private productService: ProductService,
    private cookieService: CookieService,
    private translateService: MultilingualService
  ) {}

  ngOnInit(): void {
    this.dataSource = new MatTableDataSource(this.reportData);
    this.dataSource.paginator = this.paginator;
    this.getAllDropDownData();
  }

  filterYearIdForSelectedBranch() {
    this.filteredYearIdList = this.yearIdList
      .filter(year => this.filtersForm.controls['branchId'].value === year.branchId)
      .flatMap(filteredYear => filteredYear.years);
  }
  onBranchAndYearSelect(): void {
    if (this.filtersForm.controls['branchId'].value && this.filtersForm.controls['yearId'].value)
      this.productService
        .getProductByBranchesAndYearId(
          this.filtersForm.controls['branchId'].value,
          this.filtersForm.controls['yearId'].value
        )
        .subscribe(result => {
          this.itemCodeList = result;
          this.filteredItemCodes = this.itemCodeList;
        });
  }

  onSearchInput(input: string): void {
    this.filteredItemCodes = this.itemCodeList.filter(
      item =>
        item.nameEnglish.toLowerCase().includes(input.toLowerCase()) ||
        item.nameArabic.toLowerCase().includes(input.toLowerCase()) ||
        item.itemCode.includes(input)
    );
  }
  onItemCodeSelected(item: any): void {
    this.selectedItemId = item.itemId;
  }
  onSortChange(sortState: Sort) {
    this.request.sortBy = sortState.active;
    this.request.sortDir = sortState.direction;
    this.request.sortFields = `${sortState.active}:${sortState.direction}`;

    this.getReportData();
  }

  onPageChange(event: PageEvent) {
    this.pageSize = event.pageSize;
    this.currentPage = event.pageIndex;
    this.getReportData();
  }

  getAllDropDownData() {
    const category = this.categoryService.getAllCategories(new CategoryParams());
    const branches = this.branchService.getAllBranchesWithYear(new BranchParams());
    const warehouses = this.storeService.getAllStores(new StoreParams());
    const units = this.unitApiService.getAllUnits();
    const reportTypes = this.reportService.getAllReportType(1);
    const yearIds = this.authService.getUserBranchesYearId(this.authService.getCompanyID);
    forkJoin([category, branches, warehouses, units, reportTypes, yearIds]).subscribe(results => {
      this.categoryList = results[0];
      this.branchList = results[1];
      this.allWarehouses = results[2];
      this.unitList = results[3];
      this.reportTypeList = results[4];
      this.yearIdList = results[5];
      this.loading = false;
      this.searchConfigs = this.reportTypeList[0].searchConfigs;
    });
  }
  subscribeToFormChanges(): void {
    this.filtersForm.get('branchId')!.valueChanges.subscribe(value => {
      this.onBranchAndYearSelect();
      this.filterYearIdForSelectedBranch();
    });

    this.filtersForm.get('yearId')!.valueChanges.subscribe(value => {
      this.onBranchAndYearSelect();
    });

    this.filtersForm.valueChanges.subscribe(data => {
      if (data) {
        this.warehouseList = this.allWarehouses.filter(item => {
          return this.filtersForm.get('branchId').value === item.branchId;
        });
      }
      this.isBranchNotSelected = !(data && data?.branchId?.length > 0);
    });
  }
  getReportData(event?: Event, pageEvent?: PaginatedFilter) {
    event?.preventDefault();
    this.filtersForm.markAllAsTouched();
    console.log(this.filtersForm);
    if (this.filtersForm.valid) {
      const params: ReportParams = <ReportParams>{};
      if (this.filtersForm?.controls['dateFrom']?.value)
        params.dateFrom = moment(this.filtersForm.controls['dateFrom'].value).format('YYYY-MM-DD');
      if (this.filtersForm?.controls['dateTo']?.value)
        params.dateTo = moment(this.filtersForm.controls['dateTo'].value).format('YYYY-MM-DD');
      if (this.filtersForm?.controls['date']?.value)
        params.date = moment(this.filtersForm.controls['date'].value).format('YYYY-MM-DD');
      params.templateId = this.templateId;
      params.reportType = this.selectedReportType;
      params.categoryId = this.filtersForm?.controls['categoryIds']?.value;
      params.warehouseId = this.filtersForm?.controls['warehouseIds']?.value;
      params.branchId = this.filtersForm?.controls['branchId']?.value;
      params.unitId = this.filtersForm?.controls['unitIds']?.value;
      if (this.filtersForm.controls['searchString'].value != null) {
        params.searchString = this.filtersForm.controls['searchString'].value;
        params.itemId = this.selectedItemId;
      }
      params.yearId = this.filtersForm.controls['yearId'].value;
      // Pagination parameters
      params.page = this.currentPage + 1;
      params.pageSize = this.pageSize;
      params.endPoint = this.selectedReportEndPoint;
      params.jasperEndPoint = this.selectedJasperReportEndPoint;
      const requestPayload = {
        ...(this.request || {}),
        ...params,
      };
      this.request = requestPayload;

      this.reportService
        .getInventoryReports(requestPayload)
        .subscribe(
          (result: any) => {
            console.log(result);
            this.reportData = result.reportData;
            if (this.displayedColumns.length === 0) {
              this.displayedColumns = this.translateService.updateDisplayedColumns(
                result?.columnsToDisplay
              );
              this.allColumns = this.displayedColumns;
            }

            this.dataSource = new MatTableDataSource(this.reportData);
            // Set page properties
            this.totalItems = result.totalRecordsCount;
            this.totalPages = result.totalPages;
            this.morePages = result.morePages;
          },
          error => {
            console.log(error);
          }
        )
        .add(() => (this.isReportPulling = false));
    }
  }

  getColumnDisplayName(key: string): string {
    return this.columnNames[key] || key;
  }

  downloadPdf() {
    this.request.type = 'PDF';
    console.log(this.request);

    this.reportService.getInventoryJasperReports(this.request).subscribe(
      (result: any) => {
        console.log('Success...');
      },
      error => {
        console.log(error);
      }
    );
  }

  getSelectForField(field: string): MatSelect | undefined {
    return this.matSelects?.toArray().find(matSelect => matSelect.ngControl?.name === field);
  }

  createForm() {
    this.filtersForm = this.fb.group({});
    this.searchConfigs.forEach(config => {
      const validators = config.mandatory ? [Validators.required] : [];
      this.filtersForm.addControl(config.backendParam, this.fb.control(null, validators));
      if (config.type === 'dateRange') {
        this.filtersForm.addControl(
          config.backendParam + 'From',
          this.fb.control(null, validators)
        );
        this.filtersForm.addControl(config.backendParam + 'To', this.fb.control(null, validators));
      }
    });
    this.subscribeToFormChanges();
    const branchIdFromCookie = +this.cookieService.get('branchId');
    const yearIdFromCookie = +this.cookieService.get('yearId');
    this.filtersForm.patchValue({ branchId: branchIdFromCookie, yearId: yearIdFromCookie });
    this.filterYearIdForSelectedBranch();
  }
  getListForField(field: string) {
    switch (field) {
      case 'branchId':
        return this.branchList;
      case 'yearId':
        return this.filteredYearIdList;
      case 'warehouseIds':
        return this.warehouseList;
      case 'unitIds':
        return this.unitList;
      case 'searchString':
        return this.filteredItemCodes;
      case 'categoryIds':
        return this.categoryList;
      case 'reportType':
        return this.reportTypeList;
      default:
        return [];
    }
  }

  selectReportType(report: any) {
    this.selectedReportType = report.name;
    const reportType = this.reportTypeList.find(report => report.name === this.selectedReportType);
    this.templateId = reportType.id;
    this.selectedReportEndPoint = reportType.endPoint;
    this.selectedJasperReportEndPoint = reportType.jasperEndPoint;
    this.searchConfigs = reportType.searchConfigs;
    this.createForm();
  }
  resetReportType() {
    this.selectedReportType = null;
    this.reportData = [];
    this.request = {};
  }
  transformReportName(name: string): string {
    return name ? name.replace(/ /g, '') : '';
  }

  updateDisplayedColumns(selectedColumns: string[]) {
    this.displayedColumns = selectedColumns.length ? selectedColumns : this.allColumns;
  }
}
