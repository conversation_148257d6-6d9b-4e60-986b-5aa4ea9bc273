<ng-container *ngIf="!loading">
  <mat-card appearance="outlined">
    <form [formGroup]="salesForm" autocomplete="off">
      <!-- sales top level UI elements -->
      <mat-card-title>{{ 'common.field.accountDetails' | translate }}</mat-card-title>
      <mat-expansion-panel class="readOnly" class="shadow-none no-padding" [expanded]="true">
        <mat-expansion-panel-header class="bg-primary">
          <mat-panel-description class="mat-body-1">
            <span class="f-s-20 text-white"
              >{{ 'sales.debitInvoice' | translate }} : {{ saleDetails.documentNumber }}</span
            >
          </mat-panel-description>
        </mat-expansion-panel-header>
        <div class="row no-gutters">
          <div class="col-md-3 col-lg-2 col-sm-4 p-2">
            <mat-label>{{ 'sales.salesDate' | translate }}</mat-label>

            <mat-form-field class="w-100">
              <input
                [matDatepicker]="invoiceDatePicker"
                readonly
                matInput
                formControlName="issueDate" />
              <!-- <mat-datepicker-toggle [for]="invoiceDatePicker" matSuffix></mat-datepicker-toggle>
            <mat-datepicker #invoiceDatePicker></mat-datepicker> -->
            </mat-form-field>
          </div>
          <div class="col-md-3 col-lg-2 col-sm-4 p-2">
            <mat-label>{{ 'sales.referenceNo' | translate }}</mat-label>

            <mat-form-field class="w-100">
              <input matInput readonly formControlName="orderNumber" />
            </mat-form-field>
          </div>
          <div class="col-md-3 col-lg-2 col-sm-4 p-2">
            <mat-label>{{ 'sales.referenceDate' | translate }}</mat-label>

            <mat-form-field class="w-100">
              <input
                [matDatepicker]="invoiceReferenceDatePicker"
                readonly
                matInput
                formControlName="orderDate" />
              <!-- <mat-datepicker-toggle
              [for]="invoiceReferenceDatePicker"
              matSuffix></mat-datepicker-toggle>
            <mat-datepicker #invoiceReferenceDatePicker></mat-datepicker> -->
            </mat-form-field>
          </div>
          <!-- cost accounts  -->
          <div class="col-md-3 col-lg-2 col-sm-4 p-2">
            <mat-label>{{ 'common.field.costAccount' | translate }}</mat-label>

            <mat-form-field class="w-100 pe-none">
              <mat-select class="withoutArrow" formControlName="costCentreId">
                <mat-option>{{ 'common.reset' | translate }}</mat-option>
                <mat-option
                  *ngFor="let costCentreAccount of costCentreAccounts"
                  [value]="costCentreAccount.costCentreId">
                  {{ costCentreAccount | localized }}
                </mat-option>
              </mat-select>
              <mat-error
                *ngIf="
                  salesForm?.controls['costCentreId'].hasError('required') &&
                  salesForm?.controls['costCentreId'].touched
                "
                >{{ 'common.required' | translate }}</mat-error
              >
            </mat-form-field>
          </div>
          <!-- distributor accounts  -->
          <div class="col-md-3 col-lg-2 col-sm-4 p-2">
            <mat-label>{{ 'common.field.distributorAccount' | translate }}</mat-label>

            <mat-form-field class="w-100 pe-none">
              <mat-select class="withoutArrow" formControlName="distributorAccountId">
                <mat-option>{{ 'common.reset' | translate }}</mat-option>
                <mat-option
                  *ngFor="let distributorAccount of distributorAccounts"
                  [value]="distributorAccount.accountId">
                  {{ distributorAccount | localized }}
                </mat-option>
              </mat-select>
              <mat-error
                *ngIf="
                  salesForm?.controls['distributorAccountId'].hasError('required') &&
                  salesForm?.controls['distributorAccountId'].touched
                "
                >{{ 'common.required' | translate }}</mat-error
              >
            </mat-form-field>
          </div>
          <div class="col-md-3 col-lg-2 col-sm-4 p-2">
            <mat-label>{{ 'sales.invoiceNote' | translate }}</mat-label>

            <mat-form-field class="w-100">
              <textarea
                #streetName
                #autosize="cdkTextareaAutosize"
                maxlength="90"
                type="text"
                matInput
                formControlName="notes"
                cdkTextareaAutosize
                cdkAutosizeMaxRows="5"></textarea>
            </mat-form-field>
          </div>
        </div>
        <!-- customer search -->
        <app-customer-selection
          #customerSelection
          [mode]="mode"
          [customerViewData]="customerViewData"
          (itemSelected)="customerProfileSelection($event)"
          (customerTypeSelection)="customerTypeSelection($event)"></app-customer-selection>
      </mat-expansion-panel>

      <!-- product search -->
      <app-product-search-selection
        #productSearch
        *ngIf="canSelectProduct"
        (itemSelected)="onSelection($event)"></app-product-search-selection>
      <!-- editor table -->
      <!-- <div class="table-responsive"> -->
      <!-- main table editor -->
      <div class="line-item-content-container">
        <div class="line-item-table-container">
          <div class="table-responsive">
            <table
              class="w-100"
              #demomatdatatable
              [dataSource]="dataSource"
              formArrayName="items"
              multiTemplateDataRows
              mat-table>
              <ng-container matColumnDef="itemCode">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'sales.code' | translate }}
                </th>
                <td
                  *matCellDef="let element; let i = dataIndex"
                  [formGroupName]="i"
                  [matTooltip]="element?.get('product')?.value['itemCode']"
                  tabindex="-1"
                  mat-cell>
                  <ng-container>
                    <div class="wrap text-center">
                      {{ element?.get('product')?.value['itemCode'] }}
                    </div>
                  </ng-container>
                </td>
              </ng-container>

              <ng-container matColumnDef="itemName">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'sales.name' | translate }}
                </th>
                <td
                  *matCellDef="let element; let i = dataIndex"
                  [formGroupName]="i"
                  [matTooltip]="element?.get('product')?.value['itemName']"
                  mat-cell>
                  {{ element?.get('product')?.value['itemName'] }}
                </td>
              </ng-container>

              <ng-container matColumnDef="warehouseName">
                <th *matHeaderCellDef mat-header-cell>Warehouse</th>
                <td
                  *matCellDef="let element; let i = dataIndex"
                  [formGroupName]="i"
                  [matTooltip]="element?.get('product')?.value['warehouseName']"
                  mat-cell>
                  {{ element?.get('product')?.value['warehouseName'] }}
                </td>
              </ng-container>

              <ng-container matColumnDef="unitName">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'sales.unit' | translate }}
                </th>
                <td
                  *matCellDef="let element; let i = dataIndex"
                  [formGroupName]="i"
                  [matTooltip]="element?.get('product')?.value['unitName']"
                  mat-cell>
                  {{ element?.get('product')?.value['unitName'] }}
                </td>
              </ng-container>

              <!-- <ng-container matColumnDef="returnableQty">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'sales.partial-qty-sold' | translate }}
                </th>
                <td *matCellDef="let element; let i = dataIndex" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100" class="d-flex w-120">
                    <input
                      class="next"
                      (change)="onQuantityChange($event, i)"
                      (keydown.enter)="jumpToNext($event, i)"
                      readonly
                      matInput
                      type="number"
                      formControlName="returnableQty" />
                  </mat-form-field>
                </td>
              </ng-container> -->

              <ng-container matColumnDef="retailPrice">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'purchase.retailPrice' | translate }}
                </th>
                <td *matCellDef="let element; let i = dataIndex" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100" class="d-flex">
                    <input
                      class="next"
                      (keydown.enter)="jumpToNext($event, i)"
                      matInput
                      type="number"
                      min="0"
                      formControlName="retailPrice" />
                    <mat-error
                      *ngIf="
                        (itemsArray.controls[i].get('retailPrice').hasError('required') ||
                          itemsArray.controls[i].get('retailPrice').hasError('gte')) &&
                        itemsArray.controls[i].get('retailPrice').touched
                      ">
                      Enter Valid Price
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>

              <ng-container matColumnDef="wholesalePrice">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'purchase.wholeSalePrice' | translate }}
                </th>
                <td *matCellDef="let element; let i = dataIndex" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100" class="d-flex">
                    <input
                      class="next"
                      (keydown.enter)="jumpToNext($event, i)"
                      matInput
                      type="number"
                      min="0"
                      formControlName="wholesalePrice" />
                    <mat-error
                      *ngIf="
                        (itemsArray.controls[i].get('wholesalePrice').hasError('required') ||
                          itemsArray.controls[i].get('wholesalePrice').hasError('gte')) &&
                        itemsArray.controls[i].get('wholesalePrice').touched
                      ">
                      Enter Valid Price
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>

              <ng-container matColumnDef="distributorPrice">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'purchase.distributorPrice' | translate }}
                </th>
                <td *matCellDef="let element; let i = dataIndex" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100" class="d-flex">
                    <input
                      class="next"
                      (keydown.enter)="jumpToNext($event, i)"
                      matInput
                      type="number"
                      min="0"
                      formControlName="distributorPrice" />
                    <mat-error
                      *ngIf="
                        (itemsArray.controls[i].get('distributorPrice').hasError('required') ||
                          itemsArray.controls[i].get('distributorPrice').hasError('gte')) &&
                        itemsArray.controls[i].get('distributorPrice').touched
                      ">
                      Enter Valid Price
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>

              <ng-container matColumnDef="quantity">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'purchase.quantity' | translate }}
                </th>
                <td *matCellDef="let element; let i = dataIndex" [formGroupName]="i" mat-cell>
                  <mat-form-field
                    class="w-100"
                    class="d-flex w-120"
                    [ngClass]="{ 'bg-light-primary': element.get('quantity')?.value > 0 }">
                    <input
                      class="next"
                      (change)="onQuantityChange($event, i)"
                      (keydown.enter)="jumpToNext($event, i)"
                      matInput
                      type="number"
                      min="0"
                      max="{{ element?.get('product')?.value['returnableQty'] }}"
                      formControlName="quantity" />

                    <mat-error
                      *ngIf="
                        (itemsArray.controls[i].get('quantity').hasError('required') ||
                          itemsArray.controls[i].get('quantity').hasError('lte')) &&
                        itemsArray.controls[i].get('quantity').touched
                      ">
                      Max Qty:{{ element.get('returnableQty').value }}
                    </mat-error>
                    <mat-error
                      *ngIf="itemsArray.controls[i].get('quantity').hasError('serverSideError')">
                      {{
                        itemsArray.controls[i]?.get('quantity')?.errors['serverSideError']
                      }}</mat-error
                    >
                  </mat-form-field>
                </td>
              </ng-container>

              <ng-container matColumnDef="purchasePrice">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'purchase.purchasePrice' | translate }}
                </th>
                <td *matCellDef="let element; let i = dataIndex" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100" class="d-flex">
                    <input
                      class="next"
                      (change)="onPriceChange($event, i)"
                      (keydown.enter)="jumpToNext($event, i)"
                      min="0"
                      matInput
                      formControlName="purchasePrice"
                      type="number" />
                    <mat-error
                      class="text-error-wrap"
                      *ngIf="
                        (itemsArray.controls[i].get('purchasePrice').hasError('required') ||
                          itemsArray.controls[i].get('purchasePrice').hasError('gt')) &&
                        itemsArray.controls[i].get('purchasePrice').touched
                      ">
                      Enter Valid Price
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>

              <ng-container matColumnDef="vatAmount">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'purchase.vatAmt' | translate }}
                </th>
                <td *matCellDef="let element; let i = dataIndex" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100" class="d-flex">
                    <input
                      class="next"
                      tabindex="-1"
                      matInput
                      type="number"
                      readonly
                      formControlName="vatAmount" />
                    <mat-error
                      *ngIf="
                        itemsArray.controls[i].get('vatAmount')?.errors &&
                        itemsArray.controls[i].get('vatAmount').touched
                      "
                      >Error</mat-error
                    >
                  </mat-form-field>
                </td>
              </ng-container>

              <ng-container matColumnDef="discount">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'purchase.discount' | translate }}
                </th>
                <td *matCellDef="let element; let i = dataIndex" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100" class="d-flex">
                    <input
                      class="next"
                      (keydown.enter)="focusOnSearch()"
                      (change)="onDiscountChange($event, i)"
                      matInput
                      type="number"
                      formControlName="discount" />
                    <i-tabler
                      class="icon-16"
                      *ngIf="element?.get('product')?.value['isGeneralDscntMethod']"
                      name="percentage"></i-tabler>
                    <i-tabler
                      class="icon-16"
                      *ngIf="!element?.get('product')?.value['isGeneralDscntMethod']"
                      name="cash"></i-tabler>
                    <mat-error
                      *ngIf="
                        itemsArray.controls[i].get('discount')?.hasError('percentageError') &&
                        itemsArray.controls[i].get('discount').touched
                      "
                      >Max 100%</mat-error
                    >
                    <mat-error
                      *ngIf="
                        itemsArray.controls[i].get('discount')?.hasError('priceError') &&
                        itemsArray.controls[i].get('discount').touched
                      "
                      >Disc > Price</mat-error
                    >
                  </mat-form-field>
                </td>
              </ng-container>

              <ng-container matColumnDef="subtotal">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'purchase.subTotal' | translate }}
                </th>
                <td *matCellDef="let element; let i = dataIndex" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100" class="d-flex">
                    <input
                      tabindex="-1"
                      matInput
                      type="number"
                      readonly
                      formControlName="subtotal" />
                    <mat-error
                      *ngIf="
                        itemsArray.controls[i].get('subtotal')?.errors &&
                        itemsArray.controls[i].get('subtotal').touched
                      "
                      >Error</mat-error
                    >
                  </mat-form-field>
                </td>
              </ng-container>

              <ng-container matColumnDef="action">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'common.action' | translate }}
                </th>
                <td
                  class="action-link"
                  *matCellDef="let element; let i = dataIndex"
                  tabindex="-1"
                  mat-cell>
                  <a class="m-r-10 cursor-pointer" *ngIf="canDeleteProduct"
                    ><i-tabler
                      class="icon-16"
                      (click)="
                        openDeleteConfirmationDialog(
                          element.get('itemCode').value,
                          element.get('warehouseId').value,
                          element.get('unitName').value
                        )
                      "
                      name="trash"></i-tabler
                  ></a>
                  <a class="m-r-10 cursor-pointer" (click)="toggleExpansion(i)">
                    <i-tabler
                      class="icon-16"
                      *ngIf="isRowExpanded(i)"
                      name="layout-bottombar-expand"></i-tabler>
                    <i-tabler
                      class="icon-16"
                      *ngIf="!isRowExpanded(i)"
                      name="layout-navbar-expand"></i-tabler>
                  </a>
                </td>
              </ng-container>

              <ng-container matColumnDef="expandedDetail">
                <td
                  *matCellDef="let element; let i = dataIndex"
                  [attr.colspan]="columnsToDisplayWithExpand.length"
                  mat-cell>
                  <div
                    class="expanded-detail"
                    [@detailExpand]="i === expandedRowIndex ? 'expanded' : 'collapsed'">
                    <div class="d-flex gap-16">
                      <mat-list-item *ngFor="let field of expandedFieldNames">
                        <p class="text-center f-s-16" matLine>
                          {{ field.display | translate }}
                        </p>
                        <ng-container *ngIf="field?.convert; then conversion; else noconversion">
                        </ng-container>
                        <ng-template #noconversion>
                          <p class="text-center f-s-12" matLine>
                            {{ element?.get('product')?.value[field.value] }}
                          </p>
                        </ng-template>
                        <ng-template #conversion>
                          <p class="text-center f-s-12" matLine>
                            {{ field.convert[element?.get('product')?.value[field.value]] }}
                          </p>
                        </ng-template>
                      </mat-list-item>
                    </div>
                  </div>
                </td>
              </ng-container>

              <tr class="mat-row" *matNoDataRow>
                <td class="text-center text-info" [attr.colspan]="displayedColumns.length">
                  {{ 'common.noDataFound' | translate }}
                </td>
              </tr>
              <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
              <tr
                class="table-edit-element-row"
                *matRowDef="let row; columns: displayedColumns"
                mat-row></tr>
              <tr
                class="table-edit-detail-row"
                *matRowDef="let row; columns: ['expandedDetail']"
                mat-row></tr>
            </table>
          </div>
        </div>
      </div>
      <!-- main table editor -->
    </form>
    <!-- summary and payment layout -->
    <div class="row flex-wrap m-t-10">
      <div class="col-lg-6 col-md-6 order-md-first">
        <app-standard-payments
          class="m-t-10"
          #paymentForm
          [mode]="mode"
          [saleDetails]="saleDetails"
          [paymentViewData]="paymentViewData"
          [paymentViewData]="paymentViewData"
          [totalVat]="totalVat"
          [discount]="discount"
          [totalExcVatDisc]="totalExcVatDisc"
          [grandTotal]="grandTotal"></app-standard-payments>
        <!-- notes text and date -->
        <div class="row m-t-10">
          <div class="col-md-5 col-sm-5">
            <mat-label>{{ 'paymentsType.returnDate' | translate }}</mat-label>

            <mat-form-field class="w-100">
              <input
                [min]="salesDate"
                [matDatepicker]="creditNotePicker"
                matInput
                formControlName="noteIssueDate" />
              <mat-datepicker-toggle [for]="creditNotePicker" matSuffix></mat-datepicker-toggle>
              <mat-datepicker #creditNotePicker></mat-datepicker>
              <mat-error
                class="text-danger font-10"
                *ngIf="
                  salesForm?.controls['noteIssueDate']?.hasError('required') &&
                  salesForm?.controls['noteIssueDate']?.touched
                "
                >{{ 'common.required' | translate }}</mat-error
              >
            </mat-form-field>
          </div>
          <div class="col-md-5 col-sm-5">
            <mat-label>{{ 'paymentsType.returnText' | translate }}</mat-label>

            <mat-form-field class="w-100">
              <textarea
                #streetName
                #autosize="cdkTextareaAutosize"
                maxlength="100"
                type="text"
                matInput
                formControlName="noteRemark"
                cdkTextareaAutosize
                cdkAutosizeMaxRows="5"></textarea>
            </mat-form-field>
          </div>
        </div>
        <!-- notes text and date -->
      </div>
      <div class="col-lg-6 col-md-6 order-md-last">
        <app-sales-summary
          [totalExcVatDisc]="totalExcVatDisc"
          [totalVat]="totalVat"
          [discount]="discount"
          [grandTotal]="grandTotal"
          [fractionTotals]="paymentForm.fractionTotals"
          [balanceAmount]="paymentForm.balanceAmounts"
          [changeAmount]="paymentForm.changeAmounts"></app-sales-summary>
      </div>
    </div>
    <!-- summary and payment layout -->
  </mat-card>
  <!-- action buttons -->
  <div class="text-center">
    <ng-container>
      <button
        class="m-l-10"
        *ngIf="quantityUpdated"
        (click)="submitSales($event); (false)"
        type="button"
        mat-flat-button
        color="primary">
        {{ 'common.buttons.debitNoteAction' | translate }}
      </button>
      <button
        class="m-l-10"
        [routerLink]="['../../']"
        type="button"
        mat-stroked-button
        color="warn">
        {{ 'common.buttons.cancel' | translate }}
      </button>
    </ng-container>
  </div>
  <!-- action buttons -->
</ng-container>
