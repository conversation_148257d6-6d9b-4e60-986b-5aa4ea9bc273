import { Component, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormGroup } from '@angular/forms';

@Component({
  selector: 'app-pos-payments',
  templateUrl: './pos-payments.component.html',
  styleUrls: ['./pos-payments.component.scss'],
})
export class PosPaymentsComponent implements OnInit {
  paymentForm: FormGroup;
  totalAmount = 500; // Example total amount, replace with actual value

  constructor(private fb: FormBuilder) {
    this.paymentForm = this.fb.group({
      payments: this.fb.array([
        this.createPaymentGroup(0), // CASH
        this.createPaymentGroup(0), // CARD/ONLINE
      ]),
    });
  }

  ngOnInit() {
    // Initial log for debugging
    //
    console.log(this.paymentForm.value);
  }

  ngAfterViewInit() {
    // Focus the first input field when the dialog is opened
    if (this.payments.length > 0) {
      const firstInput = document.getElementById(`paymentAmount0`);
      if (firstInput) {
        firstInput.focus();
      }
    }
  }

  get payments(): FormArray {
    return this.paymentForm.get('payments') as FormArray;
  }

  clearAmount(index: number): void {
    const payments = this.paymentForm.get('payments') as FormArray;
    return payments.at(index).get('amount')?.setValue(null); // Access the first payment (CASH)
  }

  createPaymentGroup(amount: number): FormGroup {
    return this.fb.group({
      amount: [null], // Set initial amount for both
      notes: [''], // Ensure notes control is defined
    });
  }

  calculateCashChange(): number {
    const cashAmount = this.getCashAmount(); // Get the amount entered for CASH
    const totalPaid = cashAmount; // Calculate the total amount paid
    const change = totalPaid - this.totalAmount; // Calculate the change
    return change > 0 ? change : 0; // Return change if positive, otherwise return 0
  }

  calculateChange(): number {
    const cashAmount = this.getCashAmount(); // Get the amount entered for CASH
    const cardAmount = this.getCardAmount(); // Get the amount entered for CARD/ONLINE
    const totalPaid = cashAmount + cardAmount; // Calculate the total amount paid
    const change = totalPaid - this.totalAmount; // Calculate the change
    return change > 0 ? change : 0; // Return change if positive, otherwise return 0
  }

  get isCardAmountEntered(): boolean {
    const cardAmount = this.getCardAmount();
    return cardAmount > 0;
  }
  get isAmountEntered() {
    const cashAmount = this.getCashAmount();
    const cardAmount = this.getCardAmount();
    return cashAmount + cardAmount > 0;
  }

  get pendingAmount(): number {
    const cashAmount = this.getCashAmount();
    const cardAmount = this.getCardAmount();
    return this.totalAmount - (cashAmount + cardAmount); // Pending amount
  }

  get excessAmount(): number {
    return this.totalPayments - this.totalAmount; // Excess amount
  }

  get totalPayments(): number {
    const cashAmount = this.getCashAmount();
    const cardAmount = this.getCardAmount();
    return cashAmount + cardAmount; // Total payments
  }

  private getCashAmount(): number {
    const payments = this.paymentForm.get('payments') as FormArray;
    return payments.at(0).get('amount')?.value || 0; // Access the first payment (CASH)
  }

  private getCardAmount(): number {
    const payments = this.paymentForm.get('payments') as FormArray;
    return payments.at(1).get('amount')?.value || 0; // Access the second payment (CARD/ONLINE)
  }

  isCashAmountValid(): boolean {
    return this.totalPayments === this.totalAmount; // Check if total payments equal the invoice total
  }
}
