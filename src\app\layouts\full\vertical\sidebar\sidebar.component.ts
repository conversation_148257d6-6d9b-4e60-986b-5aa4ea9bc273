import { Component, OnInit } from '@angular/core';

import { PerfectScrollbarConfigInterface } from 'ngx-perfect-scrollbar';
import { NavService } from 'src/app/core/api/nav.service';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
})
export class SidebarComponent implements OnInit {
  navopt = this.navService.showClass;
  public config: PerfectScrollbarConfigInterface = {};

  constructor(public navService: NavService) {}

  ngOnInit(): void {}
}
