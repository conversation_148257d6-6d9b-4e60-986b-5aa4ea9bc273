import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { PaymentVoucher, voucherPayments, voucherType } from '../../interfaces/sales';
import { VoucherInvoice } from '../../interfaces/payment';

@Injectable({
  providedIn: 'root',
})
export class PaymentService {
  baseUrl = environment.apiUrl + 'payments/voucher';
  constructor(private http: HttpClient) {}

  createSalesVoucher(voucherDetails: PaymentVoucher) {
    return this.http.post(this.baseUrl, voucherDetails);
  }

  getVoucherForInvoice(invoiceId: number, vocherType: voucherType) {
    return this.http.get<VoucherInvoice[]>(
      this.baseUrl + `/list?invoiceId=${invoiceId}&voucherType=${vocherType}`
    );
  }

  getPaymentVouchers(vocherType: voucherType) {
    return this.http.get<voucherPayments>(this.baseUrl + `/pages?voucherType=${vocherType}`);
  }

  getPaymentVouchersByID(vocherID: string) {
    return this.http.get<PaymentVoucher>(this.baseUrl + `/${vocherID}`);
  }
}
