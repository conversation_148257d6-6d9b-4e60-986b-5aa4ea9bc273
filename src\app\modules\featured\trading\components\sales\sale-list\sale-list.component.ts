import { Directionality } from '@angular/cdk/bidi';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { CommonService } from 'src/app/core/api/common.service';
import { SalesService } from 'src/app/core/api/trading/sales.service';
import { ZatcaService } from 'src/app/core/api/trading/zatca.service';
import {
  IInvoice,
  ISalesResponse,
  SalesIntegratedCreateResponse,
  SaletransactionTypes,
} from 'src/app/core/interfaces/sales';
import { SalesParams } from 'src/app/core/models/params/salesParams';
import { MultilingualService } from 'src/app/modules/core/core/services/multilingual.service';
import { ReportService } from 'src/app/modules/featured/settings/services/report.service';
import {
  PrintDialogComponent,
  PrintDialogData,
} from 'src/app/modules/shared/components/print-dialog/print-dialog.component';

@Component({
  selector: 'app-sale-list',
  templateUrl: './sale-list.component.html',
  styleUrls: ['./sale-list.component.scss'],
})
export class SaleListComponent implements OnInit {
  @ViewChild('filter') filter: ElementRef;
  @ViewChild('searchInput') searchInput: ElementRef;
  @ViewChild(MatPaginator) set matPaginator(paginator: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = paginator;
    }
  }
  @ViewChild(MatSort) set matSort(sort: MatSort) {
    if (this.dataSource) {
      this.dataSource.sort = sort;
    }
  }
  invoices: IInvoice[];
  displayedColumns: string[];
  dataSource: MatTableDataSource<IInvoice>;
  isLoading = true;
  isReportPulling = false;
  constructor(
    public salesService: SalesService,
    public reportService: ReportService,
    private direction: Directionality,
    private zatcaService: ZatcaService,
    private translateService: MultilingualService,
    private dialog: MatDialog,
    private commonService: CommonService
  ) {}

  ngOnInit(): void {
    this.getAllSales();
    this.initColumns();
  }

  initColumns(): void {
    this.displayedColumns = this.translateService.updateDisplayedColumns([
      'action',
      'documentNumber',
      'issueDate',
      'invoiceTotal',
      'accountNumber',
      'nameArabic',
      'nameEnglish',
      'vatNumber',
      'phoneNumber',
      'paymentMethod',
      'returnStatus',
      'stage',
    ]);
  }

  getAllSales(): void {
    const salesParams = new SalesParams();
    salesParams.transactionType = SaletransactionTypes.sales;
    this.salesService.getAllSales(salesParams).subscribe((result: ISalesResponse) => {
      console.log(result, result.transactions);
      this.invoices = result.transactions;
      this.isLoading = false;
      this.dataSource = new MatTableDataSource<IInvoice>(this.invoices);
    });
  }

  applyFilter(filterValue: string): void {
    filterValue = filterValue.trim();
    filterValue = filterValue.toLowerCase();
    this.dataSource.filter = filterValue;
  }

  clearSearchInput() {
    this.searchInput.nativeElement.value = '';
    this.getAllSales();
  }

  openSalesNotes(invoiceId: number): void {
    this.salesService.openSalesNotes(invoiceId, this.direction.value);
  }

  getZatcaErrorForInvoice(documentUUID: string): void {
    this.zatcaService.getZatcaErrorForInvoice(documentUUID, this.direction.value);
  }

  getStatusClass(stage: string) {
    return this.zatcaService.getZatcaStatusClass(stage);
  }

  getXmlForInvoice(documentUUID: string): void {
    this.zatcaService.getXmlForInvoice(documentUUID, this.direction.value);
  }

  transactionReport(transactionId: IInvoice): void {
    // Show the print dialog
    this.showPrintDialog(transactionId);
  }

  /**
   * Shows the print dialog for the selected invoice
   */
  private showPrintDialog(invoiceData: IInvoice): void {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.position = { top: '10vh' };
    dialogConfig.minWidth = '400px';
    dialogConfig.maxWidth = '500px';
    dialogConfig.panelClass = 'green_theme';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = true;
    dialogConfig.direction = this.direction.value;

    const dialogData: PrintDialogData = {
      documentResponse: invoiceData,
    };

    dialogConfig.data = dialogData;

    const dialogRef = this.dialog.open(PrintDialogComponent, dialogConfig);

    dialogRef.componentInstance.printRequested.subscribe((shouldPrint: boolean) => {
      if (shouldPrint) {
        this.handlePrintRequest(invoiceData);
      }
    });

    dialogRef.afterClosed().subscribe((shouldPrint: boolean | null) => {
      console.log('Print dialog closed, user choice:', shouldPrint);
    });
  }

  /**
   * Handles the print request - opens Invoice Demo component in new window using common service
   */
  private handlePrintRequest(invoiceData: IInvoice): void {
    if (!invoiceData.documentId) {
      console.error('No document ID available for printing');
      return;
    }

    console.log('Opening Invoice Demo in new window with params:', {
      documentId: invoiceData.documentId,
      transactionType: invoiceData.transactionType || 'SALES',
    });

    // Use the common service to open invoice in new window
    this.commonService.openInvoiceInNewWindow(invoiceData.documentId, invoiceData.transactionType);
  }
}
