import { Component, EventEmitter, Inject, Output } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-bulk-edit-products',
  templateUrl: './delete-confirmation.component.html',
  styleUrls: ['./delete-confirmation.component.scss'],
})
export class DeleteConfirmationComponent {
  @Output() accepted: EventEmitter<void> = new EventEmitter();
  constructor(
    public dialogRef: MatDialogRef<DeleteConfirmationComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  onNoClick(event: Event): void {
    event.preventDefault();
    this.dialogRef.close(false);
  }

  onSubmit(event: Event) {
    event.preventDefault();
    this.accepted.emit();
    this.dialogRef.close();
  }
}
