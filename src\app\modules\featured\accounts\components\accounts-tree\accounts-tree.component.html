<div class="row no-gutters">
  <div class="m-t-10 col-md-6 col-lg-6 col-sm-12 d-flex">
    <mat-form-field class="w-100">
      <input
        #searchInput
        [(ngModel)]="filterText"
        (input)="applyFilter()"
        type="text"
        matInput
        autocomplete="off" />
      <i-tabler class="icon-16" matPrefix name="search"></i-tabler>
      <a class="cursor-pointer" *ngIf="searchInput?.value" (click)="clearSearchInput()" matSuffix>
        <i-tabler class="icon-16 error" name="X"></i-tabler>
      </a>
    </mat-form-field>
  </div>
</div>
<mat-tree [dataSource]="dataSource" [treeControl]="treeControl">
  <mat-tree-node *matTreeNodeDef="let node" matTreeNodePadding>
    <ng-container *ngIf="node.accountType !== 'DETAILED' && node.expandable">
      <button
        class="m-r-10 {{ node.color }}"
        [attr.aria-label]="'Toggle ' + node.nameEnglish"
        (click)="treeControl.toggle(node)"
        mat-icon-button>
        <i-tabler
          class="mat-icon-rtl-mirror"
          class="icon-10"
          *ngIf="treeControl.isExpanded(node)"
          name="circle-arrow-up"></i-tabler>
        <i-tabler
          class="mat-icon-rtl-mirror"
          class="icon-10"
          *ngIf="!treeControl.isExpanded(node)"
          name="circle-arrow-down"></i-tabler>
      </button>
    </ng-container>
    <ng-container *ngIf="!node.expandable">
      <button
        class="m-r-10 {{ node.color }}"
        [attr.aria-label]="'Toggle ' + node.nameEnglish"
        mat-icon-button>
        <i-tabler class="mat-icon-rtl-mirror" class="icon-10" name="circle-dot"></i-tabler>
      </button>
    </ng-container>

    <span class="node-text" (mouseenter)="showIcons = true" (mouseleave)="showIcons = false">
      <span>{{ node.nameArabic }} - {{ node.nameEnglish }} - {{ node.accountNumber }}</span>
      <!-- <ng-container *ngIf="showIcons">
        <i-tabler
          class="add-icon icon-16"
          (click)="openModal(node.accountId, 'Add')"
          name="plus"></i-tabler>
        <i-tabler class="add-icon icon-16" name="eye"></i-tabler>
        <i-tabler class="add-icon icon-16" name="edit"></i-tabler>
        <i-tabler class="add-icon icon-16" name="trash"></i-tabler>
      </ng-container> -->
    </span>
  </mat-tree-node>
  <mat-tree-node *matTreeNodeDef="let node; when: hasChild" matTreeNodePadding>
    <ng-container *ngIf="node.accountType !== 'DETAILED' && node.expandable">
      <button
        class="m-r-10 {{ node.color }}"
        [attr.aria-label]="'Toggle ' + node.nameEnglish"
        (click)="treeControl.toggle(node)"
        mat-icon-button>
        <i-tabler
          class="mat-icon-rtl-mirror"
          class="icon-10"
          *ngIf="treeControl.isExpanded(node)"
          name="circle-arrow-up"></i-tabler>
        <i-tabler
          class="mat-icon-rtl-mirror"
          class="icon-10"
          *ngIf="!treeControl.isExpanded(node)"
          name="circle-arrow-down"></i-tabler>
      </button>
    </ng-container>
    <span class="node-text" (mouseenter)="showIcons = true" (mouseleave)="showIcons = false">
      <span>{{ node.nameArabic }} - {{ node.nameEnglish }} - {{ node.accountNumber }} </span>
      <!-- <ng-container *ngIf="showIcons">
        <i-tabler
          class="add-icon icon-16"
          (click)="openModal(node.accountId, 'Add')"
          name="plus"></i-tabler>
        <i-tabler class="add-icon icon-16" name="eye"></i-tabler>
        <i-tabler class="add-icon icon-16" name="edit"></i-tabler>
        <i-tabler class="add-icon icon-16" name="trash"></i-tabler>
      </ng-container> -->
    </span>
  </mat-tree-node>
</mat-tree>
