import { Component, EventEmitter, Output } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-unsaved-changes-dialog',
  templateUrl: './unsaved-changes-dialog.component.html',
  styleUrls: ['./unsaved-changes-dialog.component.scss'],
})
export class UnsavedChangesDialogComponent {
  @Output() accepted: EventEmitter<boolean> = new EventEmitter();
  constructor(public dialogRef: MatDialogRef<UnsavedChangesDialogComponent>) {}

  onStay(): void {
    this.accepted.emit(false);
    this.dialogRef.close(false); // Close the dialog and pass false to indicate "stay"
  }

  onLeave(): void {
    this.accepted.emit(true);
    this.dialogRef.close(true); // Close the dialog and pass true to indicate "leave"
  }
}
