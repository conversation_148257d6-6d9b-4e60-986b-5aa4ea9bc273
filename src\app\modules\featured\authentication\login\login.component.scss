@import 'variables';
.white-background {
  background-color: $white;
}

// Bilingual text styles
// Stacked title for header
.stacked-title {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: 4px;
}

.en-title {
  font-weight: 500;
  font-size: 18px;
  color: #0056b3;
}

.ar-title {
  font-family: 'Arial', sans-serif;
  direction: rtl;
  font-size: 16px;
  color: #0056b3;
}

// Override the or-border to use the same blue color
:host ::ng-deep .or-border {
  &:before,
  &:after {
    background: #0056b3 !important; // Use the same blue as the company name
    height: 2px; // Make the line slightly thicker
    opacity: 0.7; // Add some transparency
  }
}

.bilingual-button,
.bilingual-copyright,
.bilingual-copyright-subtext {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: 8px;
}

// Left-justify labels
.bilingual-label {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start; // Left-justify
  text-align: left;
  gap: 8px;
  margin-bottom: 4px;
  width: 100%; // Ensure full width
}

// Error messages use default mat-error styling

.en-text {
  font-weight: 500;
}

.ar-text {
  font-family: 'Arial', sans-serif;
  direction: rtl;
}

// Add a small separator between languages
.bilingual-label .en-text::after,
.bilingual-button .en-text::after {
  content: '|';
  margin-left: 8px;
  color: rgba(0, 0, 0, 0.3);
  font-weight: 300;
}

// Language selector removed

// Login page specific footer styles
.copyright-footer-actions {
  margin-top: 20px;
  padding: 10px 0;
  text-align: center;
  width: 100%;
  display: block;
  border-top: 1px solid rgba(0, 86, 179, 0.3); // Match the blue color with transparency
}

.copyright-text {
  color: rgba(0, 0, 0, 0.7);
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 0.2px;
  margin: 0 0 5px 0;
  text-align: center;
}

.copyright-subtext {
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 0.2px;
  margin: 0;
  text-align: center;
}

// RTL support for Arabic-preferred mode
:host-context([dir='rtl']) {
  .bilingual-title,
  .bilingual-button,
  .bilingual-copyright,
  .bilingual-copyright-subtext {
    flex-direction: row-reverse;
  }

  // For RTL, right-justify the labels
  .bilingual-label {
    flex-direction: row-reverse;
    justify-content: flex-start; // Still left-justify in the container
    text-align: right;
  }

  .en-text {
    font-weight: normal;
  }

  .ar-text {
    font-weight: 500;
  }

  // Remove the separator from English and add to Arabic in RTL mode
  .bilingual-label .en-text::after,
  .bilingual-button .en-text::after {
    content: '';
    margin-left: 0;
  }

  .bilingual-label .ar-text::after,
  .bilingual-button .ar-text::after {
    content: '|';
    margin-right: 8px;
    color: rgba(0, 0, 0, 0.3);
    font-weight: 300;
  }
}

// Dark theme support
:host-context(.dark-theme) {
  // Override the or-border for dark theme
  ::ng-deep .or-border {
    &:before,
    &:after {
      background: rgba(0, 86, 179, 0.8) !important; // Brighter blue for dark theme
    }
  }

  .copyright-footer-actions {
    border-top: 1px solid rgba(0, 86, 179, 0.4); // Match the blue color with transparency
  }

  .copyright-text {
    color: rgba(255, 255, 255, 0.7);
  }

  .copyright-subtext {
    color: rgba(255, 255, 255, 0.5);
  }
}
