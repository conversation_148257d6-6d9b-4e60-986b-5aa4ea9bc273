import { Directive, HostListener, Input } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';

@Directive({
  selector: '[appCopyClick]',
})
export class CopyClickDirective {
  @Input() copyValue: string | number | undefined | null = '';
  @HostListener('click', ['$event']) onClick($event: MouseEvent): void {
    if (this.copyValue && this.copyValue?.toString().length > 0) {
      this.copyField(this.copyValue.toString());
    }
  }

  constructor(private snackBar: MatSnackBar) {}

  private copyField(value: string): void {
    const trimmedValue = value.replace(/\s/g, '').trim();
    navigator.clipboard
      .writeText(trimmedValue)
      .then(() => {
        // Success! You can provide user feedback here upon successful copy
        this.snackBar.open(`Copied : ${trimmedValue}`, null, {
          duration: 2000,
          verticalPosition: 'top',
          horizontalPosition: 'center',
          panelClass: '',
        });
      })
      .catch(err => {
        // Handle errors if the copy operation fails
        console.error('Unable to copy:', err);
      });
  }
}
