import { AfterViewInit, Directive, Host, Input, OnDestroy } from '@angular/core';
import { NgControl } from '@angular/forms';
import { MatSelect } from '@angular/material/select';
import { Subscription } from 'rxjs';
import { ActionType } from 'src/app/core/enums/actionType';

@Directive({
  selector: '[formControlName][appPreselectOption]',
})
export class PreselectoptionDirective implements AfterViewInit, OnD<PERSON>roy {
  @Input() mode: ActionType;
  private optionsChangesSubscription: Subscription;
  constructor(@Host() private matSelect: MatSelect, private ngControl: NgControl) {}

  ngAfterViewInit(): void {
    this.optionsChangesSubscription = this.matSelect.options.changes.subscribe(() => {
      if (this.mode === ActionType.create) {
        this.setDropDown();
      }
    });
  }

  ngOnDestroy(): void {
    if (this.optionsChangesSubscription) {
      this.optionsChangesSubscription.unsubscribe();
    }
  }

  private setDropDown() {
    console.log('PreselectoptionDirective', this.matSelect, this.ngControl);
    if (this.ngControl && this.ngControl?.value === null && this.matSelect?.options?.length > 0) {
      const firstOptionValue = this.matSelect?.options?.first?.value;
      if (firstOptionValue) {
        this.ngControl.control.setValue(firstOptionValue);
      }
    }
  }
}
