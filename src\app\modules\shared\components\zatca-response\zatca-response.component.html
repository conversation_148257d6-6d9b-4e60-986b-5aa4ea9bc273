<app-dialog-header [title]="'ZATCA E-Invoice Response'"></app-dialog-header>

<mat-dialog-content>
  <h2
    class="text-center text-primary"
    *ngIf="data?.zatcaReportingStatus?.validationResults === null">
    {{ 'sales.zatcaInvoiceNoErrors' | translate }}
  </h2>
  <div class="bg-warning m-b-10" *ngIf="hasWarnings && !hasErrors && data?.viewOnly">
    <mat-checkbox [(ngModel)]="acceptWarning">
      {{ 'sales.zatcaInvoiceWarnings' | translate }}</mat-checkbox
    >
    <div class="text-center">
      <button
        class="m-l-10"
        [disabled]="!acceptWarning"
        (click)="sendToZatca()"
        mat-raised-button
        color="primary">
        {{ 'sales.zatcaInvoiceResubmit' | translate }}
      </button>
    </div>
  </div>

  <div class="bg-primary m-b-10" *ngIf="!hasWarnings && hasErrors">
    <p>
      {{ 'sales.zatcaInvoiceError' | translate }}
    </p>
  </div>

  <div class="bg-primary m-b-10" *ngIf="hasWarnings && hasErrors">
    <p>{{ 'sales.zatcaInvoiceError' | translate }}</p>
  </div>

  <mat-accordion>
    <ng-container
      *ngIf="data?.zatcaReportingStatus && data?.zatcaReportingStatus.validationResults">
      <ng-container *ngFor="let key of getObjectKeys(data?.zatcaReportingStatus.validationResults)">
        <mat-expansion-panel *ngIf="isObject(data?.zatcaReportingStatus.validationResults[key])">
          <mat-expansion-panel-header>
            <mat-panel-title class="text-primary">
              {{ key | spacePipe }} ({{ data?.zatcaReportingStatus.validationResults[key].length }})
            </mat-panel-title>
          </mat-expansion-panel-header>
          <mat-expansion-panel-content>
            <p *ngFor="let message of data?.zatcaReportingStatus.validationResults[key]">
              {{ message.message }}
            </p>
          </mat-expansion-panel-content>
        </mat-expansion-panel>
      </ng-container>
    </ng-container>
  </mat-accordion>

  <!-- <div *ngFor="let key of getObjectKeys(data?.zatcaReportingStatus)">
  <div *ngIf="!isObject(data?.zatcaReportingStatus[key])">
    <strong>{{ key | spacePipe }}:</strong> {{ data?.zatcaReportingStatus[key] }}
  </div>
</div> -->
</mat-dialog-content>
