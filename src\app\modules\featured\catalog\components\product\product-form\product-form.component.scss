.mat-mdc-card-avatar {
  height: 150px;
  width: 150px;
  flex-shrink: 0;
  object-fit: cover;
  border-radius: 0;
}

.button-centre {
  display: flex;
  justify-content: center;
  padding-top: 10px;
}

.example-action-buttons {
  display: flex;
  gap: 10px;
  padding-bottom: 20px;
}

.radio-button-margin {
  margin: 10px;
}

.p-l-5 {
  padding-left: 5px !important;
}

.line-height {
  line-height: 40px;
}

.mat-column-unitBarcode {
  width: auto;
  min-width: 180px;
}

// Set width for all number input columns to fit 10 digits
.mat-column-costPrice,
.mat-column-purchasePrice,
.mat-column-openPurchasePrice,
.mat-column-wholesalePrice,
.mat-column-distributorPrice,
.mat-column-retailPrice,
.mat-column-transportCost,
.mat-column-discount,
.mat-column-unitofmeasure,
.mat-column-isGeneralDscntMethod,
.mat-column-profit {
  min-width: 120px;
  max-width: 120px;
}

// Ensure input fields fill the cell
.mat-form-field {
  width: 100%;
}

.mdc-data-table__cell,
.mdc-data-table__header-cell {
  padding: 0 1px 0 0px;
}

.mat-mdc-header-cell {
  text-align: center !important;
}

.mat-mdc-cell {
  text-align: center;
  justify-content: center;
}

// Optionally keep a fixed width for action column
.mat-column-action {
  min-width: 80px;
  max-width: 80px;
  width: 80px;
}

// Responsive table wrapper for horizontal scroll
.table-responsive {
  overflow-x: auto;
}

.image-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.cropped-image {
  max-width: 100%;
  max-height: 200px;
  border: 1px solid #ccc;
  margin-top: 10px;
}

.image-cropper-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

input[type='file'] {
  margin-top: 20px;
}

/* Reduce the size of the cropping area */
.cropper-crop-box {
  border: 2px solid #ff4081; /* Optional: Customize the border color */
}

/* Reduce the size of the resize handles */
.cropper-face,
.cropper-line {
  background-color: #ff4081; /* Optional: Customize the handle color */
}

.icon-image {
  width: 50px; /* Set the width */
  height: 50px; /* Set the height */
  border: 1px solid #000; /* Optional: add a border */
}

.cropped-image {
  max-width: 100%;
  max-height: 200px;
  border: 1px solid #000;
  margin-top: 10px;
}

// .mat-column-unitofmeasure {}
// .mat-column-purchasePrice {}
// .mat-column-openPurchasePrice {}
// .mat-column-wholesalePrice {}
// .mat-column-distributorPrice {}
// .mat-column-retailPrice {}

// Optionally keep a fixed width for action column
.mat-column-action {
  min-width: 80px;
  max-width: 80px;
  width: 80px;
}

// Responsive table wrapper for horizontal scroll
.table-responsive {
  overflow-x: auto;
}
