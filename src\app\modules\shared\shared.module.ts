import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { TablerIconsModule } from 'angular-tabler-icons';
import * as TablerIcons from 'angular-tabler-icons/icons';
import { ImageCropperModule } from 'ngx-image-cropper';
import { NgxLoadingModule, ngxLoadingAnimationTypes } from 'ngx-loading';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import {
  PERFECT_SCROLLBAR_CONFIG,
  PerfectScrollbarConfigInterface,
  PerfectScrollbarModule,
} from 'ngx-perfect-scrollbar';
import { NgScrollbarModule } from 'ngx-scrollbar';
import { NgxScrollTopModule } from 'ngx-scrolltop';
import { ToastrModule } from 'ngx-toastr';
import { QRCodeModule } from 'angularx-qrcode';
import { BranchApiService } from 'src/app/core/api/company/branch-api.service';
import { ReportApiService } from 'src/app/core/api/report/report-api.service';
import { CustomerApiService } from 'src/app/core/api/trading/customer-api.service';
import { FullComponent } from 'src/app/layouts/full/full.component';
import { AppHorizontalHeaderComponent } from 'src/app/layouts/full/horizontal/header/header.component';
import { AppHorizontalNavItemComponent } from 'src/app/layouts/full/horizontal/sidebar/nav-item/nav-item.component';
import { AppHorizontalSidebarComponent } from 'src/app/layouts/full/horizontal/sidebar/sidebar.component';
import { AppBreadcrumbComponent } from 'src/app/layouts/full/shared/breadcrumb/breadcrumb.component';
import { AppNavItemComponent } from 'src/app/layouts/full/vertical/sidebar/nav-item/nav-item.component';
import { SidebarComponent } from 'src/app/layouts/full/vertical/sidebar/sidebar.component';
import { HasPermissionDirective } from 'src/app/modules/shared/directives/has-permission.directive';
import { HasRoleDirective } from 'src/app/modules/shared/directives/has-role.directive';
import { DataPropertyGetterPipe } from 'src/app/modules/shared/pipes/data-property-getter.pipe';
import { BranchService } from '../featured/settings/services/branch.service';
import { ReportService } from '../featured/settings/services/report.service';
import { MaterialModule } from '../material/material.module';
import { AccordionAnchorDirective, AccordionDirective, AccordionLinkDirective } from './accordion';
import { AccessDenialComponent } from './components/access-denial/access-denial.component';
import { ActionButtonsComponent } from './components/action-buttons/action-buttons.component';
import { AddressComponent } from './components/address/address.component';
import { CompareResultsComponent } from './components/compare-results/compare-results.component';
import { ConfirmDialogComponent } from './components/confirm-dialog/confirm-dialog.component';
import { CreateActionComponent } from './components/create-action/create-action.component';
import { DashboardModulesHolderComponent } from './components/dashboard-modules-holder/dashboard-modules-holder.component';
import { DeleteConfirmationComponent } from './components/delete-confirmation/delete-confirmation.component';
import { DialogHeaderComponent } from './components/dialog-header/dialog-header.component';
import { LogoutDialogComponent } from './components/logout-dialog/logout-dialog.component';
import { PaymentsPostSaleComponent } from './components/payments-post-sale/payments-post-sale.component';
import { ProductGetAllComponent } from './components/product-search/product-get-all/product-get-all.component';
import { ProductSearchSelectionComponent } from './components/product-search/product-search-selection/product-search-selection.component';
import { ProfileComponent } from './components/profile/profile.component';
import { SalesSummaryComponent } from './components/sales-summary/sales-summary.component';
import { SearchboxComponent } from './components/searchbox/searchbox.component';
import { SelectCheckAllComponent } from './components/select-check-all/select-check-all.component';
import { ServerErrorComponent } from './components/server-error/server-error.component';
import { StandardPaymentsComponent } from './components/standard-payments/standard-payments.component';
import { TableComponent } from './components/table/table.component';
import { VoucherSummaryComponent } from './components/voucher-summary/voucher-summary.component';
import { XmlViewerComponent } from './components/xml-viewer/xml-viewer.component';
import { ZatcaInvoiceStatusComponent } from './components/zatca-invoice-status/zatca-invoice-status.component';
import { ZatcaResponseComponent } from './components/zatca-response/zatca-response.component';
import { CopyClickDirective } from './directives/copy-click.directive';
import { DropDownTooltipDirective } from './directives/dropDownTooltip.directive';
import { ElevatecardDirective } from './directives/elevatecard.directive';
import { HasRoleQualifierDirective } from './directives/has-role-qualifier.directive';
import { HyphenDirective } from './directives/hyphen.directive';
import { MaxlengthDirective } from './directives/maxlength.directive';
import { PreselectoptionDirective } from './directives/preselectoption.directive';
import { TooltipDirective } from './directives/tooltip.directive';
import { HorizontalMenuItems } from './menu-items/horizontal-menu-items';
import { MenuItems } from './menu-items/menu-items';
import { Alphanumeric } from './pipes/alphanumeric.directive';
import { HasRoleQualifierPipe } from './pipes/has-role-qualifier.pipe';
import { LastWordPipe } from './pipes/last-word.pipe';
import { LocalizedPipe } from './pipes/localized.pipe';
import { MaxlengthPipe } from './pipes/maxlength.pipe';
import { SafehtmlPipe } from './pipes/safehtml.pipe';
import { SpacePipe } from './pipes/space-pipe.';
import { SpinnerComponent } from './spinner.component';
import { UnsavedChangesDialogComponent } from './components/unsaved-changes-dialog/unsaved-changes-dialog.component';
import { MatTableKeyboardNavigationDirective } from './directives/tablekeynavigator.directive';
import { PosComponent } from './components/pos/pos.component';
import { PosPaymentsComponent } from './components/pos-payments/pos-payments.component';
import { InvoiceTemplateComponent } from './components/invoice-template/invoice-template.component';
import { PrintDialogComponent } from './components/print-dialog/print-dialog.component';
import { BranchSwitcherDialogComponent } from './components/branch-switcher-dialog/branch-switcher-dialog.component';
const DEFAULT_PERFECT_SCROLLBAR_CONFIG: PerfectScrollbarConfigInterface = {
  suppressScrollX: true,
  wheelSpeed: 2,
  wheelPropagation: true,
};

@NgModule({
  imports: [
    CommonModule,
    MaterialModule,
    RouterModule,
    ReactiveFormsModule,
    PerfectScrollbarModule,
    FormsModule,
    TranslateModule,
    ToastrModule.forRoot({
      timeOut: 5000,
      closeButton: true,
      positionClass: 'toast-top-full-width',
      progressBar: false,
      progressAnimation: 'decreasing',
      preventDuplicates: true,
    }),
    FlexLayoutModule,
    ImageCropperModule,
    NgxScrollTopModule,
    NgxMatSelectSearchModule,
    TablerIconsModule.pick(TablerIcons),
    NgScrollbarModule,
    NgxLoadingModule.forRoot({
      animationType: ngxLoadingAnimationTypes.threeBounce,
      backdropBackgroundColour: 'rgba(0,0,0,0.1)',
      backdropBorderRadius: '4px',
      primaryColour: '#0a7ea4',
      secondaryColour: '#0a7ea4',
      tertiaryColour: '#0a7ea4',
    }),
    QRCodeModule,
  ],
  declarations: [
    AccordionAnchorDirective,
    AccordionLinkDirective,
    AccordionDirective,
    SpinnerComponent,
    ServerErrorComponent,
    TableComponent,
    DataPropertyGetterPipe,
    HasPermissionDirective,
    HasRoleDirective,
    LogoutDialogComponent,
    FullComponent,
    ProfileComponent,
    ElevatecardDirective,
    SelectCheckAllComponent,
    ConfirmDialogComponent,
    DashboardModulesHolderComponent,
    AddressComponent,
    SearchboxComponent,
    AccessDenialComponent,
    HasRoleQualifierDirective,
    HasRoleQualifierPipe,
    TooltipDirective,
    DropDownTooltipDirective,
    DeleteConfirmationComponent,
    ProductSearchSelectionComponent,
    ActionButtonsComponent,
    PaymentsPostSaleComponent,
    AppHorizontalHeaderComponent,
    AppHorizontalSidebarComponent,
    AppHorizontalNavItemComponent,
    AppBreadcrumbComponent,
    SidebarComponent,
    AppNavItemComponent,
    ProductGetAllComponent,
    CreateActionComponent,
    CompareResultsComponent,
    HyphenDirective,
    StandardPaymentsComponent,
    SalesSummaryComponent,
    LocalizedPipe,
    VoucherSummaryComponent,
    CopyClickDirective,
    PreselectoptionDirective,
    LastWordPipe,
    XmlViewerComponent,
    ZatcaInvoiceStatusComponent,
    ZatcaResponseComponent,
    SpacePipe,
    MaxlengthPipe,
    MaxlengthDirective,
    SafehtmlPipe,
    Alphanumeric,
    DialogHeaderComponent,
    UnsavedChangesDialogComponent,
    MatTableKeyboardNavigationDirective,
    PosComponent,
    PosPaymentsComponent,
    InvoiceTemplateComponent,
    PrintDialogComponent,
    BranchSwitcherDialogComponent,
  ],
  exports: [
    MaterialModule,
    AccordionAnchorDirective,
    AccordionLinkDirective,
    AccordionDirective,
    SpinnerComponent,
    ReactiveFormsModule,
    FormsModule,
    TableComponent,
    TranslateModule,
    HasPermissionDirective,
    HasRoleDirective,
    LogoutDialogComponent,
    FullComponent,
    ImageCropperModule,
    NgxScrollTopModule,
    ElevatecardDirective,
    SelectCheckAllComponent,
    ConfirmDialogComponent,
    DashboardModulesHolderComponent,
    AddressComponent,
    PerfectScrollbarModule,
    SearchboxComponent,
    AccessDenialComponent,
    HasRoleQualifierPipe,
    NgxMatSelectSearchModule,
    TooltipDirective,
    DropDownTooltipDirective,
    DeleteConfirmationComponent,
    ProductSearchSelectionComponent,
    ActionButtonsComponent,
    PaymentsPostSaleComponent,
    AppHorizontalHeaderComponent,
    TablerIconsModule,
    AppHorizontalSidebarComponent,
    AppHorizontalNavItemComponent,
    AppBreadcrumbComponent,
    SidebarComponent,
    AppNavItemComponent,
    ProductGetAllComponent,
    CreateActionComponent,
    NgScrollbarModule,
    CompareResultsComponent,
    HyphenDirective,
    StandardPaymentsComponent,
    SalesSummaryComponent,
    LocalizedPipe,
    VoucherSummaryComponent,
    CopyClickDirective,
    PreselectoptionDirective,
    LastWordPipe,
    XmlViewerComponent,
    ZatcaInvoiceStatusComponent,
    SpacePipe,
    MaxlengthPipe,
    MaxlengthDirective,
    SafehtmlPipe,
    Alphanumeric,
    DialogHeaderComponent,
    UnsavedChangesDialogComponent,
    MatTableKeyboardNavigationDirective,
    PosComponent,
    PosPaymentsComponent,
    InvoiceTemplateComponent,
    PrintDialogComponent,
    QRCodeModule,
    BranchSwitcherDialogComponent,
  ],
  providers: [
    MenuItems,
    HorizontalMenuItems,
    CustomerApiService,
    BranchApiService,
    BranchService,
    ReportApiService,
    ReportService,
    {
      provide: PERFECT_SCROLLBAR_CONFIG,
      useValue: DEFAULT_PERFECT_SCROLLBAR_CONFIG,
    },
  ],
})
export class SharedModule {}
