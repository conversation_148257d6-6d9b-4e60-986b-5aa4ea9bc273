import { HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { CostCentresApiService } from 'src/app/core/api/accounts/cost-centres-api.service';

@Injectable({
  providedIn: 'root',
})
export class CostCentresService {
  constructor(private costCentresApiService: CostCentresApiService) {}

  getAllCostCentres(): Observable<any> {
    const params = new HttpParams();

    return this.costCentresApiService.getAllCostCentres().pipe(map((response: any) => response));
  }
}
