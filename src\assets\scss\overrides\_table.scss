.mdc-data-table__cell,
.mdc-data-table__header-cell {
  padding: 16px;
}

// .mdc-data-table__cell,
// .mdc-data-table__header-cell {
//   border-bottom-color: $borderColor;
// }

.mat-mdc-table .mdc-data-table__row {
  height: 32px;
}

.auto-complete-search .mat-mdc-table .mdc-data-table__row {
  height: auto;
}

.table-responsive {
  margin-top: 10px;
}

.mat-mdc-table {
  background: $backgroundcolor;
}

.mdc-data-table__header-cell {
  color: #fff;
}

.mat-mdc-paginator {
  background: none;
  color: rgba(0, 0, 0, 0.87);
}

.mat-expansion-panel {
  background: none;
  color: rgba(0, 0, 0, 0.87);
}

// tr.mat-mdc-row:hover {
//   background: $background !important;
// }

tr.mat-mdc-row:focus {
  background: #dae8f5 !important;
  outline: none !important;
  transform: scale(1);
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
}

.auto-complete-searchs {
  .mdc-data-table__cell,
  .mdc-data-table__header-cell {
    border-bottom-width: 0px;
    border-bottom-style: none;
  }

  .mat-row,
  .mat-mdc-header-row {
    display: flex;
  }

  mat-option {
    display: table-row;
  }

  mat-option .mat-mdc-row {
    display: flex;
  }

  .mat-mdc-cell,
  .mat-mdc-header-cell {
    word-wrap: break-word !important;
    white-space: pre-line !important;
    flex: 1;
  }
}

.auto-complete-search {
  .mdc-data-table__cell,
  .mdc-data-table__header-cell {
    border-bottom-width: 0px;
    border-bottom-style: none;
  }

  .mat-row,
  .mat-mdc-header-row {
    display: flex;
    width: 100%; /* Ensure full width */
  }

  mat-option {
    display: table-row;
  }

  mat-option .mat-mdc-row {
    display: flex;
  }

  // mat-option {
  //   display: flex; /* Use flex to align items */
  //   width: 100%; /* Ensure it takes full width */
  // }

  // .option-content {
  //   display: flex; /* Use flex for the option content */
  //   width: 100%; /* Ensure it takes full width */
  // }

  // .mat-mdc-cell,
  // .mat-mdc-header-cell {
  //   word-wrap: break-word !important;
  //   white-space: pre-line !important;
  //   flex: 1; /* Allow cells to stretch */
  // }
}

// auto complete search

.loading-option {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.loader-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 100%;
  height: 100%;
  text-align: center;
}

.spinner-search {
  margin-top: 2px; /* Adjust this value if needed */
}

.text-center-no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  width: 100%;
}

.dialog-container {
  max-height: 500px; /* Maximum height */
  overflow-y: auto; /* Allow scrolling if content exceeds max height */
}

.mat-cell:focus {
  outline: none; /* Remove default focus outline */
  background-color: rgba(0, 0, 255, 0.1); /* Light blue background for focused row */
}
