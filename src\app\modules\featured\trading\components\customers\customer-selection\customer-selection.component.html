<form
  [formGroup]="customerSelectionForm"
  [ngClass]="{ readOnly: hasCustomerData }"
  autocomplete="off">
  <div class="row no-gutters">
    <!-- customer type -->
    <div class="col-md-2 col-lg-2 col-sm-12 p-2">
      <mat-label>{{ 'customer.customerSelection' | translate }}</mat-label>
      <mat-form-field class="w-100">
        <mat-select formControlName="existingClient">
          <mat-option *ngFor="let customerType of customerTypes" [value]="customerType.value">
            {{ customerType.display | translate }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <!-- existing customer search -->
    <div class="col-md-10 col-lg-10 col-sm-12 p-2" *ngIf="isExistingCustomer">
      <form [formGroup]="autoCompleteInput" autocomplete="off">
        <mat-label>{{ 'customer.customerSelection' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <div class="input-with-icons">
            <input
              class="text-primary"
              #userInput
              [matAutocomplete]="auto"
              type="text"
              formControlName="userInput"
              placeholder="{{ 'placeHolder.customerSearch' | translate }}"
              matInput />
            <div class="icon-container" *ngIf="!isLoading">
              <!-- full customer scan -->
              <a
                class="cursor-pointer clickable-button"
                *ngIf="isCustomerSelected"
                (click)="onViewCustomerSelection($event); (false)"
                ><i-tabler class="icon-16" name="eye"></i-tabler
              ></a>
              <a
                class="cursor-pointer"
                *ngIf="!hasCustomerData"
                (click)="getAllCustomers($event); (false)"
                matSuffix>
                <i-tabler class="icon-16" name="database-search"></i-tabler>
              </a>
              <!-- full customer scan -->
              <a
                class="cursor-pointer"
                *ngIf="!hasCustomerData"
                (click)="clearSelections($event)"
                matSuffix>
                <i-tabler class="icon-16 text-error" name="X"></i-tabler>
              </a>
            </div>
          </div>
          <mat-autocomplete
            class="new"
            #autoComplete
            #auto="matAutocomplete"
            [displayWith]="displayFn.bind(this)"
            (opened)="keyboardNavDirective?.onAutocompleteOpened()">
            <mat-option *ngIf="itemSource?.data?.length > 0; else noData">
              <div class="table-responsive">
                <table
                  class="w-100 no-wrap"
                  #table
                  #searchTable
                  [dataSource]="itemSource"
                  mat-table
                  matSort>
                  <ng-container matColumnDef="nameArabic">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'common.field.nameArabic' | translate }}
                    </th>
                    <td *matCellDef="let element" mat-cell>
                      {{ element.nameArabic }}
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="nameEnglish">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'common.field.nameEnglish' | translate }}
                    </th>
                    <td *matCellDef="let element" mat-cell>
                      {{ element.nameEnglish }}
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="vatNumber">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'common.field.vatNo' | translate }}
                    </th>
                    <td *matCellDef="let element" mat-cell>
                      {{ element.vatNumber }}
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="accountNumber">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'common.field.accountNumber' | translate }}
                    </th>
                    <td *matCellDef="let element" mat-cell>
                      {{ element.accountNumber }}
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="phoneNumber">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'common.field.phone' | translate }}
                    </th>
                    <td *matCellDef="let element" mat-cell>
                      {{ element.phoneNumber }}
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="emailId">
                    <th *matHeaderCellDef mat-header-cell mat-sort-header>
                      {{ 'common.field.email' | translate }}
                    </th>
                    <td *matCellDef="let element" mat-cell>
                      {{ element.emailId }}
                    </td>
                  </ng-container>

                  <tr *matHeaderRowDef="displayedSearchColumns" mat-header-row></tr>
                  <tr
                    *matRowDef="let row; columns: displayedSearchColumns"
                    [rowModel]="row"
                    [matTable]="table"
                    [focusFirstOption]="true"
                    [selectOnFocus]="true"
                    [ngClass]="{ selected: selection.isSelected(row) }"
                    [appMatTableKeyboardNavigation]="selection"
                    (click)="onSelection(row); $event.preventDefault()"
                    (rowSelected)="onSelection($event)"
                    mat-row></tr>
                </table>
              </div>
            </mat-option>
            <ng-template #noData>
              <mat-option *ngIf="resultNotFound" disabled>
                <div class="text-center-no-data bg-light-error" *ngIf="resultNotFound">
                  {{ 'common.searchnodata' | translate }}
                </div>
              </mat-option>
            </ng-template>
          </mat-autocomplete>
        </mat-form-field>
        <mat-progress-bar *ngIf="isLoading" mode="query"></mat-progress-bar>
      </form>
    </div>
    <div class="d-flex align-items-end" *ngIf="!isExistingCustomer">
      <a class="cursor-pointer" (click)="onViewCustomerSelection($event); (false)"
        ><i-tabler class="icon-16" name="{{ hasCustomerData ? 'eye' : 'pencil' }}"></i-tabler
      ></a>
    </div>
  </div>
</form>
