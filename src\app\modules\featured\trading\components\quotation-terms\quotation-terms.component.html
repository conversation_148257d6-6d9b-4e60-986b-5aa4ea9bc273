<!-- <mat-card appearance="outlined">
  <div class="mb-3">
    <i-tabler class="icon-16 m-r-4" (click)="openPreviewAllModal()" name="eye"></i-tabler>
  </div>
  <mat-accordion>
    <mat-expansion-panel *ngFor="let section of sections; let i = index">
      <mat-expansion-panel-header>
        <mat-panel-title>
          {{ section.name }}
        </mat-panel-title>
        <div>
          <i-tabler
            class="icon-16 m-r-4"
            (click)="openPreviewModal(i); $event.stopPropagation()"
            name="eye"></i-tabler>
          <i-tabler
            class="icon-16 m-r-4"
            (click)="openEditModal(i); $event.stopPropagation()"
            name="edit"></i-tabler>
        </div>
      </mat-expansion-panel-header>
      <pre>{{ section.clause }}</pre>
    </mat-expansion-panel>
  </mat-accordion>
</mat-card> -->

<!-- <mat-card appearance="outlined"> -->

<ng-container *ngIf="expanded; then content; else accordion"> </ng-container>

<ng-template #accordion>
  <div class="row no-gutters">
    <button
      class="col-12 p-2"
      (click)="openPreviewAllModal()"
      align="center"
      mat-button
      color="primary"
      mat-raised-button>
      {{ 'salesQuotes.termsAndConditions' | translate }}
    </button>
  </div>
  <!-- <mat-accordion>
    <mat-expansion-panel class="no-padding" [expanded]="expanded">
      <mat-expansion-panel-header>
        <mat-panel-title class="text-primary"> Terms and Conditions </mat-panel-title>
        <i-tabler
          class="icon-16 m-r-10"
          (click)="openPreviewAllModal(); $event.stopPropagation()"
          name="eye"></i-tabler>
      </mat-expansion-panel-header>
      <ng-template *ngTemplateOutlet="content"></ng-template>
    </mat-expansion-panel>
  </mat-accordion> -->
</ng-template>

<ng-template #content>
  <div class="row no-gutters">
    <ng-container *ngFor="let section of sections; let i = index">
      <div class="col-md-6 col-sm-6 col-lg-6 m-b-5 p-2">
        <mat-card class="mat-card-no-padding" appearance="outlined">
          <div class="d-flex justify-content-between align-items-center">
            <mat-card-title>{{ section.name | translate }}</mat-card-title>
            <div>
              <i-tabler
                class="icon-16 m-r-4"
                (click)="openPreviewModal(i); $event.stopPropagation()"
                name="eye"></i-tabler>
              <i-tabler
                class="icon-16 m-r-4"
                *ngIf="canEdit"
                (click)="openEditModal(i)"
                name="pencil"></i-tabler>
            </div>
          </div>
        </mat-card>
      </div>
    </ng-container>
  </div>
  <div class="row no-gutters">
    <div class="col-12 p-2">
      <mat-label>{{ 'salesQuotes.notes' | translate }}</mat-label>

      <mat-form-field class="w-100">
        <textarea
          #autosize="cdkTextareaAutosize"
          [(ngModel)]="notes"
          matInput
          cdkTextareaAutosize
          cdkAutosizeMinRows="1"
          cdkAutosizeMaxRows="5"></textarea>
      </mat-form-field>
    </div>
  </div>
  <div class="row no-gutters p-2">
    <button
      class="col-12"
      (click)="openPreviewAllModal()"
      align="center"
      mat-button
      color="primary"
      mat-raised-button>
      {{ 'salesQuotes.termsAndConditions' | translate }}
    </button>
  </div>
</ng-template>
