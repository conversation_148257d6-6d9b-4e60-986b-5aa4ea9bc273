import { Component, Inject, Output, EventEmitter } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SalesIntegratedCreateResponse } from 'src/app/core/interfaces/sales';

export interface PrintDialogData {
  documentResponse: SalesIntegratedCreateResponse;
  title?: string;
  message?: string;
}

@Component({
  selector: 'app-print-dialog',
  templateUrl: './print-dialog.component.html',
  styleUrls: ['./print-dialog.component.scss'],
})
export class PrintDialogComponent {
  @Output() printRequested = new EventEmitter<boolean>();

  constructor(
    public dialogRef: MatDialogRef<PrintDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: PrintDialogData
  ) {}

  get documentNumber(): string {
    return this.data.documentResponse?.documentNumber || 'N/A';
  }

  get documentType(): string {
    return this.data.documentResponse?.documentType || 'Document';
  }

  get status(): string {
    return this.data.documentResponse?.status || 'Unknown';
  }

  onPrintYes(): void {
    this.printRequested.emit(true);
    this.dialogRef.close(true);
  }

  onPrintNo(): void {
    this.printRequested.emit(false);
    this.dialogRef.close(false);
  }

  onCancel(): void {
    this.dialogRef.close(null);
  }
}
