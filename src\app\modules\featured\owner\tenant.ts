import { IAddress } from '../../shared/components/address/address';

export interface ITenant {
  tenantId: string;
  tenantName: string;
  commercialRegistrationNo: string;
  address: IAddress;
  phoneNumber: string;
  emailId: string;
  tenantConfig: ITenantConfig;
  contactName: string;
  expirationDate: string;
  installationDate: string;
  isActive: boolean;
  vatNumber: number;
  notes?: string;
  id?: number;
  identification?: string;
  identificationCode?: string;
  enterpriseType?: string;
}

export interface ITenantConfig {
  maxBranches: number;
  maxWarehouses: number;
  maxUsers: number;
  storageLimit: number;
  allowedFeatures: string[];
}

export interface ITenants {
  searchTimestamp: Date;
  totalRecordsCount: number;
  totalPages: number;
  morePages: boolean;
  tenants: ITenant[];
}

export interface IBranchListResponse {
  branchId: number;
  branchName: string;
  years: unknown[];
  nameEnglish: string;
  nameArabic: string;
  vatNumber: string;
  phoneNumber: string;
  emailId: string;
  maxWarehouses: null;
  address: null;
}

export interface ICertificateListResponse {
  id: number;
  tenantId: string;
  status: string;
  onboardingDate: Date;
  certificateExpiryDate: Date;
  organizationIdentifier: string;
  organizationUnit: string;
  location: string;
  serialNumber: string;
  branchId: number;
}
