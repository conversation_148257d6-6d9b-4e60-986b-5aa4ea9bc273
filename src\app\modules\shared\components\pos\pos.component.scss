/* src/app/modules/featured/trading/components/pos-session/pos-session.component.scss */
// .summary {
//   margin-top: 20px;
//   font-weight: bold;
// }

html,
body {
  height: 100%; /* Ensure the body takes full height */
  margin: 0; /* Remove default margin */
}

.action-buttons {
  // margin-top: 20px;
  // display: flex;
  // justify-content: space-between; /* Adjusts spacing between buttons */

  display: flex;
  justify-content: flex-end; /* Align buttons to the right */
  // padding: 10px; /* Add some padding */
  // background: white; /* Optional: Add background color to distinguish */
}

.action-buttons button {
  flex: 1; /* Makes buttons take equal space */
  margin: 0 10px; /* Adds margin between buttons */
  height: 40px;
  font-size: 20px;
}

.content-container {
  flex: 1; /* Take available space */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Prevent overflow */
}

.main-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 57px);
  // should be 47 after removing navigations
}

.table-container {
  flex: 1; /* Take available space */
  overflow-y: auto; /* Enable vertical scrolling */
  margin-bottom: 20px; /* Space between table and action buttons */
}
.summary {
  margin-bottom: 20px; /* Space between summary and action buttons */
}

.summary-table {
  width: 100%; /* Full width of the container */
  border-collapse: collapse; /* Merge borders */
}

.summary-table td {
  padding: 10px; /* Padding for table cells */
  border: 1px solid #ccc; /* Light border for table cells */
}

.quantity-container {
  display: flex;
  align-items: center;
}

.quantity-container input {
  width: 150px; /* Adjust width as needed */
  text-align: center; /* Center the text */
  margin: 0 5px; /* Space between buttons and input */
}

.mat-column-productInfo {
  min-width: 400px !important;
  max-width: 400px !important;
  text-wrap: wrap;
}

.mat-column-quantity {
  min-width: 80px !important;
}

.mat-column-total {
  min-width: 100px !important;
}

.mat-column-vat {
  min-width: 100px !important;
}

.mat-column-price {
  min-width: 80px !important;
}

.mat-column-actions {
  min-width: 50px !important;
}

.wrap-text {
  white-space: normal; /* Allow text to wrap */
  overflow-wrap: break-word; /* Break long words if necessary */
  word-wrap: break-word; /* For older browsers */
}
