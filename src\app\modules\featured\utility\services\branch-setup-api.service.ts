import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';

@Injectable()
export class BranchSetupApiService {
  baseUrl = environment.apiUrl + 'company/accounts/copy';

  constructor(private http: HttpClient) {}

  copyAllAccounts(params: HttpParams) {
    return this.http.get(this.baseUrl, { params: params });
  }
}
