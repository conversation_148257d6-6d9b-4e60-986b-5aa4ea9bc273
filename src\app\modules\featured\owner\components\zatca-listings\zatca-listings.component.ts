import { Component, ElementRef, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { TenantsService } from '../../services/tenants.service';
import { ICertificateListResponse, ITenant, ITenants } from '../../tenant';
import { ZatcaRegistrationService } from '../../services/zatca-registration.service';

@Component({
  selector: 'app-zatca-listings',
  templateUrl: './zatca-listings.component.html',
  styleUrls: ['./zatca-listings.component.scss'],
})
export class ZatcaListingsComponent {
  @ViewChild('filter') filter: ElementRef;
  @ViewChild('searchInput') searchInput: ElementRef;
  @ViewChild(MatPaginator) set matPaginator(paginator: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = paginator;
    }
  }
  @ViewChild(MatSort) set matSort(sort: MatSort) {
    if (this.dataSource) {
      this.dataSource.sort = sort;
    }
  }
  certificates: ICertificateListResponse[];
  displayedColumns: string[];
  dataSource: MatTableDataSource<ICertificateListResponse>;
  isLoading = true;
  constructor(
    public tenantService: TenantsService,
    public dialog: MatDialog,
    private zatcaService: ZatcaRegistrationService
  ) {}

  ngOnInit(): void {
    this.getAllCertificates();
    this.initColumns();
  }

  getAllCertificates(): void {
    this.zatcaService.getAllZatcaTenants().subscribe((result: ICertificateListResponse[]) => {
      console.log(result, result);
      this.certificates = result;
      this.isLoading = false;
      this.dataSource = new MatTableDataSource<ICertificateListResponse>(this.certificates);
    });
  }

  initColumns(): void {
    this.displayedColumns = [
      //'action',
      'status',
      'tenantId',
      'branchId',
      'organizationIdentifier',
      'organizationUnit',
      'serialNumber',
      'onboardingDate',
      'certificateExpiryDate',
      'location',
    ];
  }

  applyFilter(filterValue: string): void {
    filterValue = filterValue.trim();
    filterValue = filterValue.toLowerCase();
    this.dataSource.filter = filterValue;
  }

  clearSearchInput() {
    this.searchInput.nativeElement.value = '';
    this.getAllCertificates();
  }
}
