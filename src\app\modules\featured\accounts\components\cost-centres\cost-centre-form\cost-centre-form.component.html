<form
  *ngIf="!loading"
  [ngClass]="{ readOnly: IsViewMode }"
  [formGroup]="costCentreForm"
  autocomplete="off">
  <mat-card appearance="outlined">
    <mat-card-title>{{ formTitle | translate }}</mat-card-title>
    <div class="row no-gutters">
      <div class="p-2 col-md-4 col-lg-4 col-sm-12">
        <mat-label>{{ 'costCenter.accountNumber' | translate }}</mat-label>

        <mat-form-field class="w-100">
          <input matInput type="number" formControlName="accountNumber" />
        </mat-form-field>
        <mat-error
          *ngIf="
            costCentreForm.controls['accountNumber'].hasError('required') &&
            costCentreForm.controls['accountNumber'].touched
          "
          >{{ 'common.required' | translate }}</mat-error
        >
      </div>
      <div class="p-2 col-md-4 col-lg-4 col-sm-12">
        <mat-label>{{ 'costCenter.arabicName' | translate }}</mat-label>

        <mat-form-field class="w-100">
          <input [appInputFormat]="'capital-case'" matInput formControlName="nameArabic" />
        </mat-form-field>
        <mat-error
          *ngIf="
            costCentreForm.controls['nameArabic'].hasError('required') &&
            costCentreForm.controls['nameArabic'].touched
          "
          >{{ 'common.required' | translate }}</mat-error
        >
      </div>
      <div class="p-2 col-md-4 col-lg-4 col-sm-12">
        <mat-label>{{ 'costCenter.englishName' | translate }}</mat-label>

        <mat-form-field class="w-100">
          <input [appInputFormat]="'capital-case'" matInput formControlName="nameEnglish" />
        </mat-form-field>
        <mat-error
          *ngIf="
            costCentreForm.controls['nameEnglish'].hasError('required') &&
            costCentreForm.controls['nameEnglish'].touched
          "
          >{{ 'common.required' | translate }}</mat-error
        >
      </div>
    </div>
  </mat-card>
  <app-action-buttons [mode]="mode" (submitAction)="submitSales($event)"></app-action-buttons>
</form>
