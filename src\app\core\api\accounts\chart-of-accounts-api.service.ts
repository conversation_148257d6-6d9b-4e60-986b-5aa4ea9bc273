import { Injectable } from '@angular/core';
import { environment } from '../../../../environments/environment';
import { HttpClient, HttpParams } from '@angular/common/http';
import { SetupRequest } from 'src/app/modules/featured/accounts/components/account-setup/account-setup.payload';
import { Account, ConfiguredAccount } from 'src/app/modules/featured/accounts/models/account';
import { UntypedFormGroup } from '@angular/forms';

export interface AccountsResponse {
  searchTimeStamp: Date;
  totalRecordsCount: number;
  totalPages: number;
  morePages: boolean;
  accounts: Account[];
}

@Injectable({
  providedIn: 'root',
})
export class ChartOfAccountsApiService {
  baseUrl = environment.apiUrl + 'company/accounts';

  constructor(private http: HttpClient) {}

  getAllChartOfAccounts(params: HttpParams) {
    return this.http.get(this.baseUrl + '/pages', { params: params });
  }

  getChartOfAccountsByAccountGroupAndBusinessGroup(accountGroup: string, businessGroup: string) {
    return this.http.get(
      `${this.baseUrl}/general-accounts?accountGroup=${accountGroup}&businessGroup=${businessGroup}`
    );
  }

  getChartOfAccountsForPayments(accountGroup: string, businessGroup: string, accountType: string) {
    return this.http.get<AccountsResponse>(
      `${this.baseUrl}/pages?accountGroup=${accountGroup}&accountType=${accountType}&businessGroup=${businessGroup}`
    );
  }

  getChartOfAccountById(accountId: string) {
    return this.http.get(this.baseUrl + '/' + accountId);
  }

  createChartOfAccounts(chartOfAccounts: UntypedFormGroup) {
    return this.http.post(this.baseUrl, chartOfAccounts);
  }

  updateChartOfAccounts(accountId: string, chartOfAccounts: UntypedFormGroup) {
    return this.http.put(this.baseUrl + '/' + accountId, chartOfAccounts);
  }
  public getAccountStatementReport(params: HttpParams) {
    return this.http.get(environment.apiUrl + 'reports/accounts/entry-report-balance', {
      withCredentials: true,
      responseType: 'arraybuffer',
      params: params,
    });
  }

  public getAccountListReport(params: HttpParams) {
    return this.http.get(environment.apiUrl + 'reports/accounts/account-report', {
      withCredentials: true,
      responseType: 'arraybuffer',
      params: params,
    });
  }

  public setUpAccounts(setupRequest: ConfiguredAccount) {
    return this.http.post(this.baseUrl + '/setup', setupRequest, { withCredentials: true });
  }

  getAllConfiguredAccounts() {
    return this.http.get(this.baseUrl + '/setup', { withCredentials: true });
  }

  getBalance(accountId: number) {
    return this.http.get<number>(`${environment.apiUrl}account/balances/${accountId}`, {
      withCredentials: true,
    });
  }
}
