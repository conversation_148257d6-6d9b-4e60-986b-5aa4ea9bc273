.print-dialog-container {
  min-width: 400px;
  max-width: 500px;

  .dialog-header {
    text-align: center;
    margin-bottom: 20px;

    .dialog-title {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
      color: #2e7d32;
      font-weight: 600;
      margin: 0;

      .success-icon {
        color: #4caf50;
        font-size: 28px;
        width: 28px;
        height: 28px;
      }
    }
  }

  .dialog-content {
    padding: 0 24px;

    .success-message {
      text-align: center;

      .primary-message {
        font-size: 16px;
        color: #333;
        margin-bottom: 20px;
        font-weight: 500;
      }

      .document-details {
        background-color: #f5f5f5;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 20px;
        text-align: left;

        .detail-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }

          .label {
            font-weight: 600;
            color: #666;
          }

          .value {
            font-weight: 500;
            color: #333;

            &.status {
              padding: 4px 8px;
              border-radius: 4px;
              font-size: 12px;
              text-transform: uppercase;

              &.status-einvoice_saved {
                background-color: #e3f2fd;
                color: #1976d2;
              }

              &.status-cleared {
                background-color: #e8f5e8;
                color: #2e7d32;
              }

              &.status-rejected {
                background-color: #ffebee;
                color: #d32f2f;
              }
            }
          }
        }
      }

      .print-question {
        margin-top: 20px;

        .question-text {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          font-size: 16px;
          color: #333;
          margin: 0;

          .print-icon {
            color: #1976d2;
            font-size: 20px;
            width: 20px;
            height: 20px;
          }
        }
      }
    }
  }

  .dialog-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    padding: 16px 24px;
    margin-top: 20px;

    .cancel-button {
      color: #666;

      &:hover {
        background-color: #f5f5f5;
      }
    }

    .no-button {
      color: #f44336;

      &:hover {
        background-color: #ffebee;
      }
    }

    .yes-button {
      background-color: #1976d2;
      color: white;

      &:hover {
        background-color: #1565c0;
      }

      mat-icon {
        margin-right: 4px;
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }
}

// Responsive design
@media (max-width: 600px) {
  .print-dialog-container {
    min-width: 300px;

    .dialog-actions {
      flex-direction: column;

      button {
        width: 100%;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
