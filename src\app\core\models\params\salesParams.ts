import { PaginatedFilter } from 'src/app/core/interfaces/PaginatedFilter';
import { SaletransactionTypes } from '../../interfaces/sales';

export class SalesParams implements PaginatedFilter {
  searchString?: string;
  pageNumber: number;
  pageSize: number;
  orderBy?: string;
  transactionType?: SaletransactionTypes;
  invoiceId?: number;
}

export class SalesIntegeratedParams implements PaginatedFilter {
  searchString?: string;
  pageNumber: number;
  pageSize: number;
  orderBy?: string;
  documentUuid?: string;
  isManuallyReported?: boolean;
  areWarningsAccepted?: boolean;
  originatingSystem?: string;
}
