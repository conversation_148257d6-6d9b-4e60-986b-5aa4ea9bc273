<form [formGroup]="searchBoxForm" autocomplete="off">
  <div class="row">
    <div class="col-12">
      <mat-radio-group color="primary" formControlName="searchType">
        <mat-radio-button *ngIf="itemCode" value="itemCode">{{
          'searchPanel.itemCode' | translate
        }}</mat-radio-button>
        <mat-radio-button *ngIf="accountNo" color="primary" value="accountNo">
          {{ 'searchPanel.searchByAccountNo' | translate }}
        </mat-radio-button>
        <mat-radio-button *ngIf="nameArabic" color="primary" value="nameArabic">
          {{ 'searchPanel.nameArabic' | translate }}
        </mat-radio-button>
        <mat-radio-button *ngIf="nameEnglish" color="primary" value="nameEnglish">{{
          'searchPanel.nameEnglish' | translate
        }}</mat-radio-button>
        <mat-radio-button *ngIf="accountName" color="primary" value="accountName">
          {{ 'searchPanel.searchByAccountName' | translate }}
        </mat-radio-button>
      </mat-radio-group>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <mat-form-field class="w-100">
        <input
          matInput
          type="text"
          autocomplete="off"
          formControlName="searchString"
          placeholder="{{ searchPlaceholder | translate }}" />
        <mat-error
          *ngIf="
            (searchBoxForm.controls['searchString']?.hasError('required') ||
              searchBoxForm.controls['searchString']?.hasError('minLength')) &&
            searchBoxForm.controls['searchString'].touched
          "
          >{{ 'searchPanel.min3charsEntry' | translate }}</mat-error
        >
      </mat-form-field>
    </div>
  </div>
</form>
