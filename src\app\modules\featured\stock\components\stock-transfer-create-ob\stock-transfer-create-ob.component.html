<ng-container *ngIf="!loading">
  <mat-card appearance="outlined">
    <form [ngClass]="{ readOnly: IsViewMode }" [formGroup]="salesForm" autocomplete="off">
      <!-- sales top level UI elements -->
      <mat-card-title>{{ formTitle | translate }}</mat-card-title>
      <mat-expansion-panel
        class="shadow-none no-padding"
        [expanded]="true"
        ngClass="{bg-primary:IsViewMode}">
        <mat-expansion-panel-header>
          <mat-panel-description *ngIf="IsViewMode">
            <span class="bg-primary f-s-20 text-white">Document Number : {{ getDocNumber }}</span>
          </mat-panel-description>
        </mat-expansion-panel-header>
        <div class="row no-gutters">
          <div class="col-md-3 col-lg-2 col-sm-4 p-2">
            <mat-label class="hidden">{{ 'stockTransfer.crStocksalesDate' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <input [matDatepicker]="invoiceDatePicker" matInput formControlName="issueDate" />
              <mat-datepicker-toggle [for]="invoiceDatePicker" matSuffix></mat-datepicker-toggle>
              <mat-datepicker #invoiceDatePicker></mat-datepicker>
            </mat-form-field>
          </div>
          <div class="col-md-3 col-lg-2 col-sm-4 p-2">
            <mat-label class="hidden">{{
              'stockTransfer.crStockreferenceNo' | translate
            }}</mat-label>
            <mat-form-field class="w-100">
              <input matInput formControlName="orderNumber" />
            </mat-form-field>
          </div>
          <div class="col-md-3 col-lg-2 col-sm-4 p-2">
            <mat-label class="hidden">{{
              'stockTransfer.crStockreferenceDate' | translate
            }}</mat-label>
            <mat-form-field class="w-100">
              <input
                [matDatepicker]="invoiceReferenceDatePicker"
                matInput
                formControlName="orderDate" />
              <mat-datepicker-toggle
                [for]="invoiceReferenceDatePicker"
                matSuffix></mat-datepicker-toggle>
              <mat-datepicker #invoiceReferenceDatePicker></mat-datepicker>
            </mat-form-field>
          </div>
          <!-- cost accounts  -->
          <div class="col-md-3 col-lg-2 col-sm-4 p-2">
            <mat-label>{{ 'common.field.costAccount' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <mat-select formControlName="costCentreId">
                <mat-option>{{ 'common.reset' | translate }}</mat-option>
                <mat-option
                  *ngFor="let costCentreAccount of costCentreAccounts"
                  [value]="costCentreAccount.costCentreId">
                  {{ costCentreAccount | localized }}
                </mat-option>
              </mat-select>
              <mat-error
                *ngIf="
                  salesForm?.controls['costCentreId'].hasError('required') &&
                  salesForm?.controls['costCentreId'].touched
                "
                >{{ 'common.required' | translate }}</mat-error
              >
            </mat-form-field>
          </div>
          <div class="col-md-3 col-lg-2 col-sm-4 p-2">
            <mat-label>{{ 'stockTransfer.crStockinvoiceNote' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <textarea
                #streetName
                #autosize="cdkTextareaAutosize"
                maxlength="90"
                type="text"
                matInput
                formControlName="notes"
                cdkTextareaAutosize
                cdkAutosizeMaxRows="5"></textarea>
            </mat-form-field>
          </div>
        </div>

        <div class="row no-gutters">
          <div class="col-md-2 col-lg-2 col-sm-4 p-2">
            <mat-label>{{ 'stockTransfer.crStockstocktype' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <mat-select formControlName="isExternalTransfer">
                <mat-option *ngFor="let stockType of stockTypes" [value]="stockType.value">
                  {{ stockType.display | translate }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div class="col-md-3 col-lg-3 col-sm-6 p-2">
            <mat-label>{{ 'stockTransfer.crStockbrselection' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <mat-select (selectionChange)="onBranchChange($event)" formControlName="partner">
                <mat-option *ngFor="let partner of partners" [value]="partner">
                  {{ partner.nameArabic }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div
            class="col-md-3 col-lg-3 col-sm-6 p-2"
            *ngIf="
              !salesForm?.controls['isExternalTransfer']?.value &&
              salesForm?.controls['partner']?.value
            ">
            <mat-label>{{ 'stockTransfer.wareHouse' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <mat-select formControlName="warehouseIdExternal">
                <mat-option *ngFor="let warehouse of wareHouseList" [value]="warehouse.warehouseId">
                  {{ warehouse.nameArabic | translate }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
        <!-- supplier search -->
      </mat-expansion-panel>
      <!-- product search -->
      <app-product-search-selection
        #productSearch
        *ngIf="canSelectProduct"
        (itemSelected)="onSelection($event)"></app-product-search-selection>
      <!-- editor table -->
      <!-- <div class="table-responsive"> -->
      <div class="line-item-content-container">
        <div class="line-item-table-container">
          <div class="table-responsive">
            <table
              class="w-100"
              #demomatdatatable
              [dataSource]="dataSource"
              formArrayName="items"
              multiTemplateDataRows
              mat-table>
              <ng-container matColumnDef="itemCode">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'sales.code' | translate }}
                </th>
                <td
                  *matCellDef="let element; let i = dataIndex"
                  [formGroupName]="i"
                  [matTooltip]="element?.get('itemCode')?.value"
                  tabindex="-1"
                  mat-cell>
                  <ng-container>
                    <div class="wrap text-center">
                      {{ element?.get('itemCode')?.value }}
                    </div>
                  </ng-container>
                </td>
              </ng-container>

              <ng-container matColumnDef="itemName">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'sales.name' | translate }}
                </th>
                <td
                  *matCellDef="let element; let i = dataIndex"
                  [formGroupName]="i"
                  [matTooltip]="element?.get('itemNameArabic')?.value"
                  mat-cell>
                  {{ element?.get('itemNameArabic')?.value }}
                </td>
              </ng-container>

              <ng-container matColumnDef="warehouseName">
                <th *matHeaderCellDef mat-header-cell>Warehouse</th>
                <td
                  *matCellDef="let element; let i = dataIndex"
                  [formGroupName]="i"
                  [matTooltip]="element?.get('warehouseName')?.value"
                  mat-cell>
                  {{ element?.get('warehouseName')?.value }}
                </td>
              </ng-container>

              <ng-container matColumnDef="unitName">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'sales.unit' | translate }}
                </th>
                <td
                  *matCellDef="let element; let i = dataIndex"
                  [formGroupName]="i"
                  [matTooltip]="element?.get('unitName')?.value"
                  mat-cell>
                  {{ element?.get('unitName')?.value }}
                </td>
              </ng-container>

              <ng-container matColumnDef="retailPrice">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'purchase.retailPrice' | translate }}
                </th>
                <td *matCellDef="let element; let i = dataIndex" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100" class="d-flex">
                    <input
                      class="next"
                      (keydown.enter)="jumpToNext($event, i)"
                      matInput
                      type="number"
                      min="0"
                      formControlName="retailPrice" />
                    <mat-error
                      *ngIf="
                        (itemsArray.controls[i].get('retailPrice').hasError('required') ||
                          itemsArray.controls[i].get('retailPrice').hasError('gte')) &&
                        itemsArray.controls[i].get('retailPrice').touched
                      ">
                      Enter Valid Price
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>

              <ng-container matColumnDef="wholesalePrice">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'purchase.wholeSalePrice' | translate }}
                </th>
                <td *matCellDef="let element; let i = dataIndex" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100" class="d-flex">
                    <input
                      class="next"
                      (keydown.enter)="jumpToNext($event, i)"
                      matInput
                      type="number"
                      min="0"
                      formControlName="wholesalePrice" />
                    <mat-error
                      *ngIf="
                        (itemsArray.controls[i].get('wholesalePrice').hasError('required') ||
                          itemsArray.controls[i].get('wholesalePrice').hasError('gte')) &&
                        itemsArray.controls[i].get('wholesalePrice').touched
                      ">
                      Enter Valid Price
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>

              <ng-container matColumnDef="distributorPrice">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'purchase.distributorPrice' | translate }}
                </th>
                <td *matCellDef="let element; let i = dataIndex" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100" class="d-flex">
                    <input
                      class="next"
                      (keydown.enter)="jumpToNext($event, i)"
                      matInput
                      type="number"
                      min="0"
                      formControlName="distributorPrice" />
                    <mat-error
                      *ngIf="
                        (itemsArray.controls[i].get('distributorPrice').hasError('required') ||
                          itemsArray.controls[i].get('distributorPrice').hasError('gte')) &&
                        itemsArray.controls[i].get('distributorPrice').touched
                      ">
                      Enter Valid Price
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>

              <ng-container matColumnDef="quantity">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'purchase.quantity' | translate }}
                </th>
                <td *matCellDef="let element; let i = dataIndex" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100" class="d-flex">
                    <input
                      class="next"
                      (change)="onQuantityChange($event, i)"
                      (keydown.enter)="jumpToNext($event, i)"
                      matInput
                      type="number"
                      formControlName="quantity" />

                    <mat-error
                      *ngIf="
                        (itemsArray.controls[i].get('quantity').hasError('required') ||
                          !itemsArray.controls[i].get('quantity').hasError('gt')) &&
                        itemsArray.controls[i].get('quantity').touched
                      ">
                      Qty Invalid
                    </mat-error>
                    <mat-error
                      *ngIf="
                        itemsArray.controls[i].get('quantity').hasError('gt') &&
                        itemsArray.controls[i].get('quantity').touched
                      ">
                      Invalid Min Qty
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>

              <ng-container matColumnDef="price">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'purchase.price' | translate }}
                </th>
                <td *matCellDef="let element; let i = dataIndex" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100" class="d-flex">
                    <input
                      class="next"
                      (change)="onPriceChange($event, i)"
                      (keydown.enter)="jumpToNext($event, i)"
                      min="0"
                      matInput
                      formControlName="price"
                      type="number" />
                    <mat-error
                      class="text-error-wrap"
                      *ngIf="
                        (itemsArray.controls[i].get('price').hasError('required') ||
                          itemsArray.controls[i].get('price').hasError('gt')) &&
                        itemsArray.controls[i].get('price').touched
                      ">
                      Enter Valid Price
                    </mat-error>
                  </mat-form-field>
                </td>
              </ng-container>

              <ng-container matColumnDef="priceType">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'sales.priceType' | translate }}
                </th>
                <td *matCellDef="let element; let i = dataIndex" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100" class="d-flex w-120">
                    <mat-select
                      (selectionChange)="onPriceTypeChange($event, i)"
                      formControlName="priceType">
                      <mat-option *ngFor="let priceType of priceTypes" [value]="priceType.value">
                        {{ priceType.display | translate }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </td>
              </ng-container>

              <!-- <ng-container matColumnDef="vatAmount">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'purchase.vatAmt' | translate }}
                </th>
                <td *matCellDef="let element; let i = dataIndex" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100" class="d-flex">
                    <input
                      class="next"
                      tabindex="-1"
                      matInput
                      type="number"
                      readonly
                      formControlName="vatAmount" />
                    <mat-error
                      *ngIf="
                        itemsArray.controls[i].get('vatAmount')?.errors &&
                        itemsArray.controls[i].get('vatAmount').touched
                      "
                      >Error</mat-error
                    >
                  </mat-form-field>
                </td>
              </ng-container> -->

              <!-- <ng-container matColumnDef="discount">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'purchase.discount' | translate }}
                </th>
                <td *matCellDef="let element; let i = dataIndex" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100" class="d-flex">
                    <input
                      class="next"
                      (keydown.enter)="focusOnSearch()"
                      (change)="onDiscountChange($event, i)"
                      matInput
                      type="number"
                      formControlName="discount" />
                    <i-tabler
                      class="icon-16"
                      *ngIf="element?.get('product')?.value['isGeneralDscntMethod']"
                      name="percentage"></i-tabler>
                    <i-tabler
                      class="icon-16"
                      *ngIf="!element?.get('product')?.value['isGeneralDscntMethod']"
                      name="cash"></i-tabler>
                    <mat-error
                      *ngIf="
                        itemsArray.controls[i].get('discount')?.hasError('percentageError') &&
                        itemsArray.controls[i].get('discount').touched
                      "
                      >Max 100%</mat-error
                    >
                    <mat-error
                      *ngIf="
                        itemsArray.controls[i].get('discount')?.hasError('priceError') &&
                        itemsArray.controls[i].get('discount').touched
                      "
                      >Disc > Price</mat-error
                    >
                  </mat-form-field>
                </td>
              </ng-container> -->

              <ng-container matColumnDef="subtotal">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'purchase.subTotal' | translate }}
                </th>
                <td *matCellDef="let element; let i = dataIndex" [formGroupName]="i" mat-cell>
                  <mat-form-field class="w-100" class="d-flex">
                    <input
                      tabindex="-1"
                      matInput
                      type="number"
                      readonly
                      formControlName="subtotal" />
                    <mat-error
                      *ngIf="
                        itemsArray.controls[i].get('subtotal')?.errors &&
                        itemsArray.controls[i].get('subtotal').touched
                      "
                      >Error</mat-error
                    >
                  </mat-form-field>
                </td>
              </ng-container>

              <ng-container matColumnDef="action">
                <th *matHeaderCellDef mat-header-cell>
                  {{ 'common.action' | translate }}
                </th>
                <td
                  class="action-link"
                  *matCellDef="let element; let i = dataIndex"
                  tabindex="-1"
                  mat-cell>
                  <a class="m-r-10 cursor-pointer" *ngIf="canDeleteProduct"
                    ><i-tabler
                      class="icon-16"
                      (click)="
                        openDeleteConfirmationDialog(
                          element.get('itemCode').value,
                          element.get('warehouseId').value,
                          element.get('unitName').value
                        )
                      "
                      name="trash"></i-tabler
                  ></a>
                </td>
              </ng-container>
              <tr class="mat-row" *matNoDataRow>
                <td class="text-center text-info" [attr.colspan]="displayedColumns.length">
                  {{ 'common.noDataFound' | translate }}
                </td>
              </tr>
              <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
              <tr
                class="table-edit-element-row"
                *matRowDef="let row; columns: displayedColumns"
                mat-row></tr>
              <!-- <tr
                class="table-edit-detail-row"
                *matRowDef="let row; columns: ['expandedDetail']"
                mat-row></tr> -->
            </table>
          </div>
        </div>
      </div>
      <!-- summary and payment layout -->
      <div class="row flex-wrap m-t-10">
        <div class="col-lg-6 col-md-6 order-md-first" *ngIf="!IsFullCreditMode">
          <mat-card-title class="text-primary"
            >{{ 'stockTransfer.crStockpaymentnote' | translate }}
          </mat-card-title>
        </div>
        <div class="col-lg-6 col-md-6 order-md-last" *ngIf="!IsFullCreditMode">
          <app-sales-summary
            [grandTotal]="grandTotal"
            [showOnlyGrandTotal]="true"></app-sales-summary>
        </div>
      </div>
      <!-- summary and payment layout -->
    </form>
  </mat-card>
  <!-- <app-action-buttons
    [mode]="mode"
    [processType]="'purchase'"
    (submitAction)="submitStocks($event)"></app-action-buttons> -->
  <div class="text-center">
    <ng-container *ngIf="IsViewMode">
      <button
        class="m-l-10"
        [routerLink]="['../../../']"
        type="button"
        mat-stroked-button
        color="primary">
        {{ 'common.buttons.back' | translate }}
      </button>
    </ng-container>
    <ng-container *ngIf="!IsViewMode">
      <button
        class="m-l-10"
        (click)="submitStocks($event); (false)"
        type="button"
        mat-stroked-button
        color="primary">
        {{ 'common.buttons.submit' | translate }}
      </button>
      <button class="m-l-10" [routerLink]="['../']" type="button" mat-stroked-button color="warn">
        {{ 'common.buttons.cancel' | translate }}
      </button>
    </ng-container>
  </div>
</ng-container>
