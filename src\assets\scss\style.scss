@use '@angular/material' as mat;
@include mat.core();

@import 'variables';
@import 'layouts/transitions';
@import 'helpers/color';
@import 'helpers/icon-size';

//Theme colors
@import 'themecolors/green_theme';
@include mat.all-component-themes($greentheme);

//container layout
@import 'overrides/materialoverrides';
@import 'dark/dark';
@import 'container';
@import 'layouts/layouts';
@import 'grid/grid';
@import 'helpers/custom-flex';
@import 'helpers/index';

// horizontal
@import 'horizontal/horizontal';

// apps
@import 'apps/calendar';
@import 'apps/email';

// pages
@import 'pages/dashboards';
@import 'pages/auth';
@import 'pages/landingpage';

// RTL Theme
@import 'rtl/rtl';

// Material RTL fixes
@import 'material-rtl-fixes';

// custome app level css
@import 'app';

// sawami level css
@import 'overrides/sawami.scss';
