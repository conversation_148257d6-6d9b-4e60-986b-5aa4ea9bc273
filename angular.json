{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"project": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"allowedCommonJsDependencies": ["c3", "rxjs", "date-fns", "rxjs-compat", "angular-calendar", "chart.js", "@mattlewis92/dom-autoscroller", "ngx-quill", "qrcode", "bezier-easing", "howler", "moment", "number-to-arabic-words/dist/index-node.js", "number-to-words"], "outputPath": "dist/project", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss", "src/assets/scss/style.scss"], "scripts": [], "stylePreprocessorOptions": {"includePaths": ["src/assets/scss"]}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "150kb", "maximumError": "150kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "preview": {"budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "150kb", "maximumError": "150kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.preview.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "project:build:production"}, "development": {"browserTarget": "project:build:development"}, "options": {"proxyConfig": "src/proxy.config.json"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "project:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}, "pos": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/pos", "sourceRoot": "projects/pos/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/pos", "index": "projects/pos/src/index.html", "main": "projects/pos/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "projects/pos/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["projects/pos/src/favicon.ico", "projects/pos/src/assets"], "styles": ["projects/pos/src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "pos:build:production"}, "development": {"browserTarget": "pos:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "pos:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "projects/pos/tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["projects/pos/src/favicon.ico", "projects/pos/src/assets"], "styles": ["projects/pos/src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["projects/pos/**/*.ts", "projects/pos/**/*.html"]}}}}}, "cli": {"cache": {"enabled": false}, "schematicCollections": ["@angular-eslint/schematics"], "analytics": false}}