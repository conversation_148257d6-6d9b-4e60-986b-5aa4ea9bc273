import { Directionality } from '@angular/cdk/bidi';
import { Component, ElementRef, ViewChild } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { PaymentService } from 'src/app/core/api/trading/payment.service';
import { IPurchaseInvoice } from 'src/app/core/interfaces/purchase';
import { Voucher, voucherType } from 'src/app/core/interfaces/sales';
import { SalesNotesComponent } from '../../../../trading/components/sales/sales-notes/sales-notes.component';

@Component({
  selector: 'app-ucvoucher-payment',
  templateUrl: './ucvoucher-payment.component.html',
  styleUrls: ['./ucvoucher-payment.component.scss'],
})
export class UcvoucherPaymentComponent {
  @ViewChild('filter') filter: ElementRef;
  @ViewChild('searchInput') searchInput: ElementRef;
  @ViewChild(MatPaginator) set matPaginator(paginator: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = paginator;
    }
  }
  @ViewChild(MatSort) set matSort(sort: MatSort) {
    if (this.dataSource) {
      this.dataSource.sort = sort;
    }
  }
  invoices: IPurchaseInvoice[];
  displayedColumns: string[];
  dataSource = new MatTableDataSource<Voucher>();
  isLoading = true;
  constructor(
    public paymentService: PaymentService,
    public dialog: MatDialog,
    private direction: Directionality
  ) {}

  ngOnInit(): void {
    this.getAllSales();
    this.initColumns();
  }

  getAllSales(): void {
    this.paymentService.getPaymentVouchers(voucherType.GeneralPayable).subscribe(data => {
      console.log(data);
      this.dataSource.data = data.vouchers;
      //data.forEach(value => (this.total = value.paidAmount + this.total));
      this.isLoading = false;
    });
  }

  initColumns(): void {
    this.displayedColumns = [
      'action',
      'voucherNumber',
      'voucherDate',
      'voucherAmount',
      'voucherType',
      'paymentMethod',
    ];
  }

  applyFilter(filterValue: string): void {
    filterValue = filterValue.trim();
    filterValue = filterValue.toLowerCase();
    this.dataSource.filter = filterValue;
  }

  clearSearchInput() {
    this.searchInput.nativeElement.value = '';
    this.getAllSales();
  }
  openSalesNotes(invoiceId: number): void {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '90vw';
    dialogConfig.position = {
      top: '50px',
    };
    dialogConfig.maxHeight = '90vh';
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    dialogConfig.data = {
      invoiceId: invoiceId,
      purchaseNotes: true,
    };
    this.dialog.open(SalesNotesComponent, dialogConfig);
  }
}
