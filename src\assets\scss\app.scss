.spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.primary {
  color: $primary;
}

.accent {
  color: $accent;
}

.warning {
  color: $warning;
}

.error {
  color: $error;
}

.success {
  color: $success;
}

.copyright-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40px;
  padding: 10px;
  text-align: center;
  background-color: #ffffff;
  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
  z-index: 9999; /* Ensure it's above all other content */
  display: block !important; /* Force display */
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.copyright-text {
  color: rgba(0, 0, 0, 0.7);
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 0.2px;
  margin: 0; /* Remove any default margins */
}

.copyright-footer-actions {
  margin-top: auto; /* This will push it to the bottom of the card actions */
  text-align: center; /* Center the text if needed */
}

/* Add padding to the bottom of the content to prevent footer overlap */
.pageWrapper {
  padding-bottom: 50px !important; /* Ensure content doesn't get hidden behind the footer */
}
