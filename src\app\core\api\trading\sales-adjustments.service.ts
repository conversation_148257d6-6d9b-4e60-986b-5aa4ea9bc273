import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { ISupplier } from '../../interfaces/supplier';
import { SalesNotesParams } from '../../models/params/salesNotesParams';
import { IInvoiceAdjustmentDetails } from '../../interfaces/purchase';

@Injectable({
  providedIn: 'root',
})
export class SalesAdjustmentService {
  baseUrl = environment.apiUrl + 'trading/sales';

  constructor(private http: HttpClient) {}

  getAllSuppliers(params: SalesNotesParams) {
    const getSupplierParams = this.getParams(params);
    return this.http.get(this.baseUrl + '/pages', { params: getSupplierParams });
  }

  createSalesAdjustments(adjustments: IInvoiceAdjustmentDetails) {
    return this.http.post(this.baseUrl, adjustments);
  }

  updateSupplier(supplier: ISupplier, id: string) {
    return this.http.put(this.baseUrl + '/' + id, supplier);
  }

  deleteSupplier(id: string) {
    return this.http.delete(this.baseUrl + `/${id}`);
  }

  getParams(supplierParams: SalesNotesParams): HttpParams {
    let httpParams = new HttpParams();
    if (supplierParams?.searchString)
      httpParams = httpParams.append('searchString', supplierParams.searchString);
    if (supplierParams?.pageNumber)
      httpParams = httpParams.append('pageNumber', supplierParams.pageNumber.toString());
    if (supplierParams?.pageSize)
      httpParams = httpParams.append('pageSize', supplierParams.pageSize.toString());
    if (supplierParams?.orderBy)
      httpParams = httpParams.append('orderBy', supplierParams.orderBy.toString());
    if (supplierParams?.invoiceId)
      httpParams = httpParams.append('invoiceId', supplierParams.invoiceId);
    return httpParams;
  }
}
