<ng-container>
  <!-- action bar -->
  <app-create-action
    *appHasPermission="['Depreciation.Create', 'AllPermissions']"
    [label]="'depreciation.depreciationCreate' | translate"></app-create-action>
  <!-- action bar -->
  <mat-card appearance="outlined">
    <form [formGroup]="depreciationForm" autocomplete="off">
      <div class="row no-gutters">
        <div class="p-2 col-md-6">
          <app-depreciation-search-box
            #searchBoxForm
            formControlName="searchBoxForm"></app-depreciation-search-box>
        </div>
        <div class="p-2 col-lg-2 col-md-2 col-sm-4 d-flex align-items-end">
          <mat-label>{{ 'depreciation.deprecationMethod' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <mat-select formControlName="depreciationMethod">
              <mat-option *ngFor="let method of depreciationMethods" [value]="method.value">
                {{ method.display | translate }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>
      <ng-container>
        <button (click)="getFilterData($event); (false)" mat-stroked-button color="primary">
          {{ 'searchPanel.searchString' | translate }}
        </button>
        <button class="m-l-10" (click)="clearFilters($event); (false)" mat-stroked-button>
          {{ 'searchPanel.clear' | translate }}
        </button>
      </ng-container>
    </form>
    <mat-card-title class="m-t-10">{{ 'depreciation.listing' | translate }}</mat-card-title>
    <div class="table-responsive">
      <table [dataSource]="dataSource" mat-table matSort>
        <ng-container matColumnDef="cumulativeDepreciationAccount">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'depreciation.cumDeprAccount' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ getAccountName(element.cumulativeDepreciationAccount) }}
          </td>
        </ng-container>
        <ng-container matColumnDef="assetAccount">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'depreciation.underAsset' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ getAccountName(element.assetAccount) }}
          </td>
        </ng-container>
        <ng-container matColumnDef="depreciationExpenseAccount">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'depreciation.deprExpAccount' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ getAccountName(element.depreciationExpenseAccount) }}
          </td>
        </ng-container>
        <ng-container matColumnDef="depreciationPct">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'depreciation.deprPercentage' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ element.depreciationPct }}
          </td>
        </ng-container>
        <ng-container matColumnDef="depreciationMethod">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'depreciation.deprMethod' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ element.depreciationMethod }}
          </td>
        </ng-container>
        <ng-container matColumnDef="action">
          <th *matHeaderCellDef mat-header-cell>{{ 'common.action' | translate }}</th>
          <td *matCellDef="let element" mat-cell>
            <a
              class="m-r-10 cursor-pointer"
              class="m-b-10"
              *appHasPermission="['Depreciation.Update', 'AllPermissions']"
              [routerLink]="['edit', element.depreciationId]"
              ><i-tabler class="icon-16" name="edit"></i-tabler
            ></a>
            <a
              class="m-r-10 cursor-pointer"
              class="m-b-10"
              *appHasPermission="['Depreciation.View', 'AllPermissions']"
              [routerLink]="['view', element.depreciationId]"
              ><i-tabler class="icon-16" name="eye"></i-tabler
            ></a>
          </td>
        </ng-container>
        <tr class="mat-row" *matNoDataRow>
          <td class="text-center text-info" [attr.colspan]="displayedColumns.length">
            {{ 'common.noDataFound' | translate }}
          </td>
        </tr>
        <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
        <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
      </table>
    </div>
    <mat-paginator
      *ngIf="dataSource?.filteredData?.length > 0"
      [length]="resultsLength"
      [pageSize]="10"
      [pageSizeOptions]="[5, 10, 20]"
      (page)="onPageChange($event)"></mat-paginator>
  </mat-card>
</ng-container>
