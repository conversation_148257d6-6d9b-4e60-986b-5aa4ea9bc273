import { ComponentFixture, TestBed } from '@angular/core/testing';

import { FixedAssetDashboardComponent } from './fixed-asset-dashboard.component';

describe('FixedAssetDashboardComponent', () => {
  let component: FixedAssetDashboardComponent;
  let fixture: ComponentFixture<FixedAssetDashboardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [FixedAssetDashboardComponent],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(FixedAssetDashboardComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
