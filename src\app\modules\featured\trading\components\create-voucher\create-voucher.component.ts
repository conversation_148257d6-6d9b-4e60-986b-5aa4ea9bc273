import { Directionality } from '@angular/cdk/bidi';
import { ChangeDetectionStrategy, Component, Inject, ViewChild } from '@angular/core';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogConfig,
  MatDialogRef,
} from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { CustomValidators } from 'ngx-custom-validators';
import { BehaviorSubject, forkJoin } from 'rxjs';
import { CostCentresApiService } from 'src/app/core/api/accounts/cost-centres-api.service';
import { CommonService } from 'src/app/core/api/common.service';
import { DistributorService } from 'src/app/core/api/trading/distributor.service';
import { PaymentService } from 'src/app/core/api/trading/payment.service';
import { IDistributor } from 'src/app/core/interfaces/distributor';
import { IPaymentViewData } from 'src/app/core/interfaces/payment';
import { IPurchaseInvoice } from 'src/app/core/interfaces/purchase';
import {
  IInvoice,
  ISaleDetails,
  PaymentVoucher,
  customePayment,
  issueType,
} from 'src/app/core/interfaces/sales';
import { DistributorParams } from 'src/app/core/models/params/distributorParams';
import { convertDateForBE } from 'src/app/core/utils/date-utils';
import { BusyService } from 'src/app/modules/core/core/services/busy.service';
import { ConfirmDialogComponent } from 'src/app/modules/shared/components/confirm-dialog/confirm-dialog.component';
import { StandardPaymentsComponent } from 'src/app/modules/shared/components/standard-payments/standard-payments.component';
import { CostCentre } from '../../../accounts/models/costCentre';

@Component({
  selector: 'app-create-voucher',
  templateUrl: './create-voucher.component.html',
  styleUrls: ['./create-voucher.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CreateVoucherComponent {
  @ViewChild('paymentForm') private paymentForm: StandardPaymentsComponent;
  customePaymentSelection: customePayment = <customePayment>{};
  paymentViewData: IPaymentViewData = <IPaymentViewData>{};
  displayedColumns = [
    'invoiceNumber',
    'invoiceDate',
    'unpaidAmount',
    'distributorAccountId',
    'costCentreId',
    'paidAmount',
    'notes',
  ];
  saleDetails: ISaleDetails = <ISaleDetails>{};
  sampledata: IInvoice[] | IPurchaseInvoice[] = [];
  distributorAccounts: IDistributor[] = [];
  costCentreAccounts: CostCentre[] = [];
  salesForm: UntypedFormGroup = this.fb.group({
    fullPayment: new UntypedFormControl(null),
    voucherDate: new UntypedFormControl(new Date(), Validators.required),
    accountNumber: new UntypedFormControl(null),
    name: new UntypedFormControl(null),
    items: this.fb.array([]),
  });
  loading$ = new BehaviorSubject<boolean>(true);
  showLoader = true;
  dataSource = new BehaviorSubject<AbstractControl[]>([]);
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private fb: FormBuilder,
    private commonService: CommonService,
    private dialog: MatDialog,
    private direction: Directionality,
    private distributorService: DistributorService,
    private costCentresApiService: CostCentresApiService,
    public busyService: BusyService,
    private paymentService: PaymentService,
    private dialogRef: MatDialogRef<CreateVoucherComponent>,
    private translate: TranslateService
  ) {
    this.customePaymentSelection.bankType = true;
    this.customePaymentSelection.cardType = true;
    this.customePaymentSelection.cashType = true;
    this.customePaymentSelection.creditType = false;
  }

  ngOnInit() {
    // this.busyService.isLoading.subscribe(value => {
    //   this.showLoader = value;
    // });
    this.getAllDropDownData();
    this.salesForm.get('fullPayment').valueChanges.subscribe(change => {
      this.handleMakeFullPaymentToggle(change);
    });
  }

  patchDataToControls(data: any): void {
    const name =
      this.translate.currentLang === 'ar' ? data?.account?.nameArabic : data?.acoount?.nameEnglish;
    this.salesForm.get('name').setValue(name);
    this.salesForm.get('accountNumber').setValue(data?.account?.accountNumber);
  }

  getdata() {
    this.sampledata.forEach((data: Partial<IInvoice> | Partial<IPurchaseInvoice>) => {
      console.log(data);
      const itemFormGroup = this.fb.group({
        id: null,
        invoiceId: [data.id],
        invoiceNumber: [data.documentNumber],
        invoiceDate: [data.issueDate],
        unpaidAmount: [data.unpaidAmount],
        paidAmount: [
          0,
          Validators.compose([
            CustomValidators.gt(0),
            Validators.pattern(/^\d+(\.\d{1,2})?$/),
            this.balanceAmountValidator,
          ]),
        ],
        notes: [],
        distributorAccountId: [data?.distributorAccountId ?? null],
        costCentreId: [data?.costCentreId ?? null],
        account: [data?.account ?? null],
        issueType: issueType.General, //this.data.voucherType === voucherType.PurchasePayable,
      });
      console.log(itemFormGroup);

      this.itemRow.push(itemFormGroup);
    });
    this.dataSource.next(this.itemRow.controls);
  }

  get itemRow() {
    return this.salesForm.get('items') as FormArray;
  }

  balanceAmountValidator(control: AbstractControl): { [key: string]: boolean } | null {
    const unpaidAmount = control?.parent?.get('unpaidAmount')?.value;
    const paidAmount = control?.value;
    console.log(paidAmount, unpaidAmount);
    if (paidAmount > unpaidAmount) {
      return { balanceGreaterThanTotal: true };
    }

    return null;
  }

  handleMakeFullPaymentToggle(isChecked: boolean) {
    if (isChecked) {
      this.itemRow.controls.forEach(control => {
        console.log(control);
        control.get('paidAmount')?.setValue(control.get('unpaidAmount')?.value);
      });
    } else {
      this.itemRow.controls.forEach(control => {
        control.get('paidAmount')?.setValue(0);
      });
    }
  }

  getAllDropDownData(): void {
    const distributorAccounts = this.distributorService.getAllDistributors(new DistributorParams());
    const costCentreAccounts = this.costCentresApiService.getAllCostCentres();
    forkJoin([distributorAccounts, costCentreAccounts]).subscribe(results => {
      console.log(results);
      this.distributorAccounts = results[0]?.distributors;
      this.costCentreAccounts = results[1];
      this.loading$.next(false);
      this.sampledata = this.data.selectedData;
      this.patchDataToControls(this.data.selectedData[0]);
      this.getdata();
    });
  }

  getTotalBalanceAmount(): number {
    let total = 0;

    this.itemRow.controls.forEach(control => {
      total += control.get('paidAmount')?.value || 0;
    });

    return total;
  }

  getTotalInvoiceTotal(): number {
    let total = 0;

    this.itemRow.controls.forEach(control => {
      total += control.get('unpaidAmount')?.value || 0;
    });

    return total;
  }

  submitVoucher(event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    //this.confirmationModal();
    this.salesForm.markAllAsTouched();
    this.paymentForm.paymentFormIsAllValidPurchase();
    console.log(
      this.salesForm.valid,
      this.salesForm.getRawValue(),
      this.paymentForm.paymentFormIsAllValidPurchase()
    );
    const details: PaymentVoucher = {
      voucherDate: convertDateForBE(this.salesForm.get('voucherDate')?.value),
      voucherType: this.data.voucherType,
      items: this.salesForm.get('items').getRawValue(),
      payments: this.paymentForm.paymentForm.getRawValue(),
      paymentAccountNumber: null,
    };
    console.log('final structure', details);
    if (this.salesForm.valid && this.paymentForm.paymentFormIsAllValid()) {
      this.confirmationModal(details);
    } else {
      this.commonService.playErrorSound();
    }
  }

  confirmationModal(details: PaymentVoucher) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.minWidth = '600px';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = true;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    dialogConfig.data = 'common.confirmAction';
    const dialogRef = this.dialog.open(ConfirmDialogComponent, dialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      console.log('result', result);
      if (result) {
        // make api call
        this.paymentService.createSalesVoucher(details).subscribe(() => {
          console.log('');
          this.dialogRef.close();
        });
      }
    });
  }
}
