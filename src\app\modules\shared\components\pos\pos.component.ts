import { FocusableOption, FocusKeyManager } from '@angular/cdk/a11y';
import { Component, ElementRef, QueryList, ViewChild, ViewChildren } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { ProductService } from 'src/app/core/api/product.service';
import { IInventory } from 'src/app/modules/featured/catalog/models/product';
import { ProductAdjustmentsParams } from 'src/app/modules/featured/catalog/models/productAdjustmentParams';
import { Inventory } from 'src/app/modules/featured/stock/models/unitPrice';
import { PosPaymentsComponent } from '../pos-payments/pos-payments.component';
import { Directionality } from '@angular/cdk/bidi';
import { fromEvent } from 'rxjs';
import { debounceTime, takeUntil } from 'rxjs/operators';

export class FocusableRow extends ElementRef implements FocusableOption {
  constructor(nativeElement: any) {
    super(nativeElement);
  }

  focus(): void {
    this.nativeElement.focus(); // Call the native focus method
  }
}

@Component({
  selector: 'app-pos',
  templateUrl: './pos.component.html',
  styleUrls: ['./pos.component.scss'],
})
export class PosComponent {
  @ViewChild('barcodeInput') barcodeInput: ElementRef; // Reference to the input element
  @ViewChildren('row') rows: QueryList<FocusableRow>; // Reference to table rows
  @ViewChild('tableContainer') tableContainer: ElementRef;
  displayedColumns: string[] = ['productInfo', 'quantity', 'price', 'vat', 'total', 'actions'];
  dataSource = new MatTableDataSource<IInventory>();
  products: IInventory[] = [];
  totalAmount = 0;
  totalTax = 0;
  keyManager: FocusKeyManager<FocusableRow>; // Key manager for handling focus
  isBarcodeFocused = true; // Track if barcode input is focused

  constructor(
    private productService: ProductService,
    private dialog: MatDialog,
    private direction: Directionality
  ) {
    this.keyManager = new FocusKeyManager<FocusableRow>(this.rows).withWrap(); // Initialize key manager with wrap
  }

  addProduct(barcode: string) {
    const params = new ProductAdjustmentsParams();
    params.searchString = barcode;
    params.pageSize = 999;
    this.productService.getProductsByFilter(params).subscribe(product => {
      if (product) {
        const existingProduct = this.products.find(p => p.unitBarcode === barcode);
        if (existingProduct) {
          existingProduct.quantity += 1;
        } else {
          product.inventories[0].quantity = 1;
          this.products.push(product.inventories[0]);
        }
        //     this.products.push(...product.inventories);
        this.updateTotal();
        this.dataSource.data = this.products;
        this.scrollToBottom();
        console.log(this.dataSource.data);

        // Set focus back to the barcode input
        this.barcodeInput.nativeElement.focus();
        this.isBarcodeFocused = true; // Set the flag to indicate barcode is focused
      }
    });
  }

  updateTotal() {
    // this.totalAmount = this.products.reduce(
    //   (sum, product) => sum + product.price * product.quantity,
    //   0
    // );
    // this.totalTax = this.products.reduce((sum, product) => sum + product.tax, 0);
  }

  // openQuantityDialog(product: IProduct) {
  //   const dialogRef = this.dialog.open(QuantityDialogComponent, {
  //     data: { quantity: product.quantity },
  //   });

  //   dialogRef.afterClosed().subscribe(result => {
  //     if (result !== undefined) {
  //       product.quantity = result; // Update the quantity
  //       this.updateTotal(); // Recalculate totals
  //       this.dataSource.data = this.products; // Update the data source
  //     }
  //   });
  // }

  // deleteProduct(product: IProduct) {
  //   this.products = this.products.filter(p => p.barcode !== product.barcode);
  //   this.updateTotal(); // Recalculate totals
  //   this.dataSource.data = this.products; // Update the data source
  // }

  decreaseQuantity(product: IInventory): void {
    product.quantity--;
  }

  increaseQuantity(product: IInventory): void {
    product.quantity++;
  }

  decreasePrice(product: IInventory): void {
    product.retailPrice--;
  }

  increasePrice(product: IInventory): void {
    product.retailPrice++;
  }

  openPaymentDialog() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.panelClass = 'green_theme';
    dialogConfig.position = {
      top: '50px',
    };
    dialogConfig.width = '900px';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(PosPaymentsComponent, dialogConfig);

    dialogRef.afterClosed().subscribe(result => {
      // Handle any actions after the dialog is closed
      console.log('Payment dialog closed', result);
    });
  }

  scrollToBottom() {
    const container = this.tableContainer.nativeElement;
    container.scrollTop = container.scrollHeight;
  }

  vatAmount(product: IInventory) {
    //price: number, discount: number, discountType: boolean, vat: number
    console.log('vatAmount', product);
    return +(
      this.priceMinusDiscount(product.retailPrice, product.discount, product.isGeneralDscntMethod) *
      (product.vat / 100) *
      product.quantity
    ).toFixed(2);
  }

  priceMinusDiscount(price: number, discount: number, discountType: boolean): number {
    let discountValue = 0;
    if (discountType) {
      discountValue = price - price * (discount / 100); // percentage
    } else {
      discountValue = price - discount; // cash
    }
    console.log('priceMinusDiscount', price, discount, discountType, discountValue);
    return discountValue;
  }

  countSubTotal(
    quantity: number,
    price: number,
    discount: number,
    discountType: boolean,
    vatValue: number
  ): number {
    return +(
      quantity *
      (this.priceMinusDiscount(price, discount, discountType) + vatValue)
    ).toFixed(2);
  }

  subTotal(product: IInventory) {
    return this.countSubTotal(
      product.quantity,
      product.retailPrice,
      product.discount,
      product.isGeneralDscntMethod,
      this.subTotalVat(
        product.retailPrice,
        product.vat,
        product.discount,
        product.isGeneralDscntMethod
      )
    );
  }

  subTotalVat(price: number, tax: number, discount: number, discountType: boolean): number {
    console.log('subTotalVat', price, tax, discount, discountType);
    return +((this.priceMinusDiscount(price, discount, discountType) * tax) / 100);
  }

  get totals(): number {
    const total = this.dataSource.data.reduce((sum, item) => sum + this.subTotal(item), 0);
    console.log('countTotal', total);
    return total;
  }

  get discounts(): number {
    const discount = this.dataSource.data.reduce((sum, item) => {
      const discountAmount = item.isGeneralDscntMethod
        ? (item.discount / 100) * item.retailPrice
        : item.discount;
      return sum + discountAmount * item.quantity;
    }, 0);
    console.log('countDiscount', discount);
    return discount;
  }

  get totalExcVatDiscs(): number {
    const totalExcVatDisc = this.dataSource.data.reduce((sum, item) => {
      return sum + +(item.purchasePrice * item.quantity).toFixed(2);
    }, 0);
    console.log('CountTotalExclVatDisc', totalExcVatDisc);
    return totalExcVatDisc;
  }

  get grandTotals(): number {
    const grandTotal = this.totals - this.discounts;
    console.log('countGrandTotal', grandTotal);
    return grandTotal;
  }

  get grandVat(): number {
    const result = new Map<number, number>();
    let totalVats = 0;

    const vatSums = new Map<number, number>();

    for (const item of this.dataSource.data) {
      const controlVat = item.vat;
      const subTotalVat =
        this.subTotalVat(item.retailPrice, item.vat, item.discount, item.isGeneralDscntMethod) *
        item.quantity;

      if (vatSums.has(controlVat)) {
        vatSums.set(controlVat, vatSums.get(controlVat) + subTotalVat);
      } else {
        vatSums.set(controlVat, subTotalVat);
      }

      totalVats += subTotalVat;
    }

    for (const [vat, sum] of vatSums) {
      // Round the sum to 2 decimal places
      const roundedSum = Math.round(sum * 100) / 100;
      result.set(vat, roundedSum);
    }

    totalVats = Math.round(totalVats * 100) / 100; // Round final sum
    console.error(result, totalVats.toFixed(2));

    return totalVats;
    // return { vatMap: result, totalVats: totalVats };
  }

  startChange(product: IInventory, field: 'quantity' | 'retailPrice', change: number) {
    const mouseup$ = fromEvent(document, 'mouseup').pipe(debounceTime(300));
    const mouseleave$ = fromEvent(document, 'mouseleave');

    fromEvent(document, 'mousedown')
      .pipe(takeUntil(mouseup$), takeUntil(mouseleave$))
      .subscribe(
        () => {
          const newValue = product[field] + change;
          if (newValue >= 0) {
            product[field] = newValue; // Update the field (quantity or price)
          }
        },
        null,
        () => this.applyChange()
      );
  }

  applyChange() {
    this.updateTotal(); // Recalculate totals after updating
    this.dataSource.data = this.products; // Update the data source
  }
}
