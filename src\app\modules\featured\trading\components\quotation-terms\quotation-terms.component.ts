import { Directionality } from '@angular/cdk/bidi';
import { CdkTextareaAutosize } from '@angular/cdk/text-field';
import { Component, EventEmitter, Input, NgZone, Output, ViewChild } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { take } from 'rxjs/operators';
import { Provision } from 'src/app/core/interfaces/sales';
import { AddNewTermsModalComponent } from '../add-new-terms-modal/add-new-terms-modal.component';
import { EditTermsModalComponent } from '../edit-terms-modal/edit-terms-modal.component';
import { PreviewAllTermsModalComponent } from '../preview-all-terms-modal/preview-all-terms-modal.component';
import { PreviewTermsModalComponent } from '../preview-terms-modal/preview-terms-modal.component';

@Component({
  selector: 'app-quotation-terms',
  templateUrl: './quotation-terms.component.html',
  styleUrls: ['./quotation-terms.component.scss'],
})
export class QuotationTermsComponent {
  @Input() sections: Provision[] = [];
  @Input() canEdit = false;
  @Input() expanded = false;
  @Output() saveTerms: EventEmitter<Provision[]> = new EventEmitter<Provision[]>();
  @ViewChild('autosize') autosize: CdkTextareaAutosize;

  selectedSectionIndex: number | null = null;
  notes = '';
  constructor(
    private dialog: MatDialog,
    private direction: Directionality,
    private _ngZone: NgZone
  ) {}

  triggerResize() {
    // Wait for changes to be applied, then trigger textarea resize.
    this._ngZone.onStable.pipe(take(1)).subscribe(() => this.autosize.resizeToFitContent(true));
  }

  onSave() {
    this.saveTerms.emit(this.sections);
  }

  onAddNewSection() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '90vw';
    dialogConfig.position = {
      top: '50px',
    };
    dialogConfig.maxHeight = '90vh';
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(AddNewTermsModalComponent);

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        const { name, clause } = result;
        this.sections.push({ name: name, clause: clause });
        this.saveTerms.emit(this.sections); // Emit the updated data to the parent component
      }
    });
  }

  onRemoveSection(index: number) {
    this.sections.splice(index, 1);
    this.saveTerms.emit(this.sections); // Emit the updated data to the parent component
  }

  openPreviewModal(index: number) {
    this.selectedSectionIndex = index;
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '90vw';
    dialogConfig.position = {
      top: '50px',
    };
    dialogConfig.maxHeight = '90vh';
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    dialogConfig.data = this.sections[this.selectedSectionIndex];
    const dialogRef = this.dialog.open(PreviewTermsModalComponent, dialogConfig);

    dialogRef.afterClosed().subscribe(() => {
      this.selectedSectionIndex = null;
    });
  }

  openEditModal(index: number) {
    this.selectedSectionIndex = index;
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '90vw';
    dialogConfig.position = {
      top: '50px',
    };
    dialogConfig.maxHeight = '90vh';
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    dialogConfig.data = this.sections[this.selectedSectionIndex];
    const dialogRef = this.dialog.open(EditTermsModalComponent, dialogConfig);

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.sections[this.selectedSectionIndex].clause = result;
        this.saveTerms.emit(this.sections); // Emit the updated data to the parent component
      }
      this.selectedSectionIndex = null;
    });
  }

  openPreviewAllModal() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '90vw';
    dialogConfig.position = {
      top: '50px',
    };
    dialogConfig.maxHeight = '90vh';
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    dialogConfig.data = [
      ...this.sections,
      {
        name: 'note',
        clause: this.notes,
      },
    ];
    this.dialog.open(PreviewAllTermsModalComponent, dialogConfig);
  }

  closePreviewModal() {
    this.selectedSectionIndex = null;
  }

  closeEditModal() {
    this.selectedSectionIndex = null;
  }

  getTermsandCondtions(): Provision[] {
    return [
      ...this.sections,
      {
        name: 'note',
        clause: this.notes,
      },
    ];
  }
}
