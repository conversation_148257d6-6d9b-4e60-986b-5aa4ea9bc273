import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { NgxScrollTopModule } from 'ngx-scrolltop';
import { SharedModule } from 'src/app/modules/shared/shared.module';
import { MaterialModule } from '../../material/material.module';
import { CatalogRoutingModule } from './catalog-routing.module';
import { CatalogComponent } from './catalog.component';
import { CategoryFormComponent } from './components/category/category-form/category-form.component';
import { CategoryListComponent } from './components/category/category-list/category-list.component';
import { CatalogDashboardComponent } from './components/dashboard/catalog-dashboard.component';
import { ListProductsComponent } from './components/product/list-products/list-products.component';
import { ProductFormComponent } from './components/product/product-form/product-form.component';
import { UnitsFormComponent } from './components/units/unit-form/units-form.component';
import { UnitsComponent } from './components/units/units.component';
import { InputFormatDirective } from './directives/input-format.directive';

@NgModule({
  declarations: [
    CatalogComponent,
    ProductFormComponent,
    CategoryListComponent,
    CategoryFormComponent,
    UnitsComponent,
    UnitsFormComponent,
    ListProductsComponent,
    CatalogDashboardComponent,
    InputFormatDirective,
  ],
  imports: [
    CommonModule,
    CatalogRoutingModule,
    SharedModule,
    FlexLayoutModule,
    NgxScrollTopModule,
    MaterialModule,
  ],
  providers: [],
})
export class CatalogModule {}
