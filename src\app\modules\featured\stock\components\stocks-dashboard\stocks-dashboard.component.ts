import { Component } from '@angular/core';
import { DashboardModulesHolder } from 'src/app/modules/shared/components/dashboard-modules-holder/modulesHolder';

@Component({
  selector: 'app-stocks-dashboard',
  templateUrl: './stocks-dashboard.component.html',
  styleUrls: ['./stocks-dashboard.component.scss'],
})
export class StocksDashboardComponent {
  productModulesList: DashboardModulesHolder[] = [
    {
      moduleName: 'navigationMenus.openquantityAdjustment',
      moduleDescription: 'Manage and update Open Quantity.',
      modulePermission: ['OpenQtyAdjustments.Create', 'AllPermissions'],
      moduleImage: 'price_change',
      moduleRouterLink: '../adjustments',
      moduleButtonAction: 'BackOffice',
      moduleType: 'subModule',
    },
    {
      moduleName: 'navigationMenus.stockTracking',
      moduleDescription: 'Manage stock adjustments.',
      modulePermission: ['Adjustments.PriceUpdate', 'AllPermissions'],
      moduleImage: 'adjust',
      moduleRouterLink: '../stockadjustment',
      moduleButtonAction: 'BackOffice',
      moduleType: 'subModule',
    },
    {
      moduleName: 'navigationMenus.priceAdjustment',
      moduleDescription: 'Manage unit price adjustments.',
      modulePermission: ['UnitPriceAdjustment.Create', 'AllPermissions'],
      moduleImage: 'price_change',
      moduleRouterLink: '../priceupdate',
      moduleButtonAction: 'BackOffice',
      moduleType: 'subModule',
    },
    {
      moduleName: 'navigationMenus.branchPartner',
      moduleDescription: 'Manage unit price adjustments.',
      modulePermission: ['UnitPriceAdjustment.Create', 'AllPermissions'],
      moduleImage: 'price_change',
      moduleRouterLink: '../branchpartner',
      moduleButtonAction: 'BackOffice',
      moduleType: 'subModule',
    },
    {
      moduleName: 'navigationMenus.stockTransfer',
      moduleDescription: 'Manage unit price adjustments.',
      modulePermission: ['UnitPriceAdjustment.Create', 'AllPermissions'],
      moduleImage: 'price_change',
      moduleRouterLink: '../transfer',
      moduleButtonAction: 'BackOffice',
      moduleType: 'subModule',
    },
  ];
}
