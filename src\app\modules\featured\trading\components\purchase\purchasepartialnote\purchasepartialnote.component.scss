table {
  width: 100%;
  overflow: auto !important;
}

tr.mat-mdc-footer-row {
  font-weight: bold;
}

.mdc-data-table__cell,
.mdc-data-table__header-cell {
  padding: 0 1px 0 0px;
}

.mat-column-itemName {
  white-space: nowrap !important; /* Prevent text from wrapping */
  overflow: visible !important; /* Allow overflow to be visible */
}

.mat-column-unitName {
  white-space: nowrap !important; /* Prevent text from wrapping */
  overflow: visible !important; /* Allow overflow to be visible */
}

.mat-column-itemCode {
  white-space: nowrap !important; /* Prevent text from wrapping */
  overflow: visible !important; /* Allow overflow to be visible */
}

.mat-column-warehouseName {
  max-width: 120px !important;
}

.mat-column-priceType {
  max-width: 120px !important;
}

.mat-column-quantity {
  max-width: 120px !important;
}

.mat-column-returnableQty {
  max-width: 120px !important;
}

.mat-column-purchasePrice {
  max-width: 120px !important;
}

.mat-column-vatAmount {
  max-width: 120px !important;
}

.mat-column-discount {
  max-width: 120px !important;
}

.mat-column-subtotal {
  max-width: 120px !important;
}

.mat-column-notes {
  max-width: 120px !important;
}

.mat-column-profit {
  max-width: 120px !important;
}

.mat-column-retailPrice {
  max-width: 120px !important;
}

.mat-column-wholesalePrice {
  max-width: 120px !important;
}

.mat-column-distributorPrice {
  max-width: 120px !important;
}

mat-option .mat-mdc-row {
  display: flex;
}

.mat-mdc-header-cell {
  text-align: center !important;
}

.mat-mdc-cell {
  text-align: center;
  justify-content: center;
}
.no-wrap {
  white-space: normal;
}
