import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthLayoutComponent } from './layouts/auth-layout/auth-layout.component';
import { FullComponent } from './layouts/full/full.component';
import { InvoiceLayoutComponent } from './layouts/invoice-layout/invoice-layout.component';
import { AuthGuard } from './modules/core/core/guards/auth.guard';
import { AccessDenialComponent } from './modules/shared/components/access-denial/access-denial.component';
import { ProfileComponent } from './modules/shared/components/profile/profile.component';
import { InvoiceDemoComponent } from './modules/invoice/invoice-demo/invoice-demo.component';

export const AppRoutes: Routes = [
  {
    path: '',
    component: FullComponent,
    children: [
      {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full',
      },
      {
        // landing page to show all features on page for selection
        path: 'dashboard',
        canActivate: [AuthGuard],
        loadChildren: () =>
          import('./modules/featured/home/<USER>').then(mod => mod.HomeModule),
      },
      {
        // authentication for the application
        path: 'authentication',
        component: AuthLayoutComponent,
        loadChildren: () =>
          import('./modules/featured/authentication/authentication.module').then(
            mod => mod.AuthenticationModule
          ),
      },
      {
        // all catalog related
        path: 'inventory',
        canActivate: [AuthGuard],
        loadChildren: () =>
          import('./modules/featured/catalog/catalog.module').then(mod => mod.CatalogModule),
      },
      {
        // all accounts related
        path: 'accounts',
        canActivate: [AuthGuard],
        loadChildren: () =>
          import('./modules/featured/accounts/accounts.module').then(mod => mod.AccountsModule),
      },
      {
        // all catalog related
        path: 'stocks',
        canActivate: [AuthGuard],
        loadChildren: () =>
          import('./modules/featured/stock/stock.module').then(mod => mod.StockModule),
      },
      {
        // company related
        path: 'enterprise',
        canActivate: [AuthGuard],
        loadChildren: () =>
          import('./modules/featured/settings/settings.module').then(mod => mod.SettingsModule),
      },
      {
        path: 'identity',
        canActivate: [AuthGuard],
        loadChildren: () =>
          import('./modules/featured/identity/identity.module').then(mod => mod.IdentityModule),
      },
      {
        path: 'reports',
        canActivate: [AuthGuard],
        loadChildren: () =>
          import('./modules/featured/reporting/reporting.module').then(mod => mod.ReportingModule),
      },
      {
        // profile update
        path: 'profile',
        component: ProfileComponent,
        data: {
          title: 'Profile',
          urls: [{ title: 'Dashboard', url: '/dashboard' }],
        },
      },
      {
        // same as profile
        path: 'accountsettings',
        component: ProfileComponent,
        data: {
          title: 'Account Settings',
          urls: [{ title: 'Dashboard', url: '/dashboard' }],
        },
      },
      {
        path: 'access-denial',
        component: AccessDenialComponent,
      },
      {
        path: 'utility',
        canActivate: [AuthGuard],
        loadChildren: () =>
          import('./modules/featured/utility/utility.module').then(mod => mod.UtilityModule),
      },
      {
        // company related
        path: 'trading',
        canActivate: [AuthGuard],
        loadChildren: () =>
          import('./modules/featured/trading/trading.module').then(mod => mod.TradingModule),
      },
      {
        path: 'finance',
        canActivate: [AuthGuard],
        loadChildren: () =>
          import('./modules/featured/finance/finance.module').then(mod => mod.FinanceModule),
      },
      {
        path: 'owner',
        //canActivate: [AuthGuard],
        loadChildren: () =>
          import('./modules/featured/owner/owner.module').then(mod => mod.OwnerModule),
      },
    ],
  },
  {
    path: 'invoice',
    component: InvoiceLayoutComponent,
    children: [
      {
        path: 'demo',
        component: InvoiceDemoComponent,
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forRoot(AppRoutes)],
  exports: [RouterModule],
})
export class AppRoutingModule {}
