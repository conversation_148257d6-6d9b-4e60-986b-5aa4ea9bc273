import { Directionality } from '@angular/cdk/bidi';
import { SelectionModel } from '@angular/cdk/collections';
import {
  AfterViewInit,
  Component,
  Inject,
  Input,
  OnInit,
  Optional,
  ViewChild,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatDateRangeInput } from '@angular/material/datepicker';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/core/api/common.service';
import { SalesService } from 'src/app/core/api/trading/sales.service';
import { ZatcaService } from 'src/app/core/api/trading/zatca.service';
import {
  invoiceStages,
  invoiceType,
  salesPaymentStatus,
} from 'src/app/core/configs/dropDownConfig';
import { SalesIntegratedCreateResponse, SaletransactionTypes } from 'src/app/core/interfaces/sales';
import { IZatcaEInvoices, InvoiceParams, ZatacaInvoices } from 'src/app/core/interfaces/zatca';
import { SalesIntegeratedParams } from 'src/app/core/models/params/salesParams';
import { getFormValueExcludeSearchBox } from 'src/app/core/utils/date-utils';
import { Branch } from 'src/app/modules/featured/catalog/models/branch';
import { BulkEditData } from 'src/app/modules/featured/catalog/models/bulkedit';
import { Category } from 'src/app/modules/featured/catalog/models/category';
import { SearchboxComponent } from '../searchbox/searchbox.component';
import { ActionType } from 'src/app/core/enums/actionType';

@Component({
  selector: 'app-zatca-invoice-status',
  templateUrl: './zatca-invoice-status.component.html',
  styleUrls: ['./zatca-invoice-status.component.scss'],
})
export class ZatcaInvoiceStatusComponent implements OnInit, AfterViewInit {
  @Input() payables = false;
  @ViewChild('searchBoxForm') searchBoxForm: SearchboxComponent;
  @ViewChild(MatPaginator) set matPaginator(paginator: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = paginator;
    }
  }
  @ViewChild(MatSort) set matSort(sort: MatSort) {
    if (this.dataSource) {
      this.dataSource.sort = sort;
    }
  }
  invoices: ZatacaInvoices[];
  dataSource: MatTableDataSource<ZatacaInvoices>;
  disabledSelection = false;
  loading = true;
  categoryList: Category[];
  displayedColumns: string[] = [];
  branchList: Branch[];
  warehouseList: any;
  allWarehouses: any;
  unitList: any;
  filterForm: UntypedFormGroup;
  isReportPulling = false;
  isAdvancedSearchEnabled = false;
  isBranchNotSelected = true;
  status = salesPaymentStatus;
  todayDate: Date = new Date();
  range = new FormGroup({
    startDate: new FormControl<Date | null>(null),
    endDate: new FormControl<Date | null>(new Date()),
  });
  picker: MatDateRangeInput<Date>;
  openSubscription: any;
  selection = new SelectionModel<any>(false, []);
  invoiceStages = [];
  invoiceType = [];
  mode = ActionType.create;
  constructor(
    @Optional() @Inject(MAT_DIALOG_DATA) public data: BulkEditData,
    private fb: UntypedFormBuilder,
    private direction: Directionality,
    private salesService: SalesService,
    private zatcaService: ZatcaService,
    private toastr: ToastrService,
    private commonService: CommonService
  ) {
    this.filterForm = this.fb.group({
      issueDateFrom: [new Date(), Validators.required],
      issueDateTo: [new Date(), Validators.required],
      invoiceType: [null, Validators.required],
      stage: [null, Validators.required],
    });
  }

  /** Whether the number of selected elements matches the total number of rows. */
  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  toggleAllRows() {
    if (this.isAllSelected()) {
      this.selection.clear();
      return;
    }

    this.selection.select(...this.dataSource.data);
  }

  ngOnInit(): void {
    this.displayedColumns = [
      'select',
      'documentNumber',
      'issueDate',
      'documentType',
      'invoiceType',
      'stage',
    ];
  }

  ngAfterViewInit() {
    this.invoiceStages = invoiceStages;
    this.invoiceType = invoiceType;
  }

  clearFilters(event: Event) {
    event.preventDefault();
    this.filterForm.markAsUntouched();
    this.filterForm.markAsPristine();
    this.filterForm.reset();
    this.resetForm();
  }

  resetForm() {
    this.filterForm.get('issueDateFrom').setValue(new Date());
    this.filterForm.get('issueDateTo').setValue(new Date());
  }

  getAllZatcaInvoices(): void {
    this.zatcaService.getZatcaEInvoices().subscribe((result: IZatcaEInvoices) => {
      console.log(result, result.invoices);
      this.invoices = result.invoices;
      this.dataSource = new MatTableDataSource<ZatacaInvoices>(this.invoices);
      this.loading = false;
    });
  }

  getAllZatcaInvoicesWithFilter(): void {
    const params: InvoiceParams = {
      invoiceType: this.filterForm.get('invoiceType').value,
      issueDateFrom: this.filterForm.get('issueDateFrom').value,
      issueDateTo: this.filterForm.get('issueDateTo').value,
      stage: this.filterForm.get('stage').value,
    };
    this.zatcaService.getInvoices(params).subscribe((result: IZatcaEInvoices) => {
      console.log(result, result.invoices);
      this.invoices = result.invoices;
      this.dataSource = new MatTableDataSource<ZatacaInvoices>(this.invoices);
      this.loading = false;
    });
  }

  sendToZatca(event: Event): void {
    event.preventDefault();
    const selectedData = this.selection.selected;
    console.log('selected data', selectedData);
    const salesParams = new SalesIntegeratedParams();
    salesParams.documentUuid = selectedData[0]['documentUuid'];
    salesParams.isManuallyReported = true;
    console.log(salesParams, this.filterForm);
    this.loading = true;
    this.salesService
      .sendToZatcaInvoice(salesParams)
      .subscribe((result: SalesIntegratedCreateResponse) => {
        console.log(result);
        this.toastr.info(`${result.documentNumber} - ${result.status}`);
        this.commonService.playSuccessSound();
        this.getAllZatcaInvoicesWithFilter();
        this.selection.clear();
        this.loading = false;
      });
  }

  getZatcaParams(documentUuid: string) {
    const data = Object.assign({
      pageSize: 50,
      documentUuid: documentUuid,
      isManuallyReported: true,
    });
    return data;
  }

  getParams() {
    const data = Object.assign(
      {
        pageSize: 99999,
        invoiceStatus: 'NOTRETURNED',
        transactionType: this.payables ? SaletransactionTypes.purchase : SaletransactionTypes.sales,
      },
      ...[getFormValueExcludeSearchBox(this.filterForm), this.searchBoxForm.searchBoxForm.value]
    );
    return data;
  }

  search(event?: Event): void {
    event?.preventDefault();
    this.filterForm.markAllAsTouched();
    if (this.filterForm.valid) {
      this.getAllZatcaInvoicesWithFilter();
    } else {
      this.commonService.playErrorSound();
    }
  }

  getZatcaErrorForInvoice(documentUUID: string): void {
    this.zatcaService.getZatcaErrorForInvoice(documentUUID, this.direction.value);
  }

  getXmlForInvoice(documentUUID: string): void {
    this.zatcaService.getXmlForInvoice(documentUUID, this.direction.value);
  }

  getStatusClass(stage: string) {
    this.zatcaService.getZatcaStatusClass(stage);
  }

  onRowSelect(row: any, event: MatCheckboxChange) {
    if (event.checked) {
      // Select the row
      this.selection.select(row);
    } else {
      // Deselect the row
      this.selection.deselect(row);
    }
  }
}
