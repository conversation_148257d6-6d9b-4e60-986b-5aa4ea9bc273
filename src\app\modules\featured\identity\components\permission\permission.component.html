<ng-container *ngIf="!isLoading">
  <div class="d-flex justify-content-center align-items-center" *ngIf="isEditMode || isViewMode">
    <span class="text-primary">{{ 'roles.profile' | translate }}: {{ roleName }}</span>
  </div>
  <mat-card appearance="outlined">
    <div class="col-12 col-sm-12 m-t-10 m-b-10" *ngIf="!isViewMode">
      <mat-checkbox
        [(ngModel)]="selectAllTab"
        [disabled]="isViewMode"
        [indeterminate]="selectAllTabIntermediate"
        (change)="onClickSelectAll($event.checked)"
        color="warn">
        {{ 'common.grantAllPermission' | translate }}
      </mat-checkbox>
    </div>
    <mat-tab-group
      #tabs
      (selectedTabChange)="onChangeGroup($event)"
      disableRipple="true"
      mat-stretch-tabs="false"
      animationDuration="0ms">
      <mat-tab *ngFor="let group of data.groups" [label]="group.displayName">
        <ng-template mat-tab-label>
          <mat-icon
            class="header-badge"
            [matBadgeSize]="'medium'"
            [matBadge]="getAssignedCount(group.name)">
          </mat-icon>
          {{ group.displayName | translate }}
        </ng-template>

        <mat-checkbox
          *ngIf="!isViewMode"
          [(ngModel)]="selectThisTab"
          [disabled]="isViewMode"
          [indeterminate]="someComplete(group?.name)"
          (change)="setAll($event.checked, group)"
          color="warn">
          {{ 'common.selectAll' | translate }}
        </mat-checkbox>
        <mat-divider></mat-divider>
        <div
          *ngFor="let permission of selectedGroupPermissions; let i = index; trackBy: trackByFn"
          [ngStyle]="permission.style">
          <mat-divider *ngIf="permission?.parentName === null"></mat-divider>
          <mat-checkbox
            #permissionCheckbox
            [disabled]="isViewMode"
            [checked]="permission.isGranted"
            [color]="permission.parentName === null ? 'warn' : 'primary'"
            (change)="updateAllComplete(permission, $event.checked)">
            {{ permission.displayName }}
          </mat-checkbox>
        </div>
      </mat-tab>
    </mat-tab-group>
  </mat-card>
</ng-container>
