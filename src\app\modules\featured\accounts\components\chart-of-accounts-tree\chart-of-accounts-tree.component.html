<mat-card class="cardWithShadow">
  <mat-card-content>
    <input (input)="filtertree($event.target.value)" type="text" placeholder="Filter" />
    <cdk-tree [dataSource]="dataSource" [treeControl]="treeControl">
      <!-- This is the tree node template for leaf nodes -->
      <cdk-nested-tree-node
        class="example-tree-node"
        *cdkTreeNodeDef="let node"
        (mouseenter)="showDetailsForNode(node)">
        <!-- use a disabled button to provide padding for tree leaf -->
        <!-- <button mat-icon-button disabled></button> -->

        <button
          class="m-r-10"
          [attr.aria-label]="'Toggle ' + node.nameEnglish"
          mat-icon-button
          cdkTreeNodeToggle>
          <i-tabler class="icon-10" *ngIf="node.children.length === 0" name="circle-dot"></i-tabler>
        </button>
        <span class="f-s-16" [style.color]="'red'">{{ node.nameEnglish }}</span>
      </cdk-nested-tree-node>

      <!-- This is the tree node template for expandable nodes -->
      <cdk-nested-tree-node class="example-tree-node" *cdkTreeNodeDef="let node; when: hasChild">
        <button
          class="m-r-10"
          [attr.aria-label]="'Toggle ' + node.nameEnglish"
          mat-icon-button
          cdkTreeNodeToggle>
          <i-tabler
            class="mat-icon-rtl-mirror"
            class="icon-10"
            *ngIf="treeControl.isExpanded(node)"
            name="circle-arrow-up"></i-tabler>
          <i-tabler
            class="mat-icon-rtl-mirror"
            class="icon-10"
            *ngIf="!treeControl.isExpanded(node)"
            name="circle-arrow-down"></i-tabler>
        </button>
        <span class="f-s-16" [ngClass]="node.color">{{ node.nameEnglish }}</span>
        <div [class.example-tree-invisible]="!treeControl.isExpanded(node)">
          <ng-container cdkTreeNodeOutlet></ng-container>
        </div>
      </cdk-nested-tree-node>
    </cdk-tree>
  </mat-card-content>
</mat-card>
