import { Component, Input, OnInit, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort, Sort } from '@angular/material/sort';
import { Subject, takeUntil } from 'rxjs';
import { ReportService } from '../../../settings/services/report.service';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { CookieService } from 'ngx-cookie-service';

export interface ReportConfig {
  id: number;
  type: number;
  name: string;
  nameArabic?: string;
  authority: string;
  languageCode: string;
  searchConfigs: SearchConfig[];
  endPoint: string;
  jasperEndPoint?: string;
}

export interface SearchConfig {
  field: string;
  type: 'dropdown' | 'multiSelectDropdown' | 'input' | 'date' | 'dateRange';
  mandatory: boolean;
  placeholder: string;
  position: number;
  isAdvanced: boolean;
  fieldLabel: string;
  backendParam: string;
  idField: string;
}

export interface ReportData {
  reportData: any[];
  columnsToDisplay: string[];
  totalRecordsCount: number;
  totalPages: number;
  morePages: boolean;
}

@Component({
  selector: 'app-dynamic-report',
  templateUrl: './dynamic-report.component.html',
  styleUrls: ['./dynamic-report.component.scss'],
})
export class DynamicReportComponent implements OnInit, OnDestroy {
  @Input() reportConfigs: ReportConfig[] = [];
  @Input() dataProviders: { [key: string]: () => any[] } = {};

  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  // Component state
  selectedReport: ReportConfig | null = null;
  filterForm: FormGroup;
  dataSource = new MatTableDataSource<any>([]);
  displayedColumns: string[] = [];
  allColumns: string[] = [];

  // Pagination
  totalItems = 0;
  pageSize = 100;
  currentPage = 0;

  // Loading states
  isLoading = false;
  isGeneratingReport = false;
  showAdvancedFilters = false;

  private destroy$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private reportService: ReportService,
    private authService: AuthService,
    private cookieService: CookieService
  ) {}

  ngOnInit(): void {
    this.initializeComponent();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeComponent(): void {
    // Initialize with first report if available
    if (this.reportConfigs.length > 0) {
      this.selectReport(this.reportConfigs[0]);
    }
  }

  selectReport(report: ReportConfig): void {
    this.selectedReport = report;
    this.createDynamicForm();
    this.resetReportData();
  }

  private createDynamicForm(): void {
    if (!this.selectedReport) return;

    const formControls: { [key: string]: any } = {};

    this.selectedReport.searchConfigs.forEach(config => {
      const validators = config.mandatory ? [Validators.required] : [];
      formControls[config.backendParam] = [null, validators];

      // Handle date range fields
      if (config.type === 'dateRange') {
        formControls[`${config.backendParam}From`] = [null, validators];
        formControls[`${config.backendParam}To`] = [null, validators];
      }
    });

    this.filterForm = this.fb.group(formControls);
    this.setDefaultValues();
    this.subscribeToFormChanges();
  }

  private setDefaultValues(): void {
    // Set default branch and year from cookies
    const branchId = +this.cookieService.get('branchId');
    const yearId = +this.cookieService.get('yearId');

    if (branchId && this.filterForm.get('branchId')) {
      this.filterForm.patchValue({ branchId });
    }
    if (yearId && this.filterForm.get('yearId')) {
      this.filterForm.patchValue({ yearId });
    }
  }

  private subscribeToFormChanges(): void {
    this.filterForm.valueChanges.pipe(takeUntil(this.destroy$)).subscribe(() => {
      // Handle dependent field updates
      this.updateDependentFields();
    });
  }

  private updateDependentFields(): void {
    // Update year list based on selected branch
    const branchId = this.filterForm.get('branchId')?.value;
    if (branchId && this.dataProviders['yearId']) {
      // Trigger year list update
    }
  }

  getFieldData(fieldName: string): any[] {
    return this.dataProviders[fieldName] ? this.dataProviders[fieldName]() : [];
  }

  getFieldConfig(fieldName: string): SearchConfig | undefined {
    return this.selectedReport?.searchConfigs.find(config => config.backendParam === fieldName);
  }

  isFieldVisible(config: SearchConfig): boolean {
    return !config.isAdvanced || this.showAdvancedFilters;
  }

  generateReport(): void {
    if (!this.selectedReport || !this.filterForm.valid) {
      this.filterForm.markAllAsTouched();
      return;
    }

    this.isGeneratingReport = true;
    const params = this.buildReportParams();

    this.reportService
      .getInventoryReports(params)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (result: ReportData) => {
          this.handleReportSuccess(result);
        },
        error: error => {
          this.handleReportError(error);
        },
        complete: () => {
          this.isGeneratingReport = false;
        },
      });
  }

  private buildReportParams(): any {
    const formValue = this.filterForm.value;
    const params: any = {
      templateId: this.selectedReport?.id,
      reportType: this.selectedReport?.name,
      endPoint: this.selectedReport?.endPoint,
      jasperEndPoint: this.selectedReport?.jasperEndPoint,
      page: this.currentPage + 1,
      pageSize: this.pageSize,
    };

    // Map form values to backend parameters
    this.selectedReport?.searchConfigs.forEach(config => {
      const value = formValue[config.backendParam];
      if (value !== null && value !== undefined) {
        params[config.backendParam] = value;
      }
    });

    return params;
  }

  private handleReportSuccess(result: ReportData): void {
    this.dataSource.data = result.reportData;
    this.totalItems = result.totalRecordsCount;

    if (this.displayedColumns.length === 0) {
      this.displayedColumns = result.columnsToDisplay;
      this.allColumns = [...result.columnsToDisplay];
    }
  }

  private handleReportError(error: any): void {
    console.error('Report generation failed:', error);
    // Handle error (show toast, etc.)
  }

  onPageChange(event: PageEvent): void {
    this.pageSize = event.pageSize;
    this.currentPage = event.pageIndex;
    this.generateReport();
  }

  onSortChange(sort: Sort): void {
    // Handle sorting
    this.generateReport();
  }

  toggleAdvancedFilters(): void {
    this.showAdvancedFilters = !this.showAdvancedFilters;
  }

  resetReport(): void {
    this.selectedReport = null;
    this.resetReportData();
  }

  private resetReportData(): void {
    this.dataSource.data = [];
    this.displayedColumns = [];
    this.allColumns = [];
    this.totalItems = 0;
    this.currentPage = 0;
  }

  downloadPdf(): void {
    if (!this.selectedReport || this.dataSource.data.length === 0) return;

    const params = { ...this.buildReportParams(), type: 'PDF' };
    this.reportService
      .getInventoryJasperReports(params)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: result => {
          // Handle PDF download
        },
        error: error => {
          console.error('PDF generation failed:', error);
        },
      });
  }

  updateDisplayedColumns(selectedColumns: string[]): void {
    this.displayedColumns = selectedColumns.length ? selectedColumns : this.allColumns;
  }
}
