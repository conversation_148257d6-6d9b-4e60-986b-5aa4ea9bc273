import { Component, Input, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { Subject } from 'rxjs';

export interface ReportConfig {
  id: number;
  type: number;
  name: string;
  nameArabic?: string;
  authority: string;
  languageCode: string;
  searchConfigs: SearchConfig[];
  endPoint: string;
  jasperEndPoint?: string;
}

export interface SearchConfig {
  field: string;
  type: 'dropdown' | 'multiSelectDropdown' | 'input' | 'date' | 'dateRange';
  mandatory: boolean;
  placeholder: string;
  position: number;
  isAdvanced: boolean;
  fieldLabel: string;
  backendParam: string;
  idField: string;
}

export interface ReportData {
  reportData: any[];
  columnsToDisplay: string[];
  totalRecordsCount: number;
  totalPages: number;
  morePages: boolean;
}

@Component({
  selector: 'app-dynamic-report',
  templateUrl: './dynamic-report.component.html',
  styleUrls: ['./dynamic-report.component.scss'],
})
export class DynamicReportComponent implements OnInit, OnDestroy {
  @Input() reportConfigs: ReportConfig[] = [];
  @Input() dataProviders: { [key: string]: () => any[] } = {};

  // Component state
  selectedReport: ReportConfig | null = null;
  filterForm: FormGroup;

  // Loading states
  isLoading = false;
  isGeneratingReport = false;
  showAdvancedFilters = false;

  private destroy$ = new Subject<void>();

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.initializeComponent();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeComponent(): void {
    // Initialize with first report if available
    if (this.reportConfigs.length > 0) {
      this.selectReport(this.reportConfigs[0]);
    }
  }

  selectReport(report: ReportConfig): void {
    this.selectedReport = report;
    this.createDynamicForm();
  }

  private createDynamicForm(): void {
    if (!this.selectedReport) return;

    const formControls: { [key: string]: any } = {};

    this.selectedReport.searchConfigs.forEach(config => {
      const validators = config.mandatory ? [Validators.required] : [];
      formControls[config.backendParam] = [null, validators];

      // Handle date range fields
      if (config.type === 'dateRange') {
        formControls[`${config.backendParam}From`] = [null, validators];
        formControls[`${config.backendParam}To`] = [null, validators];
      }
    });

    this.filterForm = this.fb.group(formControls);
    this.setDefaultValues();
    this.subscribeToFormChanges();
  }

  private setDefaultValues(): void {
    // Set default values if needed
    console.log('Setting default values');
  }

  private subscribeToFormChanges(): void {
    // Subscribe to form changes if needed
    console.log('Subscribing to form changes');
  }

  getFieldData(fieldName: string): any[] {
    return this.dataProviders[fieldName] ? this.dataProviders[fieldName]() : [];
  }

  getFieldConfig(fieldName: string): SearchConfig | undefined {
    return this.selectedReport?.searchConfigs.find(config => config.backendParam === fieldName);
  }

  isFieldVisible(config: SearchConfig): boolean {
    return !config.isAdvanced || this.showAdvancedFilters;
  }

  generateReport(): void {
    if (!this.selectedReport || !this.filterForm.valid) {
      this.filterForm.markAllAsTouched();
      return;
    }

    this.isGeneratingReport = true;
    const params = this.buildReportParams();

    // Simulate API call for demo
    console.log('Report generated with params:', params);

    setTimeout(() => {
      this.isGeneratingReport = false;
    }, 2000);
  }

  private buildReportParams(): any {
    const formValue = this.filterForm.value;
    const params: any = {
      templateId: this.selectedReport?.id,
      reportType: this.selectedReport?.name,
      endPoint: this.selectedReport?.endPoint,
      jasperEndPoint: this.selectedReport?.jasperEndPoint,
    };

    // Map form values to backend parameters
    this.selectedReport?.searchConfigs.forEach(config => {
      const value = formValue[config.backendParam];
      if (value !== null && value !== undefined) {
        params[config.backendParam] = value;
      }
    });

    return params;
  }

  toggleAdvancedFilters(): void {
    this.showAdvancedFilters = !this.showAdvancedFilters;
  }

  resetReport(): void {
    this.selectedReport = null;
  }
}
