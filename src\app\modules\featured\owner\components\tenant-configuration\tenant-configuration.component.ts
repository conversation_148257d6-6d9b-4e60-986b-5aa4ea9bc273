import {
  AfterViewInit,
  Component,
  Inject,
  OnInit,
  Optional,
  SkipSelf,
  ViewChild,
} from '@angular/core';
import { AbstractControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/core/api/common.service';
import { allowedFeatures, indentifactionCode } from 'src/app/core/configs/dropDownConfig';
import { ActionType, EnterpriseType } from 'src/app/core/enums/actionType';
import { IActionEventType } from 'src/app/core/interfaces/actionEventType';
import { ICustomerModalData } from 'src/app/core/interfaces/customer';
import { IDistributor } from 'src/app/core/interfaces/distributor';
import { convertDateForBE } from 'src/app/core/utils/date-utils';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { AddressComponent } from 'src/app/modules/shared/components/address/address.component';
import { Account } from '../../../accounts/models/account';
import { CostCentre } from '../../../accounts/models/costCentre';
import { CustomerFormComponent } from '../../../trading/components/customers/customer-form/customer-form.component';
import { TenantsService } from '../../services/tenants.service';
import { ITenant } from '../../tenant';

@Component({
  selector: 'app-tenant-configuration',
  templateUrl: './tenant-configuration.component.html',
  styleUrls: ['./tenant-configuration.component.scss'],
})
export class TenantConfigurationComponent implements OnInit, AfterViewInit {
  @ViewChild('addressForm', { static: false }) addressForm: AddressComponent;
  tenantForm: UntypedFormGroup;
  tenantId: string;
  parentAccounts: Account[] = [];
  distributorAccounts: IDistributor[] = [];
  costCentreAccounts: CostCentre[] = [];
  mode: ActionType;
  actionMode = ActionType;
  allowedFeaturesList = allowedFeatures;
  identificationCodes = indentifactionCode;
  enterpriseTypes = Object.values(EnterpriseType);
  constructor(
    @Optional() @SkipSelf() @Inject(MAT_DIALOG_DATA) public modalData: ICustomerModalData,
    @Optional() @SkipSelf() public dialogRef: MatDialogRef<CustomerFormComponent>,
    private authService: AuthService,
    private toastr: ToastrService,
    private tenantService: TenantsService,
    private fb: UntypedFormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private commonService: CommonService
  ) {}

  get IsViewMode() {
    return this.mode === this.actionMode.view;
  }

  ngOnInit(): void {
    this.mode = this.route.snapshot.data['mode'];
    this.initializeForm();
    this.route.params.subscribe(params => {
      this.tenantId = params['id'];
      if (this.tenantId) {
        this.getTenant(this.tenantId);
      }
    });
  }

  ngAfterViewInit(): void {
    if (this.mode === this.actionMode.view) {
      this.disbaleAllForms();
    }
  }

  getTenant(customerId: string): void {
    this.tenantService.getTenantsById(customerId).subscribe(
      response => {
        this.patchData(response);
      },
      error => console.log(error)
    );
  }

  initializeForm(data?: ITenant) {
    this.tenantForm = this.fb.group({
      tenantId: [data?.tenantId ?? null],
      enterpriseType: [data?.enterpriseType ?? null],
      tenantName: [data?.tenantName ?? null, Validators.compose([Validators.required])],
      commercialRegistrationNo: [
        data?.commercialRegistrationNo ?? null,
        Validators.compose([Validators.required]),
      ],
      phoneNumber: [data?.phoneNumber ?? null],
      emailId: [data?.emailId ?? null],
      notes: [data?.notes ?? null],
      contactName: [data?.contactName ?? null, Validators.compose([Validators.required])],
      expirationDate: [data?.expirationDate ?? null, Validators.compose([Validators.required])],
      installationDate: [
        data?.installationDate ?? new Date(),
        Validators.compose([Validators.required]),
      ],
      isActive: [data?.isActive ?? true],
      address: [data?.address ?? null],
      identification: [data?.identification ?? '', Validators.compose([Validators.required])],
      identificationCode: [
        data?.identificationCode ?? null,
        Validators.compose([Validators.required]),
      ],
      vatNumber: [
        data?.vatNumber ?? null,
        [
          Validators.required,
          Validators.minLength(15),
          Validators.maxLength(15),
          this.vatNumberValidator,
        ],
      ],
      tenantConfig: this.fb.group({
        maxBranches: [
          data?.tenantConfig?.maxBranches ?? 2,
          Validators.compose([Validators.required]),
        ],
        maxWarehouses: [
          data?.tenantConfig?.maxWarehouses ?? 2,
          Validators.compose([Validators.required]),
        ],
        maxUsers: [data?.tenantConfig?.maxUsers ?? 5, Validators.compose([Validators.required])],
        storageLimit: [
          data?.tenantConfig?.storageLimit ?? 100,
          Validators.compose([Validators.required]),
        ],
        allowedFeatures: [
          data?.tenantConfig?.allowedFeatures ?? null,
          Validators.compose([Validators.required]),
        ],
      }),
    });
  }

  vatNumberValidator(control: AbstractControl): { [key: string]: boolean } | null {
    const vatNumber = control?.value?.toString().trim(); // Convert to string and trim whitespace
    console.log(vatNumber);

    if (
      !vatNumber ||
      vatNumber.length !== 15 ||
      vatNumber[0] !== '3' ||
      vatNumber[vatNumber.length - 1] !== '3'
    ) {
      return { invalidVatNumber: true };
    }

    return null;
  }

  patchData(data: ITenant): void {
    this.initializeForm(data);
  }

  private disableForm(): void {
    // if (this.mode === this.actionMode.view) {
    //   this.disbaleAllForms();
    // }
  }

  private disbaleAllForms(): void {
    this.tenantForm.disable();
    this.addressForm.disableForm();
  }

  onSubmit(event: IActionEventType) {
    this.addressForm.markFormAsTouched();
    this.tenantForm.markAllAsTouched();
    if (this.tenantForm?.valid && this.addressForm?.isValid()) {
      this.tenantForm.controls['expirationDate'].setValue(
        convertDateForBE(this.tenantForm.controls['expirationDate'].value)
      );
      this.tenantForm.controls['installationDate'].setValue(
        convertDateForBE(this.tenantForm.controls['installationDate'].value)
      );
      // create Mode
      if (event.actionType === this.actionMode.create) {
        console.log('this.customerForm.value', this.tenantForm.value);
        this.createTenants(this.tenantForm.value);
      }
      // edit Mode
      if (event.actionType === this.actionMode.edit) {
        this.editTenants(this.tenantForm.value);
      }
    } else {
      this.commonService.playErrorSound();
    }
  }

  onNoClick(): void {
    this.dialogRef.close('cancel');
  }

  createTenants(tenantBasic: ITenant): void {
    this.tenantService.createTenants(tenantBasic).subscribe(
      () => {
        console.log('All APIs completed successfully.');
        //this.toastr.success('Tenant Created Successfully');
        this.commonService.playSuccessSound();
        this.router.navigate(['../'], { relativeTo: this.route });
      },
      error => {
        console.error('An error occurred:', error);
        // Handle errors
      }
    );
  }

  editTenants(tenantBasic: ITenant): void {
    this.tenantService.editTenantsBasic(tenantBasic, this.tenantId).subscribe(
      () => {
        console.log('All APIs completed successfully.');
        //this.toastr.success('Tenant details Updated Successfully');
        this.commonService.playSuccessSound();
        this.router.navigate(['../../'], { relativeTo: this.route });
      },
      error => {
        console.error('An error occurred:', error);
        // Handle errors
      }
    );
  }
}
