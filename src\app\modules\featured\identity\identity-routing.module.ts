import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionGuard } from '../../core/core/guards/permission.guard';
import { IdentityDashboardComponent } from './components/dashboard/identity-dashboard.component';
import { PermissionComponent } from './components/permission/permission.component';
import { RoleFormComponent } from './components/role/role-form/role-form.component';
import { RoleListComponent } from './components/role/role-list/role-list.component';
import { UserFormComponent } from './components/users/user-form/user-form.component';
import { UserListComponent } from './components/users/user-list/user-list.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'dashboard',
    pathMatch: 'full',
  },
  {
    path: 'dashboard',
    component: IdentityDashboardComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Identity Management',
      urls: [{ title: 'Main Dashboard', url: '/dashboard' }],
      allowedPermissions: ['IdentityManagement', 'AllPermissions'],
    },
  },
  {
    path: 'roles',
    component: RoleListComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'roles.listRole',
      urls: [{ title: 'roles.listRole', url: 'identity/dashboard' }],
      allowedPermissions: ['Role', 'AllPermissions'],
    },
  },
  {
    path: 'roles/create',
    component: RoleFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'roles.createRole',
      urls: [{ title: 'Role and Access', url: '/identity/roles' }],
      allowedPermissions: ['Role.Create', 'AllPermissions'],
      mode: 'create',
    },
  },
  {
    path: 'roles/edit/:id',
    component: RoleFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'roles.editRole',
      urls: [{ title: 'roles.editRole', url: '/identity/roles' }],
      allowedPermissions: ['Role.Update', 'AllPermissions'],
      mode: 'edit',
    },
  },
  {
    path: 'roles/view/:id',
    component: RoleFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'roles.viewRole',
      urls: [{ title: 'roles.viewRole', url: '/identity/roles' }],
      allowedPermissions: ['Role.View', 'AllPermissions'],
      mode: 'view',
    },
  },
  {
    path: 'permission',
    component: PermissionComponent,
    data: {
      title: 'Inventory Management',
      urls: [{ title: 'Identity Management', url: 'identity/dashboard' }],
    },
  },
  {
    path: 'users',
    component: UserListComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'users.userListings',
      urls: [{ title: 'Identity Management', url: 'identity/dashboard' }],
      allowedPermissions: ['User', 'AllPermissions'],
    },
  },
  {
    path: 'users/create',
    component: UserFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'users.register_user',
      urls: [{ title: 'Identity Management', url: 'identity/dashboard' }],
      allowedPermissions: ['User.Create', 'AllPermissions'],
      mode: 'create',
    },
  },
  {
    path: 'users/edit/:id',
    component: UserFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'users.editUser',
      urls: [{ title: 'Identity Management', url: 'identity/dashboard' }],
      allowedPermissions: ['User.Update', 'AllPermissions'],
      mode: 'edit',
    },
  },
  {
    path: 'users/view/:id',
    component: UserFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'users.viewUser',
      urls: [{ title: 'Identity Management', url: 'identity/dashboard' }],
      allowedPermissions: ['User.View', 'AllPermissions'],
      mode: 'view',
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class IdentityRoutingModule {}
