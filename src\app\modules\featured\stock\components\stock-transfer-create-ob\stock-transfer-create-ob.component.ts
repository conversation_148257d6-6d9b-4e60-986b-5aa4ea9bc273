import { animate, state, style, transition, trigger } from '@angular/animations';
import { Directionality } from '@angular/cdk/bidi';
import { ChangeDetectorRef, Component, NgZone, OnInit, ViewChild } from '@angular/core';
import {
  AbstractControl,
  UntypedFormArray,
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatSelectChange } from '@angular/material/select';
import { MatTable } from '@angular/material/table';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { CustomValidators } from 'ngx-custom-validators';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject, forkJoin, of } from 'rxjs';
import { CostCentresApiService } from 'src/app/core/api/accounts/cost-centres-api.service';
import { CommonService } from 'src/app/core/api/common.service';
import { PartnerService } from 'src/app/core/api/partners/partner.service';
import { StaticDataService } from 'src/app/core/api/static-data.service';
import { StockService } from 'src/app/core/api/stocks/stock.service';
import { StoreService } from 'src/app/core/api/store.service';
import { DistributorService } from 'src/app/core/api/trading/distributor.service';
import { SalesAdjustmentService } from 'src/app/core/api/trading/sales-adjustments.service';
import { stockTypes } from 'src/app/core/configs/dropDownConfig';
import { ActionType } from 'src/app/core/enums/actionType';
import { ICustomerView } from 'src/app/core/interfaces/customer';
import { IDistributor } from 'src/app/core/interfaces/distributor';
import { InvalidQuantity, error } from 'src/app/core/interfaces/error';
import { IPaymentViewData } from 'src/app/core/interfaces/payment';
import { IPurchaseInvoice } from 'src/app/core/interfaces/purchase';
import { DeleteItemRows } from 'src/app/core/interfaces/sales';
import { displayValue, miscOptions } from 'src/app/core/models/wrappers/displayValue';
import { convertDateForBE } from 'src/app/core/utils/date-utils';
import { CostCentre } from 'src/app/modules/featured/accounts/models/costCentre';
import { IInventory } from 'src/app/modules/featured/catalog/models/product';
import { ConfirmDialogComponent } from 'src/app/modules/shared/components/confirm-dialog/confirm-dialog.component';
import { DeleteConfirmationComponent } from 'src/app/modules/shared/components/delete-confirmation/delete-confirmation.component';
import { PaymentsPostSaleComponent } from 'src/app/modules/shared/components/payments-post-sale/payments-post-sale.component';
import { ProductSearchSelectionComponent } from 'src/app/modules/shared/components/product-search/product-search-selection/product-search-selection.component';
import { StandardPaymentsComponent } from 'src/app/modules/shared/components/standard-payments/standard-payments.component';
import { StoreParams } from '../../../settings/models/storeParams';
import { SalesCalculation } from '../../../trading/components/sales/sales-calculation';
import { SupplierSelectionComponent } from '../../../trading/components/suppliers/supplier-selection/supplier-selection.component';
import {
  IBranchPartnerResponse,
  IItem,
  IStock,
  StockRead,
  TransferMode,
  TransferType,
} from '../../models/stocks';

@Component({
  selector: 'app-stock-transfer-create-ob',
  templateUrl: './stock-transfer-create-ob.component.html',
  styleUrls: ['./stock-transfer-create-ob.component.scss'],
  animations: [
    trigger('detailExpand', [
      state('collapsed', style({ height: '0px', minHeight: '0' })),
      state('expanded', style({ height: '*' })),
      transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
    ]),
  ],
})
export class StockTransferCreateObComponent extends SalesCalculation implements OnInit {
  @ViewChild(MatTable) table: MatTable<AbstractControl[]>;
  @ViewChild('paymentForm') private paymentForm: StandardPaymentsComponent;
  @ViewChild('supplierSelection') private suplierSelection: SupplierSelectionComponent;
  @ViewChild('paymentpostsales') private paymentpostsales: PaymentsPostSaleComponent;
  @ViewChild('productSearch') private productSearch: ProductSearchSelectionComponent;
  // Matatable
  dataSource = new BehaviorSubject<AbstractControl[]>([]);
  displayedColumns: string[] = [
    'itemCode',
    'itemName',
    'unitName',
    'quantity',
    'priceType',
    'price',
    // 'discount',
    //'retailPrice',
    //'wholesalePrice',
    //'distributorPrice',
    // 'vatAmount',
    'subtotal',
    'action',
  ];
  expandedFieldNames: Array<displayValue | miscOptions>;
  columnsToDisplayWithExpand = [...this.displayedColumns, 'expand'];
  // Holders
  stockTypes = [];
  priceTypes: displayValue[];
  distributorAccounts: IDistributor[] = [];
  costCentreAccounts: CostCentre[] = [];
  currentSalesData: IPurchaseInvoice;
  saleDetails: StockRead;
  products: IInventory[];
  salesId: string;
  customerViewData: ICustomerView = <ICustomerView>{};
  paymentViewData: IPaymentViewData = <IPaymentViewData>{};
  // Main Form
  itemRows: UntypedFormArray = this.formBuilder.array([]);
  salesForm: UntypedFormGroup = this.formBuilder.group({
    issueDate: new UntypedFormControl(new Date(), Validators.required),
    costCentreId: new UntypedFormControl(null),
    notes: new UntypedFormControl(null),
    items: this.itemRows,
    transferType: new UntypedFormControl(null),
    warehouseIdExternal: new UntypedFormControl(null),
    partner: new UntypedFormControl(null, Validators.required),
    orderNumber: new UntypedFormControl(null),
    orderDate: new UntypedFormControl(new Date(), Validators.required),
    isExternalTransfer: new UntypedFormControl(null),
  });
  loading = true;
  mode: ActionType;
  transferMode: TransferMode;
  expandedRowIndex: number | null = null;
  formTitle: string;
  partners: Partial<IBranchPartnerResponse>[];
  partnersAll: Partial<IBranchPartnerResponse>[];
  wareHouseAll = [];
  wareHouseList = [];
  constructor(
    private formBuilder: UntypedFormBuilder,
    private dialog: MatDialog,
    private costCentresApiService: CostCentresApiService,
    private distributorService: DistributorService,
    private stockService: StockService,
    private toastr: ToastrService,
    private router: Router,
    private route: ActivatedRoute,
    private staticDataService: StaticDataService,
    private readonly zone: NgZone,
    private commonService: CommonService,
    private salesAdjustmentService: SalesAdjustmentService,
    private translateService: TranslateService,
    private direction: Directionality,
    private changeDetectorRef: ChangeDetectorRef,
    private partnerService: PartnerService,
    private storeService: StoreService
  ) {
    super();
  }

  ngOnInit(): void {
    this.loading = true;
    this.mode = this.route.snapshot.data['mode'];
    this.transferMode = this.route.snapshot.data['transferMode'];
    this.formTitle = this.route.snapshot.data['title'];
    this.route.params.subscribe(params => {
      this.salesId = params['id'];
    });
    this.setPageDisabled();
    this.getDropDownData();
    this.salesForm.get('isExternalTransfer').valueChanges.subscribe(transferType => {
      if (!this.IsViewMode) {
        console.log('isExternalTransfer Type changed to: ', transferType);
        this.salesForm?.controls['warehouseIdExternal']?.setValue(null);
        this.salesForm?.controls['warehouseIdExternal']?.clearValidators();
        this.salesForm?.controls['partner']?.setValue(null);
        this.salesForm.updateValueAndValidity();
        this.updatePartnerBranches(transferType);
      }
    });
  }

  updatePartnerBranches(transferType: boolean): void {
    this.partners = this.partnersAll.filter(data =>
      transferType ? data.partnerBranchId === 0 : data.partnerBranchId !== 0
    );
  }
  // partnerBranchId=0 for external
  onBranchChange(event: MatSelectChange) {
    this.wareHouseList = this.wareHouseAll.filter(data => {
      console.log(data, event.value);
      return data.branchId === event.value.partnerBranchId;
    });
    this.salesForm?.controls['warehouseIdExternal']?.addValidators(Validators.required);
    if (this.wareHouseList?.length) {
      this.salesForm?.controls['warehouseIdExternal']?.setValue(this.wareHouseList[0]?.warehouseId);
    }
    this.salesForm.updateValueAndValidity();
  }

  get canSelectProduct() {
    return !(this.mode === ActionType.view || this.mode === ActionType.fullCreditNote);
  }

  get IsFullCreditMode(): boolean {
    return this.mode === ActionType.fullCreditNote;
  }

  get IsViewMode(): boolean {
    return this.mode === ActionType.view;
  }

  get canDeleteProduct() {
    return this.canSelectProduct;
  }

  getPartners(): void {
    this.partnerService.getAll().subscribe(result => {
      console.log(result);
      this.partners = result;
    });
  }

  get isInwardTransfer() {
    return this.transferMode === TransferMode.Inward;
  }

  get getDocNumber() {
    return this.isInwardTransfer
      ? this.saleDetails?.documentNumberIn
      : this.saleDetails?.documentNumberOut;
  }

  getDropDownData(): void {
    const partner = this.partnerService.getAll();
    const warehouses = this.storeService.getAllStores(new StoreParams());
    const costCentreAccounts = this.costCentresApiService.getAllCostCentres();
    const transferMode =
      this.transferMode === TransferMode.Inward
        ? TransferType.INWARD_TRANSFER
        : TransferType.OUTWARD_TRANSFER;
    const salesById = this.salesId
      ? this.stockService.getById(this.salesId, transferMode)
      : of(null);
    forkJoin([partner, warehouses, costCentreAccounts, salesById]).subscribe(results => {
      this.partnersAll = results[0];
      this.wareHouseAll = results[1];
      this.costCentreAccounts = results[2];
      this.wareHouseList = [...this.wareHouseAll];
      this.priceTypes = this.staticDataService.getSalesPriceTypes;
      if (results[3]) {
        this.patchData(results[3]);
      }
      if (this.mode === ActionType.create) {
        this.transferMode === TransferMode.Inward
          ? this.salesForm?.controls['isExternalTransfer'].setValue(true)
          : this.salesForm?.controls['isExternalTransfer'].setValue(false);
        this.updatePartnerBranches(this.salesForm?.controls['isExternalTransfer'].value);
      }
      // drop down
      this.stockTypes =
        this.transferMode === TransferMode.Inward
          ? stockTypes.filter(data => data.value)
          : stockTypes;
    });
    this.expandedFieldNames = this.staticDataService.getSalesExpandedColumns;
    this.loading = false;
  }

  setPageDisabled(): void {
    if (this.mode === ActionType.view) {
      this.salesForm.disable();
    }
  }

  patchData(data: StockRead): void {
    console.log('patchData', data);
    this.saleDetails = data;
    this.partners = [data.partner];
    if (data?.items && data.items.length) {
      data.items.forEach(element => {
        this.addSaleDetailForView(element);
      });
    }
    this.salesForm.patchValue(
      {
        issueDate: data.issueDate,
        notes: null,
        costCentreId: null,
        transferType: data.transferType,
        warehouseIdExternal: data.warehouseIdExternal,
        partner: data.partner,
        isExternalTransfer: data.isExternalTransfer,
      },
      { emitEvent: false }
    );
    this.setPageDisabled();
  }

  onPriceTypeChange(event: MatSelectChange, index: number) {
    console.log('onPriceTypeChange -> ', event, event.source.triggerValue, index);
    const data = this.getSpecificFormArray(index);
    const priceType = this.priceTypes.find(data => data.value === event.value);
    const productData = data.get('product').value;
    data.patchValue({
      price: productData[priceType.display],
      priceType: event.value,
      subtotal: this.countSubTotalExcluded(+data.get('quantity').value, +data.get('price').value),
    });
    this.dataSource.next(this.itemRows.controls);
    this.formItems(this.itemRows);
    this.updateTotalOnly();
  }

  addSaleDetailForView(product: IItem): void {
    const saleDetail: IItem = {
      itemId: product.itemId,
      itemCode: product.itemCode,
      quantity: product.quantity,
      itemUnitId: product.itemUnitId,
      price: product.price,
      warehouseName: product.warehouseName,
      warehouseId: product.warehouseId,
      transferItemId: product.transferItemId,
      itemNameEnglish: product.itemNameEnglish,
      itemNameArabic: product.itemNameArabic,
      qtyToAdd: product.qtyToAdd,
      warehouseIdIn: product.warehouseIdIn,
      warehouseIdOut: product.warehouseIdOut,
      itemUnitIdIn: product.itemUnitIdIn,
      itemUnitIdOut: product.itemUnitIdOut,
      subtotal: this.countSubTotalExcluded(product.quantity, product.price),
      unitName: product.unitName,
      priceType: product.priceType,
    };
    this.onAddNewItem(saleDetail);
    this.dataSource.next(this.itemRows.controls);
    this.formItems(this.itemRows);
    this.updateTotalOnly();
  }

  addSaleDetail(product: IInventory) {
    console.log('addSaleDetail ->', product);
    if (!this.checkProductExist(product)) {
      const saleDetail: IItem = {
        itemId: product.itemId,
        itemCode: product.itemCode,
        quantity: 1,
        itemUnitId: product.itemUnitId,
        price: product.purchasePrice,
        warehouseName: product.warehouseName,
        warehouseId: product.warehouseId,
        transferItemId: null,
        itemNameEnglish: product.nameEnglish,
        itemNameArabic: product.nameArabic,
        qtyToAdd: null,
        warehouseIdIn: null,
        warehouseIdOut: null,
        itemUnitIdIn: null,
        itemUnitIdOut: null,
        subtotal: this.countSubTotalExcluded(1, product.purchasePrice),
        unitName: product.unitName,
        product: product,
        priceType: 3, // always 2
      };
      this.onAddNewItem(saleDetail);
      //  always purchase price
      this.dataSource.next(this.itemRows.controls);
      this.formItems(this.itemRows);
      this.updateTotalOnly();

      const field = document.querySelectorAll('.next')[0] as HTMLInputElement;
      setTimeout(() => {
        field.focus();
      });
    } else {
      console.log('product was found so update ->', product.itemCode);
      const index = this.itemsArray.controls.findIndex(
        products =>
          products.value.itemCode === product.itemCode &&
          products.value.warehouseId === product.warehouseId &&
          products.value.unitName === product.unitName
      );
      const data = this.getSpecificFormArray(index);
      data.patchValue({
        quantity: data.get('quantity').value + 1,
        subtotal: this.countSubTotalExcluded(
          data.get('quantity').value + 1,
          data.get('price').value
        ),
      });
      this.dataSource.next(this.itemRows.controls);
      this.formItems(this.itemRows);
      this.updateTotalOnly();
    }
  }

  removeSaleDetail(data: DeleteItemRows) {
    console.log('removeSaleDetail ->', data);
    const index = this.itemsArray.controls.findIndex(product => {
      console.log(product);
      return (
        product.value.itemCode === data.itemCode &&
        product.value.warehouseId === data.warehouseId &&
        product.value.unitName === data.unitName
      );
    });
    this.itemRows.removeAt(index);
    this.dataSource.next(this.itemRows.controls);
    this.formItems(this.itemRows);
    this.updateTotalOnly();
  }

  checkProductExist(products: IInventory) {
    console.log('checkProductExist -> ', products);
    return this.itemsArray.controls.find(product => {
      console.log(product);
      return (
        product.value.itemCode === products.itemCode &&
        product.value.warehouseId === products.warehouseId &&
        product.value.unitName === products.unitName
      );
    });
  }

  getControlsIndexFromArray(controlErrors: InvalidQuantity) {
    return this.itemsArray.controls.findIndex(product => {
      console.log(product);
      return (
        product.value.itemCode === controlErrors.itemCode &&
        product.value.warehouseId === controlErrors.warehouseId &&
        product.value.unitName === controlErrors.unitName
      );
    });
  }

  onSelection(selectedProduct: IInventory) {
    this.addSaleDetail(selectedProduct);
  }

  onQuantityChange(event, index: number) {
    console.log('onQuantityChange -> ', event.srcElement.value, index);
    const data = this.getSpecificFormArray(index);
    data.patchValue({
      quantity: Number(event.srcElement.value) || 0,
      subtotal: this.countSubTotalExcluded(event.srcElement.value, data.get('price').value),
    });
    this.dataSource.next(this.itemRows.controls);
    this.formItems(this.itemRows);
    this.updateTotalOnly();
  }

  onPriceChange(event, index: number) {
    console.log('onPriceChange -> ', event, index);
    const data = this.getSpecificFormArray(index);
    data.patchValue({
      price: Number(event.srcElement.value) || 0,
      subtotal: this.countSubTotalExcluded(data.get('quantity').value, event.srcElement.value),
    });
    this.dataSource.next(this.itemRows.controls);
    this.formItems(this.itemRows);
    this.updateTotalOnly();
  }

  getSpecificFormArray(index: number): AbstractControl {
    return (<UntypedFormArray>this.salesForm.get('items')).at(index);
  }

  get itemsArray(): UntypedFormArray {
    return this.salesForm.get('items') as UntypedFormArray;
  }

  openDeleteConfirmationDialog(itemCode: number, warehouseId: number, unitName: string) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '600px';
    dialogConfig.panelClass = 'green_theme';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(DeleteConfirmationComponent, dialogConfig);
    dialogRef.componentInstance.accepted.subscribe(result => {
      console.log('delete itemcode', result);
      const data: DeleteItemRows = { itemCode, warehouseId, unitName };
      this.removeSaleDetail(data);
    });
  }

  jumpToNext(event: Event, index: number) {
    console.log('jump to next field  -> ', event, index);
    event.preventDefault();
    const nextField = document.querySelectorAll('.next');
    nextField.forEach((element, index) => {
      if (element === event.target) {
        const nextfield = nextField[index + 1] as HTMLInputElement;
        nextfield.focus();
      }
    });
  }

  onAddNewItem(product: IItem) {
    console.log('onAddNewItem -> ', product);
    if (this.itemsArray && this.itemsArray?.length > 0) {
      this.itemsArray.insert(0, this.addnewFormGroup(product));
    } else {
      this.itemsArray.push(this.addnewFormGroup(product));
    }
  }

  addnewFormGroup(saleData: IItem): UntypedFormGroup {
    console.log('addnewFormGroup -> ', saleData);
    const row = this.formBuilder.group({
      itemId: saleData.itemId,
      transferItemId: saleData?.transferItemId,
      itemCode: saleData.itemCode,
      itemNameEnglish: saleData?.itemNameEnglish,
      itemNameArabic: saleData?.itemNameArabic,
      itemUnitId: saleData.itemUnitId,
      quantity: [
        saleData.quantity,
        Validators.compose([Validators.required, CustomValidators.gt(0)]),
      ],
      qtyToAdd: saleData.qtyToAdd,
      price: saleData.price,
      warehouseId: saleData.warehouseId,
      warehouseName: saleData.warehouseName,
      warehouseIdIn: saleData.warehouseIdIn,
      warehouseIdOut: saleData.warehouseIdOut,
      itemUnitIdIn: saleData.itemUnitIdIn,
      itemUnitIdOut: saleData.itemUnitIdOut,
      subtotal: saleData.subtotal,
      unitName: saleData.unitName,
      product: saleData.product,
      priceType: saleData.priceType,
    });
    return row;
  }

  submitStocks(event: Event): void {
    event.preventDefault();
    this.salesForm.markAllAsTouched();
    this.processStockCreation();
  }

  processStockCreation(): void {
    console.log(this.salesForm.valid, this.itemsArray.length > 0);
    const isAllValid = this.salesForm.valid && this.itemsArray.length > 0;
    console.log('is all valid', isAllValid, this.salesForm.value);
    if (isAllValid) {
      const data: IStock = {
        ...this.salesForm.value,
        orderDate: convertDateForBE(this.salesForm.get('orderDate')?.value),
        issueDate: convertDateForBE(this.salesForm.get('issueDate')?.value),
        grandTotal: this.grandTotal,
        transferType:
          this.transferMode === TransferMode.Inward ? 'INWARD_TRANSFER' : 'OUTWARD_TRANSFER',
      };
      console.log('Final purchase structure -> ', data);
      this.stockService.createStock(data).subscribe(
        result => {
          console.log('stock service response', result);
          //this.toastr.success('Stock added successfully');
          this.commonService.playSuccessSound();
          this.resetPageForNewCreation();
          this.router.navigate(['../'], { relativeTo: this.route });
        },
        error => {
          console.log('errors =>', error);
          if (error.status === 422) {
            this.setFormErrors(error);
          }
        }
      );
    } else {
      this.commonService.playErrorSound();
    }
  }

  resetPageForNewCreation(): void {
    this.resetSalesForm();
    this.resetCounts();
    this.changeDetectorRef.detectChanges();
  }

  resetSalesForm(): void {
    this.salesForm.reset();
    this.itemRows.clear();
    this.dataSource = null;
    this.salesForm.markAsUntouched();
  }

  processSalesFullCreditNotes(): void {
    const isAllValid = this.paymentpostsales.isValid();
    console.log('is all valid', isAllValid);
    if (isAllValid) {
      this.onPostClick();
    } else {
      this.commonService.playErrorSound();
    }
  }

  onPostClick() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.minWidth = '600px';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    let text = '';
    this.translateService.get('sales.fullCreditConfirmation').subscribe(translation => {
      text = translation;
    });
    const dialogRef = this.dialog.open(ConfirmDialogComponent, dialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      console.log('result', result);
      if (result) {
        //
      }
    });
  }

  setFormErrors(validationErrors: error): void {
    const serverErrors = validationErrors.details.invalidQuantity;
    serverErrors.forEach((element: InvalidQuantity) => {
      const controlIndex = this.getControlsIndexFromArray(element);
      console.log('index => ', controlIndex);
      const formArray = this.getSpecificFormArray(controlIndex);
      console.log('formArray => ', formArray);
      formArray.get('quantity').setErrors({ serverSideError: element.error });
      formArray.updateValueAndValidity();
    });
  }

  focusOnSearch(): void {
    this.productSearch.setFocus();
  }

  toggleExpansion(rowIndex: number): void {
    this.expandedRowIndex = this.expandedRowIndex === rowIndex ? null : rowIndex;
  }

  isRowExpanded(index: number): boolean {
    return this.expandedRowIndex === index;
  }
}
