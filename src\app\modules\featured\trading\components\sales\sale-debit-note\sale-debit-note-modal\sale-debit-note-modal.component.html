<app-dialog-header></app-dialog-header>
<h2 class="text-center text-warning">{{ data.message | translate }}</h2>
<!-- <mat-dialog-content> -->
<mat-card>
  <div class="table-responsive">
    <table class="w-100" [dataSource]="differences" mat-table>
      <!-- Code Column -->
      <ng-container matColumnDef="itemCode">
        <th class="text-center" *matHeaderCellDef mat-header-cell>
          {{ 'compareTableDebitNote.code' | translate }}
        </th>
        <td class="text-center" *matCellDef="let row" mat-cell>
          {{ row.item.itemCode }}
        </td>
      </ng-container>

      <!-- Name Column -->
      <ng-container matColumnDef="itemName">
        <th class="text-center" *matHeaderCellDef mat-header-cell>
          {{ 'compareTableDebitNote.name' | translate }}
        </th>
        <td class="text-center" *matCellDef="let row" mat-cell>
          {{ row.item.itemName }}
        </td>
      </ng-container>

      <!-- Unit Column -->
      <ng-container matColumnDef="unitName">
        <th class="text-center" *matHeaderCellDef mat-header-cell>
          {{ 'compareTableDebitNote.unit' | translate }}
        </th>
        <td class="text-center" *matCellDef="let row" mat-cell>
          {{ row.item.product.unitName }}
        </td>
      </ng-container>

      <!-- Price Type Column -->
      <ng-container matColumnDef="priceType">
        <th class="text-center" *matHeaderCellDef mat-header-cell>
          {{ 'compareTableDebitNote.priceType' | translate }}
        </th>
        <td
          class="text-center"
          *matCellDef="let row"
          [ngClass]="{ 'bg-light-primary': row?.changes?.priceType }"
          mat-cell>
          {{ priceTypes(row.item.priceType) | translate }}
        </td>
      </ng-container>

      <!-- Price Column -->
      <ng-container matColumnDef="price">
        <th class="text-center" *matHeaderCellDef mat-header-cell>
          {{ 'compareTableDebitNote.price' | translate }}
        </th>
        <td
          class="text-center"
          *matCellDef="let row"
          [ngClass]="{ 'bg-light-primary': row?.changes?.price }"
          mat-cell>
          {{ row.item.price }}
        </td>
      </ng-container>

      <!-- Quantity Column -->
      <ng-container matColumnDef="quantity">
        <th class="text-center" *matHeaderCellDef mat-header-cell>
          {{ 'compareTableDebitNote.quantity' | translate }}
        </th>
        <td
          class="text-center"
          *matCellDef="let row"
          [ngClass]="{ 'text-warning': row?.changes?.quantity }"
          mat-cell>
          {{ row.item.quantity }}
        </td>
      </ng-container>

      <!-- Discount Column -->
      <ng-container matColumnDef="discount">
        <th class="text-center" *matHeaderCellDef mat-header-cell>
          {{ 'compareTableDebitNote.discount' | translate }}
        </th>
        <td
          class="text-center"
          *matCellDef="let row"
          [ngClass]="{ 'text-warning': row?.changes?.discount }"
          mat-cell>
          {{ row.item.discount }} {{ row.item.product.isGeneralDscntMethod ? 'SSR' : '%' }}
        </td>
      </ng-container>

      <tr
        *matHeaderRowDef="[
          'itemCode',
          'itemName',
          'unitName',
          'priceType',
          'price',
          'quantity',
          'discount'
        ]"
        mat-header-row></tr>
      <tr
        *matRowDef="
          let row;
          columns: [
            'itemCode',
            'itemName',
            'unitName',
            'priceType',
            'price',
            'quantity',
            'discount'
          ]
        "
        [ngClass]="{ 'bg-light-warning': row.status === 'deleted' }"
        mat-row></tr>
    </table>
  </div>
</mat-card>
<!-- </mat-dialog-content> -->

<mat-dialog-actions align="center">
  <button [mat-dialog-close]="true" mat-stroked-button cdkFocusInitial color="primary">
    {{ 'common.confirm' | translate }}
  </button>
  <button (click)="onNoClick()" mat-stroked-button color="warn">
    {{ 'common.cancel' | translate }}
  </button>
</mat-dialog-actions>
