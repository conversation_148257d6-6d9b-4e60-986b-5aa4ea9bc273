<app-dialog-header></app-dialog-header>
<h2 class="text-center f-s-16" mat-dialog-title>All Terms and Conditions</h2>
<mat-dialog-content>
  <ng-container *ngFor="let section of sections">
    <mat-card class="m-b-10" appearance="outlined">
      <p class="f-s-20 text-center text-primary">{{ section.name | translate }}</p>
      <pre class="f-s-14">{{ section.clause }}</pre>
    </mat-card>
  </ng-container>
</mat-dialog-content>
<mat-dialog-actions align="center">
  <button (click)="onClose()" mat-button color="warn" mat-stroked-button>
    {{ 'common.buttons.cancel' | translate }}
  </button>
</mat-dialog-actions>
