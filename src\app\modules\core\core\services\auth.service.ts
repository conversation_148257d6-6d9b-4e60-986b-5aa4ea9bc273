import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { CookieService } from 'ngx-cookie-service';
import { ToastrService } from 'ngx-toastr';
import { Observable, of } from 'rxjs';
import { catchError, map, shareReplay, tap } from 'rxjs/operators';
import { CommonService } from 'src/app/core/api/common.service';
import { Token } from 'src/app/core/models/identity/token';
import { environment } from 'src/environments/environment';
import { CookieRequest } from '../models/cookieRequest';
import { ILoginBranches } from '../models/login';
import { LocalStorageService } from './local-storage.service';
import { MultilingualService } from './multilingual.service';
import { TokenService } from './token.service';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private baseUrl = environment.apiUrl;
  private refreshToken$: Observable<string | null> | null = null;

  constructor(
    private http: HttpClient,
    private localStorage: LocalStorageService,
    private router: Router,
    private toastr: ToastrService,
    private cookieService: CookieService,
    private multilingualService: MultilingualService,
    public tokenService: TokenService,
    private commonService: CommonService
  ) {}

  public get getToken(): string {
    return this.tokenService.getToken();
  }

  public get getStorageToken(): string {
    return this.tokenService.getToken();
  }

  public get getFullName(): string {
    return this.tokenService.getFullName();
  }

  public get getCompanyID(): string {
    return this.tokenService.getCompanyID();
  }

  public get getUserId(): number {
    return this.tokenService.getUserId();
  }

  public get getBranchId(): string {
    return this.tokenService.getBranchId();
  }

  public get getUserName(): string {
    return this.tokenService.getUserName();
  }

  public get getJwtPermissions() {
    return this.tokenService.getJwtPermissions();
  }

  public sortedPermissions(permissions: string[]) {
    return this.tokenService.sortPermissions(permissions);
  }

  public get isAuthenticated(): boolean {
    return !this.tokenService.isTokenExpired();
  }

  public isAuthorized(authorizationType: string, allowedData: string[]): boolean {
    return this.tokenService.isAuthorized(authorizationType, allowedData);
  }

  public hasRoleQualifier(allowedData: string[]) {
    return this.tokenService.hasRoleQualifier(allowedData);
  }

  public loadCurrentUser(): Observable<string> {
    const token = this.tokenService.getToken();
    return of(token);
  }

  public login(values: {
    username: string;
    password: string;
    tenantId: string;
  }): Observable<Token> {
    const tenantId = values.tenantId;
    delete values.tenantId;
    return this.http
      .post(this.baseUrl + 'auth/login', values, {
        withCredentials: true,
        headers: {
          'x-tenant': tenantId,
        },
      })
      .pipe(
        tap((result: Token) => {
          this.tokenService.setStorageToken(result);
        }),
        map((result: Token) => result ?? undefined)
      );
  }

  public navigateToDashBoard() {
    // Load language after successful branch selection
    this.multilingualService.loadLanguageAfterAuth().subscribe(() => {
      this.toastr.clear();
      this.toastr.info('User Logged In');
      this.commonService.playSuccessSound();
    });
  }

  public logout(returnUrl?: boolean): void {
    // Clear all cookies
    const cookies = this.cookieService.getAll();
    Object.keys(cookies).forEach(cookieName => {
      this.cookieService.delete(cookieName, '/');
    });

    // Clear all storage
    this.localStorage.clear();

    // Clear token-specific items
    this.tokenService.clearTokens();

    this.toastr.clear();
    this.toastr.info('User Logged Out');

    if (returnUrl) {
      this.router.navigate(['/authentication/login'], {
        queryParams: { returnUrl: this.router.routerState.snapshot.url },
      });
    } else {
      this.router.navigate(['/authentication/login']);
    }

    this.commonService.playErrorSound();
  }

  public tryRefreshingToken(): Observable<string | null> {
    const jwtToken = this.tokenService.getToken() ?? null;
    const refreshToken = this.tokenService.getRefreshToken() ?? null;

    if (!jwtToken || !refreshToken) {
      return of(null); // No tokens available to refresh
    }

    if (this.refreshToken$) {
      return this.refreshToken$; // Return the ongoing refresh observable
    }

    this.refreshToken$ = this.http
      .post<Token>(this.baseUrl + 'auth/refreshToken', {
        refreshToken: refreshToken,
      })
      .pipe(
        tap((result: Token) => {
          if (result) {
            this.tokenService.setStorageToken(result);
            this.toastr.clear();
            this.toastr.info('Refreshed Token');
          } else {
            this.logout();
            this.toastr.error('Token cannot be set/refreshed');
          }
        }),
        map(result => (result ? result : null)),
        catchError(error => {
          this.logout();
          return of(null); // Token refresh failed
        }),
        shareReplay(1)
      );
    return this.refreshToken$;
  }

  getUserBranchesYearId(companyId: string) {
    return this.http.get<ILoginBranches[]>(
      this.baseUrl + `auth/users/branches-years?companyId=${companyId}`
    );
  }

  public loginforBranchandYear(cookies: CookieRequest[]): Observable<any> {
    return this.http.post(this.baseUrl + 'auth/setCookies', cookies, { withCredentials: true });
  }
}
