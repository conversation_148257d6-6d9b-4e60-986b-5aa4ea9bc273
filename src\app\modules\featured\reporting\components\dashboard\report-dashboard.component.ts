import { Component } from '@angular/core';
import { DashboardModulesHolder } from 'src/app/modules/shared/components/dashboard-modules-holder/modulesHolder';

@Component({
  selector: 'app-report-dashboard',
  templateUrl: './report-dashboard.component.html',
  styleUrls: ['./report-dashboard.component.scss'],
})
export class ReportDashboardComponent {
  reportModulesList: DashboardModulesHolder[] = [
    // {
    //   moduleName: 'navigationMenus.itemPriceReport',
    //   moduleDescription: 'Get Price Reports.',
    //   modulePermission: ['Inventory.PriceReports', 'AllPermissions'],
    //   moduleImage: 'inventory',
    //   moduleRouterLink: '../priceReports',
    //   moduleButtonAction: 'BackOffice',
    //   moduleType: 'subModule',
    // },
    // {
    //   moduleName: 'navigationMenus.stockValueReport',
    //   moduleDescription: 'Get Stocks Reports.',
    //   modulePermission: ['Inventory.StockValueReports', 'AllPermissions'],
    //   moduleImage: 'ad_units',
    //   moduleRouterLink: '../stockReports',
    //   moduleButtonAction: 'BackOffice',
    //   moduleType: 'subModule',
    // },
    {
      moduleName: 'navigationMenus.Inventory',
      moduleDescription: 'Get Stocks Reports.',
      modulePermission: ['Inventory.StockValueReports', 'AllPermissions'],
      moduleImage: 'ad_units',
      moduleRouterLink: '../inventoryReports',
      moduleButtonAction: 'BackOffice',
      moduleType: 'mainModule',
    },
    {
      moduleName: 'navigationMenus.accountingReport',
      moduleDescription: 'Get Stocks Reports.',
      modulePermission: ['Accounting.Accounts', 'AllPermissions'],
      moduleImage: 'ad_units',
      moduleRouterLink: '../accountingReports',
      moduleButtonAction: 'BackOffice',
      moduleType: 'mainModule',
    },
    {
      moduleName: 'navigationMenus.salesReports',
      moduleDescription: 'Get Sales Reports.',
      modulePermission: ['Accounting.Accounts', 'AllPermissions'],
      moduleImage: 'ad_units',
      moduleRouterLink: '../salesReports',
      moduleButtonAction: 'BackOffice',
      moduleType: 'mainModule',
    },
    {
      moduleName: 'navigationMenus.reportsSetup',
      moduleDescription: 'Reports Setup.',
      modulePermission: ['Accounting.Accounts', 'AllPermissions'],
      moduleImage: 'ad_units',
      moduleRouterLink: '../reportsSetup',
      moduleButtonAction: 'BackOffice',
      moduleType: 'mainModule',
    },
    {
      moduleName: 'navigationMenus.purchaseReports',
      moduleDescription: 'Get Purchase Reports.',
      modulePermission: ['Accounting.Accounts', 'AllPermissions'],
      moduleImage: 'ad_units',
      moduleRouterLink: '../purchaseReports',
      moduleButtonAction: 'BackOffice',
      moduleType: 'mainModule',
    },
  ];
}
