<!----------------------------------- mat table content --------------------------------------->
<!-- action bar -->
<app-create-action
  *appHasPermission="['User.Create', 'AllPermissions']"
  [label]="'users.register_user' | translate"></app-create-action>
<!-- action bar -->

<!-- search field -->
<mat-card appearance="outlined">
  <mat-card-content>
    <div class="row no-gutters">
      <div class="col-md-6 col-lg-6 col-sm-12 d-flex">
        <mat-form-field class="w-100">
          <input
            #searchInput
            (keyup)="applyFilter($event.target.value)"
            matInput
            autocomplete="off" />
          <i-tabler class="icon-16" matPrefix name="search"></i-tabler>
          <a
            class="cursor-pointer"
            *ngIf="searchInput?.value"
            (click)="clearSearchInput()"
            matSuffix>
            <i-tabler class="icon-16 error" name="X"></i-tabler>
          </a>
        </mat-form-field>
      </div>
    </div>
    <!-- search field -->
    <mat-card-title class="m-t-10">{{ formTitle | translate }}</mat-card-title>
    <div class="table-responsive">
      <table [dataSource]="dataSource" mat-table matSort>
        <ng-container matColumnDef="username">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'users.userName' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ element.username }}
          </td>
        </ng-container>
        <ng-container matColumnDef="firstName">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'users.userFname' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ element.firstName }}
          </td>
        </ng-container>
        <ng-container matColumnDef="lastName">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'users.userLname' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ element.lastName }}
          </td>
        </ng-container>
        <ng-container matColumnDef="roleNames">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'users.userRole' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ element.roleNames }}
          </td>
        </ng-container>
        <ng-container matColumnDef="action">
          <th *matHeaderCellDef mat-header-cell>{{ 'common.action' | translate }}</th>
          <td *matCellDef="let element" mat-cell>
            <a
              class="m-r-10 cursor-pointer"
              class="m-b-10"
              *appHasPermission="['User.Update', 'AllPermissions']"
              [routerLink]="['edit', element.id]"
              ><i-tabler class="icon-16" name="edit"></i-tabler
            ></a>
            <a
              class="m-r-10 cursor-pointer"
              class="m-b-10"
              *appHasPermission="['User.View', 'AllPermissions']"
              [routerLink]="['view', element.id]"
              ><i-tabler class="icon-16" name="eye"></i-tabler
            ></a>
          </td>
        </ng-container>
        <tr class="mat-row" *matNoDataRow>
          <td class="text-center" [attr.colspan]="displayedColumns.length">
            {{ 'common.noDataFound' | translate }}
          </td>
        </tr>
        <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
        <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
      </table>
    </div>
    <mat-paginator
      *ngIf="dataSource?.filteredData?.length > 0"
      [length]="dataSource.filteredData.length"
      [pageIndex]="0"
      [pageSize]="25"
      [pageSizeOptions]="[25, 50]"></mat-paginator>
    <!------------------------------------------- not units message --------------------------------->
  </mat-card-content>
</mat-card>
