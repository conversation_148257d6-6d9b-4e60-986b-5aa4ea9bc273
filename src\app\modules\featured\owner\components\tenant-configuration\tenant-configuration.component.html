<button class="close-button" *ngIf="modalData?.data" (click)="onNoClick(); (false)" mat-icon-button>
  <i-tabler class="icon-20 error" name="X"></i-tabler>
</button>

<ng-container>
  <mat-card appearance="outlined">
    <mat-card-content>
      <mat-card-title>{{ 'tenants.tenantConfiguration' | translate }}</mat-card-title>
      <form [formGroup]="tenantForm" [ngClass]="{ readOnly: IsViewMode }" autocomplete="off">
        <div class="row no-gutters m-t-10">
          <div class="p-2 col-md-3 col-sm-6">
            <mat-label>{{ 'tenants.id' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <input type="text" matInput formControlName="tenantId" />
              <mat-error
                *ngIf="
                  tenantForm?.controls['tenantId'].hasError('required') &&
                  tenantForm?.controls['tenantId'].touched
                ">
                {{ 'common.required' | translate }}
              </mat-error>
            </mat-form-field>
          </div>
          <div class="p-2 col-md-3 col-sm-6">
            <mat-label>{{ 'tenants.name' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <input type="text" maxlength="40" matInput formControlName="tenantName" />
              <mat-error
                *ngIf="
                  tenantForm?.controls['tenantName'].hasError('required') &&
                  tenantForm?.controls['tenantName'].touched
                ">
                {{ 'common.required' | translate }}
              </mat-error>
            </mat-form-field>
          </div>
          <div class="p-2 col-md-3 col-sm-6">
            <mat-label>{{ 'tenants.regNo' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <input
                type="text"
                maxlength="40"
                matInput
                formControlName="commercialRegistrationNo" />
              <mat-error
                *ngIf="
                  tenantForm?.controls['commercialRegistrationNo'].hasError('required') &&
                  tenantForm?.controls['commercialRegistrationNo'].touched
                ">
                {{ 'common.required' | translate }}
              </mat-error>
            </mat-form-field>
          </div>
          <div class="p-2 col-md-3 col-sm-6">
            <mat-label>{{ 'tenants.enterpriseType' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <mat-select
                formControlName="enterpriseType"
                placeholder="{{ 'tenants.enterpriseType' | translate }}">
                <mat-option *ngFor="let type of enterpriseTypes" [value]="type">
                  {{ type }}
                </mat-option>
              </mat-select>

              <mat-error
                *ngIf="
                  tenantForm?.controls['enterpriseType'].hasError('required') &&
                  tenantForm?.controls['enterpriseType'].touched
                ">
                {{ 'common.required' | translate }}
              </mat-error>
            </mat-form-field>
          </div>
          <div class="p-2 col-md-3 col-sm-6">
            <mat-label>{{ 'tenants.vatNo' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <input type="number" matInput formControlName="vatNumber" />
              <mat-error
                *ngIf="
                  tenantForm.get('vatNumber').hasError('required') &&
                  tenantForm.get('vatNumber').touched
                ">
                Field Required
              </mat-error>
              <mat-error
                *ngIf="
                  tenantForm.get('vatNumber').hasError('invalidVatNumber') &&
                  !tenantForm.get('vatNumber').hasError('required') &&
                  tenantForm.get('vatNumber').touched
                ">
                Invalid VAT Number
              </mat-error>
            </mat-form-field>
          </div>
          <div class="p-2 col-md-3 col-sm-6">
            <mat-label>{{ 'tenants.phone' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <input type="text" maxlength="40" matInput formControlName="phoneNumber" />
              <mat-error
                *ngIf="
                  tenantForm.controls['phoneNumber'].hasError('required') &&
                  tenantForm.controls['phoneNumber'].touched
                ">
                {{ 'common.required' | translate }}
              </mat-error>
            </mat-form-field>
          </div>
          <div class="p-2 col-md-3 col-sm-6">
            <mat-label>{{ 'tenants.email' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <input type="text" maxlength="40" matInput formControlName="emailId" />
              <mat-error
                *ngIf="
                  tenantForm.controls['emailId'].hasError('required') &&
                  tenantForm.controls['emailId'].touched
                ">
                {{ 'common.required' | translate }}
              </mat-error>
              <mat-error
                *ngIf="
                  tenantForm.controls['emailId'].errors?.email &&
                  tenantForm.controls['emailId'].touched
                ">
                {{ 'common.required' | translate }}
              </mat-error>
            </mat-form-field>
          </div>
          <div class="p-2 col-md-3 col-sm-6">
            <mat-label>{{ 'tenants.installDate' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <input [matDatepicker]="picker" matInput formControlName="installationDate" />
              <mat-datepicker-toggle [for]="picker" matSuffix></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
              <mat-error
                class="text-danger font-10"
                *ngIf="
                  tenantForm.controls['installationDate']?.hasError('required') &&
                  tenantForm.controls['installationDate']?.touched
                ">
                {{ 'common.required' | translate }}
              </mat-error>
            </mat-form-field>
          </div>
          <div class="p-2 col-md-3 col-sm-6">
            <mat-label>{{ 'tenants.expDate' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <input [matDatepicker]="expicker" matInput formControlName="expirationDate" />
              <mat-datepicker-toggle [for]="expicker" matSuffix></mat-datepicker-toggle>
              <mat-datepicker #expicker></mat-datepicker>
              <mat-error
                class="text-danger font-10"
                *ngIf="
                  tenantForm.controls['expirationDate']?.hasError('required') &&
                  tenantForm.controls['expirationDate']?.touched
                ">
                {{ 'common.required' | translate }}
              </mat-error>
            </mat-form-field>
          </div>
          <div class="p-2 col-md-3 col-sm-6">
            <mat-label>{{ 'tenants.contactNameManager' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <input type="text" matInput formControlName="contactName" />
              <mat-error
                *ngIf="
                  tenantForm?.controls['contactName'].hasError('required') &&
                  tenantForm?.controls['contactName'].touched
                ">
                {{ 'common.required' | translate }}
              </mat-error>
            </mat-form-field>
          </div>
          <div class="p-2 col-md-3 col-lg-3 col-sm-6">
            <mat-slide-toggle [labelPosition]="'after'" color="primary" formControlName="isActive"
              >Active</mat-slide-toggle
            >
          </div>
        </div>
        <div class="row no-gutters">
          <div class="p-2 col-md-6 col-sm-6">
            <mat-label>{{ 'tenants.identification' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <input [appMaxlength]="30" matInput type="text" formControlName="identification" />
              <mat-error
                *ngIf="
                  tenantForm?.controls['identification'].hasError('required') &&
                  tenantForm?.controls['identification'].touched
                ">
                {{ 'common.required' | translate }}
              </mat-error>
            </mat-form-field>
          </div>
          <div class="p-2 col-md-6 col-sm-6">
            <mat-label>{{ 'tenants.identificationCode' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <mat-select formControlName="identificationCode">
                <mat-option
                  *ngFor="let identificationCode of identificationCodes"
                  [value]="identificationCode.value">
                  {{ identificationCode.display }}({{ identificationCode.value }})
                </mat-option>
              </mat-select>
              <mat-error
                *ngIf="
                  tenantForm?.controls['identificationCode'].hasError('required') &&
                  tenantForm?.controls['identificationCode'].touched
                ">
                {{ 'common.required' | translate }}
              </mat-error>
            </mat-form-field>
          </div>
        </div>
        <div class="row no-gutters">
          <div class="col-md-22">
            <app-address #addressForm formControlName="address"></app-address>
          </div>
        </div>
        <div class="p-2">
          <mat-label>{{ 'tenants.notes' | translate }}</mat-label>
          <mat-form-field class="w-100">
            <input type="text" matInput formControlName="notes" />
            <mat-error
              *ngIf="
                tenantForm?.controls['notes'].hasError('required') &&
                tenantForm?.controls['notes'].touched
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </div>
        <div class="row no-gutters" formGroupName="tenantConfig">
          <div class="p-2 col-md-3 col-sm-6">
            <mat-label>{{ 'tenants.maxBranch' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <input type="number" matInput formControlName="maxBranches" />
              <mat-error
                *ngIf="
                  tenantForm.get('tenantConfig')?.get('maxBranches').hasError('required') &&
                  tenantForm.get('tenantConfig')?.get('maxBranches').touched
                ">
                {{ 'common.required' | translate }}
              </mat-error>
            </mat-form-field>
          </div>
          <div class="p-2 col-md-3 col-sm-6">
            <mat-label>{{ 'tenants.maxWareHouse' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <input type="number" matInput formControlName="maxWarehouses" />
              <mat-error
                *ngIf="
                  tenantForm.get('tenantConfig')?.get('maxWarehouses').hasError('required') &&
                  tenantForm.get('tenantConfig')?.get('maxWarehouses').touched
                ">
                {{ 'common.required' | translate }}
              </mat-error>
            </mat-form-field>
          </div>
          <div class="p-2 col-md-3 col-sm-6">
            <mat-label>{{ 'tenants.maxUsers' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <input type="number" matInput formControlName="maxUsers" />
              <mat-error
                *ngIf="
                  tenantForm.get('tenantConfig')?.get('maxUsers').hasError('required') &&
                  tenantForm.get('tenantConfig')?.get('maxUsers').touched
                ">
                {{ 'common.required' | translate }}
              </mat-error>
            </mat-form-field>
          </div>
          <div class="p-2 col-md-3 col-sm-6">
            <mat-label>{{ 'tenants.storageLim' | translate }}</mat-label>
            <mat-form-field class="w-100">
              <input type="number" matInput formControlName="storageLimit" />
              <mat-error
                *ngIf="
                  tenantForm.get('tenantConfig')?.get('storageLimit').hasError('required') &&
                  tenantForm.get('tenantConfig')?.get('storageLimit').touched
                ">
                {{ 'common.required' | translate }}
              </mat-error>
            </mat-form-field>
          </div>
          <mat-selection-list formControlName="allowedFeatures">
            <mat-list-option
              *ngFor="let feature of allowedFeaturesList"
              [value]="feature.value"
              color="primary"
              checkboxPosition="before">
              {{ feature.display }}
            </mat-list-option>
          </mat-selection-list>
        </div>
      </form>
    </mat-card-content></mat-card
  >
  <app-action-buttons [mode]="mode" (submitAction)="onSubmit($event)"></app-action-buttons>
</ng-container>
