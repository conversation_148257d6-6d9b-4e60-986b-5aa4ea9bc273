import { Injectable, Renderer2, RendererFactory2 } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { CookieService } from 'ngx-cookie-service';
import { BehaviorSubject, Observable } from 'rxjs';
import { DOCUMENT } from '@angular/common';
import { Inject } from '@angular/core';
import { AppSettings, defaults } from 'src/app/app.config';
import { LocalStorageService } from './local-storage.service';

@Injectable({
  providedIn: 'root',
})
export class MultilingualService {
  private renderer: Renderer2;
  private languageSubject = new BehaviorSubject<string>(null);
  private optionsSubject = new BehaviorSubject<Record<string, any>>({});
  private options: AppSettings = { ...defaults };

  // Public observables for language and options changes
  public languageChanged$: Observable<string> = this.languageSubject.asObservable();
  public optionsChanged$: Observable<Record<string, any>> = this.optionsSubject.asObservable();

  constructor(
    private translateService: TranslateService,
    private cookieService: CookieService,
    private localStorage: LocalStorageService,
    rendererFactory: RendererFactory2,
    @Inject(DOCUMENT) private document: Document
  ) {
    this.renderer = rendererFactory.createRenderer(null, null);

    // Initialize with language from cookie or default
    const currentLang = this.getLanguageFromCookie() || defaults.language;
    this.options = {
      ...defaults,
      language: currentLang,
      dir: this.getDirection(currentLang),
    };

    // Subscribe to language changes from the translate service
    this.translateService.onLangChange.subscribe(event => {
      this.setDirectionBasedOnLanguage(event.lang);
      this.languageSubject.next(event.lang);
    });
  }

  /**
   * Loads the default language based on priority:
   * 1. User's manually selected language (preferredLanguage in localStorage)
   * 2. User's locale from JWT token
   * 3. Default language ('en')
   * @returns Observable of the loaded language
   */
  loadDefaultLanguage() {
    // Priority 1: Check for user's manually selected language in localStorage
    const preferredLanguage = this.localStorage.getItem('preferredLanguage');

    // Priority 2: Check for locale in JWT token
    const decodedToken = this.getDecodedToken();
    const tokenLocale = decodedToken?.locale;

    // Priority 3: Default to English if nothing else is available
    const language =
      this.validateLanguage(preferredLanguage) || this.validateLanguage(tokenLocale) || 'en';

    // Update options
    this.options.language = language;
    this.options.dir = this.getDirection(language);
    this.optionsSubject.next({ lang: language, dir: this.options.dir });

    // Set the document direction immediately
    this.setDirectionBasedOnLanguage(language);

    return this.translateService.use(language);
  }

  /**
   * Validates if a language code is supported (ar or en)
   * @param lang The language code to validate
   * @returns The validated language code or null if invalid
   */
  private validateLanguage(lang: string): string | null {
    if (!lang) return null;

    const normalizedLang = lang.toLowerCase();
    return normalizedLang === 'ar' || normalizedLang === 'en' ? normalizedLang : null;
  }

  /**
   * Gets the decoded JWT token
   * @returns The decoded token or null if not available
   */
  private getDecodedToken() {
    const token = this.localStorage.getItem('token');
    if (!token) return null;

    try {
      return JSON.parse(atob(token.split('.')[1]));
    } catch (e) {
      return null;
    }
  }

  /**
   * Sets the application language
   * @param lang The language code (ar or en)
   * @param userToggled Whether the language was explicitly toggled by the user
   */
  setLanguage(lang: string, userToggled = false) {
    // Validate language
    if (lang !== 'ar' && lang !== 'en') {
      lang = 'en'; // Default fallback
    }

    // Save user's manually selected language preference only if explicitly toggled
    if (userToggled) {
      this.localStorage.setItem('preferredLanguage', lang);
    }

    // Update options
    this.options.language = lang;
    this.options.dir = this.getDirection(lang);
    this.optionsSubject.next({ lang, dir: this.options.dir });

    // Set the document direction immediately
    this.setDirectionBasedOnLanguage(lang);

    // Update translate service
    this.translateService.use(lang);
  }

  /**
   * Gets the current language
   * @returns The current language code
   */
  getCurrentLanguage(): string {
    return this.translateService.currentLang || this.options.language;
  }

  /**
   * Gets the current application options
   * @returns The current options
   */
  getOptions(): AppSettings {
    // Ensure direction is always in sync with current language
    const currentLang = this.getCurrentLanguage();
    const options = { ...this.options };
    options.dir = this.getDirection(currentLang);
    return options;
  }

  /**
   * Sets application options
   * @param options The options to set
   */
  setOptions(options: AppSettings) {
    this.options = { ...options };

    // Ensure direction is always in sync with language
    this.options.dir = this.getDirection(this.options.language);

    this.optionsSubject.next(this.options);
  }

  /**
   * Sets the document direction based on the current language
   * @param lang The language code (ar or en)
   */
  setDirectionBasedOnLanguage(lang: string) {
    const dir = this.getDirection(lang);

    // Set direction only on body element - centralized approach
    this.renderer.setAttribute(this.document.body, 'dir', dir);

    // Update options to keep in sync
    this.options.dir = dir;
  }

  /**
   * Gets the text direction based on language
   * @param lang The language code
   * @returns 'rtl' for Arabic, 'ltr' for other languages
   */
  getDirection(lang: string): 'rtl' | 'ltr' {
    return lang === 'ar' ? 'rtl' : 'ltr';
  }

  /**
   * Gets the language from cookie
   * @returns The language code from cookie
   */
  getLanguageFromCookie(): string {
    return this.cookieService.get('locale');
  }

  /**
   * Updates displayed columns based on language for tables with nameEnglish/nameArabic columns
   * @param columns Array of column names
   * @returns Filtered array of columns based on current language
   */
  updateDisplayedColumns(columns: string[]) {
    if (this.translateService.currentLang === 'ar') {
      return columns.filter(column => column !== 'nameEnglish');
    } else {
      return columns.filter(column => column !== 'nameArabic');
    }
  }

  /**
   * Updates displayed columns based on language for tables with description/description2 columns
   * @param columns Array of column names
   * @returns Filtered array of columns based on current language
   */
  updateDisplayedColumns1(columns: string[]) {
    if (this.translateService.currentLang === 'ar') {
      return columns.filter(column => column !== 'description');
    } else {
      return columns.filter(column => column !== 'description2');
    }
  }

  /**
   * Gets the localized property value based on current language
   * @param options Object containing nameEnglish and nameArabic properties
   * @returns The appropriate property value based on current language
   */
  getLocalizedProperty(options: any): string {
    return this.translateService.currentLang === 'ar' ? options.nameArabic : options.nameEnglish;
  }

  /**
   * Translates a key using the translate service
   * @param key Translation key
   * @param params Optional parameters for translation
   * @returns Observable of the translated text
   */
  translate(key: string | string[], params?: any): Observable<string | any> {
    return this.translateService.get(key, params);
  }
}
