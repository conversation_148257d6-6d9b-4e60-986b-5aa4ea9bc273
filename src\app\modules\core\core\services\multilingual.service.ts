import { Injectable, Renderer2, RendererFactory2 } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { CookieService } from 'ngx-cookie-service';
import { BehaviorSubject, Observable } from 'rxjs';
import { DOCUMENT } from '@angular/common';
import { Inject } from '@angular/core';
import { Directionality } from '@angular/cdk/bidi';
import { AppSettings, defaults } from 'src/app/app.config';
import { LocalStorageService } from './local-storage.service';

@Injectable({
  providedIn: 'root',
})
export class MultilingualService {
  private renderer: Renderer2;
  private languageSubject = new BehaviorSubject<string>(null);
  private optionsSubject = new BehaviorSubject<Record<string, any>>({});
  private options: AppSettings = { ...defaults };

  // Public observables for language and options changes
  public languageChanged$: Observable<string> = this.languageSubject.asObservable();
  public optionsChanged$: Observable<Record<string, any>> = this.optionsSubject.asObservable();

  constructor(
    private translateService: TranslateService,
    private cookieService: CookieService,
    private localStorage: LocalStorageService,
    private directionality: Directionality,
    rendererFactory: RendererFactory2,
    @Inject(DOCUMENT) private document: Document
  ) {
    this.renderer = rendererFactory.createRenderer(null, null);

    // Initialize with simple default language
    const currentLang = this.getLanguageFromCookie() || defaults.language;
    this.options = {
      ...defaults,
      language: currentLang,
      dir: this.getDirection(currentLang),
    };

    // Subscribe to language changes from the translate service
    this.translateService.onLangChange.subscribe(event => {
      this.setDirectionBasedOnLanguage(event.lang);
      this.languageSubject.next(event.lang);
    });
  }

  /**
   * Simplified language loading - only after successful authentication
   * Priority: 1. User preference, 2. JWT token locale, 3. Default 'en'
   * @returns Observable of the loaded language
   */
  loadDefaultLanguage() {
    // Simple priority check
    const preferredLanguage = this.localStorage.getItem('preferredLanguage');
    const tokenLocale = this.getTokenLocale();
    const language =
      this.validateLanguage(preferredLanguage) || this.validateLanguage(tokenLocale) || 'en';

    return this.setLanguage(language);
  }

  /**
   * Load language specifically after branch selection (for login flow)
   * This should be called from AuthService.navigateToDashBoard()
   */
  loadLanguageAfterAuth() {
    const tokenLocale = this.getTokenLocale();
    const preferredLanguage = this.localStorage.getItem('preferredLanguage');

    // If user has a preference, use it; otherwise use token locale
    const language =
      this.validateLanguage(preferredLanguage) || this.validateLanguage(tokenLocale) || 'en';

    return this.setLanguage(language);
  }

  /**
   * Validates if a language code is supported (ar or en)
   * @param lang The language code to validate
   * @returns The validated language code or null if invalid
   */
  private validateLanguage(lang: string): string | null {
    if (!lang) return null;

    const normalizedLang = lang.toLowerCase();
    return normalizedLang === 'ar' || normalizedLang === 'en' ? normalizedLang : null;
  }

  /**
   * Gets the locale from JWT token
   * @returns The locale from token or null if not available
   */
  private getTokenLocale(): string | null {
    const token = this.localStorage.getItem('token');
    if (!token) return null;

    try {
      const decoded = JSON.parse(atob(token.split('.')[1]));
      return decoded?.locale || null;
    } catch (e) {
      return null;
    }
  }

  /**
   * Sets the application language with proper CDK integration
   * @param lang The language code (ar or en)
   * @param userToggled Whether the language was explicitly toggled by the user
   */
  setLanguage(lang: string, userToggled = false) {
    // Validate language
    if (lang !== 'ar' && lang !== 'en') {
      lang = 'en'; // Default fallback
    }

    // Save user's manually selected language preference only if explicitly toggled
    if (userToggled) {
      this.localStorage.setItem('preferredLanguage', lang);
    }

    const direction = this.getDirection(lang);

    // Update options
    this.options.language = lang;
    this.options.dir = direction;
    this.optionsSubject.next({ lang, dir: direction });

    // Set document direction and update CDK Directionality
    this.setDirectionBasedOnLanguage(lang);

    // Update translate service and return the observable
    return this.translateService.use(lang);
  }

  /**
   * Gets the current language
   * @returns The current language code
   */
  getCurrentLanguage(): string {
    return this.translateService.currentLang || this.options.language;
  }

  /**
   * Gets the current application options
   * @returns The current options
   */
  getOptions(): AppSettings {
    // Ensure direction is always in sync with current language
    const currentLang = this.getCurrentLanguage();
    const options = { ...this.options };
    options.dir = this.getDirection(currentLang);
    return options;
  }

  /**
   * Sets application options
   * @param options The options to set
   */
  setOptions(options: AppSettings) {
    this.options = { ...options };

    // Ensure direction is always in sync with language
    this.options.dir = this.getDirection(this.options.language);

    this.optionsSubject.next(this.options);
  }

  /**
   * Sets the document direction and updates CDK Directionality
   * @param lang The language code (ar or en)
   */
  setDirectionBasedOnLanguage(lang: string) {
    const dir = this.getDirection(lang);

    // Set direction on body element
    this.renderer.setAttribute(this.document.body, 'dir', dir);

    // Update CDK Directionality for Angular Material components
    if (this.directionality.value !== dir) {
      (this.directionality as any).value = dir;
      this.directionality.change.emit(dir);
    }

    // Update options to keep in sync
    this.options.dir = dir;
  }

  /**
   * Gets the text direction based on language
   * @param lang The language code
   * @returns 'rtl' for Arabic, 'ltr' for other languages
   */
  getDirection(lang: string): 'rtl' | 'ltr' {
    return lang === 'ar' ? 'rtl' : 'ltr';
  }

  /**
   * Gets the language from cookie
   * @returns The language code from cookie
   */
  getLanguageFromCookie(): string {
    return this.cookieService.get('locale');
  }

  /**
   * Updates displayed columns based on language for tables with nameEnglish/nameArabic columns
   * @param columns Array of column names
   * @returns Filtered array of columns based on current language
   */
  updateDisplayedColumns(columns: string[]) {
    if (this.translateService.currentLang === 'ar') {
      return columns.filter(column => column !== 'nameEnglish');
    } else {
      return columns.filter(column => column !== 'nameArabic');
    }
  }

  /**
   * Updates displayed columns based on language for tables with description/description2 columns
   * @param columns Array of column names
   * @returns Filtered array of columns based on current language
   */
  updateDisplayedColumns1(columns: string[]) {
    if (this.translateService.currentLang === 'ar') {
      return columns.filter(column => column !== 'description');
    } else {
      return columns.filter(column => column !== 'description2');
    }
  }

  /**
   * Gets the localized property value based on current language
   * @param options Object containing nameEnglish and nameArabic properties
   * @returns The appropriate property value based on current language
   */
  getLocalizedProperty(options: any): string {
    return this.translateService.currentLang === 'ar' ? options.nameArabic : options.nameEnglish;
  }

  /**
   * Translates a key using the translate service
   * @param key Translation key
   * @param params Optional parameters for translation
   * @returns Observable of the translated text
   */
  translate(key: string | string[], params?: any): Observable<string | any> {
    return this.translateService.get(key, params);
  }
}
