<!-- Filter Place Holders -->
<mat-card appearance="outlined">
  <form [formGroup]="filterForm" autocomplete="off">
    <div class="row no-gutters">
      <div class="p-2 col-md-6">
        <app-searchbox
          #searchBoxForm
          [accountNo]="true"
          [accountName]="false"
          [nameEnglish]="true"
          [nameArabic]="true"
          [itemCode]="false"
          [defaultSearchType]="'accountNo'"
          [validatorApplied]="true"
          formControlName="searchBoxForm"></app-searchbox>
      </div>
      <div class="p-2 col-lg-3 col-md-3 col-sm-6 d-flex align-items-end">
        <div class="form-group">
          <mat-label>{{ 'voucher.status' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <mat-select formControlName="settlementStatus">
              <mat-option *ngFor="let stat of status" [value]="stat.value">{{
                stat.display | translate
              }}</mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>
      <div class="p-2 col-lg-3 col-md-3 col-sm-6 d-flex align-items-end">
        <div class="form-group">
          <mat-label>{{ 'voucher.dateRange' | translate }}</mat-label>

          <mat-form-field class="w-100">
            <mat-date-range-input [rangePicker]="picker" [max]="todayDate" disabled>
              <input
                matStartDate
                disabled
                formControlName="invoiceDateFrom"
                placeholder="{{ 'voucher.startDate' | translate }}" />
              <input
                matEndDate
                disabled
                formControlName="invoiceDateTo"
                placeholder="{{ 'voucher.endDate' | translate }}" />
            </mat-date-range-input>
            <mat-datepicker-toggle [for]="picker" matIconSuffix></mat-datepicker-toggle>
            <mat-date-range-picker #picker disabled="false"></mat-date-range-picker>

            <mat-error *ngIf="filterForm.controls.invoiceDateFrom.hasError('matStartDateInvalid')"
              >Invalid start date</mat-error
            >
            <mat-error *ngIf="filterForm.controls.invoiceDateTo.hasError('matEndDateInvalid')"
              >Invalid end date</mat-error
            >
          </mat-form-field>
        </div>
      </div>
    </div>
    <button
      (click)="search($event); searchBoxForm.markAllAsTouched(); (false)"
      mat-raised-button
      color="primary">
      {{ 'searchPanel.searchString' | translate }}
    </button>
    <button class="m-l-10" (click)="clearFilters($event); (false)" mat-stroked-button>
      {{ 'searchPanel.clear' | translate }}
    </button>
  </form>
</mat-card>
<!-- Filter Place Holders -->

<button
  *ngIf="selection.selected.length"
  (click)="openDialog($event)"
  mat-raised-button
  color="primary">
  {{ 'voucher.createVoucher' | translate }}
</button>
<div class="table-responsive">
  <table class="w-100" [dataSource]="dataSource" mat-table matSort>
    <!-- Checkbox Column -->
    <ng-container matColumnDef="select">
      <th *matHeaderCellDef mat-header-cell>
        <mat-checkbox
          [disabled]="disabledSelection"
          [color]="'primary'"
          [checked]="selection.hasValue() && isAllSelected()"
          [indeterminate]="selection.hasValue() && !isAllSelected()"
          (change)="$event ? toggleAllRows() : null">
        </mat-checkbox>
      </th>
      <td *matCellDef="let row" mat-cell>
        <mat-checkbox
          [disabled]="disabledSelection"
          [color]="'primary'"
          [checked]="selection.isSelected(row)"
          (click)="$event.stopPropagation()"
          (change)="$event ? selection.toggle(row) : null">
        </mat-checkbox>
      </td>
    </ng-container>
    <ng-container matColumnDef="documentNumber">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'sales.invoiceNo' | translate }}
      </th>
      <td *matCellDef="let element" mat-cell>
        {{ element.documentNumber }}
      </td>
    </ng-container>
    <ng-container matColumnDef="issueDate">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'sales.invoiceDate' | translate }}
      </th>
      <td *matCellDef="let element" mat-cell>
        {{ element.issueDate | date : 'yyyy-MM-dd' }}
      </td>
    </ng-container>
    <ng-container matColumnDef="invoiceTotal">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'sales.invoiceAmt' | translate }}
      </th>
      <td *matCellDef="let element" mat-cell>
        <div class="text-center cursor-pointer rounded bg-light-success">
          {{ element.invoiceTotal }}
        </div>
      </td>
    </ng-container>
    <ng-container matColumnDef="unpaidAmount">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'sales.unpaidAmount' | translate }}
      </th>
      <td *matCellDef="let element" mat-cell>
        <div class="text-center cursor-pointer rounded bg-light-primary">
          {{ element.unpaidAmount }}
        </div>
      </td>
    </ng-container>
    <ng-container matColumnDef="accountNumber">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'sales.invoiceCustomerNo' | translate }}
      </th>
      <td *matCellDef="let element" [data]="element?.account?.accountNumber" appHyphen mat-cell>
        {{ element?.account?.accountNumber }}
      </td>
    </ng-container>
    <ng-container matColumnDef="nameArabic">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'common.field.nameArabic' | translate }}
      </th>
      <td *matCellDef="let element" [data]="element?.account?.nameArabic" mat-cell appHyphen>
        {{ element?.account?.nameArabic }}
      </td>
    </ng-container>
    <ng-container matColumnDef="nameEnglish">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'common.field.nameEnglish' | translate }}
      </th>
      <td *matCellDef="let element" [data]="element?.account?.nameEnglish" mat-cell appHyphen>
        {{ element?.account?.nameEnglish }}
      </td>
    </ng-container>
    <ng-container matColumnDef="vatNumber">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'common.field.vatNo' | translate }}
      </th>
      <td *matCellDef="let element" [data]="element.vatNumber" mat-cell appHyphen>
        {{ element.vatNumber }}
      </td>
    </ng-container>
    <ng-container matColumnDef="phoneNumber">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'common.field.phone' | translate }}
      </th>
      <td *matCellDef="let element" [data]="element.phoneNumber" mat-cell appHyphen>
        {{ element.phoneNumber }}
      </td>
    </ng-container>
    <ng-container matColumnDef="voucherStatus">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'voucher.voucherStatus' | translate }}
      </th>
      <td *matCellDef="let element" mat-cell>
        <ng-container *ngIf="element.settlementStatus > 1; else noVoucher">
          <button (click)="openVoucherList(element)" mat-menu-item>
            <div class="d-flex align-items-center">
              <i-tabler class="icon-16 m-r-4" name="list"></i-tabler>
            </div>
          </button>
        </ng-container>
        <ng-template #noVoucher>
          <div class="bg-light-error text-center rounded">
            {{ 'voucher.noVouchersCreated' | translate }}
          </div>
        </ng-template>
      </td>
    </ng-container>
    <ng-container matColumnDef="returnStatus">
      <th *matHeaderCellDef mat-header-cell mat-sort-header>
        {{ 'sales.returnStatus' | translate }}
      </th>
      <td *matCellDef="let element" mat-cell>
        <div class="text-center cursor-pointer rounded">
          <ng-container *ngIf="element.settlementStatus === 1">
            <div class="bg-light-error text-center rounded">
              {{ 'salesStatusDropDown.notPaid' | translate }}
            </div>
          </ng-container>
          <ng-container *ngIf="element.settlementStatus === 2">
            <div class="bg-light-primary text-center rounded">
              {{ 'salesStatusDropDown.partiallyPaid' | translate }}
            </div>
          </ng-container>
          <ng-container *ngIf="element.settlementStatus === 3">
            <div class="bg-light-success text-center rounded">
              {{ 'salesStatusDropDown.fullyPaid' | translate }}
            </div>
          </ng-container>
        </div>
      </td>
    </ng-container>
    <ng-container matColumnDef="action">
      <th *matHeaderCellDef mat-header-cell>
        {{ 'common.action' | translate }}
      </th>
      <td *matCellDef="let element" mat-cell>
        <button class="d-flex justify-content-center" [matMenuTriggerFor]="menu1" mat-icon-button>
          <i-tabler class="icon-20 d-flex" name="dots-vertical"></i-tabler>
        </button>
        <mat-menu class="cardWithShadow" #menu1="matMenu">
          <button mat-menu-item>
            <div class="d-flex align-items-center" (click)="openDialog()">
              <i-tabler class="icon-16 m-r-4" name="eye"></i-tabler>
              <span>Make Payment</span>
            </div>
          </button>

          <!-- <ng-container
            *ngIf="
              element.returnStatus !== 'RETURNED' && element.returnStatus !== 'PARTIALLYRETURNED'
            ">
            <button
              *appHasPermission="['Sales.Create', 'AllPermissions']"
              [routerLink]="['creditnote', element.id]"
              mat-menu-item>
              <div class="d-flex align-items-center">
                <i-tabler class="icon-16 m-r-4" name="edit"></i-tabler>
                <span>{{ 'sales.fullCreditNote' | translate }}</span>
              </div>
            </button>
          </ng-container>

          <ng-container
            *ngIf="
              element.returnStatus !== 'RETURNED' || element.returnStatus === 'PARTIALLYRETURNED'
            ">
            <button
              *appHasPermission="['Sales.Create', 'AllPermissions']"
              [routerLink]="['partialcreditnote', element.id]"
              mat-menu-item>
              <div class="d-flex align-items-center">
                <i-tabler class="icon-16 m-r-4" name="edit"></i-tabler>
                <span>{{ 'sales.partialCreditNote' | translate }}</span>
              </div>
            </button>
          </ng-container>

          <ng-container>
            <button
              *appHasPermission="['Sales.Create', 'AllPermissions']"
              [routerLink]="['debitnote', element.id]"
              mat-menu-item>
              <div class="d-flex align-items-center">
                <i-tabler class="icon-16 m-r-4" name="edit"></i-tabler>
                <span>{{ 'sales.debitNote' | translate }}</span>
              </div>
            </button>
          </ng-container>
          <ng-container *ngIf="element.returnStatus !== 'NOTRETURNED'">
            <button
              *appHasPermission="['Sales.Create', 'AllPermissions']"
              (click)="openSalesNotes(element.id); (false)"
              mat-menu-item>
              <div class="d-flex align-items-center">
                <i-tabler class="icon-16 m-r-4" name="clipboard-list"></i-tabler>
                <span>{{ 'sales.viewNotes' | translate }}</span>
              </div>
            </button>
          </ng-container> -->
        </mat-menu>
      </td>
    </ng-container>
    <tr class="mat-row" *matNoDataRow>
      <td class="text-center" [attr.colspan]="displayedColumns.length">
        {{ 'common.noDataFound' | translate }}
      </td>
    </tr>
    <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
    <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
  </table>
</div>
