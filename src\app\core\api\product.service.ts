import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Result } from 'src/app/core/models/wrappers/Result';
import { createParamsFromObject } from 'src/app/core/utils/date-utils';
import { environment } from 'src/environments/environment';
import { AdjustmentLists } from '../../modules/featured/stock/models/adjustment-list';
import { Inventories } from '../../modules/featured/stock/models/unitPrice';
import { IProductSearch, Product } from '../../modules/featured/catalog/models/product';
import { ProductAdjustmentsParams } from '../../modules/featured/catalog/models/productAdjustmentParams';
import { ProductParams } from '../../modules/featured/catalog/models/productParams';
import { UnitPriceAdjustmentsParams } from '../../modules/featured/catalog/models/unitPriceAdjustmentParams';

@Injectable({
  providedIn: 'root',
})
export class ProductService {
  baseUrl = environment.apiUrl + 'catalog/products';
  constructor(private http: HttpClient) {}

  getAllProducts(productParams: ProductParams): Observable<any> {
    const params = createParamsFromObject(productParams);
    return this.http.get(this.baseUrl + '/pages', { params: params });
  }

  getProductsByFilter(productParams: ProductAdjustmentsParams): Observable<IProductSearch> {
    const params = createParamsFromObject(productParams);
    return this.http.get<IProductSearch>(`${environment.apiUrl}catalog/inventories/pages`, {
      params: params,
    });
  }

  getUnitsByCriteria(productParams: UnitPriceAdjustmentsParams, id?: string): Observable<any> {
    const params = createParamsFromObject(productParams);
    return this.http.get(this.baseUrl + `/units/pages`, { params: params });
  }

  getDocumentsAdjustmentsByFilter(
    productParams: ProductAdjustmentsParams
  ): Observable<AdjustmentLists> {
    const params = createParamsFromObject(productParams);
    return this.http.get<AdjustmentLists>(
      `${environment.apiUrl}catalog/inventory/adjustments/documents`,
      {
        params: params,
      }
    );
  }

  getDocumentById(id: string): Observable<any> {
    const params = new HttpParams();
    return this.http.get<Result<Product>>(
      `${environment.apiUrl}catalog/inventory/adjustments` + '/' + id,
      {
        params: params,
      }
    );
  }

  getProductById(id: string): Observable<Product> {
    const params = new HttpParams();
    return this.http.get<Product>(this.baseUrl + '/' + id, { params: params });
  }

  create(product: FormData): Observable<any> {
    const params = new HttpParams();
    return this.http.post(this.baseUrl, product, { params: params });
  }

  updateProduct(product: FormData, id: string): Observable<any> {
    const params = new HttpParams();
    return this.http.put(this.baseUrl + `/${id}`, product, { params: params });
  }

  deleteProduct(id: string): Observable<any> {
    const params = new HttpParams();
    return this.http.delete(this.baseUrl + id, { params: params });
  }

  deleteItemUnit(itemUnitId: string): Observable<any> {
    const params = new HttpParams();
    return this.http.delete(environment.apiUrl + 'catalog/products/units/' + itemUnitId, {
      params: params,
    });
  }

  updateOpenQuantity(product): Observable<any> {
    const params = new HttpParams();
    return this.http.put(`${environment.apiUrl}catalog/inventories`, product, { params: params });
  }

  createStockAdjustment(product): Observable<any> {
    const params = new HttpParams();
    return this.http.post(`${environment.apiUrl}catalog/inventory/adjustments`, product, {
      params: params,
    });
  }

  updateUnitPrices(product): Observable<any> {
    const params = new HttpParams();
    return this.http.put(this.baseUrl + `/units/bulk-update`, product, { params: params });
  }

  postStockAdjustment(product, id): Observable<any> {
    const params = new HttpParams();
    return this.http.put(`${environment.apiUrl}catalog/inventory/adjustments/post/${id}`, product, {
      params: params,
    });
  }

  preAdjustmentsUpdate(product, id): Observable<any> {
    const params = new HttpParams();
    return this.http.put(
      `${environment.apiUrl}catalog/inventory/adjustments/update/${id}`,
      product,
      {
        params: params,
      }
    );
  }

  bulkUpdateProducts(product): Observable<any> {
    const params = new HttpParams();
    return this.http.put(this.baseUrl + `/update-bulk`, product, { params: params });
  }

  importData(formData: FormData): Observable<any> {
    const params = new HttpParams();
    return this.http
      .post(this.baseUrl + '/import', formData, {
        withCredentials: true,
        responseType: 'arraybuffer',
        params: params,
      })
      .pipe(
        tap((result: ArrayBuffer) => {
          const fileURL = URL.createObjectURL(new Blob([result]));
          const link = document.createElement('a');
          link.href = fileURL;
          link.setAttribute('download', 'Product_List.xls');
          document.body.appendChild(link);
          link.click();
        })
      );
  }

  importItemUnit(formData: FormData): Observable<any> {
    const params = new HttpParams();
    return this.http
      .post(this.baseUrl + '/units/import', formData, {
        withCredentials: true,
        responseType: 'arraybuffer',
        params: params,
      })
      .pipe(
        tap((result: ArrayBuffer) => {
          const fileURL = URL.createObjectURL(new Blob([result]));
          const link = document.createElement('a');
          link.href = fileURL;
          link.setAttribute('download', 'ItemUnit_List.xls');
          document.body.appendChild(link);
          link.click();
        })
      );
  }

  importInventory(formData: FormData): Observable<any> {
    const params = new HttpParams();
    return this.http
      .post(environment.apiUrl + 'catalog/inventories/import', formData, {
        withCredentials: true,
        responseType: 'arraybuffer',
        params: params,
      })
      .pipe(
        tap((result: ArrayBuffer) => {
          const fileURL = URL.createObjectURL(new Blob([result]));
          const link = document.createElement('a');
          link.href = fileURL;
          link.setAttribute('download', 'Inventory_List.xls');
          document.body.appendChild(link);
          link.click();
        })
      );
  }
  getProductByBranchesAndYearId(branchIds: string[], yearId: string): Observable<any> {
    let params = new HttpParams();
    params = params.append('branchIds', branchIds.toString());
    params = params.append('yearId', yearId);
    return this.http.get<Product>(this.baseUrl + '/signatures', { params: params });
  }
}
