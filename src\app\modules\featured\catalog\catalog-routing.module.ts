import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionGuard } from '../../core/core/guards/permission.guard';
import { CategoryFormComponent } from './components/category/category-form/category-form.component';
import { CategoryListComponent } from './components/category/category-list/category-list.component';
import { CatalogDashboardComponent } from './components/dashboard/catalog-dashboard.component';
import { ListProductsComponent } from './components/product/list-products/list-products.component';
import { ProductFormComponent } from './components/product/product-form/product-form.component';
import { UnitsFormComponent } from './components/units/unit-form/units-form.component';
import { UnitsComponent } from './components/units/units.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'dashboard',
    pathMatch: 'full',
  },
  {
    path: 'dashboard',
    component: CatalogDashboardComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Inventory Management',
      urls: [{ title: 'Main Dashboard', url: '/dashboard' }],
      allowedPermissions: ['InventoryManagement', 'AllPermissions'],
    },
  },
  {
    path: 'category',
    component: CategoryListComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'productCategory.listings',
      urls: [{ title: 'Inventory Dashboard', url: '/inventory/dashboard' }],
      allowedPermissions: ['Categories', 'AllPermissions'],
    },
  },
  {
    path: 'units',
    component: UnitsComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'productUnits.listing',
      urls: [{ title: 'Inventory Dashboard', url: '/inventory/dashboard' }],
      allowedPermissions: ['Units', 'AllPermissions'],
    },
  },
  {
    path: 'units/create',
    component: UnitsFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'productUnits.createUnit',
      urls: [{ title: 'Unit Listing', url: '/inventory/units' }],
      allowedPermissions: ['Units.Create', 'AllPermissions'],
    },
  },
  {
    path: 'units/edit/:id',
    component: UnitsFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'productUnits.edit',
      urls: [{ title: 'Unit Listing', url: '/inventory/units' }],
      allowedPermissions: ['Units.Update', 'AllPermissions'],
    },
  },
  {
    path: 'products',
    component: ListProductsComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'productBaicTab.listing',
      urls: [{ title: 'Inventory Dashboard', url: '/inventory/dashboard' }],
      allowedPermissions: ['Product', 'AllPermissions'],
    },
  },
  {
    path: 'products/create',
    component: ProductFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'productBaicTab.productConfiguration',
      urls: [{ title: 'Product Listing', url: '/inventory/products' }],
      allowedPermissions: ['Product.Create', 'AllPermissions'],
      mode: 'create',
    },
  },
  {
    path: 'products/edit/:id',
    component: ProductFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'Edit Product',
      urls: [{ title: 'Product Listing', url: '/inventory/products' }],
      allowedPermissions: ['Product.Update', 'AllPermissions'],
      mode: 'edit',
    },
  },
  {
    path: 'products/view/:id',
    component: ProductFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'View Product',
      urls: [{ title: 'Product Listing', url: '/inventory/products' }],
      allowedPermissions: ['Product.View', 'AllPermissions'],
      mode: 'view',
    },
  },
  {
    path: 'category/create',
    component: CategoryFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'productCategory.createCatgory',
      urls: [{ title: 'Category Listing', url: '/inventory/category' }],
      allowedPermissions: ['Categories.Create', 'AllPermissions'],
    },
  },
  {
    path: 'category/edit/:id',
    component: CategoryFormComponent,
    canActivate: [PermissionGuard],
    data: {
      title: 'productCategory.edit',
      urls: [{ title: 'Category Listing', url: '/inventory/category' }],
      allowedPermissions: ['Categories.Update', 'AllPermissions'],
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class CatalogRoutingModule {}
