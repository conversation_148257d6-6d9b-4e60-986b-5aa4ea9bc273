<mat-toolbar class="topbar horizontal-topbar topbar-35">
  <div class="container">
    <!-- Mobile Menu -->
    <button class="d-block d-lg-none m-t-0" (click)="toggleMobileNav.emit()" mat-icon-button>
      <i-tabler class="icon-30 d-flex" name="menu-2"></i-tabler>
    </button>
    <button class="d-block m-t-0" (click)="onClickLogout()" mat-icon-button>
      <i-tabler class="icon-30 d-flex" name="logout"></i-tabler>
    </button>
    <!-- Branch Switcher -->
    <div
      class="branch-display d-flex align-items-center branch-button"
      (click)="openBranchSwitcher()">
      <i-tabler class="branch-icon-main" name="building"></i-tabler>
      <div class="branch-text">
        <span class="branch-name">{{ currentBranch }}</span>
        <span class="branch-year">{{ currentYear }}</span>
      </div>
    </div>

    <!-- --------------------------------------------------------------- -->

    <span class="flex-1-auto"></span>

    <button class="d-block m-t-0" routerLink="dashboard" mat-icon-button>
      <i-tabler class="icon-30 d-flex" name="home"></i-tabler>
    </button>
    <button
      class="d-block m-t-0"
      (click)="toggleLanguage()"
      mat-icon-button
      title="Toggle Language">
      <i-tabler class="icon-30 d-flex" name="language"></i-tabler>
    </button>
    <!-- Profile Dropdown -->
    <button [matMenuTriggerFor]="profilemenu" mat-icon-button aria-label="Profile">
      <i-tabler class="icon-30 d-flex" name="user-circle"></i-tabler>
    </button>
    <mat-menu class="topbar-dd cardWithShadow" #profilemenu="matMenu" xPosition="before">
      <div class="p-x-32">
        <h6 class="f-s-16 f-w-500 m-0">{{ 'header.mainProfile' | translate }}</h6>
        <div class="d-flex align-items-center p-b-24 m-t-16">
          <button class="d-flex" class="text-primary shadow-none rounded">
            <i-tabler class="icon-20 d-flex" name="building"></i-tabler>
          </button>
          <div class="m-l-16">
            <h6 class="f-s-14 f-w-400 m-0">{{ authService.getFullName }}</h6>
            <span class="f-s-14 d-block m-b-4">
              <i-tabler class="icon-15 m-r-4" name="calendar-time"></i-tabler>{{ currentYear }}
            </span>
            <span class="d-flex align-items-center">
              <i-tabler class="icon-15 m-r-4" name="building-warehouse"></i-tabler>
              {{ currentBranch }}
            </span>
          </div>
        </div>
      </div>
      <div class="p-x-32">
        <a
          class="text-decoration-none d-block text-hover-primary"
          *ngFor="let profile of profiledd"
          routerLink="profile">
          <div class="d-flex align-items-center">
            <button class="d-flex" class="text-primary shadow-none rounded">
              <i-tabler class="icon-20 d-flex" name="address-book"></i-tabler>
            </button>
            <div class="m-l-16">
              <h5 class="f-s-14 f-w-400 m-0 textprimary mat-subtitle-1 hover-text">
                {{ 'header.profile' | translate }}
              </h5>
            </div>
          </div>
        </a>
      </div>

      <div class="p-y-12 p-x-32">
        <a class="w-100" (click)="onClickLogout()" mat-raised-button color="primary">{{
          'header.logout' | translate
        }}</a>
      </div>
    </mat-menu>
  </div>
</mat-toolbar>
