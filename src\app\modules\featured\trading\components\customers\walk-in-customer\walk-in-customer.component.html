<app-dialog-header></app-dialog-header>

<form [formGroup]="walkInCustomerForm" autocomplete="off">
  <mat-card-title>{{ 'common.field.accountDetails' | translate }}</mat-card-title>
  <div class="row no-gutters m-t-10">
    <!-- nameArabic  -->
    <div class="p-2 col-md-4 col-lg-4 col-sm-12">
      <mat-label>{{ 'common.field.nameEnglish' | translate }}</mat-label>
      <mat-form-field class="w-100">
        <input type="text" maxlength="40" matInput formControlName="nameEnglish" />
        <mat-error
          *ngIf="
            walkInCustomerForm?.controls['nameEnglish'].hasError('required') &&
            walkInCustomerForm?.controls['nameEnglish'].touched
          ">
          {{ 'common.required' | translate }}
        </mat-error>
      </mat-form-field>
    </div>

    <!-- phoneNumber -->
    <div class="p-2 col-md-4 col-lg-4 col-sm-12">
      <mat-label>{{ 'common.field.phone' | translate }}</mat-label>
      <mat-form-field class="w-100">
        <input type="text" maxlength="40" matInput formControlName="phoneNumber" />
        <mat-error
          *ngIf="
            walkInCustomerForm?.controls['phoneNumber'].hasError('required') &&
            walkInCustomerForm?.controls['phoneNumber'].touched
          ">
          {{ 'common.required' | translate }}
        </mat-error>
      </mat-form-field>
    </div>
    <div class="p-2 col-md-4 col-lg-4 col-sm-12">
      <mat-label>{{ 'common.field.email' | translate }}</mat-label>
      <mat-form-field class="w-100">
        <input type="text" maxlength="40" matInput formControlName="emailId" />
        <mat-error
          *ngIf="
            walkInCustomerForm.controls['emailId'].hasError('required') &&
            walkInCustomerForm.controls['emailId'].touched
          ">
          {{ 'common.required' | translate }}
        </mat-error>
        <mat-error
          *ngIf="
            walkInCustomerForm.controls['emailId'].errors?.email &&
            walkInCustomerForm.controls['emailId'].touched
          ">
          {{ 'common.required' | translate }}
        </mat-error>
      </mat-form-field>
    </div>
  </div>
</form>
<mat-dialog-actions align="center">
  <button *ngIf="!data?.isViewMode" (click)="onSubmit($event)" mat-flat-button color="primary">
    {{ 'common.buttons.save' | translate }}
  </button>
  <button (click)="onNoClick(); (false)" mat-stroked-button>
    {{ 'common.buttons.cancel' | translate }}
  </button>
</mat-dialog-actions>
