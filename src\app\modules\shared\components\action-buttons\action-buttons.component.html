<div class="text-center">
  <!--   -->
  <ng-container *ngIf="mode === actionType.create">
    <button
      class="m-l-10"
      (click)="onSubmit({ event: $event, actionType: actionType.create }); (false)"
      type="button"
      mat-flat-button
      color="primary">
      {{ 'common.buttons.submit' | translate }}
    </button>
    <button class="m-l-10" [routerLink]="['../']" type="button" mat-stroked-button color="warn">
      {{ 'common.buttons.cancel' | translate }}
    </button>
  </ng-container>
  <ng-container *ngIf="mode === actionType.fullCreditNote">
    <button
      class="m-l-10"
      (click)="onSubmit({ event: $event, actionType: actionType.fullCreditNote }); (false)"
      type="button"
      mat-flat-button
      color="warn">
      {{ processType + '.fullCreditNoteButton' | translate }}
    </button>
    <button class="m-l-10" [routerLink]="['../../']" type="button" mat-stroked-button>
      {{ 'common.buttons.cancel' | translate }}
    </button>
  </ng-container>
  <ng-container *ngIf="mode === actionType.edit">
    <button
      class="m-l-10"
      (click)="onSubmit({ event: $event, actionType: actionType.edit }); (false)"
      type="button"
      mat-flat-button
      color="primary">
      {{ 'common.buttons.save' | translate }}
    </button>
    <button class="m-l-10" [routerLink]="['../../']" type="button" mat-stroked-button color="warn">
      {{ 'common.buttons.cancel' | translate }}
    </button>
  </ng-container>
  <ng-container *ngIf="mode === actionType.post">
    <button
      class="m-l-10"
      (click)="onSubmit({ event: $event, actionType: actionType.post }); (false)"
      type="button"
      mat-flat-button
      color="warn">
      {{ 'common.postAction' | translate }}
    </button>
    <button class="m-l-10" [routerLink]="['../../']" type="button" mat-stroked-button>
      {{ 'common.buttons.cancel' | translate }}
    </button>
  </ng-container>
  <ng-container *ngIf="mode === actionType.view">
    <button class="m-l-10" [routerLink]="['../../']" type="button" mat-stroked-button>
      {{ 'common.buttons.back' | translate }}
    </button>
  </ng-container>
  <ng-container *ngIf="mode === actionType.delete">
    <button
      class="m-l-10"
      (click)="onSubmit({ event: $event, actionType: actionType.delete }); (false)"
      type="button"
      mat-flat-button
      color="warn">
      {{ 'common.deleteAction' | translate }}
    </button>
    <button class="m-l-10" [routerLink]="['../../']" type="button" mat-stroked-button>
      {{ 'common.buttons.cancel' | translate }}
    </button>
  </ng-container>
  <!--   -->
</div>
