<!-- action bar -->
<app-create-action
  *appHasPermission="['WareHouse.Create', 'AllPermissions']"
  [label]="'stores.createStore' | translate"></app-create-action>
<!-- action bar -->

<!----------------------------------- mat table content --------------------------------------->

<mat-card appearance="outlined">
  <mat-card-content>
    <!-- search field -->
    <div class="row no-gutters">
      <div class="col-md-6 col-lg-6 col-sm-12 d-flex">
        <mat-form-field class="w-100">
          <input
            #searchInput
            (keyup)="applyFilter($event.target.value)"
            matInput
            autocomplete="off" />
          <i-tabler class="icon-16" matPrefix name="search"></i-tabler>
          <a
            class="cursor-pointer"
            *ngIf="searchInput?.value"
            (click)="clearSearchInput()"
            matSuffix>
            <i-tabler class="icon-16 error" name="X"></i-tabler>
          </a>
        </mat-form-field>
      </div>
    </div>
    <mat-card-title class="m-t-10">{{ formTitle | translate }}</mat-card-title>
    <!-- search field -->
    <div class="table-responsive">
      <table [dataSource]="dataSource" mat-table matSort>
        <!-- Branch Name Arabic Column -->
        <ng-container matColumnDef="branchNameArabic">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'branch.nameArabic' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ element.branchNameArabic }}
          </td>
        </ng-container>
        <!-- Branch Name Arabic Column -->
        <ng-container matColumnDef="branchNameEnglish">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'branch.nameEnglish' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ element.branchNameEnglish }}
          </td>
        </ng-container>
        <!-- nameArabic Column -->
        <ng-container matColumnDef="nameArabic">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'stores.nameArabic' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ element.nameArabic }}
          </td>
        </ng-container>
        <!-- nameEnglish Column -->
        <ng-container matColumnDef="nameEnglish">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'stores.nameEnglish' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ element.nameEnglish }}
          </td>
        </ng-container>
        <!-- emailId Column -->
        <ng-container matColumnDef="emailId">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'stores.email' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ element.emailId }}
          </td>
        </ng-container>
        <!-- phoneNumber Column -->
        <ng-container matColumnDef="phoneNumber">
          <th *matHeaderCellDef mat-header-cell mat-sort-header>
            {{ 'stores.phone' | translate }}
          </th>
          <td *matCellDef="let element" mat-cell>
            {{ element.phoneNumber }}
          </td>
        </ng-container>
        <!-- User Actions Column -->
        <ng-container matColumnDef="action">
          <th *matHeaderCellDef mat-header-cell>
            {{ 'common.action' | translate }}
          </th>
          <td class="action-link" *matCellDef="let element" tabindex="-1" mat-cell>
            <a
              class="m-r-10 cursor-pointer"
              *appHasPermission="['WareHouse.Delete', 'AllPermissions']"
              (click)="deleteStore(element.warehouseId)"
              ><i-tabler class="icon-16" name="trash"></i-tabler
            ></a>
            <a
              class="m-r-10 cursor-pointer"
              *appHasPermission="['WareHouse.Update', 'AllPermissions']"
              [routerLink]="['edit', element.warehouseId]">
              <i-tabler class="icon-16" name="edit"></i-tabler>
            </a>
            <a
              class="m-r-10 cursor-pointer"
              *appHasPermission="['WareHouse.View', 'AllPermissions']"
              [routerLink]="['view', element.warehouseId]">
              <i-tabler class="icon-16" name="eye"></i-tabler>
            </a>
          </td>
        </ng-container>

        <tr class="mat-row" *matNoDataRow>
          <td class="text-center" [attr.colspan]="displayedColumns.length">
            {{ 'common.noDataFound' | translate }}
          </td>
        </tr>
        <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
        <tr
          *matRowDef="let row; columns: displayedColumns"
          (click)="selection.toggle(row)"
          mat-row></tr>
      </table>
    </div>
  </mat-card-content>
</mat-card>

<!------------------------------------------- mat table content --------------------------------------->
