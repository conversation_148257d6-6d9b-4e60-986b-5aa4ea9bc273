import { animate, state, style, transition, trigger } from '@angular/animations';
import { Directionality } from '@angular/cdk/bidi';
import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import {
  AbstractControl,
  FormGroup,
  UntypedFormArray,
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatSelectChange } from '@angular/material/select';
import { MatTable } from '@angular/material/table';
import { ActivatedRoute } from '@angular/router';
import { CustomValidators } from 'ngx-custom-validators';
import { BehaviorSubject, forkJoin, of } from 'rxjs';
import { CostCentresApiService } from 'src/app/core/api/accounts/cost-centres-api.service';
import { ComponentCanDeactivate } from 'src/app/core/api/can-deactivate';
import { CommonService } from 'src/app/core/api/common.service';
import { StaticDataService } from 'src/app/core/api/static-data.service';
import { DistributorService } from 'src/app/core/api/trading/distributor.service';
import { SalesService } from 'src/app/core/api/trading/sales.service';
import { ZatcaService } from 'src/app/core/api/trading/zatca.service';
import { ActionType } from 'src/app/core/enums/actionType';
import { IActionEventType } from 'src/app/core/interfaces/actionEventType';
import { ICustomer, ICustomerView } from 'src/app/core/interfaces/customer';
import { IDistributor } from 'src/app/core/interfaces/distributor';
import { InvalidQuantity, error } from 'src/app/core/interfaces/error';
import { IPaymentViewData } from 'src/app/core/interfaces/payment';
import {
  IInvoice,
  ISaleDetails,
  ISalesItem,
  SalesIntegratedCreateResponse,
  SaletransactionTypes,
  ZatcaExceptionResponse,
} from 'src/app/core/interfaces/sales';
import { DistributorParams } from 'src/app/core/models/params/distributorParams';
import { SalesIntegeratedParams, SalesParams } from 'src/app/core/models/params/salesParams';
import { displayValue, miscOptions } from 'src/app/core/models/wrappers/displayValue';
import { convertDateForBE } from 'src/app/core/utils/date-utils';
import { CostCentre } from 'src/app/modules/featured/accounts/models/costCentre';
import { IInventory } from 'src/app/modules/featured/catalog/models/product';

import { ConfirmDialogComponent } from 'src/app/modules/shared/components/confirm-dialog/confirm-dialog.component';
import { DeleteConfirmationComponent } from 'src/app/modules/shared/components/delete-confirmation/delete-confirmation.component';
import { PaymentsPostSaleComponent } from 'src/app/modules/shared/components/payments-post-sale/payments-post-sale.component';
import {
  PrintDialogComponent,
  PrintDialogData,
} from 'src/app/modules/shared/components/print-dialog/print-dialog.component';
import { ProductSearchSelectionComponent } from 'src/app/modules/shared/components/product-search/product-search-selection/product-search-selection.component';
import { StandardPaymentsComponent } from 'src/app/modules/shared/components/standard-payments/standard-payments.component';
import { CustomerSelectionComponent } from '../../customers/customer-selection/customer-selection.component';
import { SalesCalculation } from '../sales-calculation';

@Component({
  selector: 'app-sale-form',
  templateUrl: './sale-form.component.html',
  styleUrls: ['./sale-form.component.scss'],
  animations: [
    trigger('detailExpand', [
      state('collapsed', style({ height: '0px', minHeight: '0' })),
      state('expanded', style({ height: '*' })),
      transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
    ]),
  ],
})
export class SaleFormComponent extends SalesCalculation implements OnInit, ComponentCanDeactivate {
  @ViewChild(MatTable) table: MatTable<AbstractControl[]>;
  @ViewChild('paymentForm') private paymentForm: StandardPaymentsComponent;
  @ViewChild('customerSelection') private customerSelection: CustomerSelectionComponent;
  @ViewChild('paymentpostsales') private paymentpostsales: PaymentsPostSaleComponent;
  @ViewChild('productSearch') private productSearch: ProductSearchSelectionComponent;

  dataSource = new BehaviorSubject<AbstractControl[]>([]);
  displayedColumns: string[] = [
    'itemCode',
    'itemName',
    'unitName',
    'priceType',
    'quantity',
    'price',
    'discount',
    'vatAmount',
    'subtotal',
    'notes',
    'action',
  ];
  expandedFieldNames: Array<displayValue | miscOptions>;
  columnsToDisplayWithExpand = [...this.displayedColumns, 'expand'];

  priceTypes: displayValue[];
  distributorAccounts: IDistributor[] = [];
  costCentreAccounts: CostCentre[] = [];
  currentSalesData: IInvoice;
  saleDetails: ISaleDetails;
  products: IInventory[];
  salesId: string;
  customerViewData: ICustomerView = <ICustomerView>{};
  paymentViewData: IPaymentViewData = <IPaymentViewData>{};

  itemRows: UntypedFormArray = this.formBuilder.array([]);
  salesForm: UntypedFormGroup = this.formBuilder.group({
    issueDate: new UntypedFormControl(new Date(), Validators.required),
    orderNumber: new UntypedFormControl(null),
    orderDate: new UntypedFormControl(new Date(), Validators.required),
    costCentreId: new UntypedFormControl(null),
    distributorAccountId: new UntypedFormControl(null),
    invoiceDiscount: new UntypedFormControl(0),
    notes: new UntypedFormControl(null),
    items: this.itemRows,
  });
  loading = true;
  mode: ActionType;
  expandedRowIndex: number | null = null;
  formTitle: string;
  disableButton = false;

  constructor(
    private formBuilder: UntypedFormBuilder,
    private dialog: MatDialog,
    private costCentresApiService: CostCentresApiService,
    private distributorService: DistributorService,
    private salesService: SalesService,
    private route: ActivatedRoute,
    private staticDataService: StaticDataService,
    private commonService: CommonService,
    private zatcaService: ZatcaService,
    private direction: Directionality,
    private changeDetectorRef: ChangeDetectorRef
  ) {
    super();
  }

  get canSelectProduct() {
    return !(this.mode === ActionType.view || this.mode === ActionType.fullCreditNote);
  }

  get IsFullCreditMode(): boolean {
    return this.mode === ActionType.fullCreditNote;
  }

  get IsViewMode(): boolean {
    return this.mode === ActionType.view;
  }

  get canDeleteProduct() {
    return this.canSelectProduct;
  }

  get itemsArray(): UntypedFormArray {
    return this.salesForm.get('items') as UntypedFormArray;
  }

  ngOnInit(): void {
    this.loading = true;
    this.mode = this.route.snapshot.data['mode'];
    this.formTitle = this.route.snapshot.data['title'];
    this.route.params.subscribe(params => {
      this.salesId = params['id'];
      this.getAllDropDownData();
    });
    this.setPageDisabled();
  }

  getForm(): FormGroup {
    return this.salesForm;
  }

  setPageDisabled(): void {
    if (this.mode === ActionType.view || this.mode === ActionType.fullCreditNote) {
      this.salesForm.disable();
    }
  }

  patchData(data: ISaleDetails): void {
    const viewData: ICustomerView = {
      isViewMode: this.IsViewMode || this.IsFullCreditMode,
      customer: data.customer,
      existingClient: data.existingClient,
    };
    this.customerViewData = viewData;
    this.paymentViewData = { ...data.payments, isViewMode: this.IsViewMode };
    this.saleDetails = data;
    if (data?.items && data.items.length) {
      data.items.forEach(element => {
        this.addSaleDetailForView(element);
      });
    }
    this.salesForm.patchValue({
      issueDate: data.issueDate,
      orderNumber: data.orderNumber,
      orderDate: data.orderDate,
      costCentreId: data.costCentreId,
      distributorAccountId: data.distributorAccountId,
      invoiceDiscount: data.invoiceDiscount,
      notes: data.notes,
    });
    this.setPageDisabled();
  }

  getAllDropDownData(): void {
    const salesParams = new SalesParams();
    salesParams.transactionType = SaletransactionTypes.sales;
    const salesById = this.salesId
      ? this.salesService.getSalesById(this.salesId, salesParams)
      : of(null);
    const distributorAccounts = this.distributorService.getAllDistributors(new DistributorParams());
    const costCentreAccounts = this.costCentresApiService.getAllCostCentres();
    forkJoin([distributorAccounts, costCentreAccounts, salesById]).subscribe(results => {
      this.distributorAccounts = results[0]?.distributors;
      this.costCentreAccounts = results[1];
      if (results[2]) {
        this.currentSalesData = results[2];
        this.patchData(results[2]);
      }
      this.loading = false;
    });
    this.expandedFieldNames = this.staticDataService.getSalesExpandedColumns;
    this.priceTypes = this.staticDataService.getSalesPriceTypes;
  }

  addSaleDetailForView(product: ISalesItem): void {
    const saleDetail: ISalesItem = {
      returnableQty: product.returnableQty,
      transactionItemId: product.transactionItemId,
      itemId: product.itemId,
      itemCode: product.itemCode,
      quantity: product.quantity,
      itemUnitId: product.itemUnitId,
      price: product.price,
      subtotal: product.subtotal,
      vat: product.product.vat,
      vatAmount: product.vatAmount,
      product: product.product,
      discount: product.discount,
      isGeneralDscntMethod: product.product.isGeneralDscntMethod,
      priceType: product.priceType,
      warehouseName: product.warehouseName,
      warehouseId: product.warehouseId,
      itemName: product.itemName,
      unitName: product.unitName,
      subTotalVat: product.subTotalVat,
    };
    this.onAddNewItem(saleDetail);
    this.dataSource.next(this.itemRows.controls);
    this.formItems(this.itemRows);
    this.updateAllRelevantCounts();
  }

  addSaleDetail(product: IInventory) {
    if (!this.checkProductExist(product)) {
      this.addNewSaleDetail(product);
    } else {
      this.updateExistingSaleDetail(product);
    }
    this.updateDataSourceAndForm();
    const field = document.querySelectorAll('.next')[0] as HTMLInputElement;
    setTimeout(() => field.focus());
  }

  private addNewSaleDetail(product: IInventory) {
    const saleDetail = this.createSaleDetailFromProduct(product);
    this.onAddNewItem(saleDetail);
  }

  private updateExistingSaleDetail(product: IInventory) {
    const index = this.findProductIndex(product);
    const data = this.getSpecificFormArray(index);
    this.updateQuantityAndRelatedFields(data, data.get('quantity').value + 1);
  }

  private createSaleDetailFromProduct(product: IInventory): ISalesItem {
    return {
      itemId: product.itemId,
      itemCode: product.itemCode,
      quantity: 1,
      itemUnitId: product.itemUnitId,
      price: product.retailPrice,
      subtotal: this.countSubTotal(
        1,
        product.retailPrice,
        product.discount,
        product.isGeneralDscntMethod,
        this.subTotalVat(
          product.retailPrice,
          product.vat,
          product.discount,
          product.isGeneralDscntMethod
        )
      ),
      vat: product.vat,
      vatAmount: this.vatAmount(
        product.retailPrice,
        product.discount,
        product.isGeneralDscntMethod,
        product.vat
      ),
      product: product,
      discount: product.discount,
      isGeneralDscntMethod: product.isGeneralDscntMethod,
      priceType: 0,
      warehouseName: product.warehouseName,
      warehouseId: product.warehouseId,
      itemName: product.itemName,
      unitName: product.unitName,
      subTotalVat: this.subTotalVat(
        product.retailPrice,
        product.vat,
        product.discount,
        product.isGeneralDscntMethod
      ),
    };
  }

  private findProductIndex(product: IInventory): number {
    return this.itemsArray.controls.findIndex(
      p =>
        p.value.itemCode === product.itemCode &&
        p.value.warehouseId === product.warehouseId &&
        p.value.unitName === product.unitName
    );
  }

  private updateQuantityAndRelatedFields(formGroup: AbstractControl, newQuantity: number) {
    const price = formGroup.get('price').value;
    const discount = formGroup.get('discount').value;
    const isGeneralDscntMethod = formGroup.get('isGeneralDscntMethod').value;
    const vat = formGroup.get('vat').value;

    formGroup.patchValue({
      quantity: newQuantity,
      subtotal: this.countSubTotal(
        newQuantity,
        price,
        discount,
        isGeneralDscntMethod,
        this.subTotalVat(price, vat, discount, isGeneralDscntMethod)
      ),
      subTotalVat: this.subTotalVat(price, vat, discount, isGeneralDscntMethod) * newQuantity,
      vatAmount: this.vatAmount(price, discount, isGeneralDscntMethod, vat) * newQuantity,
    });
  }

  private updateDataSourceAndForm() {
    this.dataSource.next(this.itemRows.controls);
    this.formItems(this.itemRows);
    this.updateAllRelevantCounts();
  }

  removeSaleDetail(data: IInventory) {
    const index = this.findProductIndex(data);
    this.itemRows.removeAt(index);
    this.updateDataSourceAndForm();
    this.salesForm.markAsDirty();
  }

  checkProductExist(products: IInventory) {
    return this.itemsArray.controls.find(
      product =>
        product.value.itemCode === products.itemCode &&
        product.value.warehouseId === products.warehouseId &&
        product.value.unitName === products.unitName
    );
  }

  getControlsIndexFromArray(controlErrors: InvalidQuantity) {
    return this.itemsArray.controls.findIndex(
      control =>
        control.get('itemCode').value === controlErrors.itemCode &&
        control.get('unitName').value === controlErrors.unitName &&
        control.get('warehouseId').value === controlErrors.warehouseId
    );
  }

  onSelection(selectedProduct: IInventory) {
    this.addSaleDetail(selectedProduct);
  }

  onPriceTypeChange(event: MatSelectChange, index: number) {
    const data = this.getSpecificFormArray(index);
    const priceType = this.priceTypes.find(data => data.value === event.value);
    const productData = data.get('product').value;
    this.updatePriceAndRelatedFields(data, productData[priceType.display], event.value);
  }

  private updatePriceAndRelatedFields(
    formGroup: AbstractControl,
    newPrice: number,
    newPriceType: number
  ) {
    const quantity = formGroup.get('quantity').value;
    const discount = formGroup.get('discount').value;
    const isGeneralDscntMethod = formGroup.get('isGeneralDscntMethod').value;
    const vat = formGroup.get('vat').value;

    formGroup.patchValue({
      price: newPrice,
      priceType: newPriceType,
      subtotal: this.countSubTotal(
        quantity,
        newPrice,
        discount,
        isGeneralDscntMethod,
        this.subTotalVat(newPrice, vat, discount, isGeneralDscntMethod)
      ),
      subTotalVat: this.subTotalVat(newPrice, vat, discount, isGeneralDscntMethod) * quantity,
      vatAmount: this.vatAmount(newPrice, discount, isGeneralDscntMethod, vat) * quantity,
    });
    this.updateDataSourceAndForm();
  }

  onQuantityChange(event, index: number) {
    const data = this.getSpecificFormArray(index);
    this.updateQuantityAndRelatedFields(data, event.srcElement.value || 0);
    this.updateDataSourceAndForm();
  }

  onPriceChange(event, index: number) {
    const data = this.getSpecificFormArray(index);
    this.updatePriceAndRelatedFields(
      data,
      event.srcElement.value || 0,
      data.get('priceType').value
    );
  }

  onDiscountChange(event, index: number) {
    const data = this.getSpecificFormArray(index);
    this.updateDiscountAndRelatedFields(data, event.srcElement.value || 0);
    this.updateDataSourceAndForm();
  }

  private updateDiscountAndRelatedFields(formGroup: AbstractControl, newDiscount: number) {
    const quantity = formGroup.get('quantity').value;
    const price = formGroup.get('price').value;
    const isGeneralDscntMethod = formGroup.get('isGeneralDscntMethod').value;
    const vat = formGroup.get('vat').value;

    formGroup.patchValue({
      discount: newDiscount,
      subtotal: this.countSubTotal(
        quantity,
        price,
        newDiscount,
        isGeneralDscntMethod,
        this.subTotalVat(price, vat, newDiscount, isGeneralDscntMethod)
      ),
      subTotalVat: this.subTotalVat(price, vat, newDiscount, isGeneralDscntMethod) * quantity,
      vatAmount: this.vatAmount(price, newDiscount, isGeneralDscntMethod, vat) * quantity,
    });
  }

  getSpecificFormArray(index: number): AbstractControl {
    return (<UntypedFormArray>this.salesForm.get('items')).at(index);
  }

  openDeleteConfirmationDialog(product: IInventory) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '600px';
    dialogConfig.panelClass = 'green_theme';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(DeleteConfirmationComponent, dialogConfig);
    dialogRef.componentInstance.accepted.subscribe(() => {
      this.removeSaleDetail(product);
    });
  }

  jumpToNext(event: Event, index: number) {
    event.preventDefault();
    const nextField = document.querySelectorAll('.next');
    nextField.forEach((element, idx) => {
      if (element === event.target) {
        const nextfield = nextField[idx + 1] as HTMLInputElement;
        nextfield?.focus();
      }
    });
  }

  onAddNewItem(product: ISalesItem) {
    if (this.itemsArray && this.itemsArray.length > 0) {
      this.itemsArray.insert(0, this.addnewFormGroup(product));
    } else {
      this.itemsArray.push(this.addnewFormGroup(product));
    }
  }

  addnewFormGroup(saleData: ISalesItem): UntypedFormGroup {
    const row = this.formBuilder.group({
      itemId: saleData.itemId,
      itemCode: saleData.itemCode,
      quantity: [
        saleData.quantity,
        Validators.compose([
          Validators.required,
          CustomValidators.lte(saleData.product?.totalQuantityPerUnit),
          CustomValidators.gt(0),
        ]),
      ],
      itemUnitId: saleData.itemUnitId,
      price: saleData.price,
      subtotal: [saleData.subtotal, Validators.compose([CustomValidators.gt(0)])],
      vat: saleData.vat,
      product: saleData.product,
      discount: [saleData.discount, this.discountValueValid],
      isGeneralDscntMethod: saleData.isGeneralDscntMethod,
      priceType: saleData.priceType,
      warehouseName: saleData.warehouseName,
      itemName: saleData.itemName,
      unitName: saleData.unitName,
      notes: saleData.notes,
      subTotalVat: saleData.subTotalVat,
      warehouseId: saleData.warehouseId,
      vatAmount: [saleData.vatAmount, Validators.compose([CustomValidators.gte(0)])],
      transactionItemId: saleData.transactionItemId,
      returnableQty: saleData.returnableQty,
    });
    this.salesForm.markAsDirty();
    return row;
  }

  submitSales(event: IActionEventType): void {
    event.event.preventDefault();
    if (event.actionType === ActionType.create) {
      this.salesForm.markAllAsTouched();
      this.processSalesCreation();
    }
    if (event.actionType === ActionType.fullCreditNote) {
      this.processSalesFullCreditNotes();
    }
  }

  processSalesCreation(zatcaAccept = false): void {
    if (this.isFormValid()) {
      const data = this.prepareSalesData();
      const salesParams = new SalesIntegeratedParams();
      salesParams.areWarningsAccepted = zatcaAccept;

      this.salesService.createSalesIntegrated(data, salesParams).subscribe(
        (result: SalesIntegratedCreateResponse) => this.handleSalesResponse(result, 'sales'),
        error => this.handleSalesError(error, 'sales')
      );
    } else {
      this.handleInvalidForm();
    }
  }

  private isFormValid(): boolean {
    return (
      this.salesForm.valid &&
      this.paymentForm.paymentFormIsAllValid() &&
      this.customerSelection.customerSelectionFormIsAllValid() &&
      this.itemsArray.length > 0
    );
  }

  private prepareSalesData(): ISaleDetails {
    return {
      ...this.salesForm.value,
      ...this.customerSelection.customerSelectionForm.value,
      payments: this.paymentForm.getPaymentsFormValue(),
      orderDate: convertDateForBE(this.salesForm.get('orderDate')?.value),
      issueDate: convertDateForBE(this.salesForm.get('issueDate')?.value),
      transactionType: SaletransactionTypes.sales,
    };
  }

  private handleSalesResponse(result: SalesIntegratedCreateResponse, type: string) {
    if (result?.zatcaExceptionResponse) {
      this.openZatcaStatusModal(result.zatcaExceptionResponse, type);
    } else {
      this.commonService.playSuccessSound();
      this.showPrintDialog(result);
    }
  }

  private handleSalesError(error: any, type: string) {
    this.loading = false;
    if (error.status === 422) {
      this.setFormErrors(error);
    }
    this.zatcaErrorResponse(error, type);
  }

  private handleInvalidForm() {
    this.loading = false;
    this.commonService.playErrorSound();
  }

  salesResponse(result: SalesIntegratedCreateResponse, type: string) {
    if (result?.zatcaExceptionResponse) {
      this.openZatcaStatusModal(result.zatcaExceptionResponse, type);
    } else {
      this.commonService.playSuccessSound();
      this.showPrintDialog(result);
    }
  }

  zatcaErrorResponse(error, type: string) {
    if (error?.details?.zatcaResponse) {
      this.openZatcaStatusModal(error.details.zatcaResponse, type);
    }
  }

  openZatcaStatusModal(zatcaResponse: ZatcaExceptionResponse, processFor: string): void {
    const dialog = this.zatcaService.openZatcaStatusModal(zatcaResponse, this.direction.value);
    dialog.componentInstance.accepted.subscribe(() => {
      dialog.close();
      this.salesForm.markAllAsTouched();
      if (processFor === 'sales') {
        this.processSalesCreation(true);
      }
      if (processFor === 'credit') {
        this.creditProcess(true);
      }
    });
  }

  resetPageForNewCreation(): void {
    this.resetSalesForm();
    this.customerSelection?.resetForm();
    this.paymentForm?.resetForm();
    this.resetCounts();
    this.salesForm?.get('issueDate').setValue(new Date());
    this.salesForm?.get('orderDate').setValue(new Date());
    this.changeDetectorRef.detectChanges();
  }

  resetSalesForm(): void {
    this.salesForm.reset();
    this.itemRows.clear();
    this.dataSource = new BehaviorSubject<AbstractControl[]>([]);
    this.salesForm.markAsUntouched();
  }

  processSalesFullCreditNotes(): void {
    if (this.paymentpostsales.isValid()) {
      this.onPostClick();
    } else {
      this.commonService.playErrorSound();
    }
  }

  onPostClick() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.panelClass = ['green_theme'];
    dialogConfig.minWidth = '600px';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;
    const dialogRef = this.dialog.open(ConfirmDialogComponent, dialogConfig);
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.creditProcess();
      }
    });
  }

  creditProcess(zatcaAccept = false): void {
    const data: ISaleDetails = {
      ...this.currentSalesData,
      payments: this.paymentpostsales.getNoteConfig().notesPaymentDetails,
      referenceDocumentId: this.currentSalesData.id,
      issueDate: this.paymentpostsales.getNoteConfig().notesCommentsDate.creditNoteDate,
      referenceDocumentDate: this.currentSalesData.issueDate,
      transactionType: SaletransactionTypes.fullCreditNote,
      notes: this.paymentpostsales.getNoteConfig().notesCommentsDate.creditNoteText,
      ...this.customerSelection.customerSelectionForm.value,
      items: this.salesForm.get('items').value,
    };
    const salesParams = new SalesIntegeratedParams();
    salesParams.areWarningsAccepted = zatcaAccept;
    this.salesService.createSalesIntegrated(data, salesParams).subscribe(
      (result: SalesIntegratedCreateResponse) => this.salesResponse(result, 'credit'),
      error => this.handleSalesError(error, 'credit')
    );
  }

  setFormErrors(validationErrors: error): void {
    const serverErrors = validationErrors.details.invalidQuantity;
    serverErrors.forEach((element: InvalidQuantity) => {
      const controlIndex = this.getControlsIndexFromArray(element);
      const formArray = this.getSpecificFormArray(controlIndex);
      formArray.get('quantity').setErrors({ serverSideError: element.error });
      formArray.updateValueAndValidity();
    });
  }

  customerTypeSelection(isExistingCustomer: boolean): void {
    if (!isExistingCustomer) {
      this.salesForm.get('distributorAccountId').setValue(null);
    }
  }

  discountValueValid(c: AbstractControl): ValidationErrors | null {
    if (c && c.parent) {
      if (c.parent.get('isGeneralDscntMethod').value) {
        if (c.value > 100) {
          return { percentageError: true };
        }
      } else {
        if (c.value > c.parent.get('price').value) {
          return { priceError: true };
        }
      }
    }
    return null;
  }

  customerProfileSelection(customer: ICustomer): void {
    if (customer) {
      this.patchCustomerProfileData(customer);
    }
  }

  patchCustomerProfileData(customer: ICustomer): void {
    this.salesForm.patchValue({
      costCentreId: customer.costCentreId,
      distributorAccountId: customer.distributorAccountId,
    });
    this.salesForm.markAsDirty();
  }

  focusOnSearch(): void {
    this.productSearch.setFocus();
  }

  toggleExpansion(rowIndex: number): void {
    this.expandedRowIndex = this.expandedRowIndex === rowIndex ? null : rowIndex;
  }

  isRowExpanded(index: number): boolean {
    return this.expandedRowIndex === index;
  }

  canDeactivate(): boolean {
    return !this.salesForm.dirty;
  }

  /**
   * Shows the print dialog after successful sales creation
   */
  private showPrintDialog(result: SalesIntegratedCreateResponse): void {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = '400px';
    dialogConfig.maxWidth = '500px';
    dialogConfig.panelClass = 'green_theme';
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = false;
    dialogConfig.autoFocus = false;
    dialogConfig.direction = this.direction.value;

    const dialogData: PrintDialogData = {
      documentResponse: result,
      title: 'Sales Document Created Successfully',
      message: `Your sales document has been created successfully!`,
    };

    dialogConfig.data = dialogData;

    const dialogRef = this.dialog.open(PrintDialogComponent, dialogConfig);

    dialogRef.componentInstance.printRequested.subscribe((shouldPrint: boolean) => {
      if (shouldPrint) {
        this.handlePrintRequest(result);
      } else {
        this.resetPageForNewCreation();
      }
    });

    dialogRef.afterClosed().subscribe((shouldPrint: boolean | null) => {
      if (shouldPrint === null) {
        // User clicked cancel, just reset the page
        this.resetPageForNewCreation();
      }
    });
  }

  /**
   * Handles the print request - opens Invoice Demo component in new window using common service
   */
  private handlePrintRequest(result: SalesIntegratedCreateResponse): void {
    console.log('Print requested for document:', result);
    console.log('Document ID:', result.documentId);
    console.log('Document Number:', result.documentNumber);

    if (result.documentId) {
      console.log('Opening Invoice Demo in new window with params:', {
        documentId: result.documentId,
        transactionType: result.documentType || 'SALES',
      });

      // Use the common service to open invoice in new window
      this.commonService.openInvoiceInNewWindow(result.documentId, result.documentType || 'SALES');
    } else {
      console.warn('No document ID available for printing');
    }

    // Reset the page after handling print request
    this.resetPageForNewCreation();
  }
}
