<!-- Filter Place Holders -->
<form [formGroup]="filterForm" autocomplete="off">
  <mat-card appearance="outlined">
    <div class="row">
      <div class="col-12">
        <div class="row no-gutters">
          <div class="p-2 col-md-5">
            <app-searchbox #searchBoxForm formControlName="searchBoxForm"></app-searchbox>
          </div>
          <div class="p-2 col-lg-3 col-md-3 col-sm-6 d-flex align-items-end">
            <div class="form-group">
              <mat-label>{{ 'openqty.category' | translate }}</mat-label>
              <mat-form-field class="w-100">
                <mat-select #select multiple formControlName="categoryId">
                  <app-select-check-all [model]="filterForm.get('categoryId')" [values]="select">
                  </app-select-check-all>
                  <mat-option
                    *ngFor="let parentCategory of categoryList"
                    [value]="parentCategory.categoryId">
                    {{
                      parentCategory.parentCategoryId
                        ? getParentName(parentCategory.parentCategoryId) +
                          '/' +
                          (parentCategory | localized)
                        : (parentCategory | localized)
                    }}</mat-option
                  >
                </mat-select>
              </mat-form-field>
            </div>
          </div>
          <div class="p-2 col-lg-3 col-md-3 col-sm-6 d-flex align-items-end">
            <div class="form-group">
              <mat-label>{{ 'openqty.warehouse' | translate }}</mat-label>
              <mat-form-field class="w-100">
                <mat-select #wareHouse multiple formControlName="warehouseIds">
                  <app-select-check-all
                    [model]="filterForm.get('warehouseIds')"
                    [values]="wareHouse">
                  </app-select-check-all>
                  <mat-option
                    *ngFor="let wareHouse of warehouseList"
                    [value]="wareHouse.warehouseId">
                    {{ wareHouse | localized }}
                  </mat-option>
                </mat-select>
                <mat-error
                  *ngIf="
                    filterForm?.controls['warehouseIds']?.hasError('required') &&
                    filterForm?.controls['warehouseIds']?.touched
                  "
                  >{{ 'common.required' | translate }}</mat-error
                >
              </mat-form-field>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div>
      <button
        (click)="getFilterData($event); searchBoxForm.markAllAsTouched(); (false)"
        mat-raised-button
        color="primary">
        {{ 'searchPanel.searchString' | translate }}
      </button>
      <button class="m-l-10" (click)="clearFilters($event); (false)" mat-raised-button>
        {{ 'searchPanel.clear' | translate }}
      </button>
    </div>
  </mat-card>
</form>

<!-- Filter Place Holders -->

<!-- Main Table Contents -->
<form [formGroup]="adjustmentForm" autocomplete="off">
  <div class="table-responsive">
    <table
      class="w-100"
      [dataSource]="tableData"
      mat-table
      matSort
      formArrayName="InventoryOpeningRequest">
      <ng-container matColumnDef="action">
        <th *matHeaderCellDef mat-header-cell>{{ 'openqty.action' | translate }}</th>
        <td class="f-s-14" *matCellDef="let element; let i = index; let last = last" mat-cell>
          <a class="cursor-pointer" (click)="deleteEntry(getActualIndex(i)); (false)"
            ><i-tabler class="icon-16" name="trash"></i-tabler>
          </a>
        </td>
      </ng-container>

      <ng-container matColumnDef="warehouseName">
        <th *matHeaderCellDef mat-header-cell mat-sort-header>
          {{ 'openqty.warehouse' | translate }}
        </th>
        <td class="f-s-14" *matCellDef="let element; let i = index" [formGroupName]="i" mat-cell>
          {{ element.warehouseName }}
        </td>
      </ng-container>

      <ng-container matColumnDef="itemCode">
        <th *matHeaderCellDef mat-header-cell mat-sort-header>
          {{ 'openqty.itemCode' | translate }}
        </th>
        <td class="f-s-14" *matCellDef="let element; let i = index" [formGroupName]="i" mat-cell>
          {{ element.itemCode }}
        </td>
      </ng-container>

      <!-- Cost Price -->
      <ng-container matColumnDef="nameArabic">
        <th *matHeaderCellDef mat-header-cell mat-sort-header>
          {{ 'openqty.nameArabic' | translate }}
        </th>
        <td class="f-s-14" *matCellDef="let element; let i = index" [formGroupName]="i" mat-cell>
          {{ element.nameArabic }}
        </td>
      </ng-container>

      <ng-container matColumnDef="nameEnglish">
        <th *matHeaderCellDef mat-header-cell mat-sort-header>
          {{ 'openqty.nameEnglish' | translate }}
        </th>
        <td class="f-s-14" *matCellDef="let element; let i = index" [formGroupName]="i" mat-cell>
          {{ element.nameEnglish }}
        </td>
      </ng-container>
      <!-- Purchase Price  -->
      <ng-container matColumnDef="unitName">
        <th *matHeaderCellDef mat-header-cell mat-sort-header>
          {{ 'openqty.unit' | translate }}
        </th>
        <td class="f-s-14" *matCellDef="let element; let i = index" [formGroupName]="i" mat-cell>
          {{ element.unitName }}
        </td>
      </ng-container>
      <!-- open Purchase Price -->
      <ng-container matColumnDef="openQty">
        <th *matHeaderCellDef mat-header-cell>{{ 'openqty.openQty' | translate }}</th>
        <td
          class="f-s-14"
          *matCellDef="let element; let i = index"
          [formGroupName]="getActualIndex(i)"
          mat-cell>
          <mat-form-field class="w-100">
            <input matInput type="number" formControlName="openQty" />
            <mat-error
              *ngIf="
                adjustmentForm
                  .get('InventoryOpeningRequest')
                  ?.controls[i]['controls']['openQty']?.hasError('required')
              ">
              {{ 'common.required' | translate }}
            </mat-error>
          </mat-form-field>
        </td>
      </ng-container>
      <tr class="mat-row" *matNoDataRow>
        <td
          class="f-s-14"
          class="f-s-14"
          class="text-center"
          [attr.colspan]="displayedColumns.length">
          {{ 'common.noDataFound' | translate }}
        </td>
      </tr>
      <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
      <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
    </table>
  </div>
  <mat-paginator [pageSizeOptions]="[100, 125]"> </mat-paginator>
</form>

<!------------------------------------ Action Buttons ------------------------------------------------->
<div class="text-center">
  <button class="m-l-10" (click)="onSubmit($event)" mat-stroked-button color="primary">
    {{ 'openqty.updateQty' | translate }}
  </button>
  <button class="m-l-10" [routerLink]="['../']" mat-stroked-button color="warn">
    {{ 'common.cancel' | translate }}
  </button>
</div>
<!-- Main Table Contents -->
