import { Injectable } from '@angular/core';
import { JwtHelperService } from '@auth0/angular-jwt';
import { BehaviorSubject } from 'rxjs';
import { Token } from 'src/app/core/models/identity/token';
import { LocalStorageService } from './local-storage.service';

@Injectable({
  providedIn: 'root',
})
export class TokenService {
  private currentUserTokenSource = new BehaviorSubject<string>(this.getToken() || null);
  public currentUserToken$ = this.currentUserTokenSource.asObservable();

  constructor(private localStorage: LocalStorageService) {}

  getToken(): string | null {
    return this.localStorage.getItem('token') ?? null;
  }

  getRefreshToken(): string | null {
    return this.localStorage.getItem('refreshToken') ?? null;
  }

  setToken(token: string | null): void {
    if (token) {
      this.localStorage.setItem('token', token);
    } else {
      this.localStorage.removeItem('token');
    }
    this.currentUserTokenSource.next(token);
  }

  setRefreshToken(refreshToken: string | null): void {
    if (refreshToken) {
      this.localStorage.setItem('refreshToken', refreshToken);
    } else {
      this.localStorage.removeItem('refreshToken');
    }
  }

  setStorageToken(data: Token | null): void {
    if (data && data.accessToken) {
      this.setToken(data.accessToken);
      this.setRefreshToken(data.refreshToken);
    } else {
      this.setToken(null);
      this.setRefreshToken(null);
    }
  }

  getDecodedToken(): any {
    const token = this.getToken();
    if (!token) {
      return null;
    }
    const jwtService = new JwtHelperService();
    return jwtService.decodeToken(token);
  }

  isTokenExpired(): boolean {
    const token = this.getToken();
    if (!token) return true;
    const jwtService = new JwtHelperService();
    return jwtService.isTokenExpired(token);
  }

  clearTokens(): void {
    this.setToken(null);
    this.setRefreshToken(null);
  }

  getFullName(): string {
    const decodedToken = this.getDecodedToken();
    return decodedToken?.firstName ?? '';
  }

  getCompanyID(): string {
    const decodedToken = this.getDecodedToken();
    return decodedToken?.companyId ?? '';
  }

  getUserId(): number {
    const decodedToken = this.getDecodedToken();
    return decodedToken?.userId ?? '';
  }

  getBranchId(): string {
    const decodedToken = this.getDecodedToken();
    return decodedToken?.branchId ?? '';
  }

  getUserName(): string {
    const decodedToken = this.getDecodedToken();
    return decodedToken?.userName ?? '';
  }

  getJwtPermissions(): string[] {
    const decodedToken = this.getDecodedToken();
    return this.sortPermissions(decodedToken?.permissions || []);
  }

  sortPermissions(permissions: string[]): string[] {
    return permissions.sort((a, b) => {
      const aParts = a.split('.');
      const bParts = b.split('.');

      // Compare the first qualifier
      const firstQualifierComparison = aParts[0].localeCompare(bParts[0]);

      if (firstQualifierComparison !== 0) {
        return firstQualifierComparison;
      }

      // Compare the second qualifier
      const secondQualifierComparison = aParts[1].localeCompare(bParts[1]);

      if (secondQualifierComparison !== 0) {
        return secondQualifierComparison;
      }

      // If both are the same up to this point, compare the full permission
      return a.localeCompare(b);
    });
  }

  isAuthorized(authorizationType: string, allowedData: string[]): boolean {
    if (allowedData == null || allowedData.length === 0) {
      return true;
    }
    const decodeToken = this.getDecodedToken();
    if (!decodeToken) {
      return false;
    }

    if (authorizationType === 'Role') {
      return decodeToken.roles.some(tokenRoles => {
        return allowedData.includes(tokenRoles);
      });
    } else if (authorizationType === 'Permission') {
      const permissions = decodeToken['permissions'];
      if (permissions === undefined || permissions.length === 0) return false;
      return permissions.some(permission => {
        return allowedData.some(item => permission.includes(item));
      });
    }
    return false;
  }

  hasRoleQualifier(allowedData: string[]): boolean {
    const decodeToken = this.getDecodedToken();
    if (!decodeToken) {
      return false;
    }
    const permissions = decodeToken['permissions'];
    if (permissions === undefined || permissions.length === 0) return false;
    return !!permissions.find(data => allowedData.includes(data.split('.')[0]));
  }
}
