import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { ChartOfAccountsApiService } from 'src/app/core/api/accounts/chart-of-accounts-api.service';
import { ActionType } from 'src/app/core/enums/actionType';
import { IPaymentDetails } from 'src/app/core/interfaces/payment';
import {
  IPostSalesNotesEntry,
  IPostSalesNotesPayments,
  ISaleDetails,
} from 'src/app/core/interfaces/sales';
import { convertDateForBE } from 'src/app/core/utils/date-utils';
import { Account } from 'src/app/modules/featured/accounts/models/account';

@Component({
  selector: 'app-payments-post-sale',
  templateUrl: './payments-post-sale.component.html',
  styleUrls: ['./payments-post-sale.component.scss'],
})
export class PaymentsPostSaleComponent implements OnInit, OnChanges {
  @Input() saleDetails: ISaleDetails;
  @Input() grandTotal: number;
  @Input() mode: ActionType;
  isCashPaymentAllowed = false;
  isCreditPaymentAllowed = false;
  salesDate: string;
  paymentForm: UntypedFormGroup;
  cashAccounts: Account[] = [];

  constructor(
    private formBuilder: UntypedFormBuilder,
    private chartOfAccountsService: ChartOfAccountsApiService
  ) {
    //
  }

  ngOnInit(): void {
    this.setUpPaymentForm(this.saleDetails);
    this.getAccountDetails();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes && changes?.grandTotal) {
      const currentValue = changes?.grandTotal?.currentValue;
      const previousValue = changes?.grandTotal?.previousValue;

      // Check if the current value is different from the previous value
      if (currentValue !== previousValue) {
        if (this.saleDetails) {
          this.setPayments(this.saleDetails);
        }
      }
    }
  }

  setUpPaymentForm(data: ISaleDetails) {
    this.paymentForm = this.formBuilder.group({
      //cash
      cashType: new UntypedFormControl(data.payments.cashType ?? false),
      cashAccountId: new UntypedFormControl(null),
      cashAmount: new UntypedFormControl(null),
      //credit
      creditType: new UntypedFormControl(data.payments.creditType ?? false),
      creditAmount: new UntypedFormControl(null),
      // credit note date
      creditNoteDate: new UntypedFormControl(new Date()),
      // credit note text
      creditNoteText: new UntypedFormControl(null),
    });
    this.paymentForm.disable();
    this.setPayments(data);
    this.paymentForm.get('creditNoteText').enable();
  }

  setPayments(payments: ISaleDetails) {
    if (payments.payments.creditType) {
      // set as credit
      this.paymentForm?.get('creditType').setValue(true);
      this.paymentForm?.get('creditAmount').setValue(this.grandTotal);
    } else {
      // set as cash
      this.paymentForm?.get('cashType').setValue(true);
      this.paymentForm?.get('cashAmount').setValue(this.grandTotal);
    }
    this.salesDate = payments.issueDate;
  }

  getAccountDetails(): void {
    this.chartOfAccountsService
      .getChartOfAccountsForPayments('ASSETS', 'CASHIER', 'DETAILED')
      .subscribe(accounts => {
        this.cashAccounts = accounts.accounts;
        if (accounts?.accounts?.length) {
          if (this.paymentForm.get('cashType').getRawValue) {
            this.paymentForm?.get('cashAccountId').setValue(accounts?.accounts[0].accountId);
          }
        }
      });
  }

  getNoteConfig(): IPostSalesNotesPayments {
    const formData: IPostSalesNotesPayments = <IPostSalesNotesPayments>{};
    formData.notesCommentsDate = <IPostSalesNotesEntry>{};
    formData.notesPaymentDetails = <IPaymentDetails>{};
    // notes and date field
    formData.notesCommentsDate.creditNoteDate = convertDateForBE(
      this.paymentForm.get('creditNoteDate').value
    );
    formData.notesCommentsDate.creditNoteText = this.paymentForm.get('creditNoteText').value;
    // payment types
    formData.notesPaymentDetails.cashType = this.paymentForm.get('cashType').getRawValue();
    formData.notesPaymentDetails.cashAccountId = this.paymentForm
      .get('cashAccountId')
      .getRawValue();
    formData.notesPaymentDetails.cashAmount = this.grandTotal;
    console.log(this.grandTotal);
    console.log(formData.notesPaymentDetails.cashAmount);
    formData.notesPaymentDetails.creditType = this.paymentForm.get('creditType').getRawValue();
    formData.notesPaymentDetails.creditAmount = this.paymentForm.get('creditType').getRawValue()
      ? this.grandTotal
      : null;
    // sales details
    formData.notesPaymentDetails.balanceAmount = this.saleDetails.payments.balanceAmount;
    formData.notesPaymentDetails.changeAmount = this.saleDetails.payments.changeAmount;
    formData.notesPaymentDetails.grandTotal = this.saleDetails.payments.grandTotal;
    formData.notesPaymentDetails.totalDiscount = this.saleDetails.payments.totalDiscount;
    formData.notesPaymentDetails.totalVat = this.saleDetails.payments.totalVat;
    formData.notesPaymentDetails.totalExclVatDiscount =
      this.saleDetails.payments.totalExclVatDiscount;
    console.log('getNoteConfig ->', formData);
    return formData;
  }

  isValid(): boolean {
    this.paymentForm.markAllAsTouched();
    return this.paymentForm.valid;
  }
}
