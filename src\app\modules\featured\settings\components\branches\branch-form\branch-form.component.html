<form
  class="basic-form"
  [ngClass]="{ readOnly: isViewMode }"
  [formGroup]="branchForm"
  autocomplete="off">
  <mat-card appearance="outlined">
    <mat-card-title>{{ formTitle | translate }}</mat-card-title>
    <div class="row no-gutters">
      <div class="col-md-6 col-sm-6 p-2">
        <mat-label>{{ 'branch.nameArabic' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <input #arabic type="text" maxlength="40" matInput formControlName="nameArabic" />
          <mat-error
            *ngIf="
              branchForm.controls['nameEnglish'].hasError('required') &&
              branchForm.controls['nameEnglish'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>
      <div class="col-md-6 col-sm-6 p-2">
        <mat-label>{{ 'branch.nameEnglish' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <input #english type="text" maxlength="40" matInput formControlName="nameEnglish" />
          <mat-error
            *ngIf="
              branchForm.controls['nameEnglish'].hasError('required') &&
              branchForm.controls['nameEnglish'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>

      <div class="col-md-3 col-lg-3 col-sm-6 p-2">
        <mat-label>{{ 'branch.vatNo' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <input #vat type="text" maxlength="15" matInput formControlName="vatNumber" />
          <mat-error
            *ngIf="
              branchForm.controls['nameEnglish'].hasError('required') &&
              branchForm.controls['nameEnglish'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>

      <div class="col-md-3 col-lg-3 col-sm-6 p-2">
        <mat-label>{{ 'branch.phone' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <input #phone type="text" maxlength="15" matInput formControlName="phoneNumber" />
          <mat-error
            *ngIf="
              branchForm.controls['phoneNumber'].hasError('required') &&
              branchForm.controls['phoneNumber'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
          <mat-error
            *ngIf="
              branchForm.controls['phoneNumber'].hasError('minlength') &&
              branchForm.controls['phoneNumber'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
          <mat-error
            *ngIf="
              branchForm.controls['phoneNumber'].hasError('maxlength') &&
              branchForm.controls['phoneNumber'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>
      <div class="col-md-3 col-lg-3 col-sm-6 p-2">
        <mat-label>{{ 'branch.email' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <input #email type="text" maxlength="50" matInput formControlName="emailId" />
          <mat-error
            *ngIf="
              branchForm.controls['emailId'].hasError('required') &&
              branchForm.controls['emailId'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
          <mat-error
            *ngIf="
              branchForm.controls['emailId'].errors?.email && branchForm.controls['emailId'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>
    </div>
    <div class="row no-gutters">
      <div class="p-2 col-md-6 col-sm-6">
        <mat-label>{{ 'tenants.identification' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <input [appMaxlength]="30" matInput type="text" formControlName="identification" />
          <mat-error
            *ngIf="
              branchForm?.controls['identification'].hasError('required') &&
              branchForm?.controls['identification'].touched
            ">
            {{ 'common.required' | translate }}
          </mat-error>
        </mat-form-field>
      </div>
      <div class="p-2 col-md-6 col-sm-6">
        <mat-label>{{ 'tenants.identificationCode' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <mat-select formControlName="identificationCode">
            <mat-option
              *ngFor="let identificationCode of identificationCodes"
              [value]="identificationCode.value">
              {{ identificationCode.display | translate }}({{ identificationCode.value }})
            </mat-option>
          </mat-select>
          <mat-error
            *ngIf="
              branchForm?.controls['identificationCode'].hasError('required') &&
              branchForm?.controls['identificationCode'].touched
            ">
            {{ 'common.required' | translate }}
          </mat-error>
        </mat-form-field>
      </div>
    </div>
    <app-address #addressForm formControlName="address"></app-address>
    <div class="row no-gutters">
      <div class="col-md-6 col-sm-6 p-2">
        <mat-label>{{ 'branch.businessCategoryArabic' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <input
            #arabic
            type="text"
            maxlength="90"
            matInput
            formControlName="businessCategoryArabic" />
          <mat-error
            *ngIf="
              branchForm.controls['businessCategoryArabic'].hasError('required') &&
              branchForm.controls['businessCategoryArabic'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>
      <div class="col-md-6 col-sm-6 p-2">
        <mat-label>{{ 'branch.businessCategoryEnglish' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <input
            #english
            type="text"
            maxlength="90"
            matInput
            formControlName="businessCategoryEnglish" />
          <mat-error
            *ngIf="
              branchForm.controls['businessCategoryEnglish'].hasError('required') &&
              branchForm.controls['businessCategoryEnglish'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>
      <div class="col-md-6 col-sm-6 p-2">
        <mat-label>{{ 'branch.invoiceTrailing' | translate }}</mat-label>
        <mat-form-field class="w-100">
          <input #english type="text" maxlength="300" matInput formControlName="invoiceTrailing" />
          <mat-error
            *ngIf="
              branchForm.controls['invoiceTrailing'].hasError('required') &&
              branchForm.controls['invoiceTrailing'].touched
            "
            >{{ 'common.required' | translate }}</mat-error
          >
        </mat-form-field>
      </div>
    </div>
  </mat-card>
</form>
<div class="text-center">
  <ng-container *ngIf="isCreateMode">
    <button
      class="m-l-10"
      (click)="onSubmit($event); (false)"
      type="button"
      mat-stroked-button
      color="primary">
      {{ 'common.submit' | translate }}
    </button>
    <button class="m-l-10" [routerLink]="['../']" type="button" mat-stroked-button color="warn">
      {{ 'common.cancel' | translate }}
    </button>
  </ng-container>
  <ng-container *ngIf="isEditMode">
    <button
      class="m-l-10"
      (click)="onSubmit($event); (false)"
      type="button"
      mat-stroked-button
      color="primary">
      {{ 'common.submit' | translate }}
    </button>
    <button class="m-l-10" [routerLink]="['../../']" type="button" mat-stroked-button color="warn">
      {{ 'common.cancel' | translate }}
    </button></ng-container
  >
  <button
    class="m-l-10"
    *ngIf="isViewMode"
    [routerLink]="['../../']"
    type="button"
    mat-stroked-button
    color="primary">
    {{ 'common.buttons.back' | translate }}
  </button>
</div>
