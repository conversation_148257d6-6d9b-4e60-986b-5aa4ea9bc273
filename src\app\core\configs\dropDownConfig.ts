import { Provision } from '../interfaces/sales';
import { displayValue, miscOptions } from '../models/wrappers/displayValue';

//pricetypes used in sales
export const priceTypes: displayValue[] = [
  {
    display: 'retailPrice',
    value: 0,
  },
  {
    display: 'wholesalePrice',
    value: 1,
  },
  {
    display: 'distributorPrice',
    value: 2,
  },
  {
    display: 'purchasePrice',
    value: 3,
  },
  {
    display: 'avgPurchasePrice',
    value: 4,
  },
];
// expanded colums used in sales
export const salesExpandedColumns: Array<displayValue | miscOptions> = [
  {
    display: 'sales.exp-itemCode',
    value: 'itemCode',
  },
  {
    display: 'sales.exp-wareHouse',
    value: 'warehouseName',
  },
  {
    display: 'sales.exp-vatPct',
    value: 'vat',
  },
  {
    display: 'sales.exp-currentQty',
    value: 'totalQuantityPerUnit',
  },
  {
    display: 'sales.exp-partNo',
    value: 'partNumber',
  },
  {
    display: 'sales.exp-discount',
    value: 'discount',
  },
  {
    display: 'sales.exp-discountType',
    value: 'isGeneralDscntMethod',
    convert: { false: 'Amount', true: 'Percentage' },
  },
];

export const invoiceStatusTypes = [
  {
    display: 'sales.exp-discount',
    value: 'NOTRETURNED',
  },
  {
    display: 'sales.exp-discount',
    value: 'PARTIALLYRETURNED',
  },
  {
    display: 'sales.exp-discount',
    value: 'RETURNED',
  },
];

export const sections: Provision[] = [
  {
    name: 'offer',
    clause: '',
  },
  {
    name: 'guarante',
    clause: '',
  },
  {
    name: 'delivery',
    clause: '',
  },
  {
    name: 'payment',
    clause: '',
  },
];

export const accountGroups: displayValue[] = [
  {
    display: 'accountGrpDropDown.assets',
    value: 'ASSETS',
  },
  {
    display: 'accountGrpDropDown.liabilities',
    value: 'LIABILITIES',
  },
  {
    display: 'accountGrpDropDown.capital',
    value: 'EQUITY',
  },
  {
    display: 'accountGrpDropDown.revenues',
    value: 'REVENUES',
  },
  {
    display: 'accountGrpDropDown.expenses',
    value: 'EXPENSES',
  },
];

export const accountTypes: displayValue[] = [
  {
    display: 'accountTypeDropDown.general',
    value: 'GENERAL',
  },
  {
    display: 'accountTypeDropDown.detailed',
    value: 'DETAILED',
  },
];

export const businessGroups: displayValue[] = [
  {
    display: 'bsnsGroupDropDown.general',
    value: 'GENERAL',
  },
  {
    display: 'bsnsGroupDropDown.cashier',
    value: 'CASHIER',
  },
  {
    display: 'bsnsGroupDropDown.bank',
    value: 'BANK',
  },
  {
    display: 'bsnsGroupDropDown.customer',
    value: 'CUSTOMER',
  },
  {
    display: 'bsnsGroupDropDown.supplier',
    value: 'SUPPLIER',
  },
  {
    display: 'bsnsGroupDropDown.branch',
    value: 'BRANCH',
  },
  {
    display: 'bsnsGroupDropDown.distributor',
    value: 'DISTRIBUTOR',
  },
];

export const finalAccountGroup: displayValue[] = [
  {
    display: 'finalAccountGrpDropDown.budget',
    value: 'BUDGET',
  },
  {
    display: 'finalAccountGrpDropDown.operation',
    value: 'OPERATION',
  },
  {
    display: 'finalAccountGrpDropDown.trading',
    value: 'TRADING',
  },
  {
    display: 'finalAccountGrpDropDown.ProfitandLoss',
    value: 'PROFIT_LOSS',
  },
  {
    display: 'finalAccountGrpDropDown.income',
    value: 'NET_INCOME',
  },
];

export const journalEntryType: displayValue[] = [
  {
    display: 'journalEntryDropDowns.normal',
    value: 'NORMAL',
  },
  {
    display: 'journalEntryDropDowns.opening',
    value: 'OPEN_GEN',
  },
  {
    display: 'journalEntryDropDowns.operationClose',
    value: 'CLOSE_OPR',
  },
  {
    display: 'journalEntryDropDowns.tradeClose',
    value: 'CLOSE_TRD',
  },
  {
    display: 'journalEntryDropDowns.P/LClose',
    value: 'CLOSE_PL',
  },
  {
    display: 'journalEntryDropDowns.incClose',
    value: 'CLOSE_INC',
  },
];

export const journalEntryCreationTypes = [
  {
    display: 'journalEntryCreationTypesDropDowns.manual',
    value: 'MANUAL',
  },
  {
    display: 'journalEntryCreationTypesDropDowns.automatic',
    value: 'AUTOMATIC',
  },
];

export const depreciationMethodTypes = [
  {
    display: 'depreciationMethodDropDown.straightLine',
    value: 'STRAIGHT_LINE',
  },
  {
    display: 'depreciationMethodDropDown.decliningBalance',
    value: 'DECLINING_BALANCE',
  },
];

export const salesPaymentStatus = [
  {
    display: 'salesStatusDropDown.notPaid',
    value: 'UNPAID',
  },
  {
    display: 'salesStatusDropDown.partiallyPaid',
    value: 'PARTIALLY_PAID',
  },
  {
    display: 'salesStatusDropDown.fullyPaid',
    value: 'FULLY_PAID',
  },
];
// {
//   display: 'salesStatusDropDown.allInvoices',
//   value: 'ALL_STATUS',
// },

export const issueType = [
  {
    display: 'issueTypeDropDown.general',
    value: 'GENERAL',
  },
  {
    display: 'issueTypeDropDown.vatexpenses',
    value: 'VAT_EXP',
  },
  {
    display: 'issueTypeDropDown.vatrecon',
    value: 'VAT_RECON',
  },
];

export const allowedFeatures = [
  {
    display: 'Stock System',
    value: 1,
  },
  {
    display: 'System Setup',
    value: 2,
  },
  {
    display: 'Access/Identity Management',
    value: 3,
  },
  {
    display: 'Accounting System',
    value: 4,
  },
  {
    display: 'Reports  Management',
    value: 5,
  },
  {
    display: 'Sales and Purchase',
    value: 6,
  },
];

export const stockTypes = [
  {
    display: 'stockTransfer.inward',
    value: false,
  },
  {
    display: 'stockTransfer.outward',
    value: true,
  },
];

export const indentifactionCode = [
  {
    display: 'Commercial registration number',
    value: 'CRN',
  },
  {
    display: 'Momra license',
    value: 'MOM',
  },
  {
    display: 'MLSD license',
    value: 'MLS',
  },
  {
    display: 'Sagia license',
    value: 'SAG',
  },
  {
    display: 'Other ID',
    value: 'OTH',
  },
];

export const customerIndentifactionCode = [
  {
    display: 'identificationcodes.NAT',
    value: 'NAT',
  },
  {
    display: 'identificationcodes.TIN',
    value: 'TIN',
  },
  {
    display: 'identificationcodes.IQA',
    value: 'IQA',
  },
  {
    display: 'identificationcodes.PAS',
    value: 'PAS',
  },
  {
    display: 'identificationcodes.CRN',
    value: 'CRN',
  },
  {
    display: 'identificationcodes.MOM',
    value: 'MOM',
  },
  {
    display: 'identificationcodes.MLS',
    value: 'MLS',
  },
  {
    display: 'identificationcodes.SAG',
    value: 'SAG',
  },
  {
    display: 'identificationcodes.GCC',
    value: 'GCC',
  },
  {
    display: 'identificationcodes.OTH',
    value: 'OTH',
  },
];

export const externalBranchPartner = {
  id: 99,
  branchId: 0,
  branchName: 'External Branch Partner',
  years: null,
  nameEnglish: 'External Branch Partner',
  nameArabic: 'External Branch Partner',
  vatNumber: null,
  phoneNumber: null,
  emailId: null,
  maxWarehouses: null,
  address: null,
};

export const invoiceStages = [
  { display: 'zatcaInvoiceStatus.EINVOICE_SAVED', value: 'EINVOICE_SAVED' },
  { display: 'zatcaInvoiceStatus.TO_BE_RESUBMITTED', value: 'TO_BE_RESUBMITTED' },
  { display: 'zatcaInvoiceStatus.REJECTED', value: 'REJECTED' },
  { display: 'zatcaInvoiceStatus.CLEARED', value: 'CLEARED' },
  { display: 'zatcaInvoiceStatus.CLEARED_WITH_WARNINGS', value: 'CLEARED_WITH_WARNINGS' },
  { display: 'zatcaInvoiceStatus.INVOICE_CREATED', value: 'INVOICE_CREATED' },
];

export const invoiceType = [
  { display: 'zatcaInvoiceStatus.SIMPLIFIED', value: 'SIMPLIFIED' },
  { display: 'zatcaInvoiceStatus.STANDARD', value: 'STANDARD' },
];

export const fixedPercentageDropDown = [
  { display: '$', value: false },
  { display: '%', value: true },
];

export const itemTypes = [
  { display: 'Goods', value: 0 },
  { display: 'Services', value: 1 },
];

export const supplierTypes = [
  {
    display: 'supplierTypes.cashPurchase',
    value: false,
  },
  {
    display: 'supplierTypes.registeredSupplier',
    value: true,
  },
];

export const customerTypes = [
  {
    display: 'customerTypes.walkIn',
    value: false,
  },
  {
    display: 'customerTypes.Existing',
    value: true,
  },
];

export const salesTransactionTypes: displayValue[] = [
  {
    display: 'transactionTypes.all',
    value: 'ALL',
  },
  {
    display: 'transactionTypes.sales',
    value: 'SALES',
  },
  {
    display: 'transactionTypes.allNotes',
    value: 'ALLNOTES',
  },
  {
    display: 'transactionTypes.creditNote',
    value: 'CREDIT_NOTE',
  },
  {
    display: 'transactionTypes.debitNote',
    value: 'DEBIT_NOTE',
  },
  {
    display: 'transactionTypes.quotation',
    value: 'QUOTATION',
  },
];

export const settlementStatus: displayValue[] = [
  {
    display: 'settlementStatus.unpaid',
    value: 'UNPAID',
  },
  {
    display: 'settlementStatus.partiallyPaid',
    value: 'PARTIALLY_PAID',
  },
  {
    display: 'settlementStatus.fullyPaid',
    value: 'FULLY_PAID',
  },
];

export const invoiceStatus: displayValue[] = [
  {
    display: 'invoiceStatus.notReturned',
    value: 'NOTRETURNED',
  },
  {
    display: 'invoiceStatus.partiallyReturned',
    value: 'PARTIALLYRETURNED',
  },
  {
    display: 'invoiceStatus.returned',
    value: 'RETURNED',
  },
];

export const purchaseTransactionTypes: displayValue[] = [
  {
    display: 'purchaseTransactionTypes.purchase',
    value: 'PURCHASE',
  },
  {
    display: 'purchaseTransactionTypes.creditNote',
    value: 'CREDIT_NOTE',
  },
];
