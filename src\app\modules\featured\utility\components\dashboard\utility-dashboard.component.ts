import { Component } from '@angular/core';
import { DashboardModulesHolder } from 'src/app/modules/shared/components/dashboard-modules-holder/modulesHolder';

@Component({
  selector: 'app-utility-dashboard',
  templateUrl: './utility-dashboard.component.html',
  styleUrls: ['./utility-dashboard.component.scss'],
})
export class UtilityDashboardComponent {
  utilityModulesList: DashboardModulesHolder[] = [
    {
      moduleName: 'Data Import',
      moduleDescription: 'Import the data.',
      modulePermission: ['ReportsManagement', 'AllPermissions'],
      moduleImage: 'inventory',
      moduleRouterLink: '../dataImport',
      moduleButtonAction: 'BackOffice',
      moduleType: 'subModule',
    },
    {
      moduleName: 'Branch Setup',
      moduleDescription: 'Branch setup.',
      modulePermission: ['ReportsManagement', 'AllPermissions'],
      moduleImage: 'ad_units',
      moduleRouterLink: '../branchSetup',
      moduleButtonAction: 'BackOffice',
      moduleType: 'subModule',
    },
  ];
}
