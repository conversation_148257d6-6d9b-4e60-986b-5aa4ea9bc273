import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ICompany } from 'src/app/core/interfaces/company';
import { environment } from 'src/environments/environment';

@Injectable()
export class CompanyService {
  baseUrl = environment.apiUrl + 'company';

  constructor(private http: HttpClient) {}

  createCompany(data: FormData) {
    return this.http.post(this.baseUrl, data);
  }

  getCompanyById() {
    return this.http.get<ICompany>(this.baseUrl);
  }

  updateCompany(data: FormData) {
    return this.http.put(this.baseUrl, data);
  }
}
