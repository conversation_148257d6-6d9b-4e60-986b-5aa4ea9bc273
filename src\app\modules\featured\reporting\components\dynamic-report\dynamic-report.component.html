<!-- Report Selection -->
<div class="report-selection" *ngIf="!selectedReport">
  <h2>Select a Report</h2>
  <div class="report-cards">
    <mat-card
      class="report-card"
      *ngFor="let report of reportConfigs"
      (click)="selectReport(report)">
      <mat-card-header>
        <mat-card-title>{{ report.name }}</mat-card-title>
        <mat-card-subtitle *ngIf="report.nameArabic">{{ report.nameArabic }}</mat-card-subtitle>
      </mat-card-header>
    </mat-card>
  </div>
</div>

<!-- Selected Report -->
<div class="selected-report" *ngIf="selectedReport">
  <!-- Report Header -->
  <div class="report-header">
    <div class="report-info">
      <h2>{{ selectedReport.name }}</h2>
      <p *ngIf="selectedReport.nameArabic">{{ selectedReport.nameArabic }}</p>
    </div>
    <div class="report-actions">
      <button (click)="resetReport()" mat-icon-button matTooltip="Change Report">
        <mat-icon>edit</mat-icon>
      </button>
      <button
        *ngIf="dataSource.data.length > 0"
        (click)="downloadPdf()"
        mat-icon-button
        matTooltip="Download PDF">
        <mat-icon>download</mat-icon>
      </button>
    </div>
  </div>

  <!-- Filters -->
  <mat-card class="filters-card">
    <mat-card-header>
      <mat-card-title>Filters</mat-card-title>
      <div class="filter-actions">
        <button
          *ngIf="selectedReport.searchConfigs.some(c => c.isAdvanced)"
          (click)="toggleAdvancedFilters()"
          mat-button>
          {{ showAdvancedFilters ? 'Hide' : 'Show' }} Advanced Filters
        </button>
      </div>
    </mat-card-header>

    <mat-card-content>
      <form *ngIf="filterForm" [formGroup]="filterForm">
        <div class="filters-grid">
          <ng-container *ngFor="let config of selectedReport.searchConfigs">
            <div
              class="filter-field"
              *ngIf="isFieldVisible(config)"
              [class.advanced]="config.isAdvanced">
              <!-- Dropdown Field -->
              <mat-form-field *ngIf="config.type === 'dropdown'" appearance="outline">
                <mat-label>{{ config.fieldLabel | titlecase }}</mat-label>
                <mat-select [formControlName]="config.backendParam">
                  <mat-option [value]="null">None</mat-option>
                  <mat-option
                    *ngFor="let item of getFieldData(config.backendParam)"
                    [value]="item[config.idField]">
                    {{ item.name || item.year || item.display }}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="filterForm.get(config.backendParam)?.hasError('required')">
                  {{ config.fieldLabel | titlecase }} is required
                </mat-error>
              </mat-form-field>

              <!-- Multi-Select Dropdown -->
              <mat-form-field *ngIf="config.type === 'multiSelectDropdown'" appearance="outline">
                <mat-label>{{ config.fieldLabel | titlecase }}</mat-label>
                <mat-select [formControlName]="config.backendParam" multiple>
                  <mat-option
                    *ngFor="let item of getFieldData(config.backendParam)"
                    [value]="item[config.idField]">
                    {{ item.name || item.year || item.display }}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="filterForm.get(config.backendParam)?.hasError('required')">
                  {{ config.fieldLabel | titlecase }} is required
                </mat-error>
              </mat-form-field>

              <!-- Input Field -->
              <mat-form-field *ngIf="config.type === 'input'" appearance="outline">
                <mat-label>{{ config.fieldLabel | titlecase }}</mat-label>
                <input
                  [formControlName]="config.backendParam"
                  [placeholder]="config.placeholder"
                  matInput />
                <mat-error *ngIf="filterForm.get(config.backendParam)?.hasError('required')">
                  {{ config.fieldLabel | titlecase }} is required
                </mat-error>
              </mat-form-field>

              <!-- Date Field -->
              <mat-form-field *ngIf="config.type === 'date'" appearance="outline">
                <mat-label>{{ config.fieldLabel | titlecase }}</mat-label>
                <input [matDatepicker]="picker" [formControlName]="config.backendParam" matInput />
                <mat-datepicker-toggle [for]="picker" matSuffix></mat-datepicker-toggle>
                <mat-datepicker #picker></mat-datepicker>
                <mat-error *ngIf="filterForm.get(config.backendParam)?.hasError('required')">
                  {{ config.fieldLabel | titlecase }} is required
                </mat-error>
              </mat-form-field>

              <!-- Date Range Field -->
              <mat-form-field *ngIf="config.type === 'dateRange'" appearance="outline">
                <mat-label>{{ config.fieldLabel | titlecase }}</mat-label>
                <mat-date-range-input [rangePicker]="rangePicker">
                  <input
                    [formControlName]="config.backendParam + 'From'"
                    matStartDate
                    placeholder="Start date" />
                  <input
                    [formControlName]="config.backendParam + 'To'"
                    matEndDate
                    placeholder="End date" />
                </mat-date-range-input>
                <mat-datepicker-toggle [for]="rangePicker" matSuffix></mat-datepicker-toggle>
                <mat-date-range-picker #rangePicker></mat-date-range-picker>
                <mat-error
                  *ngIf="filterForm.get(config.backendParam + 'From')?.hasError('required')">
                  {{ config.fieldLabel | titlecase }} is required
                </mat-error>
              </mat-form-field>
            </div>
          </ng-container>
        </div>

        <div class="form-actions">
          <button
            [disabled]="isGeneratingReport"
            (click)="generateReport()"
            mat-raised-button
            color="primary">
            <mat-icon *ngIf="isGeneratingReport">hourglass_empty</mat-icon>
            {{ isGeneratingReport ? 'Generating...' : 'Generate Report' }}
          </button>
          <button (click)="filterForm.reset()" mat-button type="button">Clear Filters</button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>

  <!-- Column Selection -->
  <mat-card class="column-selection" *ngIf="dataSource.data.length > 0">
    <mat-form-field appearance="outline">
      <mat-label>Select Columns</mat-label>
      <mat-select
        [value]="displayedColumns"
        (selectionChange)="updateDisplayedColumns($event.value)"
        multiple>
        <mat-option *ngFor="let column of allColumns" [value]="column">
          {{ column | titlecase }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </mat-card>

  <!-- Results Table -->
  <mat-card class="results-card">
    <mat-card-header>
      <mat-card-title>Results</mat-card-title>
    </mat-card-header>

    <mat-card-content>
      <!-- No Data -->
      <div class="no-data" *ngIf="dataSource.data.length === 0 && !isGeneratingReport">
        <mat-icon>search</mat-icon>
        <p>No data found. Please adjust your filters and try again.</p>
      </div>

      <!-- Loading -->
      <div class="loading" *ngIf="isGeneratingReport">
        <mat-spinner diameter="50"></mat-spinner>
        <p>Generating report...</p>
      </div>

      <!-- Data Table -->
      <div class="table-container" *ngIf="dataSource.data.length > 0">
        <table class="results-table" [dataSource]="dataSource" mat-table matSort>
          <ng-container *ngFor="let column of displayedColumns" [matColumnDef]="column">
            <th *matHeaderCellDef mat-header-cell mat-sort-header>
              {{ column | titlecase }}
            </th>
            <td *matCellDef="let element" mat-cell>
              {{ element[column] }}
            </td>
          </ng-container>

          <tr *matHeaderRowDef="displayedColumns" mat-header-row></tr>
          <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
        </table>

        <!-- Pagination -->
        <mat-paginator
          [length]="totalItems"
          [pageSize]="pageSize"
          [pageIndex]="currentPage"
          [pageSizeOptions]="[25, 50, 100, 200]"
          (page)="onPageChange($event)"
          showFirstLastButtons>
        </mat-paginator>
      </div>
    </mat-card-content>
  </mat-card>
</div>
