Report Selection
<div class="report-selection" *ngIf="!selectedReport">
  <h2>Select a Report</h2>
  <div class="report-cards">
    <mat-card
      class="report-card"
      *ngFor="let report of reportConfigs"
      (click)="selectReport(report)">
      <mat-card-header>
        <mat-card-title>{{ report.name }}</mat-card-title>
        <mat-card-subtitle *ngIf="report.nameArabic">{{ report.nameArabic }}</mat-card-subtitle>
      </mat-card-header>
    </mat-card>
  </div>
</div>

<!-- Selected Report -->
<div class="selected-report" *ngIf="selectedReport">
  <!-- Report Header -->
  <div class="report-header">
    <div class="report-info">
      <h2>{{ selectedReport.name }}</h2>
      <p *ngIf="selectedReport.nameArabic">{{ selectedReport.nameArabic }}</p>
    </div>
    <div class="report-actions">
      <button (click)="resetReport()" mat-icon-button matTooltip="Change Report">
        <mat-icon>edit</mat-icon>
      </button>
      <button
        *ngIf="dataSource?.data?.length > 0"
        (click)="downloadPdf()"
        mat-icon-button
        matTooltip="Download PDF">
        <mat-icon>download</mat-icon>
      </button>
    </div>
  </div>

  <!-- Filters -->
  <mat-card class="filters-card">
    <mat-card-header>
      <mat-card-title>Filters</mat-card-title>
      <div class="filter-actions">
        <button
          *ngIf="selectedReport.searchConfigs.some(c => c.isAdvanced)"
          (click)="toggleAdvancedFilters()"
          mat-button>
          {{ showAdvancedFilters ? 'Hide' : 'Show' }} Advanced Filters
        </button>
      </div>
    </mat-card-header>

    <mat-card-content>
      <form *ngIf="filterForm" [formGroup]="filterForm">
        <div class="filters-grid">
          <ng-container *ngFor="let config of selectedReport.searchConfigs">
            <div
              class="filter-field"
              *ngIf="isFieldVisible(config)"
              [ngClass]="{ advanced: config.isAdvanced }">
              <!-- Dropdown Field -->
              <mat-form-field *ngIf="config.type === 'dropdown'" appearance="outline">
                <mat-label>{{ config.fieldLabel | titlecase }}</mat-label>
                <mat-select formControlName="config.backendParam">
                  <mat-option [value]="null">None</mat-option>
                  <mat-option
                    *ngFor="let item of getFieldData(config.backendParam)"
                    [value]="item[config.idField]">
                    {{ item.name || item.year || item.display }}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="filterForm.get(config.backendParam)?.hasError('required')">
                  {{ config.fieldLabel | titlecase }} is required </mat-error
                >formControlName
              </mat-form-field>

              <!-- Multi-Select Dropdown -->
              <mat-form-field *ngIf="config.type === 'multiSelectDropdown'" appearance="outline">
                <mat-label>{{ config.fieldLabel | titlecase }}</mat-label>
                <mat-select ="config.backendParam" multiple>
                  <mat-option
                    *ngFor="let item of getFieldData(config.backendParam)"
                    [value]="item[config.idField]">
                    {{ item.name || item.year || item.display }}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="filterForm.get(config.backendParam)?.hasError('required')">
                  {{ config.fieldLabel | titlecase }} is required
                </mat-error>
              </mat-form-field>

              <!-- Input Field -->
              <mat-form-field *ngIf="config.type === 'input'" appearance="outline">
                <mat-label>{{ config.fieldLabel | titlecase }}</mat-label>
                <input
                  [placeholder]="config.placeholder"
                  formControlName="config.backendParam"
                  matInput />
                <mat-error *ngIf="filterForm.get(config.backendParam)?.hasError('required')">
                  {{ config.fieldLabel | titlecase }} is required
                </mat-error>
              </mat-form-field>

              <!-- Date Field -->
              <mat-form-field *ngIf="config.type === 'date'" appearance="outline">
                <mat-label>{{ config.fieldLabel | titlecase }}</mat-label>
                <input formControlName="config.backendParam" matInput />
                <mat-datepicker-toggle [for]="picker" matSuffix></mat-datepicker-toggle>
                <mat-datepicker #picker></mat-datepicker>
              </mat-form-field>

              <!-- Date Range Field -->
              <div class="date-range-container" *ngIf="config.type === 'dateRange'">
                <mat-form-field appearance="outline">
                  <mat-label>From</mat-label>
                  <input formControlName="getDateRangeControlName(config, 'From')" matInput />
                  <mat-datepicker-toggle [for]="fromPicker" matSuffix></mat-datepicker-toggle>
                  <mat-datepicker #fromPicker></mat-datepicker>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>To</mat-label>
                  <input formControlName="getDateRangeControlName(config, 'To')" matInput />
                  <mat-datepicker-toggle [for]="toPicker" matSuffix></mat-datepicker-toggle>
                  <mat-datepicker #toPicker></mat-datepicker>
                </mat-form-field>
              </div>
            </div>
          </ng-container>
        </div>

        <div class="form-actions">
          <button
            [disabled]="isGeneratingReport"
            (click)="generateReport()"
            mat-raised-button
            color="primary">
            <mat-icon *ngIf="isGeneratingReport">hourglass_empty</mat-icon>
            {{ isGeneratingReport ? 'Generating...' : 'Generate Report' }}
          </button>
          <button (click)="filterForm.reset()" mat-button type="button">Clear Filters</button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>

  <!-- Results Message -->
  <mat-card class="results-card">
    <mat-card-header>
      <mat-card-title>Results</mat-card-title>
    </mat-card-header>

    <mat-card-content>
      <!-- No Data -->
      <div class="no-data" *ngIf="!isGeneratingReport">
        <mat-icon>search</mat-icon>
        <p>Click "Generate Report" to see results. This is a demo with hardcoded data.</p>
      </div>

      <!-- Loading -->
      <div class="loading" *ngIf="isGeneratingReport">
        <mat-spinner diameter="50"></mat-spinner>
        <p>Generating report...</p>
      </div>
    </mat-card-content>
  </mat-card>
</div>
