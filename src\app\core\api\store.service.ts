import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { AuthService } from 'src/app/modules/core/core/services/auth.service';
import { LocalStorageService } from 'src/app/modules/core/core/services/local-storage.service';
import { StoreParams } from '../../modules/featured/settings/models/storeParams';
import { WareHouse } from '../../modules/featured/catalog/models/store';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class StoreService {
  baseUrl = environment.apiUrl + 'company/warehouses';
  constructor(
    private authService: AuthService,
    private localStorage: LocalStorageService,
    private http: HttpClient
  ) {}

  getStores(storeParams: StoreParams, companyId: string): Observable<any> {
    const params = this.getParams(storeParams);
    return this.http.get(this.baseUrl + `?companyId=${companyId}`, { params: params });
  }

  getBrancheStores(storeParams: StoreParams): Observable<WareHouse[]> {
    const params = this.getParams(storeParams);
    return this.http.get<WareHouse[]>(this.baseUrl, { params: params });
  }

  getAllStores(storeParams: StoreParams): Observable<WareHouse[]> {
    const params = this.getParams(storeParams);
    return this.http.get<WareHouse[]>(this.baseUrl + `/all`, { params: params });
  }

  getById(storeParams: StoreParams, storeID: string): Observable<any> {
    const params = this.getParams(storeParams);
    return this.http.get(this.baseUrl + '/' + storeID, { params: params });
  }

  create(storeParams: StoreParams, store: any): Observable<any> {
    const params = this.getParams(storeParams);
    return this.http.post(this.baseUrl, store, { params: params });
  }

  update(storeParams: StoreParams, storeId: string, store: any): Observable<any> {
    const params = this.getParams(storeParams);
    return this.http.put(this.baseUrl + '/' + storeId, store, { params: params });
  }

  deleteStore(warehouseId: string) {
    const params = new HttpParams();
    return this.http.delete(this.baseUrl + '/' + warehouseId, { params: params });
  }

  importData(formData: FormData) {
    const params = new HttpParams();
    return this.http
      .post(this.baseUrl + '/import', formData, {
        withCredentials: true,
        responseType: 'arraybuffer',
        params: params,
      })
      .pipe(
        tap((result: ArrayBuffer) => {
          const fileURL = URL.createObjectURL(new Blob([result]));
          const link = document.createElement('a');
          link.href = fileURL;
          link.setAttribute('download', 'Store_List.xls');
          document.body.appendChild(link);
          link.click();
        })
      );
  }

  getParams(storeParams: StoreParams): HttpParams {
    let params = new HttpParams();
    storeParams.companyId = this.authService.getCompanyID;
    storeParams.currentBranchId = this.localStorage.getItem('currentBranchId');
    params = params.append('yearId', this.localStorage.getItem('yearId'));
    if (storeParams.companyId) params = params.append('companyId', storeParams.companyId);
    if (storeParams.branchIds)
      params = params.append('branchIds', storeParams.branchIds.toString());
    if (storeParams.currentBranchId)
      params = params.append('currentBranchId', storeParams.currentBranchId);
    if (storeParams.searchString) params = params.append('searchString', storeParams.searchString);
    if (storeParams.pageNumber)
      params = params.append('pageNumber', storeParams.pageNumber.toString());
    if (storeParams.pageSize) params = params.append('pageSize', storeParams.pageSize.toString());
    if (storeParams.orderBy) params = params.append('orderBy', storeParams.orderBy.toString());
    return params;
  }
}
