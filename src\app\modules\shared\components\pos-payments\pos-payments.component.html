<app-dialog-header [title]="'confirmationModal.delete' | translate"></app-dialog-header>
<form class="m-t-20 dense-0 f-s-24 p-2" *ngIf="paymentForm" [formGroup]="paymentForm">
  <div formArrayName="payments">
    <div
      class="row no-gutters m-t-20"
      *ngFor="let paymentType of ['CASH', 'CARD/ONLINE']; let i = index"
      [formGroupName]="i">
      <div class="col-md-6 col-lg-6 col-sm-12 p-2 centered-form-field">
        <mat-label>{{ paymentType }}</mat-label>
        <mat-form-field class="w-100 f-s-24">
          <input
            id="paymentAmount{{ i }}"
            [min]="0"
            (input)="calculateChange()"
            autofocus
            style="color: green"
            matInput
            type="number"
            formControlName="amount" />
          <button (click)="clearAmount(i)" mat-icon-button matSuffix tabindex="-1">
            <mat-icon>clear</mat-icon>
          </button>
        </mat-form-field>
      </div>

      <div class="col-md-6 col-lg-6 col-sm-12 p-2 centered-form-field">
        <mat-label>Notes</mat-label>
        <mat-form-field class="w-100 f-s-20">
          <textarea id="paymentNotes{{ i }}" matInput formControlName="notes"></textarea>
        </mat-form-field>
      </div>
    </div>
  </div>

  <div class="text-center m-t-20 f-w-600">Total Amount: {{ totalAmount }}</div>

  <div class="m-t-10" class="text-center f-w-600 m-t-20">
    <div
      *ngIf="isAmountEntered && totalPayments > totalAmount && !isCardAmountEntered"
      style="color: blue">
      Change Return: {{ calculateCashChange() }}
    </div>

    <div
      class="text-center f-w-600 m-t-20"
      *ngIf="isAmountEntered && totalPayments > totalAmount && isCardAmountEntered"
      style="color: red">
      Excess Amount : {{ excessAmount }}
    </div>

    <div
      class="text-center f-w-600 m-t-20"
      *ngIf="isAmountEntered && totalPayments < totalAmount"
      style="color: red">
      Balance Amount : {{ pendingAmount }}
    </div>
  </div>

  <mat-dialog-actions align="center">
    <button
      class="action-buttons"
      [mat-dialog-close]="true"
      [disabled]="!isCashAmountValid()"
      mat-flat-button
      color="warn">
      Confirm Payment
    </button>
    <button class="action-buttons" mat-stroked-button color="primary">
      {{ 'common.cancel' | translate }}
    </button>
  </mat-dialog-actions>
</form>
