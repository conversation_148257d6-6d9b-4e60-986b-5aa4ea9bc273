import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { CostCentre } from 'src/app/modules/featured/accounts/models/costCentre';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class CostCentresApiService {
  baseUrl = environment.apiUrl + 'accounts/costcentres';

  constructor(private http: HttpClient) {}

  getAllCostCentres() {
    // const params = this.getParams(new CostCentresParams());
    return this.http.get<CostCentre[]>(this.baseUrl);
  }

  getCostCentreById(id: string) {
    // const params = this.getParams(new CostCentresParams());
    return this.http.get<CostCentre>(this.baseUrl + '/' + id);
  }

  createCostCentres(costCentres: CostCentre) {
    // const params = this.getParams(new CostCentresParams());
    return this.http.post(this.baseUrl, costCentres);
  }

  updateCostCentres(costCentre: CostCentre, id: string) {
    // const params = this.getParams(new CostCentresParams());
    return this.http.put(this.baseUrl + '/' + id, costCentre);
  }
}
