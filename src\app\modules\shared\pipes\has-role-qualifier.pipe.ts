import { Pipe, PipeTransform } from '@angular/core';
import { AuthService } from '../../core/core/services/auth.service';
import { DashboardModulesHolder } from '../components/dashboard-modules-holder/modulesHolder';

@Pipe({
  name: 'hasRoleQualifier',
})
export class HasRoleQualifierPipe implements PipeTransform {
  constructor(private authService: AuthService) {}
  transform(value: DashboardModulesHolder[]): DashboardModulesHolder[] {
    if (value[0].moduleType === 'mainModule') {
      return value.filter(modules =>
        this.authService.isAuthorized('Permission', modules.modulePermission)
      );
    } else {
      return value.filter(modules =>
        this.authService.isAuthorized('Permission', modules.modulePermission)
      );
    }
  }
}
